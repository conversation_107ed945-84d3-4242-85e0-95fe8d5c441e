{
  description = "Forge Protocol - Mobile-first fitness tracking API";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        
        # Python environment with specific packages
        pythonEnv = pkgs.python310.withPackages (ps: with ps; [
          pip
          virtualenv
          setuptools
          wheel
          # Core dependencies that benefit from Nix packaging
          psycopg2
          cryptography
          cffi
        ]);
        
      in {
        devShells.default = pkgs.mkShell {
          name = "forge-protocol-dev";
          
          buildInputs = with pkgs; [
            # Python environment
            pythonEnv
            
            # Database services
            postgresql_15
            redis
            
            # Development tools
            git
            curl
            jq
            tree
            htop
            
            # Docker for containerization
            docker
            docker-compose
            
            # System dependencies for Python packages
            pkg-config
            libffi
            openssl
            zlib
            
            # Build tools for Python packages
            gcc
            gnumake
            
            # Optional development utilities
            tmux
            vim
            
            # Database management tools
            pgcli
            redis-cli
          ];
          
          shellHook = ''
            echo "🔥 Forge Protocol Development Environment (Flake)"
            echo "================================================"
            echo "Python: $(python --version)"
            echo "PostgreSQL: $(postgres --version | head -n1)"
            echo "Redis: $(redis-server --version)"
            echo "Docker: $(docker --version)"
            echo ""
            echo "📋 Development Commands:"
            echo "  • nix develop                    # Enter this shell"
            echo "  • pip install -r requirements/dev.txt  # Install Python deps"
            echo "  • docker compose -f docker/docker-compose.yml up -d  # Start services"
            echo "  • python -m alembic upgrade head  # Run migrations"
            echo "  • python -m uvicorn app.main:app --reload  # Start API"
            echo "  • python -m pytest              # Run tests"
            echo ""
            echo "🎯 Mobile-first API development ready!"
            echo "🏗️  Clean Architecture | 🔐 JWT Auth | 🐘 PostgreSQL | 📱 Mobile-optimized"
            
            # Set up Python environment
            export PYTHONPATH="$PWD:$PYTHONPATH"
            
            # Ensure pip uses user directory
            export PIP_USER=1
            export PATH="$HOME/.local/bin:$PATH"
            
            # Development environment variables
            export ENVIRONMENT="development"
            export DEBUG="true"
            export PYTHONDONTWRITEBYTECODE="1"
            export PYTHONUNBUFFERED="1"
            
            # Database connection defaults
            export PGHOST="localhost"
            export PGPORT="5432"
            export PGUSER="rp_user"
            export PGPASSWORD="rp_password"
            export PGDATABASE="rp_training"
          '';
          
          # Environment variables
          env = {
            LD_LIBRARY_PATH = pkgs.lib.makeLibraryPath [
              pkgs.libffi
              pkgs.openssl
              pkgs.zlib
            ];
          };
        };
        
        # Package the application
        packages.default = pkgs.python310Packages.buildPythonApplication {
          pname = "forge-protocol";
          version = "0.1.0";
          src = ./.;
          
          propagatedBuildInputs = with pkgs.python310Packages; [
            fastapi
            uvicorn
            sqlalchemy
            alembic
            asyncpg
            python-jose
            passlib
            bcrypt
            python-multipart
            python-dotenv
            httpx
            redis
            pydantic
            pydantic-settings
          ];
          
          # Skip tests during build (they require database)
          doCheck = false;
          
          meta = with pkgs.lib; {
            description = "Mobile-first fitness tracking API with RP Scientific algorithms";
            homepage = "https://github.com/forkrul/forge-protocol";
            license = licenses.mit;
            maintainers = [ "forkrul" ];
          };
        };
      });
}
