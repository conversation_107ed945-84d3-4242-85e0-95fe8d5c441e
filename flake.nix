{
  description = "RP Training API - Renaissance Periodization Training Platform";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        python = pkgs.python311;
        pythonPackages = python.pkgs;

        # Custom Python environment with all dependencies
        pythonEnv = python.withPackages (ps: with ps; [
          # Core web framework
          fastapi
          uvicorn

          # Database
          sqlalchemy
          alembic
          asyncpg
          psycopg2

          # Authentication & Security
          passlib
          python-jose
          python-multipart
          bcrypt

          # HTTP client
          httpx

          # Configuration
          pydantic
          pydantic-settings
          python-dotenv

          # Testing
          pytest
          pytest-asyncio
          pytest-cov
          pytest-mock
          coverage

          # BDD Testing
          behave

          # Code Quality
          black
          isort
          flake8
          mypy
          bandit

          # Development tools
          pre-commit

          # Performance monitoring
          psutil

          # Additional utilities
          requests
          aiofiles
        ]);

      in
      {
        # Development shell
        devShells.default = pkgs.mkShell {
          name = "rp-training-api";

          buildInputs = with pkgs; [
            pythonEnv

            # Database tools
            postgresql
            redis

            # Docker
            docker
            docker-compose

            # Development tools
            git
            curl
            jq

            # Node.js for potential frontend tooling
            nodejs_20
          ];

          shellHook = ''
            echo "🏋️  RP Training API Development Environment"
            echo "================================================"
            echo "Python: $(python --version)"
            echo "FastAPI: $(python -c 'import fastapi; print(f"v{fastapi.__version__}")' 2>/dev/null || echo 'Not available')"
            echo "SQLAlchemy: $(python -c 'import sqlalchemy; print(f"v{sqlalchemy.__version__}")' 2>/dev/null || echo 'Not available')"
            echo "Pytest: $(python -c 'import pytest; print(f"v{pytest.__version__}")' 2>/dev/null || echo 'Not available')"
            echo ""
            echo "🧪 Testing Commands:"
            echo "  python scripts/run_tests.py --all         # Run all tests"
            echo "  python scripts/run_tests.py --unit        # Unit tests only"
            echo "  python scripts/run_tests.py --integration # Integration tests"
            echo "  python scripts/run_tests.py --coverage    # Coverage report"
            echo "  python scripts/run_tests.py --quick       # Quick test suite"
            echo ""
            echo "🚀 Development Commands:"
            echo "  python -m uvicorn app.main:app --reload   # Start dev server"
            echo "  python scripts/test_structure.py          # Test project structure"
            echo "  python -m alembic upgrade head            # Run migrations"
            echo ""
            echo "🔧 Code Quality:"
            echo "  black .                                    # Format code"
            echo "  isort .                                    # Sort imports"
            echo "  flake8 .                                   # Lint code"
            echo "  mypy app/                                  # Type checking"
            echo "  python scripts/run_tests.py --quality     # All quality checks"
            echo ""
            echo "🐳 Docker Commands:"
            echo "  ./scripts/deploy.sh                       # Full deployment"
            echo "  docker-compose -f docker/docker-compose.yml up -d  # Start services"
            echo ""

            # Set up environment
            export PYTHONPATH="$PWD:$PYTHONPATH"
            export TESTING=true
            export SECRET_KEY="dev-secret-key-for-nix-environment-only"
            export DATABASE_URL="sqlite+aiosqlite:///./test.db"
            export REDIS_URL="redis://localhost:6379/0"

            # Create directories
            mkdir -p logs htmlcov .pytest_cache

            # Make scripts executable
            chmod +x scripts/*.py scripts/*.sh 2>/dev/null || true

            echo "Environment ready! Run 'python scripts/run_tests.py --help' for testing options."
            echo "================================================"
          '';

          # Environment variables
          PYTHONPATH = ".";
          TESTING = "true";
          SECRET_KEY = "dev-secret-key-for-nix-environment";
          DATABASE_URL = "sqlite+aiosqlite:///./test.db";
        };

        # Package for the application
        packages.default = pythonPackages.buildPythonApplication {
          pname = "rp-training-api";
          version = "1.0.0";

          src = ./.;

          propagatedBuildInputs = with pythonPackages; [
            fastapi
            uvicorn
            sqlalchemy
            alembic
            asyncpg
            passlib
            python-jose
            python-multipart
            bcrypt
            httpx
            pydantic
            pydantic-settings
            python-dotenv
          ];

          checkInputs = with pythonPackages; [
            pytest
            pytest-asyncio
            pytest-cov
            pytest-mock
          ];

          # Skip tests during build for now
          doCheck = false;

          meta = with pkgs.lib; {
            description = "Renaissance Periodization Training API";
            homepage = "https://github.com/forkrul/forge-protocol";
            license = licenses.mit;
            maintainers = [ ];
          };
        };

        # Apps for easy running
        apps = {
          # Run the API server
          serve = flake-utils.lib.mkApp {
            drv = pkgs.writeShellScriptBin "rp-serve" ''
              cd ${./.}
              ${pythonEnv}/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
            '';
          };

          # Run tests
          test = flake-utils.lib.mkApp {
            drv = pkgs.writeShellScriptBin "rp-test" ''
              cd ${./.}
              ${pythonEnv}/bin/python scripts/run_tests.py "$@"
            '';
          };

          # Run migrations
          migrate = flake-utils.lib.mkApp {
            drv = pkgs.writeShellScriptBin "rp-migrate" ''
              cd ${./.}
              ${pythonEnv}/bin/python -m alembic upgrade head
            '';
          };
        };
      });
}
