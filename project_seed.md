# Renaissance Periodization Training App - Product Requirements Document

## Executive Summary

### Project Overview
The Renaissance Periodization (RP) Training App is a comprehensive hypertrophy-focused training platform that implements evidence-based periodization principles. The application leverages the scientific methodologies outlined in "Scientific Principles of Hypertrophy Training" to provide intelligent, adaptive training programs that optimize muscle growth through precise volume management and progressive overload.

### Key Objectives
- Implement RP's core algorithms for MEV estimation, set progression, and stimulus-to-fatigue ratio calculations
- Provide an intelligent mesocycle planning system with automated volume progression
- Deliver a comprehensive exercise database with proper form guidance
- Create a data-driven feedback system for continuous program optimization
- Build a scalable, maintainable platform following modern software engineering practices

### Target Users
- Intermediate to advanced trainees seeking evidence-based hypertrophy programming
- Personal trainers and coaches requiring scientifically-backed programming tools
- Fitness enthusiasts interested in optimizing their training through data-driven approaches

## Technical Architecture

### Technology Stack
- **Backend Framework**: FastAPI (Python 3.11+)
- **Data Validation**: Pydantic v2 with type hints (PEP 484)
- **Database**: PostgreSQL 15+ with asyncpg driver
- **Migrations**: Alembic for database schema management
- **Containerization**: Docker with multi-stage builds
- **Monitoring**: Cachet for service health monitoring
- **Testing**: pytest, behave (BDD), coverage.py
- **Code Quality**: black, isort, flake8, mypy
- **Documentation**: Sphinx with autodoc

### Architecture Principles
- Clean Architecture with dependency injection
- Domain-driven design for training concepts
- CQRS pattern for read/write operations
- Event-driven architecture for progress tracking
- RESTful API design with OpenAPI 3.0 specification

### Code Standards Compliance
- **PEP 8**: Code style and formatting
- **PEP 257**: Docstring conventions
- **PEP 484**: Type hints for all functions and classes

## Functional Requirements

### 1. User Management
- **User Registration/Authentication**: OAuth2 with JWT tokens
- **Profile Management**: Training experience, goals, preferences
- **Progress Tracking**: Historical data analysis and visualization
- **Soft Delete**: All user data retained for analytics (GDPR compliant)

### 2. Mesocycle Planning System
- **Template Selection**: Pre-built programs (Black Adam, Superman, etc.)
- **Custom Mesocycle Creation**: User-defined training splits
- **Volume Landmarks Configuration**: MEV, MAV, MRV per muscle group
- **Deload Week Scheduling**: Automatic and manual deload triggers

### 3. Workout Execution Engine
- **Real-time Workout Logging**: Set-by-set tracking with RIR
- **Exercise Database**: 500+ exercises with form videos
- **Auto-progression Logic**: Weight and rep recommendations
- **Rest Timer Integration**: Muscle group-specific rest periods

### 4. Scientific Algorithm Implementation

#### MEV Stimulus Estimator
```python
class MEVEstimator:
    def calculate_stimulus_score(
        self, 
        mind_muscle_connection: int,
        pump_rating: int,
        muscle_disruption: int
    ) -> MEVRecommendation
```

#### Set Progression Algorithm
```python
class SetProgressionCalculator:
    def calculate_weekly_sets(
        self,
        soreness_score: int,
        performance_score: int,
        current_sets: int
    ) -> int
```

#### Stimulus-to-Fatigue Ratio
```python
class SFRCalculator:
    def calculate_sfr(
        self,
        raw_stimulus_magnitude: int,
        fatigue_score: int
    ) -> float
```

### 5. Feedback and Analytics System
- **Post-Exercise Feedback**: Joint pain, pump, workload assessment
- **Weekly Progress Reviews**: Soreness and performance ratings
- **Volume Progression Tracking**: Automatic MEV→MAV progression
- **Exercise Effectiveness Analysis**: SFR-based exercise ranking

### 6. Template System
- **Pre-built Programs**: RP-certified training templates
- **Customization Engine**: Modify existing templates
- **Exercise Substitution**: Automatic alternatives based on equipment
- **Periodization Models**: Linear, undulating, block periodization

## Database Schema Design

### Core Entities

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    training_experience_years INTEGER,
    primary_goal training_goal_enum,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);
```

#### Mesocycles Table
```sql
CREATE TABLE mesocycles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    template_id UUID REFERENCES templates(id),
    start_date DATE NOT NULL,
    planned_weeks INTEGER NOT NULL,
    current_week INTEGER DEFAULT 1,
    status mesocycle_status_enum DEFAULT 'active',
    mev_multiplier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);
```

#### Exercises Table
```sql
CREATE TABLE exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    primary_muscle_group muscle_group_enum NOT NULL,
    secondary_muscle_groups muscle_group_enum[],
    movement_pattern movement_pattern_enum,
    equipment_required equipment_enum[],
    difficulty_level difficulty_enum,
    video_url VARCHAR(500),
    form_cues TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);
```

#### Workouts Table
```sql
CREATE TABLE workouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mesocycle_id UUID REFERENCES mesocycles(id),
    workout_date DATE NOT NULL,
    week_number INTEGER NOT NULL,
    day_number INTEGER NOT NULL,
    template_day_id UUID REFERENCES template_days(id),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_volume_load DECIMAL(10,2),
    average_rpe DECIMAL(3,1),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Exercise Sets Table
```sql
CREATE TABLE exercise_sets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workout_id UUID REFERENCES workouts(id),
    exercise_id UUID REFERENCES exercises(id),
    set_number INTEGER NOT NULL,
    weight_kg DECIMAL(5,2),
    reps_completed INTEGER,
    reps_target INTEGER,
    rir_target INTEGER,
    rir_actual INTEGER,
    rest_seconds INTEGER,
    rpe DECIMAL(3,1),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### MEV Feedback Table
```sql
CREATE TABLE mev_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workout_id UUID REFERENCES workouts(id),
    muscle_group muscle_group_enum NOT NULL,
    mind_muscle_connection INTEGER CHECK (mind_muscle_connection BETWEEN 0 AND 3),
    pump_rating INTEGER CHECK (pump_rating BETWEEN 0 AND 3),
    muscle_disruption INTEGER CHECK (muscle_disruption BETWEEN 0 AND 3),
    total_score INTEGER GENERATED ALWAYS AS (mind_muscle_connection + pump_rating + muscle_disruption) STORED,
    recommendation mev_recommendation_enum,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Weekly Progress Table
```sql
CREATE TABLE weekly_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mesocycle_id UUID REFERENCES mesocycles(id),
    week_number INTEGER NOT NULL,
    muscle_group muscle_group_enum NOT NULL,
    soreness_score INTEGER CHECK (soreness_score BETWEEN 0 AND 3),
    performance_score INTEGER CHECK (performance_score BETWEEN 0 AND 3),
    sets_recommendation INTEGER,
    actual_sets_added INTEGER,
    volume_change_percent DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(mesocycle_id, week_number, muscle_group)
);
```

### Enums and Types
```sql
CREATE TYPE training_goal_enum AS ENUM ('hypertrophy', 'strength', 'endurance', 'general_fitness');
CREATE TYPE mesocycle_status_enum AS ENUM ('planning', 'active', 'completed', 'paused');
CREATE TYPE muscle_group_enum AS ENUM ('chest', 'back', 'shoulders', 'biceps', 'triceps', 'quads', 'hamstrings', 'glutes', 'calves', 'abs');
CREATE TYPE movement_pattern_enum AS ENUM ('push', 'pull', 'squat', 'hinge', 'carry', 'isolation');
CREATE TYPE equipment_enum AS ENUM ('barbell', 'dumbbell', 'cable', 'machine', 'bodyweight', 'resistance_band');
CREATE TYPE difficulty_enum AS ENUM ('beginner', 'intermediate', 'advanced');
CREATE TYPE mev_recommendation_enum AS ENUM ('increase_2_4_sets', 'progress_normally', 'drop_volume', 'recovery_session');
```

## API Specifications

### Authentication Endpoints
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/refresh
DELETE /api/v1/auth/logout
```

### User Management
```
GET /api/v1/users/profile
PUT /api/v1/users/profile
DELETE /api/v1/users/account
```

### Mesocycle Management
```
GET /api/v1/mesocycles
POST /api/v1/mesocycles
GET /api/v1/mesocycles/{mesocycle_id}
PUT /api/v1/mesocycles/{mesocycle_id}
DELETE /api/v1/mesocycles/{mesocycle_id}
POST /api/v1/mesocycles/{mesocycle_id}/start
POST /api/v1/mesocycles/{mesocycle_id}/deload
```

### Workout Execution
```
GET /api/v1/workouts/current
POST /api/v1/workouts/start
PUT /api/v1/workouts/{workout_id}
POST /api/v1/workouts/{workout_id}/complete
POST /api/v1/workouts/{workout_id}/sets
PUT /api/v1/sets/{set_id}
```

### Progress Tracking
```
POST /api/v1/feedback/mev
POST /api/v1/feedback/weekly-progress
GET /api/v1/analytics/volume-progression
GET /api/v1/analytics/exercise-effectiveness
```

### Exercise Database
```
GET /api/v1/exercises
GET /api/v1/exercises/{exercise_id}
GET /api/v1/exercises/search
GET /api/v1/exercises/muscle-group/{muscle_group}
```

## Testing Strategy

### Unit Testing (pytest)
```python
# Example test structure
tests/
├── unit/
│   ├── test_algorithms/
│   │   ├── test_mev_estimator.py
│   │   ├── test_set_progression.py
│   │   └── test_sfr_calculator.py
│   ├── test_models/
│   │   ├── test_user_model.py
│   │   ├── test_mesocycle_model.py
│   │   └── test_workout_model.py
│   └── test_services/
│       ├── test_workout_service.py
│       └── test_progression_service.py
├── integration/
│   ├── test_api_endpoints.py
│   ├── test_database_operations.py
│   └── test_algorithm_integration.py
└── behavioral/
    ├── features/
    │   ├── mesocycle_planning.feature
    │   ├── workout_execution.feature
    │   └── progress_tracking.feature
    └── steps/
        ├── mesocycle_steps.py
        ├── workout_steps.py
        └── progress_steps.py
```

### Behavioral Testing (Behave)
```gherkin
# Example feature file
Feature: MEV Stimulus Estimation
  As a user training for hypertrophy
  I want to estimate my MEV accurately
  So that I can optimize my training volume

  Scenario: User receives increase recommendation
    Given I complete a workout session
    And my mind-muscle connection score is 1
    And my pump rating is 1  
    And my muscle disruption score is 1
    When I submit MEV feedback
    Then I should receive "increase_2_4_sets" recommendation
    And my next week volume should increase by 2-4 sets
```

### Performance Testing
- **Load Testing**: 1000 concurrent users during peak workout times
- **Database Performance**: Sub-100ms query response times
- **API Response Times**: 95th percentile under 200ms
- **Memory Usage**: Container memory under 512MB baseline

### Regression Testing
- **Automated Regression Suite**: Run on every deployment
- **Algorithm Accuracy Tests**: Validate RP calculations remain consistent
- **Data Integrity Tests**: Ensure soft-delete and migration safety
- **API Contract Tests**: Validate backward compatibility

## DevOps and Deployment

### Docker Configuration
```dockerfile
# Multi-stage build for production
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim as production
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose Services
```yaml
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/rp_training
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: rp_training
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  cachet:
    image: cachethq/docker
    ports:
      - "8080:8000"
    environment:
      - DB_DRIVER=postgres
      - DB_HOST=db
    depends_on:
      - db
```

### CI/CD Pipeline
```yaml
# GitHub Actions workflow
name: CI/CD Pipeline
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install -r requirements-dev.txt
      - name: Run linting
        run: |
          black --check .
          isort --check-only .
          flake8 .
          mypy .
      - name: Run tests
        run: |
          pytest --cov=app --cov-report=xml
          behave
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Performance and Scalability Requirements

### Performance Targets
- **API Response Time**: 95th percentile < 200ms
- **Database Query Time**: Average < 50ms
- **Concurrent Users**: Support 10,000 active users
- **Data Processing**: Handle 1M+ exercise sets per day
- **Algorithm Computation**: MEV calculations < 10ms

### Scalability Strategy
- **Horizontal Scaling**: Stateless API design for load balancing
- **Database Optimization**: Read replicas and connection pooling
- **Caching Strategy**: Redis for session management and computed results
- **CDN Integration**: Static assets and exercise videos
- **Background Processing**: Celery for heavy computations

## Security Requirements

### Data Protection
- **Encryption**: TLS 1.3 for data in transit
- **Password Security**: bcrypt with minimum 12 rounds
- **API Security**: Rate limiting and request validation
- **Database Security**: Parameterized queries to prevent SQL injection
- **GDPR Compliance**: Right to deletion and data portability

### Authentication & Authorization
- **JWT Tokens**: Short-lived access tokens with refresh mechanism
- **Role-Based Access**: User, coach, and admin permissions
- **Session Management**: Secure session handling with Redis
- **API Key Management**: Service-to-service authentication

## User Stories and Acceptance Criteria

### Epic: Mesocycle Planning
**As a user, I want to create a customized mesocycle so that I can follow a structured training program.**

#### User Story 1: Template Selection
- **Given** I am a new user planning my first mesocycle
- **When** I browse available templates
- **Then** I should see templates categorized by experience level and goals
- **And** I should be able to preview the weekly structure
- **And** I should see estimated time commitment per workout

#### User Story 2: Volume Configuration
- **Given** I have selected a template
- **When** I customize volume landmarks
- **Then** I should be able to set MEV, MAV, and MRV for each muscle group
- **And** the system should validate that MEV < MAV < MRV
- **And** I should receive recommendations based on my training history

### Epic: Workout Execution
**As a user, I want to log my workouts accurately so that the system can provide intelligent feedback.**

#### User Story 3: Real-time Logging
- **Given** I am in an active workout
- **When** I complete a set
- **Then** I should be able to log weight, reps, and RIR quickly
- **And** the system should auto-suggest progression for the next set
- **And** I should see my rest timer start automatically

#### User Story 4: Exercise Substitution
- **Given** my planned exercise is unavailable
- **When** I request an alternative
- **Then** the system should suggest exercises targeting the same muscle group
- **And** maintain similar movement patterns and difficulty
- **And** preserve the training stimulus

### Epic: Progress Analytics
**As a user, I want to track my progress over time so that I can optimize my training.**

#### User Story 5: Volume Progression Tracking
- **Given** I have completed multiple weeks of training
- **When** I view my progress dashboard
- **Then** I should see volume progression charts per muscle group
- **And** identify when I approached my MRV
- **And** see recommendations for future mesocycles

## Glossary of Terms

### Renaissance Periodization (RP) Concepts

#### Volume Landmarks
- **Maintenance Volume (MV)**: The minimum weekly training volume required to maintain current muscle mass and strength levels without loss.

- **Minimum Effective Volume (MEV)**: The smallest amount of weekly training volume that produces a meaningful hypertrophy response. This varies by individual, muscle group, and training phase.

- **Maximum Adaptive Volume (MAV)**: The weekly training volume that produces the greatest rate of muscle growth at any given time. This represents the "sweet spot" for hypertrophy.

- **Maximum Recoverable Volume (MRV)**: The highest weekly training volume from which an individual can recover. Exceeding MRV leads to overreaching and performance decrements.

#### Training Metrics
- **Reps in Reserve (RIR)**: A subjective measure of how many additional repetitions could be performed at the end of a set. RIR 0 = muscular failure, RIR 2 = 2 more reps possible.

- **Rate of Perceived Exertion (RPE)**: A 1-10 scale measuring subjective training intensity. Often correlated with RIR (RPE 10 = RIR 0).

- **Relative Intensity**: The percentage of one-repetition maximum (%1RM) used for a given exercise.

- **Volume Load**: Total training volume calculated as sets × reps × weight, providing a quantitative measure of training stress.

#### Periodization Models
- **Mesocycle**: A training block typically lasting 4-8 weeks with a specific focus (e.g., hypertrophy, strength, deload).

- **Accumulation Phase**: The portion of a mesocycle where training volume progressively increases from MEV toward MAV.

- **Intensification Phase**: A training phase emphasizing higher intensities and lower volumes, often used in strength-focused mesocycles.

- **Deload Week**: A planned reduction in training volume and/or intensity to facilitate recovery and supercompensation.

#### Scientific Algorithms

##### MEV Stimulus Estimator Algorithm
**Purpose**: Determine if current training volume meets the minimum threshold for hypertrophy stimulus.

**Input Variables**:
- Mind-Muscle Connection (0-3): Subjective awareness and tension in target muscles
- Pump Rating (0-3): Degree of muscle swelling and blood pooling
- Muscle Disruption (0-3): Level of fatigue and subsequent soreness

**Calculation**:
```
Total Stimulus Score = Mind-Muscle Connection + Pump + Muscle Disruption
```

**Interpretation**:
- 0-1: Below MEV, increase volume by 2-4 sets
- 2-3: At or slightly below MEV, increase volume by 2-4 sets  
- 4-6: At or just above MEV, progress normally
- 7-9: Between MAV and MRV, consider reducing volume

##### Set Progression Algorithm
**Purpose**: Autoregulate weekly volume increases based on recovery and performance indicators.

**Input Variables**:
- Soreness Score (0-3): Delayed onset muscle soreness (DOMS) severity and duration
- Performance Score (0-3): Ability to match previous session's performance

**Logic Matrix**:
```
                Performance Score
Soreness    0        1        2        3
    0    +1-3 sets +0-2 sets  0 sets  Recovery
    1    +1-2 sets +0-1 sets  0 sets  Recovery  
    2     0 sets    0 sets   0 sets  Recovery
    3     0 sets    0 sets   0 sets  Recovery
```

##### Stimulus-to-Fatigue Ratio (SFR)
**Purpose**: Quantify exercise efficiency by comparing growth stimulus to systemic fatigue.

**Formula**:
```
SFR = Raw Stimulus Magnitude (RSM) / Total Fatigue Score

Where:
RSM = Mind-Muscle Connection + Pump + Muscle Disruption (0-9)
Fatigue = Joint Pain + Perceived Exertion + Performance Impact (0-9)
```

**Applications**:
- Exercise selection and ranking
- Training method comparison
- Program optimization for individual responses

#### Muscle Groups and Training Splits
- **Primary Muscle Groups**: Chest, back, shoulders, biceps, triceps, quadriceps, hamstrings, glutes, calves, abdominals
- **Push/Pull/Legs Split**: Common 3-day rotation separating pushing movements, pulling movements, and leg exercises
- **Upper/Lower Split**: 2-day rotation alternating upper body and lower body training
- **Full Body**: Training all major muscle groups in each session

#### Exercise Classifications
- **Compound Movements**: Multi-joint exercises recruiting multiple muscle groups (e.g., squats, deadlifts, bench press)
- **Isolation Exercises**: Single-joint movements targeting specific muscles (e.g., bicep curls, leg extensions)
- **Movement Patterns**: Push, pull, squat, hinge, carry, and isolation categories for exercise organization

#### Recovery and Adaptation
- **Supercompensation**: The physiological process where training stress leads to enhanced performance capacity
- **Overreaching**: Short-term performance decrement from high training loads, recoverable within days to weeks
- **Overtraining Syndrome**: Chronic state of decreased performance and recovery capacity requiring extended rest
- **Delayed Onset Muscle Soreness (DOMS)**: Muscle pain and stiffness occurring 24-72 hours post-exercise

### Technical Implementation Terms

#### Software Architecture
- **Clean Architecture**: Architectural pattern emphasizing separation of concerns and dependency inversion
- **Domain-Driven Design (DDD)**: Software development approach focusing on complex business domains and their models
- **CQRS**: Command Query Responsibility Segregation pattern separating read and write operations
- **Event Sourcing**: Data storage pattern capturing all changes as a sequence of events

#### Database Patterns
- **Soft Delete**: Marking records as deleted without physical removal, preserving data for audit trails
- **Database Migration**: Versioned schema changes managed through tools like Alembic
- **Connection Pooling**: Reusing database connections to improve performance and resource utilization
- **Read Replicas**: Separate database instances for handling read queries, improving scalability

#### API Design
- **RESTful API**: Architectural style for web services using standard HTTP methods and status codes
- **OpenAPI Specification**: Standard for describing REST APIs, enabling documentation and code generation
- **Rate Limiting**: Controlling the number of requests a client can make within a time window
- **JWT (JSON Web Token)**: Compact, URL-safe means of representing claims securely between parties

#### Testing Methodologies
- **Unit Testing**: Testing individual components in isolation
- **Integration Testing**: Testing interactions between integrated components
- **Behavioral Testing**: Testing from the user's perspective using natural language scenarios
- **Regression Testing**: Re-running previous tests to ensure new changes don't break existing functionality
- **Test Coverage**: Metric indicating the percentage of code exercised by tests

#### DevOps and Deployment
- **Containerization**: Packaging applications with their dependencies using technologies like Docker
- **Orchestration**: Managing containerized applications at scale using tools like Kubernetes
- **CI/CD Pipeline**: Automated processes for continuous integration and continuous deployment
- **Infrastructure as Code**: Managing infrastructure through machine-readable configuration files
- **Service Monitoring**: Tracking application health, performance, and availability in production

This comprehensive PRD provides the foundation for building a scientifically-grounded, technically robust Renaissance Periodization training application that serves both novice and advanced users in their hypertrophy goals.