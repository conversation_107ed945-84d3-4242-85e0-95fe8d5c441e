[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "forge-protocol"
version = "0.1.0"
description = "Forge Protocol - Evidence-based hypertrophy training platform implementing RP scientific principles"
authors = [
    {name = "Forge Protocol Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

[project.urls]
Homepage = "https://github.com/forge-protocol/api"
Repository = "https://github.com/forge-protocol/api"
Documentation = "https://docs.forgeprotocol.com"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | alembic/versions
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# mypy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "behave.*",
    "jose.*",
    "passlib.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=90",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "auth: Authentication tests",
    "database: Database tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/alembic/*",
    "*/__init__.py",
    "*/config.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Ruff configuration
[tool.ruff]
target-version = "py311"
line-length = 88
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "alembic/versions",
]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # pycodestyle
    "E",
    "W",
    # isort
    "I",
    # pydocstyle
    "D",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # flake8-comprehensions
    "C4",
    # flake8-pie
    "PIE",
    # flake8-type-checking
    "TCH",
    # flake8-unused-arguments
    "ARG",
    # flake8-use-pathlib
    "PTH",
    # Ruff-specific rules
    "RUF",
]
ignore = [
    # Allow non-abstract empty methods in abstract base classes
    "B027",
    # Allow boolean positional values in function calls
    "FBT003",
    # Ignore complexity
    "C901",
    # Allow unused variables when underscore-prefixed
    "F841",
    # Ignore specific docstring rules that conflict with our style
    "D100",  # Missing docstring in public module
    "D104",  # Missing docstring in public package
    "D106",  # Missing docstring in public nested class
    "D107",  # Missing docstring in __init__
    "D203",  # 1 blank line required before class docstring (conflicts with D211)
    "D213",  # Multi-line docstring summary should start at the second line (conflicts with D212)
    # Allow function calls in argument defaults for FastAPI dependencies
    "B008",  # Do not perform function call in argument defaults
    # Allow unused method arguments for validators and callbacks
    "ARG002",  # Unused method argument
    # Allow mutable class attributes for Pydantic Config classes
    "RUF012",  # Mutable class attributes should be annotated with ClassVar
    # Allow implicit Optional for backward compatibility
    "RUF013",  # PEP 484 prohibits implicit Optional
]
unfixable = [
    # Don't touch unused imports
    "F401",
]

[tool.ruff.lint.per-file-ignores]
# Tests can use magic values, assertions, and imports
"tests/**/*" = ["PLR2004", "S101", "TID252"]
# __init__.py files can have unused imports
"__init__.py" = ["F401"]
# Alembic files can have unused imports and long lines
"alembic/**/*" = ["F401", "E501"]
# API routes can have function calls in defaults and exception chaining
"app/presentation/api/**/*" = ["B008", "B904"]

[tool.ruff.lint.isort]
known-first-party = ["app"]
known-third-party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.mccabe]
max-complexity = 10

# Bandit security linting configuration
[tool.bandit]
exclude_dirs = ["tests", "alembic/versions"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
