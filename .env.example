# Application Configuration
APP_NAME="RP Training API"
APP_VERSION="0.1.0"
DEBUG=false

# API Configuration
API_V1_PREFIX="/api/v1"

# Security Configuration
SECRET_KEY="your-super-secret-key-here-minimum-32-characters-long"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# Database Configuration
DATABASE_URL="postgresql+asyncpg://rp_user:rp_password@localhost:5432/rp_training"
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Test Database (for testing)
TEST_DATABASE_URL="postgresql+asyncpg://rp_user:rp_password@localhost:5432/rp_training_test"

# Redis Configuration
REDIS_URL="redis://localhost:6379/0"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080,http://localhost:5173"

# Logging Configuration
LOG_LEVEL="INFO"

# Testing
TESTING=false
