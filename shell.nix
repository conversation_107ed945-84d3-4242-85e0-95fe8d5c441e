# Nix shell for Forge Protocol development
# Provides reproducible development environment with all dependencies
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "forge-protocol-dev";
  
  buildInputs = with pkgs; [
    # Python runtime and tools
    python310
    python310Packages.pip
    python310Packages.virtualenv
    python310Packages.setuptools
    python310Packages.wheel
    
    # Database
    postgresql_15
    redis
    
    # Development tools
    git
    curl
    jq
    
    # Docker for containerization
    docker
    docker-compose
    
    # System dependencies for Python packages
    pkg-config
    libffi
    openssl
    zlib
    
    # Build tools
    gcc
    gnumake
    
    # Optional: useful development utilities
    tree
    htop
    tmux
  ];
  
  shellHook = ''
    echo "🔥 Forge Protocol Development Environment"
    echo "========================================"
    echo "Python: $(python --version)"
    echo "PostgreSQL: $(postgres --version | head -n1)"
    echo "Redis: $(redis-server --version)"
    echo "Docker: $(docker --version)"
    echo ""
    echo "📋 Quick Commands:"
    echo "  • Install Python deps: pip install -r requirements/dev.txt"
    echo "  • Start database: docker compose -f docker/docker-compose.yml up -d"
    echo "  • Run migrations: python -m alembic upgrade head"
    echo "  • Start API: python -m uvicorn app.main:app --reload"
    echo "  • Run tests: python -m pytest"
    echo ""
    echo "🚀 Mobile-first development ready!"
    
    # Set up Python environment
    export PYTHONPATH="$PWD:$PYTHONPATH"
    
    # Ensure pip uses user directory in Nix environment
    export PIP_USER=1
    export PATH="$HOME/.local/bin:$PATH"
    
    # PostgreSQL environment variables for development
    export PGDATA="$PWD/.nix-postgres"
    export PGHOST="localhost"
    export PGPORT="5432"
    export PGUSER="rp_user"
    export PGPASSWORD="rp_password"
    export PGDATABASE="rp_training"
    
    # Create .nix-postgres directory if it doesn't exist
    if [ ! -d "$PGDATA" ]; then
      echo "🔧 Setting up local PostgreSQL data directory..."
      mkdir -p "$PGDATA"
    fi
  '';
  
  # Environment variables
  env = {
    # Ensure Python packages can find system libraries
    LD_LIBRARY_PATH = pkgs.lib.makeLibraryPath [
      pkgs.libffi
      pkgs.openssl
      pkgs.zlib
    ];
    
    # Python development
    PYTHONDONTWRITEBYTECODE = "1";
    PYTHONUNBUFFERED = "1";
    
    # Development environment
    ENVIRONMENT = "development";
    DEBUG = "true";
  };
}
