{ pkgs ? import <nixpkgs> {} }:

let
  python = pkgs.python311;
  pythonPackages = python.pkgs;
in

pkgs.mkShell {
  name = "rp-training-api-dev";

  buildInputs = with pkgs; [
    # Python and core tools
    python
    pythonPackages.pip
    pythonPackages.setuptools
    pythonPackages.wheel

    # Core web framework
    pythonPackages.fastapi
    pythonPackages.uvicorn

    # Database
    pythonPackages.sqlalchemy
    pythonPackages.alembic
    pythonPackages.asyncpg
    pythonPackages.psycopg2
    pythonPackages.aiosqlite

    # Authentication & Security
    pythonPackages.passlib
    pythonPackages.python-jose
    pythonPackages.python-multipart
    pythonPackages.bcrypt
    pythonPackages.pyjwt

    # HTTP client
    pythonPackages.httpx

    # Configuration
    pythonPackages.pydantic
    pythonPackages.pydantic-settings
    pythonPackages.python-dotenv
    pythonPackages.email-validator

    # Testing
    pythonPackages.pytest
    pythonPackages.pytest-asyncio
    pythonPackages.pytest-cov
    pythonPackages.pytest-mock
    pythonPackages.coverage

    # BDD Testing (may need to be installed via pip)
    # pythonPackages.behave

    # Code Quality
    pythonPackages.black
    pythonPackages.isort
    pythonPackages.flake8
    pythonPackages.mypy
    # pythonPackages.bandit  # May not be available

    # Development tools
    pre-commit

    # Database tools
    postgresql
    redis

    # Docker (if needed)
    docker
    docker-compose

    # Git
    git

    # System tools
    curl
    jq

    # Performance monitoring
    pythonPackages.psutil
  ];

  shellHook = ''
    echo "🚀 RP Training API Development Environment"
    echo "Python version: $(python --version)"
    echo "FastAPI available: $(python -c 'import fastapi; print(fastapi.__version__)' 2>/dev/null || echo 'Not found')"
    echo "SQLAlchemy available: $(python -c 'import sqlalchemy; print(sqlalchemy.__version__)' 2>/dev/null || echo 'Not found')"
    echo "Pytest available: $(python -c 'import pytest; print(pytest.__version__)' 2>/dev/null || echo 'Not found')"
    echo ""
    echo "Available commands:"
    echo "  python scripts/run_tests.py --help    # Run comprehensive tests"
    echo "  python scripts/test_structure.py      # Test project structure"
    echo "  python -m pytest                      # Run pytest directly"
    echo "  python -m uvicorn app.main:app --reload  # Start development server"
    echo "  python -m alembic upgrade head        # Run database migrations"
    echo ""
    echo "Code quality:"
    echo "  black .                               # Format code"
    echo "  isort .                               # Sort imports"
    echo "  flake8 .                              # Lint code"
    echo "  mypy app/                             # Type checking"
    echo ""

    # Set up environment variables
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export TESTING=true
    export SECRET_KEY="dev-secret-key-for-testing-only-not-for-production"
    export DATABASE_URL="sqlite+aiosqlite:///./test.db"

    # Create necessary directories
    mkdir -p logs
    mkdir -p htmlcov

    echo "Environment ready! 🎉"
  '';

  # Environment variables
  PYTHONPATH = ".";
  TESTING = "true";
}
