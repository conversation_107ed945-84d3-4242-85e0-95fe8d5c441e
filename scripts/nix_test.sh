#!/usr/bin/env bash

# RP Training API Test Runner for Nix Environment
# Runs tests in a Nix shell with all dependencies

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${YELLOW}$1${NC}"
    echo "================================================"
}

# Check if we're in a Nix environment
if [ -z "$IN_NIX_SHELL" ]; then
    log_info "Not in Nix shell, entering Nix environment..."
    if [ -f "flake.nix" ]; then
        exec nix develop --command "$0" "$@"
    elif [ -f "shell.nix" ]; then
        exec nix-shell --command "$0 $*"
    else
        log_error "No Nix configuration found (flake.nix or shell.nix)"
        exit 1
    fi
fi

log_header "🏋️  RP Training API Test Suite (Nix Environment)"

# Set up environment
export PYTHONPATH="$PWD:$PYTHONPATH"
export TESTING=true
export SECRET_KEY="test-secret-key-for-nix-testing"
export DATABASE_URL="sqlite+aiosqlite:///./test.db"

# Parse command line arguments
TEST_TYPE="${1:-quick}"

case "$TEST_TYPE" in
    "unit")
        log_header "🧪 Running Unit Tests"
        python -m pytest tests/unit/ -v --cov=app --cov-report=term-missing
        ;;
    "integration")
        log_header "🔗 Running Integration Tests"
        python -m pytest tests/integration/ -v
        ;;
    "security")
        log_header "🔒 Running Security Tests"
        python -m pytest tests/integration/test_security.py -v -m security
        ;;
    "performance")
        log_header "⚡ Running Performance Tests"
        python -m pytest tests/integration/test_performance.py -v -m performance
        ;;
    "coverage")
        log_header "📊 Generating Coverage Report"
        python -m pytest tests/unit/ tests/integration/ --cov=app --cov-report=html --cov-report=term-missing --cov-fail-under=85
        log_info "Coverage report generated in htmlcov/index.html"
        ;;
    "quality")
        log_header "🔧 Running Code Quality Checks"
        
        log_info "Running Black formatter check..."
        python -m black --check . || { log_error "Black formatting failed"; exit 1; }
        
        log_info "Running isort import check..."
        python -m isort --check-only . || { log_error "Import sorting failed"; exit 1; }
        
        log_info "Running flake8 linting..."
        python -m flake8 . || { log_error "Flake8 linting failed"; exit 1; }
        
        log_info "Running mypy type checking..."
        python -m mypy app/ || { log_error "MyPy type checking failed"; exit 1; }
        
        log_success "All code quality checks passed!"
        ;;
    "structure")
        log_header "🏗️  Testing Project Structure"
        python scripts/test_structure.py
        ;;
    "all")
        log_header "🚀 Running Complete Test Suite"
        
        # Structure tests
        log_info "Testing project structure..."
        python scripts/test_structure.py || { log_error "Structure tests failed"; exit 1; }
        
        # Code quality
        log_info "Running code quality checks..."
        "$0" quality || { log_error "Quality checks failed"; exit 1; }
        
        # Unit tests
        log_info "Running unit tests..."
        python -m pytest tests/unit/ -v --cov=app --cov-report=term-missing || { log_error "Unit tests failed"; exit 1; }
        
        # Integration tests
        log_info "Running integration tests..."
        python -m pytest tests/integration/ -v || { log_error "Integration tests failed"; exit 1; }
        
        log_success "All tests passed! 🎉"
        ;;
    "quick")
        log_header "⚡ Running Quick Test Suite"
        
        # Structure test
        python scripts/test_structure.py || { log_error "Structure test failed"; exit 1; }
        
        # Quick unit tests
        python -m pytest tests/unit/ -x --tb=short || { log_error "Unit tests failed"; exit 1; }
        
        # Basic integration test
        python -m pytest tests/integration/test_health_endpoints.py -x --tb=short || { log_error "Basic integration test failed"; exit 1; }
        
        log_success "Quick tests passed! ✅"
        ;;
    "help"|"--help"|"-h")
        echo "RP Training API Test Runner (Nix)"
        echo ""
        echo "Usage: $0 [TEST_TYPE]"
        echo ""
        echo "Test Types:"
        echo "  quick       - Quick test suite (default)"
        echo "  unit        - Unit tests only"
        echo "  integration - Integration tests only"
        echo "  security    - Security tests only"
        echo "  performance - Performance tests only"
        echo "  coverage    - Generate coverage report"
        echo "  quality     - Code quality checks"
        echo "  structure   - Project structure tests"
        echo "  all         - Complete test suite"
        echo "  help        - Show this help"
        echo ""
        echo "Examples:"
        echo "  $0                    # Run quick tests"
        echo "  $0 unit              # Run unit tests"
        echo "  $0 coverage          # Generate coverage report"
        echo "  $0 all               # Run everything"
        ;;
    *)
        log_error "Unknown test type: $TEST_TYPE"
        echo "Run '$0 help' for available options"
        exit 1
        ;;
esac
