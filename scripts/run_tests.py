#!/usr/bin/env python3
"""
Comprehensive test runner for RP Training API.

Provides various test execution options including unit tests, integration tests,
performance tests, and comprehensive test suites with reporting.
"""

import argparse
import subprocess
import sys
import time
from pathlib import Path
from typing import List

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


# Colors for output
class Colors:
    RED = "\033[0;31m"
    GREEN = "\033[0;32m"
    YELLOW = "\033[1;33m"
    BLUE = "\033[0;34m"
    PURPLE = "\033[0;35m"
    CYAN = "\033[0;36m"
    WHITE = "\033[1;37m"
    NC = "\033[0m"  # No Color


def log_info(message: str) -> None:
    """Log info message."""
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")


def log_success(message: str) -> None:
    """Log success message."""
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")


def log_warning(message: str) -> None:
    """Log warning message."""
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")


def log_error(message: str) -> None:
    """Log error message."""
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")


def log_header(message: str) -> None:
    """Log header message."""
    print(f"\n{Colors.WHITE}{'='*60}{Colors.NC}")
    print(f"{Colors.WHITE}{message.center(60)}{Colors.NC}")
    print(f"{Colors.WHITE}{'='*60}{Colors.NC}\n")


def run_command(cmd: List[str], description: str) -> bool:
    """Run a command and return success status."""
    log_info(f"Running: {description}")
    log_info(f"Command: {' '.join(cmd)}")

    start_time = time.time()

    try:
        result = subprocess.run(
            cmd, cwd=project_root, capture_output=True, text=True, check=False
        )

        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            log_success(f"{description} completed successfully in {duration:.2f}s")
            if result.stdout.strip():
                print(result.stdout)
            return True
        else:
            log_error(f"{description} failed in {duration:.2f}s")
            if result.stdout.strip():
                print(f"STDOUT:\n{result.stdout}")
            if result.stderr.strip():
                print(f"STDERR:\n{result.stderr}")
            return False

    except Exception as e:
        log_error(f"Failed to run {description}: {e}")
        return False


def run_unit_tests(verbose: bool = False, coverage: bool = True) -> bool:
    """Run unit tests."""
    log_header("UNIT TESTS")

    cmd = ["python", "-m", "pytest", "tests/unit/"]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.extend(["--cov=app", "--cov-report=term-missing"])

    cmd.extend(["-m", "unit"])

    return run_command(cmd, "Unit tests")


def run_integration_tests(verbose: bool = False) -> bool:
    """Run integration tests."""
    log_header("INTEGRATION TESTS")

    cmd = ["python", "-m", "pytest", "tests/integration/"]

    if verbose:
        cmd.append("-v")

    cmd.extend(["-m", "integration"])

    return run_command(cmd, "Integration tests")


def run_security_tests(verbose: bool = False) -> bool:
    """Run security tests."""
    log_header("SECURITY TESTS")

    cmd = ["python", "-m", "pytest", "tests/integration/test_security.py"]

    if verbose:
        cmd.append("-v")

    cmd.extend(["-m", "security"])

    return run_command(cmd, "Security tests")


def run_performance_tests(verbose: bool = False) -> bool:
    """Run performance tests."""
    log_header("PERFORMANCE TESTS")

    cmd = ["python", "-m", "pytest", "tests/integration/test_performance.py"]

    if verbose:
        cmd.append("-v")

    cmd.extend(["-m", "performance"])

    return run_command(cmd, "Performance tests")


def run_behavioral_tests(verbose: bool = False) -> bool:
    """Run behavioral tests with behave."""
    log_header("BEHAVIORAL TESTS")

    cmd = ["python", "-m", "behave", "tests/behavioral/features/"]

    if verbose:
        cmd.append("--verbose")

    return run_command(cmd, "Behavioral tests")


def run_code_quality_checks() -> bool:
    """Run code quality checks."""
    log_header("CODE QUALITY CHECKS")

    checks = [
        (["python", "-m", "black", "--check", "."], "Black formatting check"),
        (["python", "-m", "isort", "--check-only", "."], "Import sorting check"),
        (["python", "-m", "flake8", "."], "Flake8 linting"),
        (["python", "-m", "mypy", "app/"], "MyPy type checking"),
    ]

    all_passed = True

    for cmd, description in checks:
        if not run_command(cmd, description):
            all_passed = False

    return all_passed


def run_structure_tests() -> bool:
    """Run project structure tests."""
    log_header("PROJECT STRUCTURE TESTS")

    cmd = ["python", "scripts/test_structure.py"]
    return run_command(cmd, "Project structure validation")


def run_all_tests(verbose: bool = False, include_slow: bool = False) -> bool:
    """Run all tests in sequence."""
    log_header("COMPREHENSIVE TEST SUITE")

    test_suites = [
        ("Project Structure", lambda: run_structure_tests()),
        ("Code Quality", lambda: run_code_quality_checks()),
        ("Unit Tests", lambda: run_unit_tests(verbose, coverage=True)),
        ("Integration Tests", lambda: run_integration_tests(verbose)),
        ("Security Tests", lambda: run_security_tests(verbose)),
    ]

    if include_slow:
        test_suites.extend(
            [
                ("Performance Tests", lambda: run_performance_tests(verbose)),
                ("Behavioral Tests", lambda: run_behavioral_tests(verbose)),
            ]
        )

    results = []
    total_start_time = time.time()

    for suite_name, test_func in test_suites:
        log_info(f"Starting {suite_name}...")
        success = test_func()
        results.append((suite_name, success))

        if success:
            log_success(f"{suite_name} passed")
        else:
            log_error(f"{suite_name} failed")

        print()  # Add spacing

    total_end_time = time.time()
    total_duration = total_end_time - total_start_time

    # Summary
    log_header("TEST RESULTS SUMMARY")

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for suite_name, success in results:
        status = (
            f"{Colors.GREEN}PASS{Colors.NC}"
            if success
            else f"{Colors.RED}FAIL{Colors.NC}"
        )
        print(f"  {suite_name:<25} {status}")

    print(f"\n{Colors.WHITE}Total: {passed}/{total} test suites passed{Colors.NC}")
    print(f"{Colors.WHITE}Duration: {total_duration:.2f} seconds{Colors.NC}")

    if passed == total:
        log_success("All test suites passed! 🎉")
        return True
    else:
        log_error(f"{total - passed} test suite(s) failed")
        return False


def run_quick_tests() -> bool:
    """Run quick tests for development."""
    log_header("QUICK TEST SUITE")

    # Run essential tests quickly
    success = True

    if not run_structure_tests():
        success = False

    if not run_unit_tests(verbose=False, coverage=False):
        success = False

    # Run a subset of integration tests
    cmd = ["python", "-m", "pytest", "tests/integration/test_health_endpoints.py", "-v"]
    if not run_command(cmd, "Basic integration tests"):
        success = False

    return success


def generate_coverage_report() -> bool:
    """Generate detailed coverage report."""
    log_header("COVERAGE REPORT GENERATION")

    # Run tests with coverage
    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/unit/",
        "tests/integration/",
        "--cov=app",
        "--cov-report=html",
        "--cov-report=xml",
        "--cov-report=term-missing",
        "--cov-fail-under=85",
    ]

    success = run_command(cmd, "Coverage report generation")

    if success:
        log_info("Coverage reports generated:")
        log_info("  - HTML: htmlcov/index.html")
        log_info("  - XML: coverage.xml")

    return success


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="RP Training API Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_tests.py --all              # Run all tests
  python scripts/run_tests.py --unit             # Run unit tests only
  python scripts/run_tests.py --integration      # Run integration tests only
  python scripts/run_tests.py --quick            # Run quick test suite
  python scripts/run_tests.py --security         # Run security tests only
  python scripts/run_tests.py --performance      # Run performance tests only
  python scripts/run_tests.py --coverage         # Generate coverage report
  python scripts/run_tests.py --quality          # Run code quality checks
        """,
    )

    parser.add_argument("--all", action="store_true", help="Run all test suites")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument(
        "--integration", action="store_true", help="Run integration tests"
    )
    parser.add_argument("--security", action="store_true", help="Run security tests")
    parser.add_argument(
        "--performance", action="store_true", help="Run performance tests"
    )
    parser.add_argument(
        "--behavioral", action="store_true", help="Run behavioral tests"
    )
    parser.add_argument("--quick", action="store_true", help="Run quick test suite")
    parser.add_argument(
        "--quality", action="store_true", help="Run code quality checks"
    )
    parser.add_argument("--structure", action="store_true", help="Run structure tests")
    parser.add_argument(
        "--coverage", action="store_true", help="Generate coverage report"
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument(
        "--include-slow", action="store_true", help="Include slow tests in --all"
    )

    args = parser.parse_args()

    # If no specific test type is specified, run quick tests
    if not any(
        [
            args.all,
            args.unit,
            args.integration,
            args.security,
            args.performance,
            args.behavioral,
            args.quick,
            args.quality,
            args.structure,
            args.coverage,
        ]
    ):
        args.quick = True

    success = True

    if args.structure:
        success &= run_structure_tests()

    if args.quality:
        success &= run_code_quality_checks()

    if args.unit:
        success &= run_unit_tests(args.verbose)

    if args.integration:
        success &= run_integration_tests(args.verbose)

    if args.security:
        success &= run_security_tests(args.verbose)

    if args.performance:
        success &= run_performance_tests(args.verbose)

    if args.behavioral:
        success &= run_behavioral_tests(args.verbose)

    if args.coverage:
        success &= generate_coverage_report()

    if args.quick:
        success &= run_quick_tests()

    if args.all:
        success &= run_all_tests(args.verbose, args.include_slow)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
