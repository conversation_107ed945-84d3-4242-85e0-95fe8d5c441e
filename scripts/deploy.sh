#!/bin/bash

# RP Training API Deployment Script
# Handles local development deployment with Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="rp-training-api"
DOCKER_COMPOSE_FILE="docker/docker-compose.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."

    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    log_success "All requirements met"
}

setup_environment() {
    log_info "Setting up environment..."

    if [ ! -f "$ENV_FILE" ]; then
        log_warning ".env file not found. Creating from template..."
        cp .env.example .env
        log_warning "Please edit .env file with your configuration before continuing."
        log_warning "At minimum, set a secure SECRET_KEY."
        read -p "Press Enter to continue after editing .env file..."
    fi

    log_success "Environment setup complete"
}

build_services() {
    log_info "Building Docker services..."

    docker-compose -f $DOCKER_COMPOSE_FILE build --no-cache

    log_success "Docker services built successfully"
}

start_services() {
    log_info "Starting services..."

    # Start database and Redis first
    docker-compose -f $DOCKER_COMPOSE_FILE up -d db redis

    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10

    # Check database health
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f $DOCKER_COMPOSE_FILE exec -T db pg_isready -U rp_user -d rp_training; then
            log_success "Database is ready"
            break
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_error "Database failed to start after $max_attempts attempts"
            exit 1
        fi

        log_info "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    # Start remaining services
    docker-compose -f $DOCKER_COMPOSE_FILE up -d

    log_success "All services started"
}

run_migrations() {
    log_info "Running database migrations..."

    # Wait a bit more for the API container to be ready
    sleep 5

    # Run migrations
    docker-compose -f $DOCKER_COMPOSE_FILE exec -T api alembic upgrade head

    log_success "Database migrations completed"
}

check_health() {
    log_info "Checking service health..."

    # Wait for API to be ready
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/api/v1/health &> /dev/null; then
            log_success "API health check passed"
            break
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_error "API health check failed after $max_attempts attempts"
            exit 1
        fi

        log_info "Waiting for API... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    # Check database health
    if curl -f http://localhost:8000/api/v1/health/db &> /dev/null; then
        log_success "Database health check passed"
    else
        log_error "Database health check failed"
        exit 1
    fi
}

show_status() {
    log_info "Service status:"
    docker-compose -f $DOCKER_COMPOSE_FILE ps

    echo ""
    log_info "Available endpoints:"
    echo "  - API Documentation: http://localhost:8000/docs"
    echo "  - Health Check: http://localhost:8000/api/v1/health"
    echo "  - Database Admin: http://localhost:5050"
    echo "  - API Base: http://localhost:8000"

    echo ""
    log_info "Database connection:"
    echo "  - Host: localhost"
    echo "  - Port: 5432"
    echo "  - Database: rp_training"
    echo "  - Username: rp_user"
    echo "  - Password: rp_password"
}

stop_services() {
    log_info "Stopping services..."
    docker-compose -f $DOCKER_COMPOSE_FILE down
    log_success "Services stopped"
}

clean_services() {
    log_info "Cleaning up services and volumes..."
    docker-compose -f $DOCKER_COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
    log_success "Cleanup completed"
}

# Main deployment function
deploy() {
    log_info "Starting RP Training API deployment..."

    check_requirements
    setup_environment
    build_services
    start_services
    run_migrations
    check_health
    show_status

    log_success "Deployment completed successfully!"
}

# Command handling
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "start")
        start_services
        check_health
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        stop_services
        start_services
        check_health
        show_status
        ;;
    "clean")
        clean_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose -f $DOCKER_COMPOSE_FILE logs -f "${2:-api}"
        ;;
    "shell")
        docker-compose -f $DOCKER_COMPOSE_FILE exec "${2:-api}" /bin/bash
        ;;
    *)
        echo "Usage: $0 {deploy|start|stop|restart|clean|status|logs|shell}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment (build, start, migrate)"
        echo "  start    - Start services"
        echo "  stop     - Stop services"
        echo "  restart  - Restart services"
        echo "  clean    - Stop services and clean volumes"
        echo "  status   - Show service status"
        echo "  logs     - Show logs (optionally specify service)"
        echo "  shell    - Open shell in container (optionally specify service)"
        exit 1
        ;;
esac
