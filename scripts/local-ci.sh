#!/bin/bash
# Local CI/CD pipeline for Forge Protocol
# Mobile-first quality assurance without external CI services

set -e  # Exit on any error

echo "🔥 Forge Protocol - Local CI/CD Pipeline"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in Nix environment
if [ -z "$NIX_STORE" ]; then
    print_warning "Not in Nix environment. Run 'nix develop' first for best results."
fi

# 1. Environment Setup
print_status "Setting up environment..."
export PYTHONPATH="$PWD:$PYTHONPATH"
export ENVIRONMENT="testing"
export DEBUG="false"

# 2. Code Quality Checks
print_status "Running code quality checks..."

echo "  📝 Checking code formatting (black)..."
if black --check . --quiet; then
    print_success "Code formatting passed"
else
    print_error "Code formatting failed. Run: black ."
    exit 1
fi

echo "  📦 Checking import sorting (isort)..."
if isort --check-only . --quiet; then
    print_success "Import sorting passed"
else
    print_error "Import sorting failed. Run: isort ."
    exit 1
fi

echo "  🔍 Running linting (flake8)..."
if flake8 . --quiet; then
    print_success "Linting passed"
else
    print_error "Linting failed. Check flake8 output above."
    exit 1
fi

echo "  🏷️  Running type checking (mypy)..."
if mypy . --quiet; then
    print_success "Type checking passed"
else
    print_error "Type checking failed. Check mypy output above."
    exit 1
fi

# 3. Security Checks
print_status "Running security checks..."
if command -v bandit &> /dev/null; then
    echo "  🔒 Security scanning (bandit)..."
    if bandit -r app/ -f json -o bandit-report.json --quiet; then
        print_success "Security scan passed"
    else
        print_warning "Security scan found issues. Check bandit-report.json"
    fi
else
    print_warning "Bandit not installed. Install with: pip install bandit"
fi

# 4. Start Test Services
print_status "Starting test services..."
if command -v docker &> /dev/null; then
    echo "  🐳 Starting database..."
    docker compose -f docker/docker-compose.yml up -d db redis --quiet-pull

    # Wait for database to be ready
    echo "  ⏳ Waiting for database to be ready..."
    sleep 5

    # Run migrations
    echo "  🗄️  Running database migrations..."
    python -m alembic upgrade head
else
    print_warning "Docker not available. Some tests may fail."
fi

# 5. Run Tests
print_status "Running test suite..."

echo "  🧪 Unit tests..."
if python -m pytest tests/unit/ -v --cov=app --cov-report=term-missing --cov-report=html --cov-fail-under=85 --quiet; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

echo "  🔗 Integration tests..."
if python -m pytest tests/integration/ -v --quiet; then
    print_success "Integration tests passed"
else
    print_error "Integration tests failed"
    exit 1
fi

# 6. Performance Tests
print_status "Running performance tests..."
echo "  ⚡ API performance..."
if python -m pytest tests/integration/test_performance.py -v --quiet; then
    print_success "Performance tests passed"
else
    print_warning "Performance tests failed or not found"
fi

# 7. Build Test
print_status "Testing Docker build..."
if command -v docker &> /dev/null; then
    echo "  🏗️  Building Docker image..."
    if docker build -f docker/Dockerfile.dev -t forge-protocol:test . --quiet; then
        print_success "Docker build passed"

        # Test the built image
        echo "  🧪 Testing Docker image..."
        docker run -d --name forge-test \
            -e SECRET_KEY=test-secret-key-for-ci-minimum-32-characters-long \
            -e DATABASE_URL=sqlite+aiosqlite:///:memory: \
            -p 8001:8000 \
            forge-protocol:test

        sleep 5

        if curl -f http://localhost:8001/api/v1/health --silent > /dev/null; then
            print_success "Docker image test passed"
        else
            print_error "Docker image test failed"
        fi

        # Cleanup
        docker stop forge-test --time 1 > /dev/null 2>&1
        docker rm forge-test > /dev/null 2>&1
    else
        print_error "Docker build failed"
        exit 1
    fi
fi

# 8. Generate Reports
print_status "Generating reports..."
echo "  📊 Test coverage report: htmlcov/index.html"
echo "  🔒 Security report: bandit-report.json"

# 9. Cleanup
print_status "Cleaning up..."
if command -v docker &> /dev/null; then
    docker compose -f docker/docker-compose.yml down --quiet
fi

# 10. Summary
echo ""
echo "🎯 CI/CD Pipeline Complete!"
echo "=========================="
print_success "✅ Code quality checks passed"
print_success "✅ Security scan completed"
print_success "✅ All tests passed"
print_success "✅ Docker build successful"
print_success "✅ Mobile-first API ready for Phase 2"

echo ""
echo "📋 Next Steps:"
echo "  • Review test coverage: open htmlcov/index.html"
echo "  • Check security report: cat bandit-report.json"
echo "  • Ready to proceed to Phase 2: Exercise Database"
echo ""
echo "🚀 Forge Protocol foundation is solid!"
