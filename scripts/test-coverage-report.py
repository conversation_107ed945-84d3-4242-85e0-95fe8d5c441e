#!/usr/bin/env python3
"""Test Coverage Report Generator for Forge Protocol.

Analyzes test files and generates comprehensive coverage statistics.
"""

import re
import subprocess
from collections import defaultdict
from pathlib import Path
from typing import Any


class TestCoverageAnalyzer:
    """Analyzes test coverage across the Forge Protocol test suite."""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.tests_dir = self.project_root / "tests"

    def analyze_test_files(self) -> dict[str, Any]:
        """Analyze all test files and return statistics."""
        stats = {
            "total_test_files": 0,
            "total_test_functions": 0,
            "test_categories": defaultdict(int),
            "test_markers": defaultdict(int),
            "files_by_category": defaultdict(list),
            "coverage_by_layer": defaultdict(int),
        }

        # Walk through test directory
        for test_file in self.tests_dir.rglob("test_*.py"):
            if test_file.is_file():
                stats["total_test_files"] += 1
                file_stats = self._analyze_test_file(test_file)

                # Update overall stats
                stats["total_test_functions"] += file_stats["test_functions"]

                # Categorize by directory
                category = test_file.parent.name
                stats["test_categories"][category] += file_stats["test_functions"]
                stats["files_by_category"][category].append(test_file.name)

                # Count markers
                for marker in file_stats["markers"]:
                    stats["test_markers"][marker] += 1

                # Layer analysis
                if "domain" in str(test_file):
                    stats["coverage_by_layer"]["domain"] += file_stats["test_functions"]
                elif "application" in str(test_file):
                    stats["coverage_by_layer"]["application"] += file_stats[
                        "test_functions"
                    ]
                elif "infrastructure" in str(test_file):
                    stats["coverage_by_layer"]["infrastructure"] += file_stats[
                        "test_functions"
                    ]
                elif "integration" in str(test_file):
                    stats["coverage_by_layer"]["integration"] += file_stats[
                        "test_functions"
                    ]

        return stats

    def _analyze_test_file(self, file_path: Path) -> dict[str, Any]:
        """Analyze a single test file."""
        stats = {
            "test_functions": 0,
            "test_classes": 0,
            "markers": set(),
            "async_tests": 0,
        }

        try:
            content = file_path.read_text()

            # Count test functions
            test_functions = re.findall(r"def test_\w+", content)
            stats["test_functions"] = len(test_functions)

            # Count async test functions
            async_tests = re.findall(r"async def test_\w+", content)
            stats["async_tests"] = len(async_tests)

            # Count test classes
            test_classes = re.findall(r"class Test\w+", content)
            stats["test_classes"] = len(test_classes)

            # Extract pytest markers
            markers = re.findall(r"@pytest\.mark\.(\w+)", content)
            stats["markers"] = set(markers)

        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")

        return stats

    def generate_report(self) -> str:
        """Generate comprehensive test coverage report."""
        stats = self.analyze_test_files()

        report = []
        report.append("🧪 FORGE PROTOCOL - COMPREHENSIVE TEST COVERAGE REPORT")
        report.append("=" * 60)
        report.append("")

        # Overall Statistics
        report.append("📊 OVERALL STATISTICS")
        report.append("-" * 30)
        report.append(f"Total Test Files: {stats['total_test_files']}")
        report.append(f"Total Test Functions: {stats['total_test_functions']}")
        report.append(
            f"Average Tests per File: {stats['total_test_functions'] / max(stats['total_test_files'], 1):.1f}"
        )
        report.append("")

        # Test Categories
        report.append("📂 TEST CATEGORIES")
        report.append("-" * 30)
        for category, count in sorted(stats["test_categories"].items()):
            percentage = (count / stats["total_test_functions"]) * 100
            report.append(
                f"{category.capitalize():15} {count:3d} tests ({percentage:5.1f}%)"
            )
        report.append("")

        # Test Markers
        report.append("🏷️  TEST MARKERS")
        report.append("-" * 30)
        for marker, count in sorted(
            stats["test_markers"].items(), key=lambda x: x[1], reverse=True
        ):
            report.append(f"{marker:15} {count:3d} files")
        report.append("")

        # Architecture Layer Coverage
        report.append("🏗️  ARCHITECTURE LAYER COVERAGE")
        report.append("-" * 30)
        total_layer_tests = sum(stats["coverage_by_layer"].values())
        for layer, count in stats["coverage_by_layer"].items():
            percentage = (count / max(total_layer_tests, 1)) * 100
            report.append(
                f"{layer.capitalize():15} {count:3d} tests ({percentage:5.1f}%)"
            )
        report.append("")

        # Files by Category
        report.append("📁 FILES BY CATEGORY")
        report.append("-" * 30)
        for category, files in sorted(stats["files_by_category"].items()):
            report.append(f"\n{category.upper()}:")
            for file in sorted(files):
                report.append(f"  • {file}")
        report.append("")

        # Test Quality Metrics
        report.append("📈 TEST QUALITY METRICS")
        report.append("-" * 30)

        # Calculate quality scores
        has_unit_tests = stats["test_categories"]["unit"] > 0
        has_integration_tests = stats["test_categories"]["integration"] > 0
        has_security_tests = stats["test_categories"]["security"] > 0
        has_performance_tests = stats["test_categories"]["performance"] > 0
        has_database_tests = stats["test_categories"]["database"] > 0

        quality_score = (
            sum(
                [
                    has_unit_tests,
                    has_integration_tests,
                    has_security_tests,
                    has_performance_tests,
                    has_database_tests,
                ]
            )
            / 5
            * 100
        )

        report.append(f"Quality Score: {quality_score:.0f}%")
        report.append(f"✅ Unit Tests: {'Yes' if has_unit_tests else 'No'}")
        report.append(
            f"✅ Integration Tests: {'Yes' if has_integration_tests else 'No'}"
        )
        report.append(f"✅ Security Tests: {'Yes' if has_security_tests else 'No'}")
        report.append(
            f"✅ Performance Tests: {'Yes' if has_performance_tests else 'No'}"
        )
        report.append(f"✅ Database Tests: {'Yes' if has_database_tests else 'No'}")
        report.append("")

        # Mobile-First Testing
        mobile_markers = stats["test_markers"].get("mobile", 0)
        performance_markers = stats["test_markers"].get("performance", 0)
        real_markers = stats["test_markers"].get("real", 0)

        report.append("📱 MOBILE-FIRST TESTING")
        report.append("-" * 30)
        report.append(f"Mobile-Specific Tests: {mobile_markers} files")
        report.append(f"Performance Tests: {performance_markers} files")
        report.append(f"Real Service Tests: {real_markers} files")
        report.append("")

        # Recommendations
        report.append("💡 RECOMMENDATIONS")
        report.append("-" * 30)

        recommendations = []

        if stats["total_test_functions"] < 100:
            recommendations.append(
                "• Consider adding more test cases for better coverage"
            )

        if not has_security_tests:
            recommendations.append("• Add security tests for vulnerability assessment")

        if not has_performance_tests:
            recommendations.append("• Add performance tests for mobile optimization")

        if mobile_markers < 5:
            recommendations.append("• Add more mobile-specific test scenarios")

        if stats["test_categories"]["unit"] < stats["total_test_functions"] * 0.6:
            recommendations.append(
                "• Increase unit test coverage (target: 60%+ of total tests)"
            )

        if not recommendations:
            recommendations.append(
                "• Excellent test coverage! Consider adding edge cases and stress tests"
            )

        for rec in recommendations:
            report.append(rec)

        report.append("")
        report.append(
            "🎯 TESTING PHILOSOPHY: No Mocking - Real Services - Mobile-First"
        )
        report.append("Generated by Forge Protocol Test Coverage Analyzer")

        return "\n".join(report)

    def run_pytest_coverage(self) -> str:
        """Run pytest with coverage and return results."""
        try:
            result = subprocess.run(
                [
                    "python",
                    "-m",
                    "pytest",
                    "--cov=app",
                    "--cov-report=term-missing",
                    "--cov-report=json",
                    "--quiet",
                ],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            return result.stdout
        except Exception as e:
            return f"Error running coverage: {e}"


def main():
    """Main function to generate and display test coverage report."""
    analyzer = TestCoverageAnalyzer()

    print("Analyzing Forge Protocol test coverage...")
    print()

    # Generate comprehensive report
    report = analyzer.generate_report()
    print(report)

    # Save report to file
    report_file = Path("test-coverage-report.txt")
    report_file.write_text(report)
    print(f"\n📄 Report saved to: {report_file}")

    # Run pytest coverage if available
    print("\n🔍 Running pytest coverage analysis...")
    coverage_output = analyzer.run_pytest_coverage()
    if coverage_output:
        print(coverage_output)


if __name__ == "__main__":
    main()
