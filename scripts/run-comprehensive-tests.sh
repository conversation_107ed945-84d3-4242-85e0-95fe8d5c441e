#!/bin/bash
# Comprehensive test runner for Forge Protocol
# Runs all test types in the correct order with proper setup/teardown

set -e  # Exit on any error

echo "🧪 Forge Protocol - Comprehensive Test Suite"
echo "============================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_section() {
    echo -e "${PURPLE}[SECTION]${NC} $1"
}

# Check if we're in Nix environment
if [ -z "$NIX_STORE" ]; then
    print_warning "Not in Nix environment. Run 'nix develop' first for best results."
fi

# Configuration
export PYTHONPATH="$PWD:$PYTHONPATH"
export ENVIRONMENT="testing"
export DEBUG="false"
FAILED_TESTS=()

# Function to run test category
run_test_category() {
    local category=$1
    local description=$2
    local command=$3
    local required=${4:-true}
    
    print_section "Running $description"
    echo "Command: $command"
    
    if eval "$command"; then
        print_success "$description passed"
        return 0
    else
        print_error "$description failed"
        FAILED_TESTS+=("$category")
        if [ "$required" = "true" ]; then
            return 1
        else
            print_warning "$description failed but marked as optional"
            return 0
        fi
    fi
}

# Start test services
print_status "Starting test services..."
if command -v docker &> /dev/null; then
    docker compose -f docker/docker-compose.yml up -d db redis --quiet-pull
    sleep 5
    python -m alembic upgrade head
else
    print_warning "Docker not available. Some tests may fail."
fi

# 1. Unit Tests (Domain, Application, Infrastructure)
run_test_category "unit" "Unit Tests" \
    "python -m pytest tests/unit/ -v --cov=app --cov-report=term-missing --cov-report=html --cov-fail-under=90"

# 1a. Domain Layer Tests
run_test_category "unit-domain" "Domain Layer Tests" \
    "python -m pytest tests/unit/domain/ -v -m domain"

# 1b. Application Layer Tests
run_test_category "unit-application" "Application Layer Tests" \
    "python -m pytest tests/unit/application/ -v -m application"

# 1c. Infrastructure Layer Tests
run_test_category "unit-infrastructure" "Infrastructure Layer Tests" \
    "python -m pytest tests/unit/infrastructure/ -v -m infrastructure"

# 2. Integration Tests (API, Database, Full Stack)
run_test_category "integration" "Integration Tests" \
    "python -m pytest tests/integration/ -v -m integration"

# 2a. API Integration Tests
run_test_category "integration-api" "API Integration Tests" \
    "python -m pytest tests/integration/ -v -m api"

# 3. Database Tests (Migrations, Performance, Constraints)
run_test_category "database" "Database Tests" \
    "python -m pytest tests/database/ -v -m database"

# 4. Security Tests (Authentication, Authorization, Attack Vectors)
run_test_category "security" "Security Tests" \
    "python -m pytest tests/security/ -v -m security"

# 4a. Authentication Security Tests
run_test_category "security-auth" "Authentication Security Tests" \
    "python -m pytest tests/security/ -v -m 'security and auth'"

# 5. Performance Tests (Response Times, Load, Scalability)
run_test_category "performance" "Performance Tests" \
    "python -m pytest tests/performance/ -v -m performance" false

# 5a. Mobile Performance Tests
run_test_category "performance-mobile" "Mobile Performance Tests" \
    "python -m pytest tests/performance/ -v -m 'performance and mobile'" false

# 6. Behavioral Tests (BDD)
if command -v behave &> /dev/null; then
    run_test_category "behavioral" "Behavioral Tests (BDD)" \
        "behave tests/behavioral/ --format=pretty"
else
    print_warning "Behave not installed. Skipping BDD tests."
    FAILED_TESTS+=("behavioral-skipped")
fi

# 7. Container Tests (if Docker available)
if command -v docker &> /dev/null; then
    run_test_category "container" "Production Container Tests" \
        "python -m pytest tests/container/ -v -m container -s" false
else
    print_warning "Docker not available. Skipping container tests."
    FAILED_TESTS+=("container-skipped")
fi

# 8. Code Quality Checks
print_section "Running Code Quality Checks"

run_test_category "format" "Code Formatting (black)" \
    "python -m black --check ."

run_test_category "imports" "Import Sorting (isort)" \
    "python -m isort --check-only ."

run_test_category "lint" "Linting (flake8)" \
    "python -m flake8 ."

run_test_category "types" "Type Checking (mypy)" \
    "python -m mypy ."

# 9. Security Scanning
if command -v bandit &> /dev/null; then
    run_test_category "security-scan" "Security Scanning (bandit)" \
        "python -m bandit -r app/ -f json -o bandit-report.json" false
else
    print_warning "Bandit not installed. Skipping security scan."
fi

# 10. Test Structure Validation
run_test_category "structure" "Project Structure" \
    "python scripts/test_structure.py"

# Cleanup
print_status "Cleaning up test services..."
if command -v docker &> /dev/null; then
    docker compose -f docker/docker-compose.yml down --quiet
fi

# Generate comprehensive report
print_section "Test Results Summary"
echo "=================================="

if [ ${#FAILED_TESTS[@]} -eq 0 ]; then
    print_success "🎉 ALL TESTS PASSED!"
    echo ""
    echo "✅ Unit Tests: PASSED"
    echo "✅ Integration Tests: PASSED" 
    echo "✅ Database Tests: PASSED"
    echo "✅ Security Tests: PASSED"
    echo "✅ Performance Tests: PASSED"
    echo "✅ Behavioral Tests: PASSED"
    echo "✅ Container Tests: PASSED"
    echo "✅ Code Quality: PASSED"
    echo ""
    echo "📊 Test Coverage Report: htmlcov/index.html"
    echo "🔒 Security Report: bandit-report.json"
    echo ""
    echo "🚀 Forge Protocol is ready for production!"
    exit 0
else
    print_error "❌ SOME TESTS FAILED"
    echo ""
    echo "Failed test categories:"
    for test in "${FAILED_TESTS[@]}"; do
        echo "  ❌ $test"
    done
    echo ""
    echo "📋 Next Steps:"
    echo "  • Review failed tests above"
    echo "  • Check test coverage: open htmlcov/index.html"
    echo "  • Review security report: cat bandit-report.json"
    echo "  • Fix issues and re-run tests"
    echo ""
    exit 1
fi
