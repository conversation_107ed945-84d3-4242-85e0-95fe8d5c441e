#!/usr/bin/env python3
"""
Documentation Build Script for RP Training API

This script provides a convenient way to build and manage
the comprehensive documentation for the RP Training API.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_dependencies():
    """Check if required documentation dependencies are installed."""
    print("Checking documentation dependencies...")
    
    required_packages = [
        "sphinx",
        "sphinx-rtd-theme",
        "myst-parser"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r docs/requirements.txt")
        return False
    
    print("✅ All documentation dependencies are installed")
    return True


def build_html_docs(project_root):
    """Build HTML documentation."""
    print("\n🔨 Building HTML documentation...")
    docs_dir = project_root / "docs"
    
    # Clean previous build
    run_command("make clean", cwd=docs_dir)
    
    # Build HTML documentation
    result = run_command("make html", cwd=docs_dir)
    
    if result.returncode == 0:
        print("✅ HTML documentation built successfully!")
        print(f"📁 Documentation available at: {docs_dir / '_build' / 'html' / 'index.html'}")
        return True
    else:
        print("❌ Failed to build HTML documentation")
        return False


def build_pdf_docs(project_root):
    """Build PDF documentation."""
    print("\n📄 Building PDF documentation...")
    docs_dir = project_root / "docs"
    
    # Check if LaTeX is available
    latex_check = run_command("which pdflatex", check=False)
    if latex_check.returncode != 0:
        print("⚠️  LaTeX not found. PDF generation skipped.")
        print("Install LaTeX to enable PDF generation.")
        return False
    
    # Build PDF documentation
    result = run_command("make pdf", cwd=docs_dir)
    
    if result.returncode == 0:
        print("✅ PDF documentation built successfully!")
        print(f"📁 PDF available at: {docs_dir / '_build' / 'latex'}")
        return True
    else:
        print("❌ Failed to build PDF documentation")
        return False


def generate_api_docs(project_root):
    """Generate API documentation from source code."""
    print("\n🔧 Generating API documentation...")
    docs_dir = project_root / "docs"
    
    # Generate API documentation
    result = run_command("make api-docs", cwd=docs_dir)
    
    if result.returncode == 0:
        print("✅ API documentation generated successfully!")
        return True
    else:
        print("❌ Failed to generate API documentation")
        return False


def check_links(project_root):
    """Check for broken links in documentation."""
    print("\n🔗 Checking for broken links...")
    docs_dir = project_root / "docs"
    
    result = run_command("make linkcheck", cwd=docs_dir)
    
    if result.returncode == 0:
        print("✅ No broken links found!")
        return True
    else:
        print("⚠️  Some links may be broken. Check the linkcheck report.")
        return False


def serve_docs(project_root, port=8080):
    """Serve documentation locally."""
    print(f"\n🌐 Serving documentation at http://localhost:{port}")
    docs_dir = project_root / "docs" / "_build" / "html"
    
    if not docs_dir.exists():
        print("❌ Documentation not built. Run with --build first.")
        return False
    
    try:
        run_command(f"python -m http.server {port}", cwd=docs_dir)
    except KeyboardInterrupt:
        print("\n👋 Documentation server stopped.")
    
    return True


def live_reload(project_root):
    """Start live reload server for development."""
    print("\n🔄 Starting live reload server...")
    docs_dir = project_root / "docs"
    
    # Check if sphinx-autobuild is available
    try:
        import sphinx_autobuild
        print("✅ sphinx-autobuild is available")
    except ImportError:
        print("❌ sphinx-autobuild not found. Install with: pip install sphinx-autobuild")
        return False
    
    try:
        run_command("make livehtml", cwd=docs_dir)
    except KeyboardInterrupt:
        print("\n👋 Live reload server stopped.")
    
    return True


def validate_docs(project_root):
    """Validate documentation quality."""
    print("\n✅ Validating documentation quality...")
    docs_dir = project_root / "docs"
    
    # Check documentation build without warnings
    result = run_command("make check", cwd=docs_dir)
    
    if result.returncode == 0:
        print("✅ Documentation quality validation passed!")
        return True
    else:
        print("❌ Documentation quality issues found")
        return False


def main():
    """Main entry point for the documentation build script."""
    parser = argparse.ArgumentParser(
        description="Build and manage RP Training API documentation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Build HTML documentation
  python scripts/build_docs.py --build

  # Build all formats
  python scripts/build_docs.py --build --pdf

  # Generate API docs and build
  python scripts/build_docs.py --api --build

  # Serve documentation locally
  python scripts/build_docs.py --serve

  # Live reload for development
  python scripts/build_docs.py --live

  # Full build with validation
  python scripts/build_docs.py --full

  # Check for broken links
  python scripts/build_docs.py --check-links
        """
    )
    
    parser.add_argument(
        "--build",
        action="store_true",
        help="Build HTML documentation"
    )
    
    parser.add_argument(
        "--pdf",
        action="store_true",
        help="Build PDF documentation (requires LaTeX)"
    )
    
    parser.add_argument(
        "--api",
        action="store_true",
        help="Generate API documentation from source code"
    )
    
    parser.add_argument(
        "--serve",
        action="store_true",
        help="Serve documentation locally"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="Port for serving documentation (default: 8080)"
    )
    
    parser.add_argument(
        "--live",
        action="store_true",
        help="Start live reload server for development"
    )
    
    parser.add_argument(
        "--check-links",
        action="store_true",
        help="Check for broken links in documentation"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate documentation quality"
    )
    
    parser.add_argument(
        "--full",
        action="store_true",
        help="Full build: API docs + HTML + PDF + validation"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check documentation dependencies"
    )
    
    args = parser.parse_args()
    
    # Get project root directory
    project_root = Path(__file__).parent.parent
    
    print("🚀 RP Training API Documentation Builder")
    print("=" * 50)
    
    # Check dependencies if requested or if building
    if args.check_deps or args.build or args.pdf or args.api or args.full:
        if not check_dependencies():
            print("\n❌ Please install missing dependencies before building documentation.")
            sys.exit(1)
    
    success = True
    
    # Handle full build
    if args.full:
        print("\n🎯 Running full documentation build...")
        success &= generate_api_docs(project_root)
        success &= build_html_docs(project_root)
        success &= build_pdf_docs(project_root)
        success &= validate_docs(project_root)
        success &= check_links(project_root)
    else:
        # Handle individual options
        if args.api:
            success &= generate_api_docs(project_root)
        
        if args.build:
            success &= build_html_docs(project_root)
        
        if args.pdf:
            success &= build_pdf_docs(project_root)
        
        if args.validate:
            success &= validate_docs(project_root)
        
        if args.check_links:
            success &= check_links(project_root)
    
    # Handle serving options
    if args.live:
        live_reload(project_root)
    elif args.serve:
        serve_docs(project_root, args.port)
    
    # Handle no arguments case
    if not any(vars(args).values()):
        print("No action specified. Use --help for available options.")
        print("\nQuick start:")
        print("  python scripts/build_docs.py --build    # Build HTML docs")
        print("  python scripts/build_docs.py --serve    # Serve docs locally")
        print("  python scripts/build_docs.py --full     # Full build")
    
    # Final status
    if success:
        print("\n🎉 Documentation operations completed successfully!")
    else:
        print("\n❌ Some documentation operations failed.")
        sys.exit(1)


if __name__ == "__main__":
    main()
