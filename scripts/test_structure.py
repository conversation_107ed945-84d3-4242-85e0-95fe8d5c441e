#!/usr/bin/env python3
"""
Test script to verify project structure and basic imports.

This script validates that our Clean Architecture setup is working
correctly by testing imports and basic functionality.
"""

import sys
import traceback
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all major components can be imported."""
    print("🧪 Testing imports...")
    
    try:
        # Test domain layer
        from app.domain.entities.user import User
        from app.domain.services.auth_service import AuthService
        from app.domain.repositories.user_repository import UserRepository
        print("✅ Domain layer imports successful")
        
        # Test application layer
        from app.application.use_cases.register_user import RegisterUserUseCase
        from app.application.dto.user_dto import UserDTO
        print("✅ Application layer imports successful")
        
        # Test infrastructure layer
        from app.infrastructure.database.connection import DatabaseManager
        from app.infrastructure.auth.jwt_handler import JWTHandler
        from app.infrastructure.auth.password_handler import PasswordHandler
        print("✅ Infrastructure layer imports successful")
        
        # Test presentation layer
        from app.presentation.schemas.user_schema import UserResponse
        from app.presentation.api.v1.health import router
        print("✅ Presentation layer imports successful")
        
        # Test main application
        from app.main import app
        print("✅ Main application import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False


def test_user_entity():
    """Test User entity business logic."""
    print("\n🧪 Testing User entity...")
    
    try:
        from app.domain.entities.user import User
        from app.domain.exceptions.auth_exceptions import InvalidEmailError, WeakPasswordError
        
        # Test valid user creation
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe"
        )
        
        assert user.email == "<EMAIL>"
        assert user.first_name == "John"
        assert user.get_full_name() == "John Doe"
        assert user.can_authenticate() is True
        print("✅ User entity creation and methods working")
        
        # Test email validation
        try:
            User.create(email="invalid-email", password="SecurePass123!")
            assert False, "Should have raised InvalidEmailError"
        except InvalidEmailError:
            print("✅ Email validation working")
        
        # Test password validation
        try:
            User.create(email="<EMAIL>", password="weak")
            assert False, "Should have raised WeakPasswordError"
        except WeakPasswordError:
            print("✅ Password validation working")
        
        return True
        
    except Exception as e:
        print(f"❌ User entity test failed: {e}")
        traceback.print_exc()
        return False


def test_password_handler():
    """Test password hashing functionality."""
    print("\n🧪 Testing password handler...")
    
    try:
        from app.infrastructure.auth.password_handler import PasswordHandler
        
        handler = PasswordHandler()
        password = "TestPassword123!"
        
        # Test hashing
        hashed = handler.hash_password(password)
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        print("✅ Password hashing working")
        
        # Test verification
        assert handler.verify_password(password, hashed) is True
        assert handler.verify_password("wrong_password", hashed) is False
        print("✅ Password verification working")
        
        return True
        
    except Exception as e:
        print(f"❌ Password handler test failed: {e}")
        traceback.print_exc()
        return False


def test_jwt_handler():
    """Test JWT token functionality."""
    print("\n🧪 Testing JWT handler...")
    
    try:
        from app.infrastructure.auth.jwt_handler import JWTHandler
        from uuid import uuid4
        
        handler = JWTHandler()
        user_id = str(uuid4())
        
        # Test token generation
        access_token = await_sync(handler.generate_access_token(user_id))
        refresh_token = await_sync(handler.generate_refresh_token(user_id))
        
        assert isinstance(access_token, str)
        assert isinstance(refresh_token, str)
        assert access_token != refresh_token
        print("✅ JWT token generation working")
        
        # Test token decoding
        payload = handler.decode_token(access_token)
        assert payload["sub"] == user_id
        assert payload["type"] == "access"
        print("✅ JWT token decoding working")
        
        return True
        
    except Exception as e:
        print(f"❌ JWT handler test failed: {e}")
        traceback.print_exc()
        return False


def await_sync(coro):
    """Helper to run async functions in sync context."""
    import asyncio
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


def test_configuration():
    """Test configuration loading."""
    print("\n🧪 Testing configuration...")
    
    try:
        from app.config import settings
        
        # Test that settings can be loaded
        assert hasattr(settings, 'app_name')
        assert hasattr(settings, 'secret_key')
        assert hasattr(settings, 'database_url')
        print("✅ Configuration loading working")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Testing RP Training API Structure\n")
    
    tests = [
        test_imports,
        test_configuration,
        test_user_entity,
        test_password_handler,
        test_jwt_handler,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Project structure is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
