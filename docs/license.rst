License
=======

MIT License
-----------

Copyright (c) 2025 Forge Protocol Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

Third-Party Licenses
--------------------

Forge Protocol includes and depends on several open-source libraries and frameworks. 
Below are the licenses for the major dependencies:

FastAPI
~~~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2018 Sebastián Ramírez
**URL**: https://github.com/tiangolo/fastapi

SQLAlchemy
~~~~~~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2006-2023 the SQLAlchemy authors and contributors
**URL**: https://github.com/sqlalchemy/sqlalchemy

Pydantic
~~~~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2017 to present Pydantic Services Inc. and individual contributors
**URL**: https://github.com/pydantic/pydantic

PostgreSQL
~~~~~~~~~~

**License**: PostgreSQL License (similar to BSD or MIT)
**Copyright**: Copyright (c) 1996-2023, The PostgreSQL Global Development Group
**URL**: https://www.postgresql.org/

Docker
~~~~~~

**License**: Apache License 2.0
**Copyright**: Copyright 2013-2023 Docker, Inc.
**URL**: https://github.com/docker/docker

pytest
~~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2004 Holger Krekel and others
**URL**: https://github.com/pytest-dev/pytest

Alembic
~~~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2009-2023 by the Alembic authors and contributors
**URL**: https://github.com/sqlalchemy/alembic

bcrypt
~~~~~~

**License**: Apache License 2.0
**Copyright**: Copyright (c) 2013 Donald Stufft and individual contributors
**URL**: https://github.com/pyca/bcrypt

PyJWT
~~~~~

**License**: MIT License
**Copyright**: Copyright (c) 2015 José Padilla
**URL**: https://github.com/jpadilla/pyjwt

uvicorn
~~~~~~~

**License**: BSD 3-Clause License
**Copyright**: Copyright (c) 2017-present, Encode OSS Ltd.
**URL**: https://github.com/encode/uvicorn

Scientific References
---------------------

The scientific methodology implemented in Forge Protocol is based on research and principles 
developed by Renaissance Periodisation and the broader exercise science community. While the 
code implementation is licensed under MIT, the scientific principles and methodologies are 
based on published research and established training principles.

**Key Scientific Sources:**

* Renaissance Periodisation methodology and principles
* "Scientific Principles of Hypertrophy Training" by Dr. Mike Israetel et al.
* Peer-reviewed research in exercise science and sports medicine
* Evidence-based training principles from the scientific community

Data and Privacy
----------------

**User Data**
User data collected and processed by Forge Protocol is subject to our Privacy Policy 
and Terms of Service. The software license does not grant rights to user data.

**Training Data**
Aggregated and anonymised training data may be used for research purposes in accordance 
with our Privacy Policy and applicable data protection regulations.

**Scientific Data**
Exercise databases, training algorithms, and scientific methodologies are provided 
under the MIT license for research and educational purposes.

Disclaimer
----------

**No Warranty**
This software is provided "as is" without warranty of any kind. The authors and 
contributors are not liable for any damages arising from the use of this software.

**Medical Disclaimer**
Forge Protocol is not intended to provide medical advice. Users should consult with 
qualified healthcare professionals before beginning any exercise program. The software 
is for informational and educational purposes only.

**Fitness Disclaimer**
While based on scientific principles, individual responses to training may vary. 
Users should listen to their bodies and adjust training as needed. The software 
provides general guidance and should not replace professional coaching or medical advice.

**Research Disclaimer**
The algorithms and methodologies implemented are based on current scientific understanding 
and may be updated as new research emerges. Results may vary based on individual factors 
and adherence to the program.

Attribution
-----------

If you use Forge Protocol in your research, applications, or publications, we appreciate 
attribution. Suggested citation:

.. code-block:: text

   Forge Protocol Team. (2025). Forge Protocol: Evidence-based hypertrophy training platform. 
   GitHub. https://github.com/forkrul/forge-protocol

For academic publications, please also cite the relevant scientific sources that inform 
the methodology implemented in the platform.

Contact Information
-------------------

For questions about licensing, usage rights, or commercial applications:

* **General Inquiries**: <EMAIL>
* **Legal Questions**: <EMAIL>
* **Commercial Licensing**: <EMAIL>
* **Research Collaboration**: <EMAIL>

**Mailing Address**:
Forge Protocol Team
[Address to be provided]

**GitHub Repository**:
https://github.com/forkrul/forge-protocol

Updates to License
------------------

This license may be updated from time to time. Users will be notified of significant 
changes through the project repository and official communication channels.

**Version History**:
* v1.0 (2025-01-06): Initial MIT license
* Future versions will be documented here

For the most current version of this license, please refer to the LICENSE file in the 
project repository: https://github.com/forkrul/forge-protocol/blob/main/LICENSE
