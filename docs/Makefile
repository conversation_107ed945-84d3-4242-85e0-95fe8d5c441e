# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# Custom targets for RP Training API documentation

# Build HTML documentation
html:
	@echo "Building HTML documentation..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS)
	@echo "HTML documentation built in $(BUILDDIR)/html/"

# Build PDF documentation
pdf:
	@echo "Building PDF documentation..."
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS)
	@make -C "$(BUILDDIR)/latex" all-pdf
	@echo "PDF documentation built in $(BUILDDIR)/latex/"

# Clean build directory
clean:
	@echo "Cleaning build directory..."
	@rm -rf "$(BUILDDIR)"
	@echo "Build directory cleaned."

# Live reload for development
livehtml:
	@echo "Starting live reload server..."
	@sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS)

# Check for broken links
linkcheck:
	@echo "Checking for broken links..."
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS)
	@echo "Link check complete. See $(BUILDDIR)/linkcheck/output.txt"

# Build all formats
all: clean html pdf
	@echo "All documentation formats built successfully!"

# Quick build for development
dev: html
	@echo "Development build complete!"
	@echo "Open $(BUILDDIR)/html/index.html in your browser"

# Install documentation dependencies
install-deps:
	@echo "Installing documentation dependencies..."
	@pip install sphinx sphinx-rtd-theme myst-parser sphinx-autobuild
	@echo "Documentation dependencies installed!"

# Serve documentation locally
serve:
	@echo "Serving documentation at http://localhost:8080"
	@cd "$(BUILDDIR)/html" && python -m http.server 8080

# Check documentation quality
check:
	@echo "Checking documentation quality..."
	@$(SPHINXBUILD) -b html -W "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS)
	@echo "Documentation quality check passed!"

# Generate API documentation
api-docs:
	@echo "Generating API documentation..."
	@sphinx-apidoc -o modules/ ../app/ --force --separate
	@echo "API documentation generated in modules/"

# Full documentation build with API docs
full: clean api-docs html
	@echo "Full documentation build complete!"

.PHONY: html pdf clean livehtml linkcheck all dev install-deps serve check api-docs full
