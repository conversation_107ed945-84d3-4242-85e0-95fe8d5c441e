# Phase 2: Exercise Database & Workout Logging - Detailed PRD

## Overview
Build a comprehensive exercise database and real-time workout logging system that enables users to track their training with precision, including set-by-set RIR/RPE tracking and intelligent exercise search capabilities.

## Status: 🔮 PLANNED
**Start Date**: TBD (After Phase 1 completion)
**Target Completion**: TBD
**Current Progress**: 0% (Awaiting Phase 1 completion)

## Objectives

### Primary Goals
1. **Exercise Database**: Comprehensive database with 100+ exercises, multimedia content, and detailed metadata
2. **Workout Logging**: Real-time set-by-set tracking with RIR/RPE, weight, and rep logging
3. **Exercise Search**: Advanced filtering by muscle group, equipment, movement pattern, and difficulty
4. **Media Management**: Video and image handling for exercise demonstrations and form cues
5. **Data Foundation**: Establish the data layer for future RP algorithm implementation

### Success Criteria
- [ ] 100+ exercises in database with complete metadata
- [ ] Users can create and log complete workouts
- [ ] Set-by-set tracking with RIR/RPE functional
- [ ] Exercise search and filtering working efficiently
- [ ] Video/image content properly managed and served
- [ ] Workout data ready for Phase 3 algorithm integration
- [ ] API response times < 200ms for all exercise/workout endpoints
- [ ] Mobile-optimised workout logging flow

## Technical Requirements

### Database Schema Extensions

#### Exercises Table
```sql
CREATE TABLE exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    primary_muscle_group muscle_group_enum NOT NULL,
    secondary_muscle_groups muscle_group_enum[],
    movement_pattern movement_pattern_enum NOT NULL,
    equipment_required equipment_enum[],
    difficulty_level difficulty_enum NOT NULL,
    video_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    form_cues TEXT[],
    setup_instructions TEXT,
    execution_steps TEXT[],
    common_mistakes TEXT[],
    safety_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Indexes for efficient searching
CREATE INDEX idx_exercises_primary_muscle ON exercises(primary_muscle_group);
CREATE INDEX idx_exercises_equipment ON exercises USING GIN(equipment_required);
CREATE INDEX idx_exercises_movement_pattern ON exercises(movement_pattern);
CREATE INDEX idx_exercises_difficulty ON exercises(difficulty_level);
CREATE INDEX idx_exercises_active ON exercises(is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercises_search ON exercises USING GIN(to_tsvector('english', name || ' ' || description));
```

#### Workouts Table
```sql
CREATE TABLE workouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255),
    workout_date DATE NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_volume_load DECIMAL(10,2),
    average_rpe DECIMAL(3,1),
    total_sets INTEGER DEFAULT 0,
    total_exercises INTEGER DEFAULT 0,
    duration_minutes INTEGER,
    notes TEXT,
    is_template BOOLEAN DEFAULT FALSE,
    template_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Indexes for efficient querying
CREATE INDEX idx_workouts_user_id ON workouts(user_id);
CREATE INDEX idx_workouts_date ON workouts(workout_date);
CREATE INDEX idx_workouts_completed ON workouts(completed_at) WHERE completed_at IS NOT NULL;
CREATE INDEX idx_workouts_template ON workouts(is_template) WHERE is_template = TRUE;
```

#### Exercise Sets Table
```sql
CREATE TABLE exercise_sets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workout_id UUID REFERENCES workouts(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    set_number INTEGER NOT NULL,
    weight_kg DECIMAL(5,2),
    reps_completed INTEGER,
    reps_target INTEGER,
    rir_target INTEGER CHECK (rir_target BETWEEN 0 AND 10),
    rir_actual INTEGER CHECK (rir_actual BETWEEN 0 AND 10),
    rpe DECIMAL(3,1) CHECK (rpe BETWEEN 1.0 AND 10.0),
    rest_seconds INTEGER,
    tempo VARCHAR(20), -- e.g., "3-1-2-1" for eccentric-pause-concentric-pause
    range_of_motion VARCHAR(50), -- e.g., "full", "partial", "lengthened"
    notes TEXT,
    is_warmup BOOLEAN DEFAULT FALSE,
    is_dropset BOOLEAN DEFAULT FALSE,
    is_failure BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_exercise_sets_workout ON exercise_sets(workout_id);
CREATE INDEX idx_exercise_sets_exercise ON exercise_sets(exercise_id);
CREATE INDEX idx_exercise_sets_date ON exercise_sets(created_at);
```

#### Exercise Media Table
```sql
CREATE TABLE exercise_media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
    media_type media_type_enum NOT NULL, -- 'video', 'image', 'gif'
    url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    title VARCHAR(255),
    description TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER, -- for videos
    width_pixels INTEGER,
    height_pixels INTEGER,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_exercise_media_exercise ON exercise_media(exercise_id);
CREATE INDEX idx_exercise_media_type ON exercise_media(media_type);
CREATE INDEX idx_exercise_media_primary ON exercise_media(is_primary) WHERE is_primary = TRUE;
```

### Enums and Types
```sql
CREATE TYPE muscle_group_enum AS ENUM (
    'chest', 'back', 'shoulders', 'biceps', 'triceps', 
    'quads', 'hamstrings', 'glutes', 'calves', 'abs', 
    'forearms', 'traps', 'lats', 'rear_delts', 'side_delts'
);

CREATE TYPE movement_pattern_enum AS ENUM (
    'push', 'pull', 'squat', 'hinge', 'carry', 'isolation', 
    'rotation', 'anti_extension', 'anti_flexion', 'anti_rotation'
);

CREATE TYPE equipment_enum AS ENUM (
    'barbell', 'dumbbell', 'cable', 'machine', 'bodyweight', 
    'resistance_band', 'kettlebell', 'suspension_trainer', 
    'medicine_ball', 'plate', 'smith_machine'
);

CREATE TYPE difficulty_enum AS ENUM ('beginner', 'intermediate', 'advanced');
CREATE TYPE media_type_enum AS ENUM ('video', 'image', 'gif');
```

## API Endpoints (Phase 2)

### Exercise Database
```
GET    /api/v1/exercises                    # List exercises with pagination
GET    /api/v1/exercises/{exercise_id}      # Get exercise details
GET    /api/v1/exercises/search             # Search exercises with filters
GET    /api/v1/exercises/muscle-group/{group} # Exercises by muscle group
GET    /api/v1/exercises/equipment/{equipment} # Exercises by equipment
GET    /api/v1/exercises/random             # Get random exercise suggestions
POST   /api/v1/exercises                    # Create new exercise (admin)
PUT    /api/v1/exercises/{exercise_id}      # Update exercise (admin)
DELETE /api/v1/exercises/{exercise_id}      # Soft delete exercise (admin)
```

### Workout Management
```
GET    /api/v1/workouts                     # List user's workouts
POST   /api/v1/workouts                     # Create new workout
GET    /api/v1/workouts/{workout_id}        # Get workout details
PUT    /api/v1/workouts/{workout_id}        # Update workout
DELETE /api/v1/workouts/{workout_id}        # Delete workout
POST   /api/v1/workouts/{workout_id}/start  # Start workout session
POST   /api/v1/workouts/{workout_id}/complete # Complete workout
GET    /api/v1/workouts/current             # Get active workout
GET    /api/v1/workouts/templates           # Get workout templates
```

### Set Tracking
```
POST   /api/v1/workouts/{workout_id}/sets   # Add set to workout
PUT    /api/v1/sets/{set_id}                # Update set details
DELETE /api/v1/sets/{set_id}                # Delete set
GET    /api/v1/sets/{set_id}/suggestions    # Get progression suggestions
POST   /api/v1/sets/{set_id}/copy           # Copy set for next workout
```

### Exercise Media
```
GET    /api/v1/exercises/{exercise_id}/media # Get exercise media
POST   /api/v1/exercises/{exercise_id}/media # Upload media (admin)
DELETE /api/v1/media/{media_id}              # Delete media (admin)
```

## Implementation Tasks

### Task 1: Exercise Database Foundation
**Estimated Time**: 12 hours
- [ ] Create exercise database schema and migrations
- [ ] Implement exercise entity and repository
- [ ] Create exercise service with CRUD operations
- [ ] Build exercise search functionality with filters
- [ ] Add exercise seeding with initial 100+ exercises
- [ ] Implement exercise validation and business rules

### Task 2: Workout Management System
**Estimated Time**: 10 hours
- [ ] Create workout and exercise_sets schemas
- [ ] Implement workout entity and repository
- [ ] Build workout service with session management
- [ ] Create workout templates functionality
- [ ] Add workout statistics calculation
- [ ] Implement workout sharing and copying

### Task 3: Set Tracking & Logging
**Estimated Time**: 8 hours
- [ ] Implement set entity and repository
- [ ] Build real-time set logging functionality
- [ ] Add RIR/RPE validation and tracking
- [ ] Create progression suggestion algorithms
- [ ] Implement rest timer functionality
- [ ] Add set modification and deletion

### Task 4: Exercise Media Management
**Estimated Time**: 6 hours
- [ ] Create media storage infrastructure
- [ ] Implement file upload and validation
- [ ] Build image/video processing pipeline
- [ ] Add thumbnail generation
- [ ] Create media serving endpoints
- [ ] Implement media cleanup and optimization

### Task 5: API Layer Implementation
**Estimated Time**: 8 hours
- [ ] Create Pydantic schemas for all entities
- [ ] Implement FastAPI routes for exercises
- [ ] Build workout management endpoints
- [ ] Add set tracking API endpoints
- [ ] Implement search and filtering
- [ ] Add proper error handling and validation

### Task 6: Testing & Documentation
**Estimated Time**: 10 hours
- [ ] Write unit tests for all services
- [ ] Create integration tests for API endpoints
- [ ] Add behavioral tests for workout flows
- [ ] Write performance tests for search
- [ ] Update API documentation
- [ ] Create user guides and examples

## Quality Gates

### Functional Requirements
- [ ] All exercise CRUD operations working
- [ ] Workout creation and logging functional
- [ ] Set tracking with RIR/RPE operational
- [ ] Exercise search returns accurate results
- [ ] Media upload and serving working
- [ ] Data validation preventing invalid entries

### Performance Requirements
- [ ] Exercise search < 100ms response time
- [ ] Workout loading < 200ms
- [ ] Set logging < 50ms response time
- [ ] Media serving optimised for mobile
- [ ] Database queries optimised with proper indexes
- [ ] API pagination working efficiently

### Data Quality
- [ ] 100+ exercises with complete metadata
- [ ] All exercises have primary muscle group
- [ ] Equipment and difficulty properly categorised
- [ ] Form cues and instructions comprehensive
- [ ] Media files properly linked and accessible

## Dependencies

### New Dependencies
```
# Media handling
Pillow>=10.0.0              # Image processing
python-multipart>=0.0.6     # File upload support

# Search functionality
elasticsearch>=8.0.0        # Full-text search (optional)
# OR
whoosh>=2.7.4              # Lightweight search alternative

# File storage
boto3>=1.28.0              # AWS S3 integration (optional)
# OR local file storage

# Additional validation
email-validator>=2.0.0     # Enhanced validation
```

## Risks & Mitigation

### Technical Risks
1. **Media Storage Costs**: Start with local storage, plan S3 migration
2. **Search Performance**: Implement proper indexing, consider Elasticsearch
3. **Data Volume**: Plan for pagination and efficient queries
4. **Mobile Performance**: Optimise payload sizes and implement caching

### Data Risks
1. **Exercise Data Quality**: Implement validation rules and review process
2. **User Data Loss**: Ensure proper backup and soft delete strategies
3. **Media File Management**: Implement cleanup processes and storage limits

## Definition of Done

### Phase 2 Complete When:
- [ ] 100+ exercises in database with complete metadata
- [ ] Users can create, start, and complete workouts
- [ ] Set-by-set logging with RIR/RPE functional
- [ ] Exercise search and filtering working efficiently
- [ ] Media management system operational
- [ ] All API endpoints tested and documented
- [ ] Performance benchmarks met
- [ ] Data migration scripts ready
- [ ] User acceptance testing completed

**Next Phase**: Phase 3 - RP Scientific Algorithms

## User Stories

### Epic: Exercise Discovery
**As a user, I want to find appropriate exercises so that I can build effective workouts.**

#### User Story 1: Exercise Search
- **Given** I am planning a workout
- **When** I search for chest exercises
- **Then** I should see exercises filtered by primary muscle group
- **And** I should be able to filter by available equipment
- **And** I should see difficulty levels and movement patterns

#### User Story 2: Exercise Details
- **Given** I have found an exercise
- **When** I view its details
- **Then** I should see form cues and setup instructions
- **And** I should see video demonstrations
- **And** I should see muscle groups targeted

### Epic: Workout Logging
**As a user, I want to log my workouts accurately so that I can track my progress.**

#### User Story 3: Real-time Logging
- **Given** I am in an active workout
- **When** I complete a set
- **Then** I should be able to log weight, reps, and RIR quickly
- **And** the system should save my data immediately
- **And** I should see my rest timer start automatically

#### User Story 4: Workout Management
- **Given** I want to start a workout
- **When** I create a new workout session
- **Then** I should be able to add exercises from the database
- **And** I should be able to plan my sets and targets
- **And** I should be able to save the workout as a template
