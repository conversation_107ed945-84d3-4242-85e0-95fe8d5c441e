Periodization Algorithms
========================

This document explains the scientific periodization algorithms used in the RP Training API to optimize long-term training adaptations and prevent plateaus.

Periodization Overview
----------------------

Periodization is the systematic planning of athletic training that involves progressive cycling of various aspects of a training program during a specific period.

.. mermaid::

   graph TB
       subgraph "Periodization Hierarchy"
           MACROCYCLE[Macrocycle<br/>6-12 months<br/>Annual Plan]
           MESOCYCLE[Mesocycle<br/>3-6 weeks<br/>Training Block]
           MICROCYCLE[Microcycle<br/>1 week<br/>Training Week]
           SESSION[Training Session<br/>1-2 hours<br/>Individual Workout]
       end
       
       subgraph "Periodization Models"
           LINEAR[Linear Periodization<br/>Progressive Overload]
           UNDULATING[Undulating Periodization<br/>Variable Loading]
           BLOCK[Block Periodization<br/>Concentrated Loading]
           CONJUGATE[Conjugate Method<br/>Concurrent Training]
       end
       
       subgraph "Training Phases"
           ACCUMULATION[Accumulation Phase<br/>Volume Focus]
           INTENSIFICATION[Intensification Phase<br/>Intensity Focus]
           REALIZATION[Realization Phase<br/>Peak Performance]
           DELOAD[Deload Phase<br/>Recovery Focus]
       end
       
       MACROCYCLE --> MESOCYCLE
       MESOCYCLE --> MICROCYCLE
       MICROCYCLE --> SESSION
       
       MACROCYCLE --> LINEAR
       MACROCYCLE --> UNDULATING
       MACROCYCLE --> BLOCK
       MACROCYCLE --> CONJUGATE
       
       LINEAR --> ACCUMULATION
       UNDULATING --> INTENSIFICATION
       BLOCK --> REALIZATION
       CONJUGATE --> DELOAD
       
       %% Styling
       classDef hierarchy fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef models fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef phases fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       
       class MACROCYCLE,MESOCYCLE,MICROCYCLE,SESSION hierarchy
       class LINEAR,UNDULATING,BLOCK,CONJUGATE models
       class ACCUMULATION,INTENSIFICATION,REALIZATION,DELOAD phases

Linear Periodization Algorithm
------------------------------

Traditional linear periodization progressively increases intensity while decreasing volume:

.. mermaid::

   graph LR
       subgraph "Linear Periodization Progression"
           WEEK1[Week 1-2<br/>High Volume<br/>Low Intensity]
           WEEK3[Week 3-4<br/>Moderate Volume<br/>Moderate Intensity]
           WEEK5[Week 5-6<br/>Low Volume<br/>High Intensity]
           DELOAD1[Week 7<br/>Deload<br/>Recovery]
       end
       
       subgraph "Training Variables"
           VOLUME[Training Volume<br/>Sets × Reps]
           INTENSITY[Training Intensity<br/>% of 1RM]
           FREQUENCY[Training Frequency<br/>Sessions/Week]
           DENSITY[Training Density<br/>Work:Rest Ratio]
       end
       
       subgraph "Adaptation Focus"
           HYPERTROPHY[Hypertrophy<br/>Muscle Growth]
           STRENGTH[Strength<br/>Force Production]
           POWER[Power<br/>Rate of Force]
           PEAK[Peak Performance<br/>Competition Ready]
       end
       
       WEEK1 --> WEEK3
       WEEK3 --> WEEK5
       WEEK5 --> DELOAD1
       DELOAD1 -.->|"Repeat Cycle"| WEEK1
       
       WEEK1 --> VOLUME
       WEEK3 --> INTENSITY
       WEEK5 --> FREQUENCY
       DELOAD1 --> DENSITY
       
       VOLUME --> HYPERTROPHY
       INTENSITY --> STRENGTH
       FREQUENCY --> POWER
       DENSITY --> PEAK
       
       %% Volume and intensity relationship
       WEEK1 -.->|"85% Volume<br/>65% Intensity"| WEEK1
       WEEK3 -.->|"70% Volume<br/>75% Intensity"| WEEK3
       WEEK5 -.->|"55% Volume<br/>85% Intensity"| WEEK5
       DELOAD1 -.->|"40% Volume<br/>60% Intensity"| DELOAD1
       
       %% Styling
       classDef progression fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef variables fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef adaptations fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef deload fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class WEEK1,WEEK3,WEEK5 progression
       class VOLUME,INTENSITY,FREQUENCY,DENSITY variables
       class HYPERTROPHY,STRENGTH,POWER,PEAK adaptations
       class DELOAD1 deload

Daily Undulating Periodization (DUP)
------------------------------------

DUP varies training stimuli on a daily basis to optimize adaptations:

.. mermaid::

   graph TB
       subgraph "Weekly DUP Structure"
           MON[Monday<br/>Hypertrophy Day<br/>3x8-12 @ 70-80%]
           WED[Wednesday<br/>Strength Day<br/>5x3-5 @ 85-95%]
           FRI[Friday<br/>Power Day<br/>6x1-3 @ 60-80%]
           SUN[Sunday<br/>Recovery Day<br/>Active Recovery]
       end
       
       subgraph "Training Adaptations"
           HYPER_ADAPT[Hypertrophy Adaptations<br/>Protein Synthesis<br/>Muscle Growth]
           STRENGTH_ADAPT[Strength Adaptations<br/>Neural Drive<br/>Force Production]
           POWER_ADAPT[Power Adaptations<br/>Rate Coding<br/>Explosive Force]
           RECOVERY_ADAPT[Recovery Adaptations<br/>Parasympathetic<br/>Restoration]
       end
       
       subgraph "Physiological Systems"
           METABOLIC[Metabolic System<br/>Energy Production]
           NEURAL[Neural System<br/>Motor Control]
           STRUCTURAL[Structural System<br/>Tissue Adaptation]
           HORMONAL[Hormonal System<br/>Anabolic Response]
       end
       
       MON --> HYPER_ADAPT
       WED --> STRENGTH_ADAPT
       FRI --> POWER_ADAPT
       SUN --> RECOVERY_ADAPT
       
       HYPER_ADAPT --> METABOLIC
       HYPER_ADAPT --> STRUCTURAL
       STRENGTH_ADAPT --> NEURAL
       STRENGTH_ADAPT --> HORMONAL
       POWER_ADAPT --> NEURAL
       POWER_ADAPT --> METABOLIC
       RECOVERY_ADAPT --> HORMONAL
       RECOVERY_ADAPT --> NEURAL
       
       %% Weekly progression
       SUN -.->|"Next Week"| MON
       
       %% Training parameters
       MON -.->|"Volume: High<br/>Intensity: Moderate"| MON
       WED -.->|"Volume: Low<br/>Intensity: High"| WED
       FRI -.->|"Volume: Moderate<br/>Intensity: Variable"| FRI
       SUN -.->|"Volume: Low<br/>Intensity: Low"| SUN
       
       %% Styling
       classDef training fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef adaptations fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef systems fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef recovery fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class MON,WED,FRI training
       class HYPER_ADAPT,STRENGTH_ADAPT,POWER_ADAPT adaptations
       class METABOLIC,NEURAL,STRUCTURAL,HORMONAL systems
       class SUN,RECOVERY_ADAPT recovery

Block Periodization Algorithm
-----------------------------

Block periodization concentrates training loads to maximize specific adaptations:

.. mermaid::

   sequenceDiagram
       participant Block1 as Accumulation Block<br/>(4-6 weeks)
       participant Block2 as Intensification Block<br/>(2-4 weeks)
       participant Block3 as Realization Block<br/>(1-2 weeks)
       participant Recovery as Recovery Block<br/>(1 week)
       
       Note over Block1,Recovery: Block Periodization Sequence
       
       Block1->>Block1: High Volume Training<br/>70-85% 1RM<br/>15-25 sets/muscle
       Block1->>Block1: Hypertrophy Focus<br/>Metabolic Stress<br/>Work Capacity
       
       Block1->>Block2: Transition Phase<br/>Reduce Volume<br/>Increase Intensity
       
       Block2->>Block2: High Intensity Training<br/>85-95% 1RM<br/>8-12 sets/muscle
       Block2->>Block2: Strength Focus<br/>Neural Adaptations<br/>Force Production
       
       Block2->>Block3: Peak Phase<br/>Maintain Intensity<br/>Minimize Volume
       
       Block3->>Block3: Competition Preparation<br/>90-100% 1RM<br/>3-6 sets/muscle
       Block3->>Block3: Performance Focus<br/>Skill Refinement<br/>Peak Expression
       
       Block3->>Recovery: Planned Recovery<br/>Active Rest<br/>Regeneration
       
       Recovery->>Recovery: Deload Training<br/>40-60% 1RM<br/>Reduced Volume
       Recovery->>Recovery: Recovery Focus<br/>Tissue Repair<br/>Adaptation
       
       Recovery->>Block1: New Cycle<br/>Progressive Overload<br/>Higher Baseline
       
       Note over Block1,Recovery: Residual Training Effects
       
       Block1-->>Block2: Hypertrophy Residuals<br/>2-4 weeks
       Block2-->>Block3: Strength Residuals<br/>2-3 weeks
       Block3-->>Recovery: Power Residuals<br/>1-2 weeks

Autoregulation Algorithm
------------------------

Autoregulation adjusts training based on real-time performance and recovery markers:

.. mermaid::

   graph TB
       subgraph "Input Variables"
           RPE[Rate of Perceived Exertion<br/>1-10 Scale]
           VELOCITY[Bar Velocity<br/>m/s]
           HRV[Heart Rate Variability<br/>RMSSD]
           SLEEP[Sleep Quality<br/>Duration/Efficiency]
           STRESS[Stress Levels<br/>Subjective Rating]
       end
       
       subgraph "Algorithm Processing"
           READINESS[Training Readiness Score<br/>Weighted Algorithm]
           ADJUSTMENT[Training Adjustment<br/>Volume/Intensity Modification]
           PRESCRIPTION[Session Prescription<br/>Individualized Program]
       end
       
       subgraph "Training Modifications"
           INCREASE[Increase Load<br/>+10-20% Volume<br/>+5-10% Intensity]
           MAINTAIN[Maintain Load<br/>Planned Training<br/>No Modification]
           DECREASE[Decrease Load<br/>-20-40% Volume<br/>-10-20% Intensity]
           SKIP[Skip Session<br/>Complete Rest<br/>Recovery Focus]
       end
       
       subgraph "Readiness Zones"
           HIGH[High Readiness<br/>Score: 8-10<br/>Green Zone]
           MODERATE[Moderate Readiness<br/>Score: 5-7<br/>Yellow Zone]
           LOW[Low Readiness<br/>Score: 2-4<br/>Orange Zone]
           VERY_LOW[Very Low Readiness<br/>Score: 0-1<br/>Red Zone]
       end
       
       RPE --> READINESS
       VELOCITY --> READINESS
       HRV --> READINESS
       SLEEP --> READINESS
       STRESS --> READINESS
       
       READINESS --> ADJUSTMENT
       ADJUSTMENT --> PRESCRIPTION
       
       READINESS --> HIGH
       READINESS --> MODERATE
       READINESS --> LOW
       READINESS --> VERY_LOW
       
       HIGH --> INCREASE
       MODERATE --> MAINTAIN
       LOW --> DECREASE
       VERY_LOW --> SKIP
       
       %% Feedback loop
       PRESCRIPTION -.->|"Performance Data"| RPE
       PRESCRIPTION -.->|"Velocity Feedback"| VELOCITY
       
       %% Algorithm weights
       RPE -.->|"Weight: 25%"| READINESS
       VELOCITY -.->|"Weight: 20%"| READINESS
       HRV -.->|"Weight: 20%"| READINESS
       SLEEP -.->|"Weight: 20%"| READINESS
       STRESS -.->|"Weight: 15%"| READINESS
       
       %% Styling
       classDef inputs fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef processing fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       classDef high fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef moderate fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef low fill:#e67e22,stroke:#d35400,stroke-width:2px,color:#fff
       classDef very_low fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       
       class RPE,VELOCITY,HRV,SLEEP,STRESS inputs
       class READINESS,ADJUSTMENT,PRESCRIPTION processing
       class HIGH,INCREASE high
       class MODERATE,MAINTAIN moderate
       class LOW,DECREASE low
       class VERY_LOW,SKIP very_low

Fatigue Management Algorithm
----------------------------

Systematic fatigue management prevents overreaching and optimizes recovery:

.. mermaid::

   graph LR
       subgraph "Fatigue Monitoring"
           PERFORMANCE[Performance Decline<br/>>5% drop]
           SUBJECTIVE[Subjective Fatigue<br/>RPE >8 for easy sets]
           PHYSIOLOGICAL[Physiological Markers<br/>Elevated RHR, Low HRV]
           PSYCHOLOGICAL[Psychological State<br/>Motivation, Mood]
       end
       
       subgraph "Fatigue Classification"
           FUNCTIONAL[Functional Overreaching<br/>1-3 weeks recovery]
           NON_FUNCTIONAL[Non-functional Overreaching<br/>Weeks to months]
           OVERTRAINING[Overtraining Syndrome<br/>Months to years]
           NORMAL[Normal Training Stress<br/>24-72 hours recovery]
       end
       
       subgraph "Intervention Strategies"
           DELOAD[Planned Deload<br/>40-60% volume reduction]
           ACTIVE_RECOVERY[Active Recovery<br/>Light movement, mobility]
           COMPLETE_REST[Complete Rest<br/>No training stimulus]
           MEDICAL[Medical Intervention<br/>Professional assessment]
       end
       
       PERFORMANCE --> FUNCTIONAL
       SUBJECTIVE --> FUNCTIONAL
       PHYSIOLOGICAL --> NON_FUNCTIONAL
       PSYCHOLOGICAL --> OVERTRAINING
       
       FUNCTIONAL --> DELOAD
       NON_FUNCTIONAL --> ACTIVE_RECOVERY
       OVERTRAINING --> COMPLETE_REST
       NORMAL --> DELOAD
       
       %% Recovery timeline
       DELOAD -.->|"5-10 days"| NORMAL
       ACTIVE_RECOVERY -.->|"1-3 weeks"| NORMAL
       COMPLETE_REST -.->|"2-8 weeks"| NORMAL
       MEDICAL -.->|"Variable"| NORMAL
       
       %% Prevention strategies
       NORMAL -.->|"Monitor Load"| PERFORMANCE
       NORMAL -.->|"Track RPE"| SUBJECTIVE
       NORMAL -.->|"HRV Monitoring"| PHYSIOLOGICAL
       NORMAL -.->|"Wellness Surveys"| PSYCHOLOGICAL
       
       %% Styling
       classDef monitoring fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef normal fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef functional fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef non_functional fill:#e67e22,stroke:#d35400,stroke-width:2px,color:#fff
       classDef overtraining fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef interventions fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       
       class PERFORMANCE,SUBJECTIVE,PHYSIOLOGICAL,PSYCHOLOGICAL monitoring
       class NORMAL normal
       class FUNCTIONAL functional
       class NON_FUNCTIONAL non_functional
       class OVERTRAINING overtraining
       class DELOAD,ACTIVE_RECOVERY,COMPLETE_REST,MEDICAL interventions
