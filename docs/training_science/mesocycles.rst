Mesocycle Structure and Progression
===================================

This document explains the mesocycle algorithms and structures used in the RP Training API to optimize medium-term training adaptations and progression.

Mesocycle Overview
------------------

A mesocycle is a training block lasting 3-6 weeks that focuses on specific adaptations through systematic progression and planned recovery.

.. mermaid::

   graph TB
       subgraph "Mesocycle Structure"
           WEEK1[Week 1<br/>Adaptation Phase<br/>Moderate Stress]
           WEEK2[Week 2<br/>Progression Phase<br/>Increased Stress]
           WEEK3[Week 3<br/>Intensification Phase<br/>Peak Stress]
           WEEK4[Week 4<br/>Deload Phase<br/>Recovery Stress]
       end
       
       subgraph "Training Variables"
           VOLUME[Volume Progression<br/>Sets × Reps]
           INTENSITY[Intensity Progression<br/>Load × %1RM]
           FREQUENCY[Frequency Adjustment<br/>Sessions/Week]
           EXERCISE[Exercise Selection<br/>Movement Patterns]
       end
       
       subgraph "Physiological Adaptations"
           NEURAL[Neural Adaptations<br/>Motor Learning<br/>Coordination]
           STRUCTURAL[Structural Adaptations<br/>Muscle Growth<br/>Connective Tissue]
           METABOLIC[Metabolic Adaptations<br/>Energy Systems<br/>Enzyme Activity]
           HORMONAL[Hormonal Adaptations<br/>Anabolic Response<br/>Recovery Hormones]
       end
       
       WEEK1 --> WEEK2
       WEEK2 --> WEEK3
       WEEK3 --> WEEK4
       WEEK4 -.->|"Next Mesocycle"| WEEK1
       
       WEEK1 --> VOLUME
       WEEK2 --> INTENSITY
       WEEK3 --> FREQUENCY
       WEEK4 --> EXERCISE
       
       VOLUME --> NEURAL
       INTENSITY --> STRUCTURAL
       FREQUENCY --> METABOLIC
       EXERCISE --> HORMONAL
       
       %% Progression indicators
       WEEK1 -.->|"70% Intensity<br/>100% Volume"| WEEK1
       WEEK2 -.->|"75% Intensity<br/>110% Volume"| WEEK2
       WEEK3 -.->|"80% Intensity<br/>120% Volume"| WEEK3
       WEEK4 -.->|"65% Intensity<br/>60% Volume"| WEEK4
       
       %% Styling
       classDef progression fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef variables fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef adaptations fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef deload fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class WEEK1,WEEK2,WEEK3 progression
       class VOLUME,INTENSITY,FREQUENCY,EXERCISE variables
       class NEURAL,STRUCTURAL,METABOLIC,HORMONAL adaptations
       class WEEK4 deload

Hypertrophy Mesocycle Algorithm
-------------------------------

Specialized mesocycle design for maximizing muscle hypertrophy:

.. mermaid::

   sequenceDiagram
       participant Week1 as Week 1<br/>Volume Accumulation
       participant Week2 as Week 2<br/>Volume Progression
       participant Week3 as Week 3<br/>Volume Peak
       participant Week4 as Week 4<br/>Deload Recovery
       participant Adaptation as Hypertrophy Adaptation
       
       Note over Week1,Adaptation: Hypertrophy Mesocycle Progression
       
       Week1->>Week1: 12-16 sets/muscle<br/>8-12 reps<br/>70-75% 1RM
       Week1->>Adaptation: Metabolic Stress<br/>Mechanical Tension<br/>Muscle Damage
       
       Week2->>Week2: 14-18 sets/muscle<br/>8-12 reps<br/>72-77% 1RM
       Week2->>Adaptation: Increased Protein Synthesis<br/>Satellite Cell Activation<br/>mTOR Signaling
       
       Week3->>Week3: 16-20 sets/muscle<br/>8-12 reps<br/>75-80% 1RM
       Week3->>Adaptation: Peak Anabolic Response<br/>Maximum Growth Stimulus<br/>Accumulated Fatigue
       
       Week3->>Week4: Planned Deload<br/>Fatigue Management<br/>Recovery Focus
       
       Week4->>Week4: 8-10 sets/muscle<br/>8-12 reps<br/>65-70% 1RM
       Week4->>Adaptation: Tissue Repair<br/>Supercompensation<br/>Growth Realization
       
       Adaptation->>Week1: Next Mesocycle<br/>Higher Baseline<br/>Progressive Overload
       
       Note over Week1,Adaptation: Key Hypertrophy Factors
       
       Week1-->>Adaptation: Time Under Tension: 40-70s
       Week2-->>Adaptation: Rest Periods: 60-90s
       Week3-->>Adaptation: Rep Tempo: 2-1-2-1
       Week4-->>Adaptation: Exercise Selection: Compound + Isolation

Strength Mesocycle Algorithm
----------------------------

Mesocycle structure optimized for maximal strength development:

.. mermaid::

   graph LR
       subgraph "Strength Mesocycle Phases"
           ACCUMULATION[Accumulation Phase<br/>Week 1-2<br/>Volume Focus]
           INTENSIFICATION[Intensification Phase<br/>Week 3-4<br/>Intensity Focus]
           REALIZATION[Realization Phase<br/>Week 5<br/>Peak Performance]
           RECOVERY[Recovery Phase<br/>Week 6<br/>Deload]
       end
       
       subgraph "Training Parameters"
           VOLUME_PARAM[Volume Parameters<br/>Sets: 3-6<br/>Reps: 1-6]
           INTENSITY_PARAM[Intensity Parameters<br/>Load: 80-100% 1RM<br/>RPE: 7-10]
           FREQUENCY_PARAM[Frequency Parameters<br/>Sessions: 3-6/week<br/>Movement: 2-3x/week]
           REST_PARAM[Rest Parameters<br/>Between Sets: 3-5min<br/>Between Sessions: 24-48h]
       end
       
       subgraph "Neural Adaptations"
           MOTOR_LEARNING[Motor Learning<br/>Skill Acquisition<br/>Movement Efficiency]
           RECRUITMENT[Motor Unit Recruitment<br/>High-Threshold Units<br/>Synchronization]
           RATE_CODING[Rate Coding<br/>Firing Frequency<br/>Force Production]
           COORDINATION[Intermuscular Coordination<br/>Synergist Activation<br/>Antagonist Inhibition]
       end
       
       ACCUMULATION --> INTENSIFICATION
       INTENSIFICATION --> REALIZATION
       REALIZATION --> RECOVERY
       RECOVERY -.->|"Next Cycle"| ACCUMULATION
       
       ACCUMULATION --> VOLUME_PARAM
       INTENSIFICATION --> INTENSITY_PARAM
       REALIZATION --> FREQUENCY_PARAM
       RECOVERY --> REST_PARAM
       
       VOLUME_PARAM --> MOTOR_LEARNING
       INTENSITY_PARAM --> RECRUITMENT
       FREQUENCY_PARAM --> RATE_CODING
       REST_PARAM --> COORDINATION
       
       %% Phase characteristics
       ACCUMULATION -.->|"85% 1RM<br/>4-6 reps<br/>4-5 sets"| ACCUMULATION
       INTENSIFICATION -.->|"90-95% 1RM<br/>2-4 reps<br/>3-4 sets"| INTENSIFICATION
       REALIZATION -.->|"95-100% 1RM<br/>1-3 reps<br/>2-3 sets"| REALIZATION
       RECOVERY -.->|"70-80% 1RM<br/>3-5 reps<br/>2-3 sets"| RECOVERY
       
       %% Styling
       classDef phases fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef parameters fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef adaptations fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef recovery fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class ACCUMULATION,INTENSIFICATION,REALIZATION phases
       class VOLUME_PARAM,INTENSITY_PARAM,FREQUENCY_PARAM parameters
       class MOTOR_LEARNING,RECRUITMENT,RATE_CODING,COORDINATION adaptations
       class RECOVERY,REST_PARAM recovery

Power Development Mesocycle
---------------------------

Mesocycle design for explosive power and rate of force development:

.. mermaid::

   graph TB
       subgraph "Power Mesocycle Structure"
           FOUNDATION[Foundation Phase<br/>Week 1-2<br/>Strength-Speed]
           DEVELOPMENT[Development Phase<br/>Week 3-4<br/>Speed-Strength]
           PEAK[Peak Phase<br/>Week 5<br/>Speed Dominant]
           MAINTENANCE[Maintenance Phase<br/>Week 6<br/>Active Recovery]
       end
       
       subgraph "Training Methods"
           HEAVY_SLOW[Heavy Slow Training<br/>80-90% 1RM<br/>Maximal Intent]
           MODERATE_FAST[Moderate Load Fast<br/>60-80% 1RM<br/>Maximal Velocity]
           LIGHT_EXPLOSIVE[Light Explosive<br/>30-60% 1RM<br/>Peak Power]
           PLYOMETRIC[Plyometric Training<br/>Bodyweight<br/>Reactive Strength]
       end
       
       subgraph "Power Qualities"
           STARTING_STRENGTH[Starting Strength<br/>0-30ms<br/>Initial Force]
           EXPLOSIVE_STRENGTH[Explosive Strength<br/>30-100ms<br/>RFD Peak]
           SPEED_STRENGTH[Speed-Strength<br/>100-250ms<br/>Dynamic Strength]
           REACTIVE_STRENGTH[Reactive Strength<br/>Ground Contact<br/>SSC Efficiency]
       end
       
       subgraph "Assessment Methods"
           JUMP_TEST[Jump Testing<br/>CMJ, SJ Height<br/>Power Output]
           VELOCITY_TEST[Velocity Testing<br/>Bar Speed<br/>Load-Velocity Profile]
           FORCE_PLATE[Force Plate Analysis<br/>RFD, Impulse<br/>Force-Time Curve]
           SPORT_SPECIFIC[Sport-Specific Tests<br/>Movement Patterns<br/>Transfer Assessment]
       end
       
       FOUNDATION --> DEVELOPMENT
       DEVELOPMENT --> PEAK
       PEAK --> MAINTENANCE
       MAINTENANCE -.->|"Next Cycle"| FOUNDATION
       
       FOUNDATION --> HEAVY_SLOW
       DEVELOPMENT --> MODERATE_FAST
       PEAK --> LIGHT_EXPLOSIVE
       MAINTENANCE --> PLYOMETRIC
       
       HEAVY_SLOW --> STARTING_STRENGTH
       MODERATE_FAST --> EXPLOSIVE_STRENGTH
       LIGHT_EXPLOSIVE --> SPEED_STRENGTH
       PLYOMETRIC --> REACTIVE_STRENGTH
       
       STARTING_STRENGTH --> JUMP_TEST
       EXPLOSIVE_STRENGTH --> VELOCITY_TEST
       SPEED_STRENGTH --> FORCE_PLATE
       REACTIVE_STRENGTH --> SPORT_SPECIFIC
       
       %% Training parameters
       FOUNDATION -.->|"3-5 reps<br/>3-5 sets<br/>3-5min rest"| FOUNDATION
       DEVELOPMENT -.->|"3-6 reps<br/>4-6 sets<br/>3-4min rest"| DEVELOPMENT
       PEAK -.->|"1-3 reps<br/>6-8 sets<br/>2-3min rest"| PEAK
       MAINTENANCE -.->|"5-10 reps<br/>3-4 sets<br/>Full recovery"| MAINTENANCE
       
       %% Styling
       classDef phases fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef methods fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef qualities fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef assessment fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       classDef maintenance fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class FOUNDATION,DEVELOPMENT,PEAK phases
       class HEAVY_SLOW,MODERATE_FAST,LIGHT_EXPLOSIVE methods
       class STARTING_STRENGTH,EXPLOSIVE_STRENGTH,SPEED_STRENGTH,REACTIVE_STRENGTH qualities
       class JUMP_TEST,VELOCITY_TEST,FORCE_PLATE,SPORT_SPECIFIC assessment
       class MAINTENANCE,PLYOMETRIC maintenance

Mesocycle Transition Algorithm
------------------------------

Algorithm for transitioning between different mesocycle types:

.. mermaid::

   graph LR
       subgraph "Mesocycle Sequence Planning"
           ASSESSMENT[Initial Assessment<br/>Goals, Experience<br/>Current Capacity]
           SELECTION[Mesocycle Selection<br/>Hypertrophy/Strength<br/>Power/Endurance]
           PROGRESSION[Progression Planning<br/>Volume/Intensity<br/>Complexity]
           TRANSITION[Transition Strategy<br/>Deload/Bridge<br/>Adaptation]
       end
       
       subgraph "Transition Types"
           DIRECT[Direct Transition<br/>Immediate Change<br/>Same Training Stress]
           BRIDGE[Bridge Week<br/>Gradual Transition<br/>Mixed Qualities]
           DELOAD[Deload Transition<br/>Recovery Focus<br/>Reduced Stress]
           TAPER[Taper Transition<br/>Peak Preparation<br/>Skill Refinement]
       end
       
       subgraph "Adaptation Considerations"
           RESIDUAL[Residual Effects<br/>Previous Training<br/>Decay Timeline]
           INTERFERENCE[Interference Effects<br/>Competing Adaptations<br/>Concurrent Training]
           READINESS[Training Readiness<br/>Recovery Status<br/>Motivation Level]
           PERIODIZATION[Periodization Goals<br/>Long-term Plan<br/>Competition Schedule]
       end
       
       ASSESSMENT --> SELECTION
       SELECTION --> PROGRESSION
       PROGRESSION --> TRANSITION
       
       TRANSITION --> DIRECT
       TRANSITION --> BRIDGE
       TRANSITION --> DELOAD
       TRANSITION --> TAPER
       
       DIRECT --> RESIDUAL
       BRIDGE --> INTERFERENCE
       DELOAD --> READINESS
       TAPER --> PERIODIZATION
       
       %% Decision matrix
       ASSESSMENT -.->|"Beginner:<br/>Hypertrophy Focus"| SELECTION
       ASSESSMENT -.->|"Intermediate:<br/>Strength Focus"| SELECTION
       ASSESSMENT -.->|"Advanced:<br/>Power Focus"| SELECTION
       
       %% Transition timing
       RESIDUAL -.->|"Hypertrophy: 2-4 weeks<br/>Strength: 2-3 weeks<br/>Power: 1-2 weeks"| RESIDUAL
       
       %% Styling
       classDef planning fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef transitions fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef considerations fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       
       class ASSESSMENT,SELECTION,PROGRESSION,TRANSITION planning
       class DIRECT,BRIDGE,DELOAD,TAPER transitions
       class RESIDUAL,INTERFERENCE,READINESS,PERIODIZATION considerations

Deload Week Algorithm
---------------------

Systematic approach to deload week implementation within mesocycles:

.. mermaid::

   sequenceDiagram
       participant Week3 as Week 3<br/>Peak Stress
       participant Assessment as Fatigue Assessment
       participant Deload as Deload Week
       participant Recovery as Recovery Monitoring
       participant Week1 as Week 1<br/>Next Mesocycle
       
       Note over Week3,Week1: Deload Implementation Process
       
       Week3->>Assessment: Evaluate Fatigue Markers
       Assessment->>Assessment: Performance Decline?<br/>Subjective Fatigue?<br/>Physiological Stress?
       
       alt High Fatigue
           Assessment->>Deload: Implement Full Deload<br/>40-50% Volume Reduction
           Deload->>Deload: Maintain Movement Patterns<br/>Reduce Training Stress<br/>Focus on Recovery
       else Moderate Fatigue
           Assessment->>Deload: Implement Partial Deload<br/>20-30% Volume Reduction
           Deload->>Deload: Maintain Some Intensity<br/>Reduce Volume Only<br/>Active Recovery
       else Low Fatigue
           Assessment->>Deload: Implement Mini Deload<br/>10-20% Volume Reduction
           Deload->>Deload: Maintain Training Stimulus<br/>Slight Reduction<br/>Skill Practice
       end
       
       Deload->>Recovery: Monitor Recovery Markers
       Recovery->>Recovery: Sleep Quality<br/>HRV Normalization<br/>Motivation Return
       
       Recovery->>Week1: Begin Next Mesocycle
       Week1->>Week1: Progressive Overload<br/>Higher Baseline<br/>Continued Adaptation
       
       Note over Week3,Week1: Deload Strategies
       
       Deload-->>Recovery: Volume Deload: Reduce sets/reps
       Deload-->>Recovery: Intensity Deload: Reduce load %
       Deload-->>Recovery: Frequency Deload: Reduce sessions
       Deload-->>Recovery: Complete Rest: No training
