Training Load Algorithms
========================

This document explains the comprehensive training load calculation algorithms used in the RP Training API to quantify, monitor, and optimize training stress.

Training Load Overview
----------------------

Training load represents the cumulative stress imposed on an athlete's physiological systems through training activities.

.. mermaid::

   graph TB
       subgraph "Training Load Components"
           EXTERNAL[External Load<br/>Objective Measures<br/>Volume, Intensity, Frequency]
           INTERNAL[Internal Load<br/>Physiological Response<br/>RPE, HR, Lactate]
           MECHANICAL[Mechanical Load<br/>Force, Power, Work<br/>Biomechanical Stress]
           METABOLIC[Metabolic Load<br/>Energy Expenditure<br/>Substrate Utilization]
       end
       
       subgraph "Load Calculations"
           ACUTE[Acute Load<br/>7-day Rolling Average<br/>Recent Training Stress]
           CHRONIC[Chronic Load<br/>28-day Rolling Average<br/>Fitness Baseline]
           RATIO[Acute:Chronic Ratio<br/>Training Stress Balance<br/>Injury Risk Indicator]
           MONOTONY[Training Monotony<br/>Load Variation<br/>Adaptation Stimulus]
       end
       
       subgraph "Load Metrics"
           VOLUME_LOAD[Volume Load<br/>Sets × Reps × Weight<br/>Total Work Done]
           RPE_LOAD[RPE Load<br/>RPE × Duration<br/>Perceived Stress]
           IMPULSE[Training Impulse<br/>HR × Duration<br/>Cardiovascular Stress]
           POWER_LOAD[Power Load<br/>Force × Velocity<br/>Neuromuscular Stress]
       end
       
       EXTERNAL --> ACUTE
       INTERNAL --> CHRONIC
       MECHANICAL --> RATIO
       METABOLIC --> MONOTONY
       
       ACUTE --> VOLUME_LOAD
       CHRONIC --> RPE_LOAD
       RATIO --> IMPULSE
       MONOTONY --> POWER_LOAD
       
       %% Load relationships
       VOLUME_LOAD --> ACUTE
       RPE_LOAD --> CHRONIC
       IMPULSE --> RATIO
       POWER_LOAD --> MONOTONY
       
       %% Styling
       classDef components fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef calculations fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef metrics fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       
       class EXTERNAL,INTERNAL,MECHANICAL,METABOLIC components
       class ACUTE,CHRONIC,RATIO,MONOTONY calculations
       class VOLUME_LOAD,RPE_LOAD,IMPULSE,POWER_LOAD metrics

Volume Load Calculation Algorithm
---------------------------------

Comprehensive algorithm for calculating training volume load:

.. mermaid::

   graph LR
       subgraph "Exercise Data Input"
           SETS[Number of Sets<br/>Per Exercise]
           REPS[Repetitions<br/>Per Set]
           WEIGHT[Load/Weight<br/>Per Rep]
           EXERCISES[Exercise Count<br/>Per Session]
       end
       
       subgraph "Calculation Steps"
           SET_VOLUME[Set Volume<br/>Reps × Weight]
           EXERCISE_VOLUME[Exercise Volume<br/>Sets × Set Volume]
           SESSION_VOLUME[Session Volume<br/>Sum of Exercise Volumes]
           WEEKLY_VOLUME[Weekly Volume<br/>Sum of Session Volumes]
       end
       
       subgraph "Volume Metrics"
           TOTAL_TONNAGE[Total Tonnage<br/>Sum of All Weights]
           RELATIVE_VOLUME[Relative Volume<br/>Volume per Body Weight]
           MUSCLE_VOLUME[Muscle Group Volume<br/>Sets per Muscle]
           INTENSITY_VOLUME[Intensity-Volume<br/>Load × Volume]
       end
       
       subgraph "Progressive Tracking"
           BASELINE[Baseline Volume<br/>Initial Capacity]
           PROGRESSION[Volume Progression<br/>Weekly Increase]
           PLATEAU[Volume Plateau<br/>Adaptation Limit]
           DELOAD[Volume Deload<br/>Recovery Period]
       end
       
       SETS --> SET_VOLUME
       REPS --> SET_VOLUME
       WEIGHT --> SET_VOLUME
       
       SET_VOLUME --> EXERCISE_VOLUME
       EXERCISE_VOLUME --> SESSION_VOLUME
       SESSION_VOLUME --> WEEKLY_VOLUME
       
       WEEKLY_VOLUME --> TOTAL_TONNAGE
       WEEKLY_VOLUME --> RELATIVE_VOLUME
       WEEKLY_VOLUME --> MUSCLE_VOLUME
       WEEKLY_VOLUME --> INTENSITY_VOLUME
       
       TOTAL_TONNAGE --> BASELINE
       RELATIVE_VOLUME --> PROGRESSION
       MUSCLE_VOLUME --> PLATEAU
       INTENSITY_VOLUME --> DELOAD
       
       %% Calculation formulas
       SET_VOLUME -.->|"Formula:<br/>Reps × Weight"| SET_VOLUME
       EXERCISE_VOLUME -.->|"Formula:<br/>Sets × Set Volume"| EXERCISE_VOLUME
       SESSION_VOLUME -.->|"Formula:<br/>Σ Exercise Volumes"| SESSION_VOLUME
       WEEKLY_VOLUME -.->|"Formula:<br/>Σ Session Volumes"| WEEKLY_VOLUME
       
       %% Styling
       classDef input fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef calculation fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef metrics fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef tracking fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class SETS,REPS,WEIGHT,EXERCISES input
       class SET_VOLUME,EXERCISE_VOLUME,SESSION_VOLUME,WEEKLY_VOLUME calculation
       class TOTAL_TONNAGE,RELATIVE_VOLUME,MUSCLE_VOLUME,INTENSITY_VOLUME metrics
       class BASELINE,PROGRESSION,PLATEAU,DELOAD tracking

RPE-Based Load Algorithm
------------------------

Algorithm for calculating training load using Rate of Perceived Exertion:

.. mermaid::

   sequenceDiagram
       participant Exercise as Exercise Execution
       participant RPE as RPE Assessment
       participant Duration as Duration Tracking
       participant Load as Load Calculation
       participant Analysis as Load Analysis
       
       Note over Exercise,Analysis: RPE Load Calculation Process
       
       Exercise->>RPE: Complete Set/Exercise
       RPE->>RPE: Assess Effort Level<br/>1-10 Scale<br/>Subjective Rating
       
       Exercise->>Duration: Track Time<br/>Set Duration<br/>Rest Periods
       Duration->>Duration: Calculate Total<br/>Work Time<br/>Session Duration
       
       RPE->>Load: RPE × Duration
       Duration->>Load: Session RPE Load
       Load->>Load: Daily RPE Load<br/>Sum of Sessions
       
       Load->>Analysis: Weekly RPE Load<br/>7-day Sum
       Analysis->>Analysis: Acute Load<br/>Rolling Average
       
       Analysis->>Analysis: Chronic Load<br/>28-day Average
       Analysis->>Analysis: A:C Ratio<br/>Load Management
       
       Note over Exercise,Analysis: RPE Load Zones
       
       RPE-->>Load: RPE 1-3: Very Light<br/>Recovery/Warm-up
       RPE-->>Load: RPE 4-6: Moderate<br/>Aerobic/Technique
       RPE-->>Load: RPE 7-8: Hard<br/>Threshold/Strength
       RPE-->>Load: RPE 9-10: Maximal<br/>Competition/Testing
       
       Note over Exercise,Analysis: Load Interpretation
       
       Analysis-->>Load: Low Load: <300 AU<br/>Light Training Day
       Analysis-->>Load: Moderate Load: 300-600 AU<br/>Standard Training
       Analysis-->>Load: High Load: 600-1000 AU<br/>Intense Training
       Analysis-->>Load: Very High Load: >1000 AU<br/>Competition/Testing

Acute:Chronic Workload Ratio
-----------------------------

Algorithm for calculating and interpreting the acute:chronic workload ratio:

.. mermaid::

   graph TB
       subgraph "Data Collection"
           DAILY[Daily Training Load<br/>Volume, RPE, Duration<br/>Objective + Subjective]
           WEEKLY[Weekly Load Sum<br/>7-day Total<br/>Recent Training Stress]
           ROLLING[Rolling Averages<br/>Exponentially Weighted<br/>Decay Function]
       end
       
       subgraph "Load Calculations"
           ACUTE_CALC[Acute Load<br/>7-day EWMA<br/>α = 0.3]
           CHRONIC_CALC[Chronic Load<br/>28-day EWMA<br/>α = 0.075]
           RATIO_CALC[A:C Ratio<br/>Acute ÷ Chronic<br/>Training Stress Balance]
       end
       
       subgraph "Risk Zones"
           LOW_RISK[Low Risk Zone<br/>0.8 - 1.3<br/>Optimal Training]
           MODERATE_RISK[Moderate Risk<br/>1.3 - 1.5<br/>Caution Zone]
           HIGH_RISK[High Risk Zone<br/>>1.5<br/>Injury Risk]
           DETRAINING[Detraining Zone<br/><0.8<br/>Insufficient Stimulus]
       end
       
       subgraph "Recommendations"
           MAINTAIN[Maintain Load<br/>Continue Current<br/>Training Plan]
           MONITOR[Monitor Closely<br/>Watch for Fatigue<br/>Adjust if Needed]
           REDUCE[Reduce Load<br/>Implement Deload<br/>Focus Recovery]
           INCREASE[Increase Load<br/>Progressive Overload<br/>Avoid Detraining]
       end
       
       DAILY --> WEEKLY
       WEEKLY --> ROLLING
       ROLLING --> ACUTE_CALC
       ROLLING --> CHRONIC_CALC
       
       ACUTE_CALC --> RATIO_CALC
       CHRONIC_CALC --> RATIO_CALC
       
       RATIO_CALC --> LOW_RISK
       RATIO_CALC --> MODERATE_RISK
       RATIO_CALC --> HIGH_RISK
       RATIO_CALC --> DETRAINING
       
       LOW_RISK --> MAINTAIN
       MODERATE_RISK --> MONITOR
       HIGH_RISK --> REDUCE
       DETRAINING --> INCREASE
       
       %% EWMA formulas
       ACUTE_CALC -.->|"EWMA Formula:<br/>α × Today + (1-α) × Yesterday"| ACUTE_CALC
       CHRONIC_CALC -.->|"EWMA Formula:<br/>α × Today + (1-α) × Yesterday"| CHRONIC_CALC
       
       %% Risk thresholds
       LOW_RISK -.->|"Sweet Spot:<br/>1.0 - 1.25"| LOW_RISK
       MODERATE_RISK -.->|"Caution:<br/>Monitor Fatigue"| MODERATE_RISK
       HIGH_RISK -.->|"Danger:<br/>High Injury Risk"| HIGH_RISK
       DETRAINING -.->|"Underload:<br/>Fitness Decline"| DETRAINING
       
       %% Styling
       classDef collection fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef calculation fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       classDef low_risk fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef moderate_risk fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef high_risk fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef recommendations fill:#95a5a6,stroke:#7f8c8d,stroke-width:2px,color:#fff
       
       class DAILY,WEEKLY,ROLLING collection
       class ACUTE_CALC,CHRONIC_CALC,RATIO_CALC calculation
       class LOW_RISK low_risk
       class MODERATE_RISK moderate_risk
       class HIGH_RISK,DETRAINING high_risk
       class MAINTAIN,MONITOR,REDUCE,INCREASE recommendations

Training Monotony and Strain
-----------------------------

Algorithm for calculating training monotony and strain to optimize training variation:

.. mermaid::

   graph LR
       subgraph "Weekly Load Data"
           DAY1[Day 1 Load<br/>Training Session<br/>Load Value]
           DAY2[Day 2 Load<br/>Training Session<br/>Load Value]
           DAY3[Day 3 Load<br/>Training Session<br/>Load Value]
           DAY7[Day 7 Load<br/>Training Session<br/>Load Value]
       end
       
       subgraph "Statistical Calculations"
           MEAN[Weekly Mean<br/>Average Daily Load<br/>Central Tendency]
           STDEV[Standard Deviation<br/>Load Variation<br/>Dispersion Measure]
           MONOTONY[Training Monotony<br/>Mean ÷ SD<br/>Variation Index]
           STRAIN[Training Strain<br/>Mean × Monotony<br/>Combined Stress]
       end
       
       subgraph "Interpretation Zones"
           LOW_MONOTONY[Low Monotony<br/><2.0<br/>High Variation]
           MODERATE_MONOTONY[Moderate Monotony<br/>2.0-3.0<br/>Balanced Variation]
           HIGH_MONOTONY[High Monotony<br/>>3.0<br/>Low Variation]
           OPTIMAL_STRAIN[Optimal Strain<br/>Balanced Load<br/>Effective Training]
       end
       
       subgraph "Programming Adjustments"
           INCREASE_VARIATION[Increase Variation<br/>Vary Intensity<br/>Change Exercises]
           MAINTAIN_PROGRAM[Maintain Program<br/>Current Variation<br/>Effective Balance]
           REDUCE_MONOTONY[Reduce Monotony<br/>Add Variety<br/>Different Stimuli]
           MANAGE_STRAIN[Manage Strain<br/>Load Distribution<br/>Recovery Planning]
       end
       
       DAY1 --> MEAN
       DAY2 --> MEAN
       DAY3 --> MEAN
       DAY7 --> MEAN
       
       DAY1 --> STDEV
       DAY2 --> STDEV
       DAY3 --> STDEV
       DAY7 --> STDEV
       
       MEAN --> MONOTONY
       STDEV --> MONOTONY
       MEAN --> STRAIN
       MONOTONY --> STRAIN
       
       MONOTONY --> LOW_MONOTONY
       MONOTONY --> MODERATE_MONOTONY
       MONOTONY --> HIGH_MONOTONY
       STRAIN --> OPTIMAL_STRAIN
       
       LOW_MONOTONY --> MAINTAIN_PROGRAM
       MODERATE_MONOTONY --> MAINTAIN_PROGRAM
       HIGH_MONOTONY --> INCREASE_VARIATION
       OPTIMAL_STRAIN --> MANAGE_STRAIN
       
       %% Calculation formulas
       MONOTONY -.->|"Formula:<br/>Mean ÷ Standard Deviation"| MONOTONY
       STRAIN -.->|"Formula:<br/>Mean × Monotony"| STRAIN
       
       %% Interpretation guidelines
       LOW_MONOTONY -.->|"Good Variation<br/>Diverse Training"| LOW_MONOTONY
       MODERATE_MONOTONY -.->|"Balanced Approach<br/>Optimal Zone"| MODERATE_MONOTONY
       HIGH_MONOTONY -.->|"Too Repetitive<br/>Risk of Staleness"| HIGH_MONOTONY
       
       %% Styling
       classDef data fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef calculations fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef low fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef moderate fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef high fill:#e67e22,stroke:#d35400,stroke-width:2px,color:#fff
       classDef adjustments fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       
       class DAY1,DAY2,DAY3,DAY7 data
       class MEAN,STDEV,MONOTONY,STRAIN calculations
       class LOW_MONOTONY,MAINTAIN_PROGRAM low
       class MODERATE_MONOTONY,OPTIMAL_STRAIN moderate
       class HIGH_MONOTONY high
       class INCREASE_VARIATION,REDUCE_MONOTONY,MANAGE_STRAIN adjustments

Load Progression Algorithm
--------------------------

Algorithm for systematic progression of training load over time:

.. mermaid::

   sequenceDiagram
       participant Baseline as Baseline Assessment
       participant Week1 as Week 1<br/>Initial Load
       participant Week2 as Week 2<br/>Progression
       participant Week3 as Week 3<br/>Peak Load
       participant Assessment as Load Assessment
       participant Adjustment as Load Adjustment
       
       Note over Baseline,Adjustment: Load Progression Cycle
       
       Baseline->>Week1: Establish Baseline<br/>Current Capacity<br/>Starting Point
       Week1->>Week1: 100% Baseline Load<br/>Adaptation Phase<br/>Movement Quality
       
       Week1->>Week2: Progress Load<br/>+10-20% Increase<br/>Systematic Overload
       Week2->>Week2: 110-120% Load<br/>Progression Phase<br/>Adaptation Stress
       
       Week2->>Week3: Continue Progression<br/>+5-15% Increase<br/>Peak Stimulus
       Week3->>Week3: 115-135% Load<br/>Peak Phase<br/>Maximum Stress
       
       Week3->>Assessment: Evaluate Response<br/>Performance Metrics<br/>Fatigue Markers
       
       alt Positive Adaptation
           Assessment->>Adjustment: Continue Progression<br/>Maintain Rate<br/>+10-15% Next Week
       else Plateau Response
           Assessment->>Adjustment: Modify Variables<br/>Change Stimulus<br/>Vary Progression
       else Negative Response
           Assessment->>Adjustment: Implement Deload<br/>Reduce Load<br/>-20-40% Volume
       end
       
       Adjustment->>Week1: New Cycle<br/>Adjusted Baseline<br/>Progressive Overload
       
       Note over Baseline,Adjustment: Progression Guidelines
       
       Week1-->>Assessment: Beginner: +15-25% per week
       Week2-->>Assessment: Intermediate: +10-15% per week
       Week3-->>Assessment: Advanced: +5-10% per week
       Assessment-->>Adjustment: Individual Response Varies
