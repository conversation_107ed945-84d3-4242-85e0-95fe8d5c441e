/* Custom CSS for Forge Protocol Documentation */

/* Brand colors */
:root {
    --forge-primary: #ff6b6b;
    --forge-secondary: #4ecdc4;
    --forge-accent: #ffe66d;
    --forge-dark: #2c3e50;
    --forge-light: #ecf0f1;
}

/* Header customization */
.wy-side-nav-search {
    background-color: var(--forge-primary) !important;
}

.wy-side-nav-search input[type=text] {
    border-color: var(--forge-secondary);
}

/* Navigation styling */
.wy-menu-vertical a {
    color: var(--forge-dark);
}

.wy-menu-vertical a:hover {
    background-color: var(--forge-light);
    color: var(--forge-primary);
}

.wy-menu-vertical li.current a {
    color: var(--forge-primary);
    border-right: 3px solid var(--forge-primary);
}

/* Content area */
.wy-nav-content {
    max-width: 1200px;
}

/* Code blocks */
.highlight {
    background-color: #f8f9fa;
    border-left: 4px solid var(--forge-secondary);
    padding: 1rem;
    margin: 1rem 0;
}

/* Mermaid diagrams */
.mermaid {
    text-align: center;
    margin: 2rem 0;
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal;
}

.rst-content table.docutils {
    border: 1px solid var(--forge-light);
}

.rst-content table.docutils th {
    background-color: var(--forge-primary);
    color: white;
}

/* Admonitions */
.rst-content .admonition.note {
    background-color: #e3f2fd;
    border-left: 4px solid var(--forge-secondary);
}

.rst-content .admonition.warning {
    background-color: #fff3e0;
    border-left: 4px solid var(--forge-accent);
}

.rst-content .admonition.danger {
    background-color: #ffebee;
    border-left: 4px solid var(--forge-primary);
}

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
    
    .wy-nav-side {
        left: -300px;
    }
    
    .wy-nav-side.shift {
        left: 0;
    }
}

/* Performance indicators */
.performance-metric {
    display: inline-block;
    background-color: var(--forge-secondary);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.9rem;
    margin: 0.2rem;
}

/* Architecture diagrams */
.architecture-diagram {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

/* API endpoint styling */
.api-endpoint {
    background-color: #f8f9fa;
    border-left: 4px solid var(--forge-primary);
    padding: 1rem;
    margin: 1rem 0;
    font-family: monospace;
}

.api-method {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    color: white;
    font-weight: bold;
    margin-right: 0.5rem;
}

.api-method.get { background-color: #4caf50; }
.api-method.post { background-color: #2196f3; }
.api-method.put { background-color: #ff9800; }
.api-method.delete { background-color: #f44336; }

/* Database schema styling */
.schema-table {
    margin: 1rem 0;
    border-collapse: collapse;
    width: 100%;
}

.schema-table th,
.schema-table td {
    border: 1px solid var(--forge-light);
    padding: 0.5rem;
    text-align: left;
}

.schema-table th {
    background-color: var(--forge-primary);
    color: white;
}

/* Test coverage indicators */
.coverage-badge {
    display: inline-block;
    background-color: #4caf50;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin: 0.2rem;
}

.coverage-badge.low { background-color: #f44336; }
.coverage-badge.medium { background-color: #ff9800; }
.coverage-badge.high { background-color: #4caf50; }

/* Mobile-first indicators */
.mobile-optimized {
    display: inline-block;
    background-color: var(--forge-secondary);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    margin: 0.2rem;
}

.mobile-optimized::before {
    content: "📱 ";
}

/* Security indicators */
.security-feature {
    display: inline-block;
    background-color: var(--forge-dark);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    margin: 0.2rem;
}

.security-feature::before {
    content: "🔒 ";
}
