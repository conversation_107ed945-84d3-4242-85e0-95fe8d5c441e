# Documentation dependencies for RP Training API
# Install with: pip install -r docs/requirements.txt

# Core Sphinx
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
sphinx-autobuild>=2021.3.14

# Markdown support
myst-parser>=2.0.0

# API documentation
sphinx-autodoc-typehints>=1.24.0

# Additional Sphinx extensions
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.1
sphinxcontrib-mermaid>=0.9.2

# Documentation quality
doc8>=1.1.1
rstcheck>=6.1.0

# Development tools
livereload>=2.6.3
