API Endpoints
=============

This section provides comprehensive documentation for all RP Training API endpoints, including request/response formats, authentication requirements, and usage examples.

Base URL
--------

All API endpoints are prefixed with the base URL:

- **Development**: ``http://localhost:8000``
- **Production**: ``https://api.rptraining.com`` (when deployed)

All endpoints use the ``/api/v1`` prefix for versioning.

Authentication Endpoints
------------------------

Authentication Flow Overview
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The authentication system follows a secure JWT-based workflow:

.. mermaid::

   sequenceDiagram
       participant Client
       participant API as FastAPI
       participant UseCase as Register Use Case
       participant Domain as User Entity
       participant Auth as Auth Service
       participant Repo as User Repository
       participant DB as Database

       Note over Client,DB: User Registration Flow

       Client->>API: POST /auth/register
       API->>API: Validate Request Schema
       API->>UseCase: Execute Registration
       UseCase->>Repo: Check Email Exists
       Repo->>DB: Query User by Email
       DB-->>Repo: No User Found
       Repo-->>UseCase: Email Available
       UseCase->>Domain: Create User Entity
       Domain->>Auth: Hash Password
       Auth-->>Domain: Hashed Password
       Domain-->>UseCase: User Entity Created
       UseCase->>Repo: Save User
       Repo->>DB: Insert User Record
       DB-->>Repo: User Saved
       Repo-->>UseCase: User Persisted
       UseCase->>Auth: Generate JWT Tokens
       Auth-->>UseCase: Access & Refresh Tokens
       UseCase-->>API: Registration Response
       API-->>Client: 201 Created + Tokens

User Registration
~~~~~~~~~~~~~~~~~

Register a new user account.

.. http:post:: /api/v1/auth/register

   Register a new user with email and password.

   **Request Body:**

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "password": "SecurePassword123!",
        "first_name": "John",
        "last_name": "Doe",
        "training_experience_years": 2
      }

   **Response (201 Created):**

   .. code-block:: json

      {
        "user": {
          "id": "uuid-string",
          "email": "<EMAIL>",
          "first_name": "John",
          "last_name": "Doe",
          "training_experience_years": 2,
          "is_active": true,
          "is_verified": false,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        },
        "access_token": "jwt-access-token",
        "refresh_token": "jwt-refresh-token",
        "token_type": "bearer"
      }

   **Error Responses:**

   - ``400 Bad Request``: Invalid input data
   - ``409 Conflict``: Email already exists
   - ``422 Unprocessable Entity``: Validation errors

   **Example Request:**

   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/auth/register" \
           -H "Content-Type: application/json" \
           -d '{
             "email": "<EMAIL>",
             "password": "SecurePassword123!",
             "first_name": "John",
             "last_name": "Doe",
             "training_experience_years": 2
           }'

User Login
~~~~~~~~~~

Authenticate a user and receive access tokens.

**Login Sequence Flow:**

.. mermaid::

   sequenceDiagram
       participant Client
       participant API as FastAPI
       participant UseCase as Login Use Case
       participant Auth as Auth Service
       participant Repo as User Repository
       participant DB as Database

       Note over Client,DB: User Login Flow

       Client->>API: POST /auth/login
       API->>API: Validate Credentials Schema
       API->>UseCase: Execute Authentication
       UseCase->>Repo: Find User by Email
       Repo->>DB: Query User Record
       DB-->>Repo: User Record Found
       Repo-->>UseCase: User Entity
       UseCase->>Auth: Verify Password
       Auth->>Auth: Compare Hashed Password
       Auth-->>UseCase: Password Valid
       UseCase->>Auth: Generate JWT Tokens
       Auth-->>UseCase: Access & Refresh Tokens
       UseCase-->>API: Authentication Response
       API-->>Client: 200 OK + Tokens

       Note over Client,DB: Invalid Credentials Flow

       Client->>API: POST /auth/login (invalid)
       API->>UseCase: Execute Authentication
       UseCase->>Repo: Find User by Email
       Repo->>DB: Query User Record
       alt User Not Found
           DB-->>Repo: No User Record
           Repo-->>UseCase: User Not Found
           UseCase-->>API: Invalid Credentials Error
       else Password Invalid
           DB-->>Repo: User Record Found
           Repo-->>UseCase: User Entity
           UseCase->>Auth: Verify Password
           Auth-->>UseCase: Password Invalid
           UseCase-->>API: Invalid Credentials Error
       end
       API-->>Client: 401 Unauthorized

.. http:post:: /api/v1/auth/login

   Authenticate user credentials and return JWT tokens.

   **Request Body:**

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "password": "SecurePassword123!"
      }

   **Response (200 OK):**

   .. code-block:: json

      {
        "user": {
          "id": "uuid-string",
          "email": "<EMAIL>",
          "first_name": "John",
          "last_name": "Doe",
          "training_experience_years": 2,
          "is_active": true,
          "is_verified": false,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z"
        },
        "access_token": "jwt-access-token",
        "refresh_token": "jwt-refresh-token",
        "token_type": "bearer"
      }

   **Error Responses:**

   - ``401 Unauthorized``: Invalid credentials or inactive user
   - ``422 Unprocessable Entity``: Validation errors

   **Example Request:**

   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/auth/login" \
           -H "Content-Type: application/json" \
           -d '{
             "email": "<EMAIL>",
             "password": "SecurePassword123!"
           }'

User Profile Endpoints
----------------------

Profile Management Flow
~~~~~~~~~~~~~~~~~~~~~~~

The profile management system handles authenticated user operations:

.. mermaid::

   sequenceDiagram
       participant Client
       participant API as FastAPI
       participant Auth as JWT Middleware
       participant UseCase as Profile Use Case
       participant Repo as User Repository
       participant DB as Database

       Note over Client,DB: Get Profile Flow

       Client->>API: GET /auth/me + Bearer Token
       API->>Auth: Validate JWT Token
       Auth->>Auth: Decode & Verify Token
       Auth-->>API: User ID from Token
       API->>UseCase: Get User Profile
       UseCase->>Repo: Find User by ID
       Repo->>DB: Query User Record
       DB-->>Repo: User Record
       Repo-->>UseCase: User Entity
       UseCase-->>API: User Profile Data
       API-->>Client: 200 OK + Profile

       Note over Client,DB: Update Profile Flow

       Client->>API: PUT /auth/me + Bearer Token + Data
       API->>Auth: Validate JWT Token
       Auth-->>API: User ID from Token
       API->>API: Validate Update Schema
       API->>UseCase: Update User Profile
       UseCase->>Repo: Find User by ID
       Repo->>DB: Query User Record
       DB-->>Repo: Current User Record
       Repo-->>UseCase: User Entity
       UseCase->>UseCase: Apply Updates to Entity
       UseCase->>Repo: Save Updated User
       Repo->>DB: Update User Record
       DB-->>Repo: Updated Record
       Repo-->>UseCase: Updated User Entity
       UseCase-->>API: Updated Profile Data
       API-->>Client: 200 OK + Updated Profile

Get Current User Profile
~~~~~~~~~~~~~~~~~~~~~~~~~

Retrieve the authenticated user's profile information.

.. http:get:: /api/v1/auth/me

   Get the current user's profile data.

   **Authentication Required:** Yes

   **Headers:**

   .. code-block:: http

      Authorization: Bearer <access_token>

   **Response (200 OK):**

   .. code-block:: json

      {
        "id": "uuid-string",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "training_experience_years": 2,
        "is_active": true,
        "is_verified": false,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }

   **Error Responses:**

   - ``401 Unauthorized``: Invalid or missing token
   - ``404 Not Found``: User not found

   **Example Request:**

   .. code-block:: bash

      curl -X GET "http://localhost:8000/api/v1/auth/me" \
           -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

Update User Profile
~~~~~~~~~~~~~~~~~~~

Update the authenticated user's profile information.

.. http:put:: /api/v1/auth/me

   Update user profile with new information.

   **Authentication Required:** Yes

   **Headers:**

   .. code-block:: http

      Authorization: Bearer <access_token>
      Content-Type: application/json

   **Request Body (all fields optional):**

   .. code-block:: json

      {
        "first_name": "Jane",
        "last_name": "Smith",
        "training_experience_years": 5
      }

   **Response (200 OK):**

   .. code-block:: json

      {
        "id": "uuid-string",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "training_experience_years": 5,
        "is_active": true,
        "is_verified": false,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }

   **Error Responses:**

   - ``401 Unauthorized``: Invalid or missing token
   - ``404 Not Found``: User not found
   - ``422 Unprocessable Entity``: Validation errors

   **Example Request:**

   .. code-block:: bash

      curl -X PUT "http://localhost:8000/api/v1/auth/me" \
           -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
           -H "Content-Type: application/json" \
           -d '{
             "first_name": "Jane",
             "training_experience_years": 5
           }'

Health Check Endpoints
----------------------

Basic Health Check
~~~~~~~~~~~~~~~~~~

Get basic API health status.

.. http:get:: /api/v1/health

   Check if the API is running and responsive.

   **Authentication Required:** No

   **Response (200 OK):**

   .. code-block:: json

      {
        "status": "healthy",
        "service": "RP Training API",
        "version": "0.1.0",
        "timestamp": "2024-01-01T12:00:00Z"
      }

   **Example Request:**

   .. code-block:: bash

      curl -X GET "http://localhost:8000/api/v1/health"

Detailed Health Check
~~~~~~~~~~~~~~~~~~~~~

Get comprehensive system health information.

.. http:get:: /api/v1/health/detailed

   Get detailed health status including system components.

   **Authentication Required:** No

   **Response (200 OK):**

   .. code-block:: json

      {
        "status": "healthy",
        "service": "RP Training API",
        "version": "0.1.0",
        "timestamp": "2024-01-01T12:00:00Z",
        "checks": {
          "database": {
            "status": "healthy",
            "details": {
              "connected": true,
              "response_time_ms": 15.2
            }
          },
          "memory": {
            "status": "healthy",
            "details": {
              "usage_percent": 45.2
            }
          },
          "disk": {
            "status": "healthy",
            "details": {
              "usage_percent": 62.1
            }
          }
        }
      }

   **Possible Status Values:**

   - ``healthy``: All systems operating normally
   - ``degraded``: Some components experiencing issues
   - ``unhealthy``: Critical systems failing

   **Example Request:**

   .. code-block:: bash

      curl -X GET "http://localhost:8000/api/v1/health/detailed"

Database Health Check
~~~~~~~~~~~~~~~~~~~~~

Get database-specific health information.

.. http:get:: /api/v1/health/database

   Check database connectivity and performance.

   **Authentication Required:** No

   **Response (200 OK):**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2024-01-01T12:00:00Z",
        "database": {
          "connected": true,
          "response_time_ms": 12.5
        }
      }

   **Error Response (when database is down):**

   .. code-block:: json

      {
        "status": "unhealthy",
        "timestamp": "2024-01-01T12:00:00Z",
        "database": {
          "connected": false,
          "error": "Connection timeout"
        }
      }

   **Example Request:**

   .. code-block:: bash

      curl -X GET "http://localhost:8000/api/v1/health/database"

Request/Response Formats
------------------------

Content Types
~~~~~~~~~~~~~

All API endpoints use JSON for request and response bodies:

- **Request Content-Type**: ``application/json``
- **Response Content-Type**: ``application/json``

Authentication
~~~~~~~~~~~~~~

Most endpoints require authentication using JWT Bearer tokens:

.. code-block:: http

   Authorization: Bearer <access_token>

Timestamps
~~~~~~~~~~

All timestamps are in ISO 8601 format with UTC timezone:

.. code-block:: json

   {
     "created_at": "2024-01-01T12:00:00Z",
     "updated_at": "2024-01-01T12:30:00Z"
   }

Error Responses
~~~~~~~~~~~~~~~

All error responses follow a consistent format:

.. code-block:: json

   {
     "detail": "Human-readable error message",
     "type": "error_type",
     "code": "ERROR_CODE"
   }

Rate Limiting
~~~~~~~~~~~~~

The API implements rate limiting to ensure fair usage:

- **Authentication endpoints**: 5 requests per minute per IP
- **Profile endpoints**: 60 requests per minute per user
- **Health endpoints**: 100 requests per minute per IP

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 60
   X-RateLimit-Remaining: 59
   X-RateLimit-Reset: **********

Pagination
~~~~~~~~~~

For endpoints that return lists (future implementation):

.. code-block:: json

   {
     "items": [...],
     "total": 100,
     "page": 1,
     "size": 20,
     "pages": 5
   }

Query parameters for pagination:

- ``page``: Page number (default: 1)
- ``size``: Items per page (default: 20, max: 100)

Status Codes
------------

The API uses standard HTTP status codes:

**Success Codes:**

- ``200 OK``: Request successful
- ``201 Created``: Resource created successfully
- ``204 No Content``: Request successful, no content returned

**Client Error Codes:**

- ``400 Bad Request``: Invalid request format
- ``401 Unauthorized``: Authentication required
- ``403 Forbidden``: Access denied
- ``404 Not Found``: Resource not found
- ``409 Conflict``: Resource conflict (e.g., email already exists)
- ``422 Unprocessable Entity``: Validation errors
- ``429 Too Many Requests``: Rate limit exceeded

**Server Error Codes:**

- ``500 Internal Server Error``: Unexpected server error
- ``502 Bad Gateway``: Upstream service error
- ``503 Service Unavailable``: Service temporarily unavailable
