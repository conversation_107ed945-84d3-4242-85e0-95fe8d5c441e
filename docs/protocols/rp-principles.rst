Renaissance Periodisation Principles
====================================

This section details the core Renaissance Periodisation (RP) principles that form the scientific foundation of Forge Protocol.

Overview
--------

Renaissance Periodisation represents a systematic, evidence-based approach to hypertrophy training developed by Dr. <PERSON> and the RP team. The methodology emphasises precise volume management, intelligent exercise selection, and strategic periodisation to optimise muscle growth whilst minimising injury risk and overtraining.

🎯 Core Principles
------------------

Volume Landmarks
~~~~~~~~~~~~~~~~

The foundation of RP methodology centres on understanding and applying volume landmarks for each muscle group:

**Maintenance Volume (MV)**
   The minimum training volume required to maintain current muscle mass and strength.

**Minimum Effective Volume (MEV)**
   The lowest training volume that produces measurable hypertrophy adaptations.

**Maximum Adaptive Volume (MAV)**
   The training volume that produces the greatest rate of hypertrophy adaptations.

**Maximum Recoverable Volume (MRV)**
   The highest training volume from which an individual can recover within their normal recovery timeframe.

Stimulus-to-Fatigue Ratio (SFR)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The SFR concept evaluates exercise effectiveness by comparing the hypertrophic stimulus generated to the systemic fatigue cost incurred.

**High SFR Exercises**
   * Generate significant muscle activation with minimal systemic fatigue
   * Allow for higher training frequencies and volumes
   * Examples: Isolation exercises, machine-based movements

**Moderate SFR Exercises**
   * Provide good muscle stimulus with moderate fatigue cost
   * Form the backbone of most training programmes
   * Examples: Compound movements with moderate loading

**Low SFR Exercises**
   * Generate high systemic fatigue relative to muscle-specific stimulus
   * Used strategically for strength and movement quality
   * Examples: Heavy compound lifts, high-skill movements

Progressive Overload Strategies
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

RP emphasises multiple vectors of progressive overload:

**Volume Progression**
   * Primary driver of hypertrophy adaptations
   * Systematic increase in weekly training volume
   * Progression rate: 1-3 sets per muscle group per week

**Intensity Progression**
   * Secondary driver through load increases
   * Maintained within hypertrophy-specific rep ranges
   * Progression rate: 2.5-5% load increases when rep targets exceeded

**Frequency Progression**
   * Increased training frequency as volume requirements grow
   * Allows for higher weekly volumes whilst managing session fatigue
   * Progression: From 1x to 3x per week per muscle group

**Range of Motion Progression**
   * Emphasis on full range of motion for maximum stimulus
   * Progressive lengthening of range where possible
   * Focus on stretched position emphasis

Periodisation Framework
~~~~~~~~~~~~~~~~~~~~~~~

**Mesocycle Structure**
   * 4-8 week training blocks with specific volume progression
   * Systematic increase from MEV towards MAV/MRV
   * Planned deload every 4-8 weeks based on fatigue accumulation

**Volume Progression Pattern**
   * Week 1: Start near MEV (conservative beginning)
   * Week 2-4: Progressive volume increases
   * Week 5-6: Approach MAV/MRV (peak volume)
   * Week 7: Deload to MV (recovery and adaptation)

**Deload Strategies**
   * Volume reduction: 40-60% of peak volume
   * Intensity maintenance: Keep loads similar
   * Duration: 1 week typically sufficient
   * Indicators: Performance decline, excessive soreness, poor recovery

🧮 Mathematical Models
----------------------

Volume Landmark Calculations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The platform implements sophisticated algorithms to estimate individual volume landmarks:

.. math::

   MEV_{individual} = MEV_{baseline} \times (1 + experience_{factor} + genetics_{factor})

Where:
   * :math:`MEV_{baseline}` = Research-based starting point
   * :math:`experience_{factor}` = Training history adjustment (-0.2 to +0.3)
   * :math:`genetics_{factor}` = Individual response modifier (-0.3 to +0.5)

SFR Calculation
~~~~~~~~~~~~~~~

Exercise effectiveness is quantified using the SFR formula:

.. math::

   SFR = \frac{Stimulus_{score}}{Fatigue_{cost}} \times Recovery_{factor}

Where:
   * :math:`Stimulus_{score}` = Muscle activation × Range of motion × Load
   * :math:`Fatigue_{cost}` = Neural demand + Metabolic cost + Joint stress
   * :math:`Recovery_{factor}` = Individual recovery capacity modifier

Volume Progression Algorithm
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Weekly volume increases follow an adaptive progression model:

.. math::

   Volume_{week+1} = Volume_{week} + \Delta V \times Readiness_{score}

Where:
   * :math:`\Delta V` = Base progression increment (1-3 sets)
   * :math:`Readiness_{score}` = Recovery and performance indicator (0.5-1.5)

🎯 Practical Implementation
---------------------------

Individual Assessment
~~~~~~~~~~~~~~~~~~~~~

**Training History Evaluation**
   * Years of consistent training experience
   * Previous volume tolerance and response patterns
   * Injury history and movement limitations
   * Current fitness and strength levels

**Response Pattern Analysis**
   * Rate of adaptation to volume increases
   * Recovery capacity and sleep quality
   * Stress levels and lifestyle factors
   * Genetic indicators (family history, body type)

**Goal-Specific Adjustments**
   * Hypertrophy vs. strength emphasis
   * Time availability and training frequency
   * Equipment access and exercise preferences
   * Competition or event preparation requirements

Programme Design Process
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Baseline Assessment**: Determine current volume landmarks
2. **Goal Setting**: Define specific hypertrophy objectives
3. **Exercise Selection**: Choose high-SFR movements for each muscle group
4. **Volume Planning**: Map mesocycle progression from MEV to MAV
5. **Frequency Distribution**: Optimise training frequency for volume targets
6. **Progression Monitoring**: Track performance and adjust accordingly
7. **Deload Planning**: Schedule recovery periods based on fatigue indicators

Monitoring and Adjustment
~~~~~~~~~~~~~~~~~~~~~~~~~

**Performance Indicators**
   * Rep performance at given loads
   * Rate of Perceived Exertion (RPE) trends
   * Muscle soreness patterns and duration
   * Sleep quality and recovery metrics

**Adjustment Triggers**
   * Performance decline over 2+ sessions
   * Excessive soreness lasting >48 hours
   * RPE increases without load progression
   * Poor sleep or elevated resting heart rate

**Modification Strategies**
   * Volume reduction (10-20% decrease)
   * Exercise substitution (lower SFR alternatives)
   * Frequency adjustment (redistribute volume)
   * Early deload implementation

.. note::
   These principles represent general guidelines that must be individualised based on personal response patterns, 
   training history, and lifestyle factors. The algorithms in Forge Protocol automatically adjust these 
   principles based on user feedback and performance data.

.. tip::
   Start conservatively with volume recommendations and allow the adaptive algorithms to guide progression. 
   The platform learns from your responses and becomes more accurate over time.
