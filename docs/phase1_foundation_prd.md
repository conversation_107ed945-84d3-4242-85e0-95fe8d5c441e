# Phase 1: Foundation Infrastructure - Detailed PRD

## Overview
Establish the foundational infrastructure for the RP Training App with Clean Architecture, authentication, and comprehensive testing framework.

## Status: 🔄 IN PROGRESS
**Start Date**: $(date +%Y-%m-%d)
**Target Completion**: TBD
**Current Progress**: 5% (Project Structure Setup)

## Objectives

### Primary Goals
1. **Clean Architecture Foundation**: Implement proper separation of concerns from day one
2. **Authentication System**: Secure JWT-based auth with refresh tokens
3. **Database Infrastructure**: PostgreSQL with migrations and connection pooling
4. **Development Environment**: Docker-based local development with CI/CD
5. **Testing Framework**: Comprehensive testing setup (unit, integration, behavioral)

### Success Criteria
- [ ] User registration/login API endpoints functional
- [ ] JWT authentication with refresh token flow working
- [ ] Database migrations running successfully
- [ ] Docker Compose environment fully operational
- [ ] CI/CD pipeline executing all tests locally
- [ ] Code quality checks (linting, type checking) passing
- [ ] API documentation auto-generated and accessible
- [ ] Basic health check and monitoring endpoints

## Technical Requirements

### Project Structure (Clean Architecture)
```
rp-training-app/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry point
│   ├── config.py                  # Configuration management
│   ├── domain/                    # Business logic layer
│   │   ├── __init__.py
│   │   ├── entities/              # Core business entities
│   │   │   ├── __init__.py
│   │   │   ├── user.py           # User entity
│   │   │   └── base.py           # Base entity class
│   │   ├── repositories/          # Abstract repository interfaces
│   │   │   ├── __init__.py
│   │   │   └── user_repository.py
│   │   ├── services/              # Domain services
│   │   │   ├── __init__.py
│   │   │   └── auth_service.py
│   │   └── exceptions/            # Domain exceptions
│   │       ├── __init__.py
│   │       └── auth_exceptions.py
│   ├── application/               # Application layer
│   │   ├── __init__.py
│   │   ├── use_cases/            # Application use cases
│   │   │   ├── __init__.py
│   │   │   ├── register_user.py
│   │   │   └── authenticate_user.py
│   │   ├── dto/                  # Data transfer objects
│   │   │   ├── __init__.py
│   │   │   └── user_dto.py
│   │   └── interfaces/           # Application interfaces
│   │       ├── __init__.py
│   │       └── auth_interface.py
│   ├── infrastructure/           # Infrastructure layer
│   │   ├── __init__.py
│   │   ├── database/            # Database implementation
│   │   │   ├── __init__.py
│   │   │   ├── connection.py    # Database connection
│   │   │   ├── models/          # SQLAlchemy models
│   │   │   │   ├── __init__.py
│   │   │   │   └── user_model.py
│   │   │   └── repositories/    # Repository implementations
│   │   │       ├── __init__.py
│   │   │       └── user_repository_impl.py
│   │   ├── auth/               # Authentication implementation
│   │   │   ├── __init__.py
│   │   │   ├── jwt_handler.py
│   │   │   └── password_handler.py
│   │   └── external/           # External service integrations
│   │       └── __init__.py
│   └── presentation/           # Presentation layer
│       ├── __init__.py
│       ├── api/               # API routes
│       │   ├── __init__.py
│       │   ├── v1/
│       │   │   ├── __init__.py
│       │   │   ├── auth.py    # Authentication endpoints
│       │   │   └── health.py  # Health check endpoints
│       │   └── dependencies.py # FastAPI dependencies
│       ├── schemas/           # Pydantic schemas
│       │   ├── __init__.py
│       │   ├── user_schema.py
│       │   └── auth_schema.py
│       └── middleware/        # Custom middleware
│           ├── __init__.py
│           ├── auth_middleware.py
│           └── cors_middleware.py
├── tests/
│   ├── __init__.py
│   ├── unit/                 # Unit tests
│   │   ├── __init__.py
│   │   ├── domain/
│   │   ├── application/
│   │   └── infrastructure/
│   ├── integration/          # Integration tests
│   │   ├── __init__.py
│   │   └── test_api_endpoints.py
│   ├── behavioral/           # BDD tests
│   │   ├── features/
│   │   │   └── user_registration.feature
│   │   └── steps/
│   │       └── auth_steps.py
│   └── conftest.py          # Pytest configuration
├── alembic/                 # Database migrations
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── docker/
│   ├── Dockerfile
│   ├── Dockerfile.dev
│   └── docker-compose.yml
├── .github/
│   └── workflows/
│       └── ci.yml
├── requirements/
│   ├── base.txt
│   ├── dev.txt
│   └── test.txt
├── alembic.ini
├── pytest.ini
├── pyproject.toml
├── .env.example
└── README.md
```

### Database Schema (Initial)
```sql
-- Users table (Phase 1)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Refresh tokens table
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_expires ON refresh_tokens(expires_at);
```

### API Endpoints (Phase 1)
```
POST   /api/v1/auth/register     # User registration
POST   /api/v1/auth/login        # User login
POST   /api/v1/auth/refresh      # Refresh access token
DELETE /api/v1/auth/logout       # Logout (revoke refresh token)
GET    /api/v1/auth/me          # Get current user profile
PUT    /api/v1/auth/me          # Update user profile
GET    /api/v1/health           # Health check
GET    /api/v1/health/db        # Database health check
```

## Implementation Tasks

### Task 1: Project Setup ⏳
**Estimated Time**: 4 hours
- [ ] Initialize project structure with Clean Architecture
- [ ] Set up virtual environment and dependencies
- [ ] Configure pyproject.toml with all tools
- [ ] Create base configuration management
- [ ] Set up environment variables

### Task 2: Database Infrastructure ⏳
**Estimated Time**: 6 hours
- [ ] Configure PostgreSQL connection with asyncpg
- [ ] Set up Alembic for migrations
- [ ] Create initial user and refresh_tokens tables
- [ ] Implement database connection pooling
- [ ] Add database health check endpoint

### Task 3: Authentication System ⏳
**Estimated Time**: 8 hours
- [ ] Implement JWT token generation and validation
- [ ] Create password hashing utilities (bcrypt)
- [ ] Build refresh token mechanism
- [ ] Implement user registration use case
- [ ] Implement user authentication use case
- [ ] Add authentication middleware

### Task 4: API Layer ⏳
**Estimated Time**: 6 hours
- [ ] Set up FastAPI application with proper structure
- [ ] Create Pydantic schemas for requests/responses
- [ ] Implement authentication endpoints
- [ ] Add proper error handling and validation
- [ ] Configure CORS and security headers
- [ ] Generate OpenAPI documentation

### Task 5: Testing Framework ⏳
**Estimated Time**: 8 hours
- [ ] Configure pytest with fixtures and markers
- [ ] Set up test database with Docker
- [ ] Write unit tests for domain logic
- [ ] Write integration tests for API endpoints
- [ ] Set up behavioral testing with behave
- [ ] Configure test coverage reporting

### Task 6: Docker & CI/CD ⏳
**Estimated Time**: 6 hours
- [ ] Create Dockerfile for development and production
- [ ] Set up Docker Compose with PostgreSQL and Redis
- [ ] Configure GitHub Actions for local CI/CD
- [ ] Set up code quality checks (black, isort, flake8, mypy)
- [ ] Configure automated testing pipeline
- [ ] Add deployment scripts

## Testing Requirements

### Unit Tests
```python
# Example test structure
tests/unit/domain/test_user_entity.py
tests/unit/application/test_register_user.py
tests/unit/infrastructure/test_jwt_handler.py
```

### Integration Tests
```python
# Example API test
def test_user_registration_success(client, db_session):
    response = client.post("/api/v1/auth/register", json={
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "first_name": "John",
        "last_name": "Doe"
    })
    assert response.status_code == 201
    assert "access_token" in response.json()
```

### Behavioral Tests
```gherkin
Feature: User Registration
  As a new user
  I want to register for an account
  So that I can access the RP training system

  Scenario: Successful registration
    Given I am a new user
    When I register with valid credentials
    Then I should receive an access token
    And my account should be created
```

## Quality Gates

### Code Quality
- [ ] All code passes mypy type checking
- [ ] Black formatting applied consistently
- [ ] Import sorting with isort
- [ ] Flake8 linting passes
- [ ] No security vulnerabilities detected

### Test Coverage
- [ ] Unit test coverage > 90%
- [ ] Integration test coverage > 80%
- [ ] All critical paths covered
- [ ] Behavioral tests for main user flows

### Performance
- [ ] API response times < 200ms
- [ ] Database queries < 50ms
- [ ] Memory usage < 256MB baseline
- [ ] No memory leaks detected

## Dependencies

### Core Dependencies
```
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.4.0
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
```

### Development Dependencies
```
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
behave>=1.2.6
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.5.0
```

## Risks & Mitigation

### Technical Risks
1. **Clean Architecture Complexity**: Start simple, refactor incrementally
2. **JWT Security**: Use established libraries, implement proper validation
3. **Database Performance**: Monitor queries, implement connection pooling
4. **Testing Complexity**: Start with critical paths, expand coverage

### Timeline Risks
1. **Scope Creep**: Stick to Phase 1 requirements strictly
2. **Architecture Decisions**: Make decisions quickly, document rationale
3. **Testing Setup**: Invest time upfront for long-term benefits

## Definition of Done

### Phase 1 Complete When:
- [ ] All API endpoints functional and tested
- [ ] User can register, login, and access protected routes
- [ ] Database migrations working in all environments
- [ ] Docker Compose environment fully operational
- [ ] CI/CD pipeline passing all checks
- [ ] Code coverage meets minimum thresholds
- [ ] Documentation complete and up-to-date
- [ ] Security review completed
- [ ] Performance benchmarks established

**Next Phase**: Phase 2 - Exercise Database & Workout Logging
