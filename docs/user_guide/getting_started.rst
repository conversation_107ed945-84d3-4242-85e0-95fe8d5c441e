Getting Started
===============

Welcome to the RP Training API! This guide will help you get up and running quickly with the API, whether you're a developer integrating with our services or a user exploring the capabilities.

Prerequisites
-------------

Before you begin, ensure you have the following:

**For API Users:**
- Basic understanding of REST APIs
- HTTP client (curl, Postman, or similar)
- Valid API credentials (obtained through registration)

**For Developers:**
- Python 3.11 or higher
- Git for version control
- Nix package manager (recommended) or pip/poetry

Quick Start Guide
-----------------

1. API Access
~~~~~~~~~~~~~

The RP Training API is available at:

- **Production**: ``https://api.rptraining.com`` (when deployed)
- **Development**: ``http://localhost:8000`` (local development)
- **Documentation**: ``/docs`` endpoint for interactive API explorer

2. Authentication
~~~~~~~~~~~~~~~~~

All API endpoints (except health checks) require authentication. Here's how to get started:

**Step 1: Register a new account**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/register" \
        -H "Content-Type: application/json" \
        -d '{
          "email": "<EMAIL>",
          "password": "SecurePassword123!",
          "first_name": "John",
          "last_name": "Doe",
          "training_experience_years": 2
        }'

**Step 2: Login to get access token**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "email": "<EMAIL>",
          "password": "SecurePassword123!"
        }'

**Step 3: Use the access token**

.. code-block:: bash

   curl -X GET "http://localhost:8000/api/v1/auth/me" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

3. Basic API Usage
~~~~~~~~~~~~~~~~~~

Once authenticated, you can access all API endpoints:

**Get your profile:**

.. code-block:: bash

   curl -X GET "http://localhost:8000/api/v1/auth/me" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

**Update your profile:**

.. code-block:: bash

   curl -X PUT "http://localhost:8000/api/v1/auth/me" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "first_name": "Jane",
          "training_experience_years": 3
        }'

**Check API health:**

.. code-block:: bash

   curl -X GET "http://localhost:8000/api/v1/health"

Development Setup
-----------------

For developers who want to run the API locally or contribute to the project:

1. Clone the Repository
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

2. Set Up Development Environment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Option A: Using Nix (Recommended)**

.. code-block:: bash

   # Enter the Nix development shell
   nix-shell

   # The environment will be automatically configured with all dependencies

**Option B: Using pip/poetry**

.. code-block:: bash

   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate

   # Install dependencies
   pip install -r requirements.txt

3. Configure Environment
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Copy environment template
   cp .env.example .env

   # Edit .env file with your configuration
   # Most defaults work for local development

4. Run the API
~~~~~~~~~~~~~~

.. code-block:: bash

   # Start the development server
   python -m uvicorn app.main:app --reload

   # The API will be available at http://localhost:8000
   # Interactive docs at http://localhost:8000/docs

5. Run Tests
~~~~~~~~~~~~

.. code-block:: bash

   # Run all tests
   python -m pytest

   # Run with coverage
   python -m pytest --cov=app

   # Run BDD tests
   python run_bdd_tests.py

API Explorer
------------

The RP Training API includes an interactive API explorer powered by FastAPI's automatic documentation generation.

**Access the API Explorer:**

1. Start the API server (see development setup above)
2. Open your browser to ``http://localhost:8000/docs``
3. Explore all available endpoints with interactive forms
4. Test API calls directly from the browser

**Features of the API Explorer:**

- **Interactive Forms**: Test API endpoints with real data
- **Authentication**: Login and use tokens directly in the interface
- **Schema Documentation**: View request/response schemas
- **Example Requests**: See example data for all endpoints
- **Response Codes**: Understand all possible response codes

Common Use Cases
----------------

1. User Registration and Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Scenario**: New user wants to create an account and start using the API.

.. code-block:: python

   import requests

   # Register new user
   registration_data = {
       "email": "<EMAIL>",
       "password": "SecurePassword123!",
       "first_name": "New",
       "last_name": "User",
       "training_experience_years": 1
   }

   response = requests.post(
       "http://localhost:8000/api/v1/auth/register",
       json=registration_data
   )

   if response.status_code == 201:
       user_data = response.json()
       access_token = user_data["access_token"]
       print(f"Registration successful! Token: {access_token}")

2. Profile Management
~~~~~~~~~~~~~~~~~~~~~

**Scenario**: User wants to update their training experience.

.. code-block:: python

   import requests

   headers = {"Authorization": f"Bearer {access_token}"}

   # Get current profile
   profile_response = requests.get(
       "http://localhost:8000/api/v1/auth/me",
       headers=headers
   )

   # Update training experience
   update_data = {"training_experience_years": 3}
   update_response = requests.put(
       "http://localhost:8000/api/v1/auth/me",
       json=update_data,
       headers=headers
   )

3. Health Monitoring
~~~~~~~~~~~~~~~~~~~~

**Scenario**: Monitor API health and system status.

.. code-block:: python

   import requests

   # Basic health check
   health_response = requests.get("http://localhost:8000/api/v1/health")
   print(f"API Status: {health_response.json()['status']}")

   # Detailed health check
   detailed_response = requests.get("http://localhost:8000/api/v1/health/detailed")
   health_data = detailed_response.json()
   
   for component, status in health_data["checks"].items():
       print(f"{component}: {status['status']}")

Error Handling
--------------

The API uses standard HTTP status codes and provides detailed error messages:

**Common Status Codes:**

- ``200 OK``: Request successful
- ``201 Created``: Resource created successfully
- ``400 Bad Request``: Invalid request data
- ``401 Unauthorized``: Authentication required
- ``403 Forbidden``: Access denied
- ``404 Not Found``: Resource not found
- ``422 Unprocessable Entity``: Validation error
- ``500 Internal Server Error``: Server error

**Error Response Format:**

.. code-block:: json

   {
     "detail": "Detailed error message",
     "type": "error_type",
     "code": "ERROR_CODE"
   }

**Example Error Handling:**

.. code-block:: python

   import requests

   try:
       response = requests.post(
           "http://localhost:8000/api/v1/auth/login",
           json={"email": "invalid", "password": "wrong"}
       )
       response.raise_for_status()
   except requests.exceptions.HTTPError as e:
       if response.status_code == 401:
           print("Invalid credentials")
       elif response.status_code == 422:
           print("Validation error:", response.json()["detail"])
       else:
           print(f"Error {response.status_code}: {response.text}")

Next Steps
----------

Now that you're familiar with the basics:

1. **Explore the API**: Use the interactive documentation at ``/docs``
2. **Read the API Reference**: Detailed endpoint documentation
3. **Check Examples**: Real-world usage examples
4. **Join the Community**: Get help and share feedback

**Useful Links:**

- :doc:`authentication` - Detailed authentication guide
- :doc:`api_usage` - Advanced API usage patterns
- :doc:`examples` - Code examples and tutorials
- :doc:`../api/endpoints` - Complete API reference
