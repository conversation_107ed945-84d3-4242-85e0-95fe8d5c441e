Component Architecture
=====================

This document provides detailed component diagrams showing the system modules, their dependencies, and interactions within the RP Training API.

System Components Overview
---------------------------

The RP Training API is composed of well-defined components organized by architectural layers:

.. mermaid::

   graph TB
       subgraph "Presentation Layer Components"
           API_AUTH[Authentication API]
           API_PROFILE[Profile API]
           API_HEALTH[Health API]
           SCHEMAS[Pydantic Schemas]
           DEPS[FastAPI Dependencies]
       end
       
       subgraph "Application Layer Components"
           UC_AUTH[Authentication Use Cases]
           UC_PROFILE[Profile Use Cases]
           UC_HEALTH[Health Use Cases]
           DTOS[Data Transfer Objects]
           INTERFACES[Service Interfaces]
       end
       
       subgraph "Domain Layer Components"
           ENT_USER[User Entity]
           SRV_AUTH[Authentication Service]
           SRV_VALIDATION[Validation Service]
           SRV_DOMAIN[Domain Services]
           REPO_INTERFACES[Repository Interfaces]
       end
       
       subgraph "Infrastructure Layer Components"
           REPO_USER[User Repository Impl]
           REPO_HEALTH[Health Repository Impl]
           AUTH_JWT[JWT Handler]
           AUTH_PASSWORD[Password Handler]
           DB_CONNECTION[Database Connection]
           HEALTH_MONITOR[Health Monitor]
       end
       
       %% Presentation to Application
       API_AUTH --> UC_AUTH
       API_PROFILE --> UC_PROFILE
       API_HEALTH --> UC_HEALTH
       SCHEMAS --> DTOS
       DEPS --> INTERFACES
       
       %% Application to Domain
       UC_AUTH --> ENT_USER
       UC_AUTH --> SRV_AUTH
       UC_PROFILE --> ENT_USER
       UC_PROFILE --> SRV_VALIDATION
       UC_HEALTH --> SRV_DOMAIN
       DTOS --> ENT_USER
       INTERFACES --> REPO_INTERFACES
       
       %% Domain to Infrastructure (Implementation)
       REPO_INTERFACES -.-> REPO_USER
       REPO_INTERFACES -.-> REPO_HEALTH
       SRV_AUTH -.-> AUTH_JWT
       SRV_AUTH -.-> AUTH_PASSWORD
       SRV_DOMAIN -.-> HEALTH_MONITOR
       
       %% Infrastructure Dependencies
       REPO_USER --> DB_CONNECTION
       REPO_HEALTH --> HEALTH_MONITOR
       
       %% Styling
       classDef presentation fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef application fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef infrastructure fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       
       class API_AUTH,API_PROFILE,API_HEALTH,SCHEMAS,DEPS presentation
       class UC_AUTH,UC_PROFILE,UC_HEALTH,DTOS,INTERFACES application
       class ENT_USER,SRV_AUTH,SRV_VALIDATION,SRV_DOMAIN,REPO_INTERFACES domain
       class REPO_USER,REPO_HEALTH,AUTH_JWT,AUTH_PASSWORD,DB_CONNECTION,HEALTH_MONITOR infrastructure

Authentication Component Details
--------------------------------

The authentication system consists of several interconnected components:

.. mermaid::

   graph LR
       subgraph "Authentication Flow"
           CLIENT[Client Request]
           ENDPOINT[Auth Endpoint]
           MIDDLEWARE[JWT Middleware]
           USE_CASE[Auth Use Case]
           USER_ENTITY[User Entity]
           AUTH_SERVICE[Auth Service]
           USER_REPO[User Repository]
           JWT_HANDLER[JWT Handler]
           PWD_HANDLER[Password Handler]
           DATABASE[(Database)]
       end
       
       CLIENT --> ENDPOINT
       ENDPOINT --> MIDDLEWARE
       MIDDLEWARE --> JWT_HANDLER
       ENDPOINT --> USE_CASE
       USE_CASE --> USER_ENTITY
       USE_CASE --> AUTH_SERVICE
       USE_CASE --> USER_REPO
       AUTH_SERVICE --> JWT_HANDLER
       AUTH_SERVICE --> PWD_HANDLER
       USER_REPO --> DATABASE
       
       %% Component responsibilities
       ENDPOINT -.->|"Validates input<br/>Handles HTTP"| ENDPOINT
       USE_CASE -.->|"Orchestrates<br/>business logic"| USE_CASE
       USER_ENTITY -.->|"Encapsulates<br/>business rules"| USER_ENTITY
       AUTH_SERVICE -.->|"Handles auth<br/>operations"| AUTH_SERVICE
       USER_REPO -.->|"Data access<br/>abstraction"| USER_REPO
       
       %% Styling
       classDef endpoint fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef usecase fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef infrastructure fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef external fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       
       class CLIENT,ENDPOINT,MIDDLEWARE endpoint
       class USE_CASE usecase
       class USER_ENTITY,AUTH_SERVICE domain
       class USER_REPO,JWT_HANDLER,PWD_HANDLER infrastructure
       class DATABASE external

Profile Management Components
-----------------------------

The profile management system handles user data operations:

.. mermaid::

   graph TB
       subgraph "Profile Management System"
           PROFILE_API[Profile API Endpoint]
           PROFILE_SCHEMA[Profile Schemas]
           PROFILE_UC[Profile Use Case]
           USER_ENT[User Entity]
           VALIDATION_SRV[Validation Service]
           USER_REPO_IMPL[User Repository]
           DB_CONN[Database Connection]
       end
       
       subgraph "Cross-Cutting Concerns"
           AUTH_MIDDLEWARE[Authentication Middleware]
           ERROR_HANDLER[Error Handler]
           LOGGING[Logging Service]
       end
       
       PROFILE_API --> PROFILE_SCHEMA
       PROFILE_API --> AUTH_MIDDLEWARE
       PROFILE_API --> PROFILE_UC
       PROFILE_UC --> USER_ENT
       PROFILE_UC --> VALIDATION_SRV
       PROFILE_UC --> USER_REPO_IMPL
       USER_REPO_IMPL --> DB_CONN
       
       %% Cross-cutting concerns
       PROFILE_API --> ERROR_HANDLER
       PROFILE_UC --> LOGGING
       USER_REPO_IMPL --> LOGGING
       
       %% Component interactions
       AUTH_MIDDLEWARE -.->|"Validates JWT<br/>Extracts user ID"| PROFILE_UC
       VALIDATION_SRV -.->|"Validates business<br/>rules"| USER_ENT
       ERROR_HANDLER -.->|"Handles exceptions<br/>Returns errors"| PROFILE_API
       
       %% Styling
       classDef api fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef usecase fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef infrastructure fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef crosscutting fill:#95a5a6,stroke:#7f8c8d,stroke-width:2px,color:#fff
       
       class PROFILE_API,PROFILE_SCHEMA api
       class PROFILE_UC usecase
       class USER_ENT,VALIDATION_SRV domain
       class USER_REPO_IMPL,DB_CONN infrastructure
       class AUTH_MIDDLEWARE,ERROR_HANDLER,LOGGING crosscutting

Health Monitoring Components
----------------------------

The health monitoring system provides system observability:

.. mermaid::

   graph LR
       subgraph "Health Monitoring Architecture"
           HEALTH_API[Health API]
           HEALTH_UC[Health Use Case]
           HEALTH_SRV[Health Service]
           DB_HEALTH[Database Health Check]
           SYS_HEALTH[System Health Check]
           METRICS[Metrics Collector]
       end
       
       subgraph "System Resources"
           DATABASE[(Database)]
           MEMORY[Memory Monitor]
           DISK[Disk Monitor]
           CPU[CPU Monitor]
       end
       
       HEALTH_API --> HEALTH_UC
       HEALTH_UC --> HEALTH_SRV
       HEALTH_SRV --> DB_HEALTH
       HEALTH_SRV --> SYS_HEALTH
       HEALTH_SRV --> METRICS
       
       DB_HEALTH --> DATABASE
       SYS_HEALTH --> MEMORY
       SYS_HEALTH --> DISK
       SYS_HEALTH --> CPU
       
       %% Health check types
       HEALTH_API -.->|"/health<br/>Basic check"| HEALTH_UC
       HEALTH_API -.->|"/health/detailed<br/>Comprehensive"| HEALTH_UC
       HEALTH_API -.->|"/health/database<br/>DB specific"| HEALTH_UC
       
       %% Styling
       classDef api fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef usecase fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef service fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef monitor fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef resource fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       
       class HEALTH_API api
       class HEALTH_UC usecase
       class HEALTH_SRV service
       class DB_HEALTH,SYS_HEALTH,METRICS monitor
       class DATABASE,MEMORY,DISK,CPU resource

Dependency Injection Container
------------------------------

The dependency injection system manages component lifecycles and dependencies:

.. mermaid::

   graph TB
       subgraph "Dependency Injection Container"
           CONTAINER[DI Container]
           FACTORY[Component Factory]
           LIFECYCLE[Lifecycle Manager]
       end
       
       subgraph "Service Registration"
           REG_REPO[Repository Registration]
           REG_UC[Use Case Registration]
           REG_SRV[Service Registration]
           REG_INFRA[Infrastructure Registration]
       end
       
       subgraph "Runtime Resolution"
           RESOLVE[Dependency Resolution]
           INJECT[Dependency Injection]
           SCOPE[Scope Management]
       end
       
       CONTAINER --> FACTORY
       CONTAINER --> LIFECYCLE
       CONTAINER --> REG_REPO
       CONTAINER --> REG_UC
       CONTAINER --> REG_SRV
       CONTAINER --> REG_INFRA
       
       FACTORY --> RESOLVE
       RESOLVE --> INJECT
       INJECT --> SCOPE
       
       %% Registration details
       REG_REPO -.->|"UserRepository<br/>HealthRepository"| REG_REPO
       REG_UC -.->|"AuthUseCase<br/>ProfileUseCase"| REG_UC
       REG_SRV -.->|"AuthService<br/>ValidationService"| REG_SRV
       REG_INFRA -.->|"JWTHandler<br/>PasswordHandler"| REG_INFRA
       
       %% Scope management
       SCOPE -.->|"Singleton<br/>Request<br/>Transient"| SCOPE
       
       %% Styling
       classDef container fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef registration fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef resolution fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       
       class CONTAINER,FACTORY,LIFECYCLE container
       class REG_REPO,REG_UC,REG_SRV,REG_INFRA registration
       class RESOLVE,INJECT,SCOPE resolution

Testing Component Architecture
------------------------------

The testing system provides comprehensive validation across all layers:

.. mermaid::

   graph TB
       subgraph "Testing Framework"
           TEST_RUNNER[Test Runner]
           TEST_CONFIG[Test Configuration]
           TEST_FIXTURES[Test Fixtures]
       end
       
       subgraph "Unit Testing"
           UNIT_DOMAIN[Domain Tests]
           UNIT_APP[Application Tests]
           UNIT_INFRA[Infrastructure Tests]
           UNIT_API[API Tests]
       end
       
       subgraph "Integration Testing"
           INT_API[API Integration]
           INT_DB[Database Integration]
           INT_CROSS[Cross-Layer Integration]
       end
       
       subgraph "BDD Testing"
           BDD_FEATURES[Feature Files]
           BDD_STEPS[Step Definitions]
           BDD_CONTEXT[Test Context]
       end
       
       subgraph "Test Support"
           FACTORIES[Test Factories]
           MOCKS[Mock Objects]
           TEST_DB[Test Database]
       end
       
       TEST_RUNNER --> TEST_CONFIG
       TEST_RUNNER --> TEST_FIXTURES
       TEST_RUNNER --> UNIT_DOMAIN
       TEST_RUNNER --> UNIT_APP
       TEST_RUNNER --> UNIT_INFRA
       TEST_RUNNER --> UNIT_API
       TEST_RUNNER --> INT_API
       TEST_RUNNER --> INT_DB
       TEST_RUNNER --> INT_CROSS
       TEST_RUNNER --> BDD_FEATURES
       
       BDD_FEATURES --> BDD_STEPS
       BDD_STEPS --> BDD_CONTEXT
       
       TEST_FIXTURES --> FACTORIES
       TEST_FIXTURES --> MOCKS
       TEST_FIXTURES --> TEST_DB
       
       %% Test dependencies
       UNIT_DOMAIN --> MOCKS
       UNIT_APP --> MOCKS
       INT_DB --> TEST_DB
       BDD_CONTEXT --> FACTORIES
       
       %% Styling
       classDef framework fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef unit fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef integration fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef bdd fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef support fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
       
       class TEST_RUNNER,TEST_CONFIG,TEST_FIXTURES framework
       class UNIT_DOMAIN,UNIT_APP,UNIT_INFRA,UNIT_API unit
       class INT_API,INT_DB,INT_CROSS integration
       class BDD_FEATURES,BDD_STEPS,BDD_CONTEXT bdd
       class FACTORIES,MOCKS,TEST_DB support

Component Interaction Patterns
------------------------------

Common interaction patterns used throughout the system:

.. mermaid::

   graph LR
       subgraph "Request-Response Pattern"
           REQ[Request]
           PROC[Process]
           RESP[Response]
           REQ --> PROC --> RESP
       end
       
       subgraph "Repository Pattern"
           ENTITY[Entity]
           REPO_INT[Repository Interface]
           REPO_IMPL[Repository Implementation]
           ENTITY --> REPO_INT
           REPO_INT -.-> REPO_IMPL
       end
       
       subgraph "Use Case Pattern"
           INPUT[Input DTO]
           UC[Use Case]
           OUTPUT[Output DTO]
           INPUT --> UC --> OUTPUT
       end
       
       subgraph "Dependency Injection Pattern"
           INTERFACE[Interface]
           IMPLEMENTATION[Implementation]
           CONSUMER[Consumer]
           INTERFACE -.-> IMPLEMENTATION
           CONSUMER --> INTERFACE
       end
       
       %% Pattern descriptions
       REQ -.->|"HTTP Request<br/>Validation"| PROC
       PROC -.->|"Business Logic<br/>Data Processing"| RESP
       
       ENTITY -.->|"Domain Model<br/>Business Rules"| REPO_INT
       REPO_INT -.->|"Data Access<br/>Abstraction"| REPO_IMPL
       
       INPUT -.->|"Request Data<br/>Validation"| UC
       UC -.->|"Business Logic<br/>Orchestration"| OUTPUT
       
       INTERFACE -.->|"Contract<br/>Definition"| IMPLEMENTATION
       CONSUMER -.->|"Loose Coupling<br/>Testability"| INTERFACE
       
       %% Styling
       classDef pattern fill:#34495e,stroke:#2c3e50,stroke-width:2px,color:#fff
       classDef component fill:#95a5a6,stroke:#7f8c8d,stroke-width:1px,color:#2c3e50
       
       class REQ,PROC,RESP,ENTITY,REPO_INT,REPO_IMPL,INPUT,UC,OUTPUT,INTERFACE,IMPLEMENTATION,CONSUMER component
