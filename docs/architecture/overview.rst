Architecture Overview
====================

The RP Training API is built using **Clean Architecture** principles, ensuring separation of concerns, testability, and maintainability. This document provides a comprehensive overview of the system architecture, design patterns, and technical decisions.

System Architecture
-------------------

The application follows a **layered architecture** with clear boundaries and dependency inversion:

.. mermaid::

   graph TB
       subgraph "External Interfaces"
           HTTP[HTTP Clients]
           DB[Database Server]
           EXT[External Services]
       end

       subgraph "Presentation Layer"
           API[FastAPI Endpoints]
           SCHEMA[Pydantic Schemas]
           DI[Dependency Injection]
       end

       subgraph "Application Layer"
           UC[Use Cases]
           DTO[Data Transfer Objects]
           INT[Interfaces/Contracts]
       end

       subgraph "Domain Layer"
           ENT[Entities]
           SRV[Domain Services]
           REPO[Repository Interfaces]
       end

       subgraph "Infrastructure Layer"
           DBIMPL[Database Implementation]
           AUTH[Authentication/JWT]
           EXTIMPL[External Service Clients]
       end

       %% External connections
       HTTP --> API
       API --> DB
       API --> EXT

       %% Layer dependencies (inward)
       API --> UC
       SCHEMA --> DTO
       DI --> INT

       UC --> ENT
       DTO --> ENT
       INT --> REPO

       ENT --> SRV
       REPO --> ENT

       %% Infrastructure implementations
       DBIMPL -.-> REPO
       AUTH -.-> INT
       EXTIMPL -.-> INT

       %% Styling
       classDef presentation fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef application fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef infrastructure fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef external fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff

       class API,SCHEMA,DI presentation
       class UC,DTO,INT application
       class ENT,SRV,REPO domain
       class DBIMPL,AUTH,EXTIMPL infrastructure
       class HTTP,DB,EXT external

Clean Architecture Principles
-----------------------------

The architecture follows **Uncle Bob's Clean Architecture** with these key principles:

1. **Dependency Inversion**
   - High-level modules don't depend on low-level modules
   - Both depend on abstractions (interfaces)
   - Dependencies point inward toward the domain

2. **Separation of Concerns**
   - Each layer has a single responsibility
   - Business logic is isolated from infrastructure concerns
   - UI logic is separated from business logic

3. **Testability**
   - Business logic can be tested without external dependencies
   - Infrastructure can be mocked or stubbed
   - Each layer can be tested in isolation

4. **Independence**
   - Business rules don't depend on frameworks
   - Business rules don't depend on databases
   - Business rules don't depend on external services

Layer Responsibilities
----------------------

Domain Layer (Core)
~~~~~~~~~~~~~~~~~~~

**Location**: ``app/domain/``

**Responsibilities**:
- Contains business entities and core business logic
- Defines repository interfaces (contracts)
- Implements domain services
- Defines domain exceptions
- Contains no dependencies on external frameworks

**Key Components**:

.. code-block:: python

   # Domain Entity
   class User(BaseEntity):
       def __init__(self, email: str, hashed_password: str):
           self.email = email
           self.hashed_password = hashed_password
           self.is_active = True
       
       @classmethod
       def create(cls, email: str, password: str) -> "User":
           # Business logic for user creation
           if not cls._is_valid_email(email):
               raise InvalidEmailError("Invalid email format")
           
           hashed_password = PasswordHandler.hash_password(password)
           return cls(email=email, hashed_password=hashed_password)

   # Repository Interface
   class UserRepository(ABC):
       @abstractmethod
       async def create(self, user: User) -> User:
           pass
       
       @abstractmethod
       async def get_by_email(self, email: str) -> Optional[User]:
           pass

Application Layer
~~~~~~~~~~~~~~~~~

**Location**: ``app/application/``

**Responsibilities**:
- Orchestrates business workflows (Use Cases)
- Coordinates between domain and infrastructure
- Handles data transformation (DTOs)
- Implements application-specific business rules

**Key Components**:

.. code-block:: python

   # Use Case
   class RegisterUserUseCase:
       def __init__(
           self,
           user_repository: UserRepository,
           password_handler: PasswordHandler
       ):
           self._user_repository = user_repository
           self._password_handler = password_handler
       
       async def execute(
           self,
           email: str,
           password: str,
           first_name: str,
           last_name: str
       ) -> RegisterUserResponse:
           # Application logic
           if await self._user_repository.exists_by_email(email):
               raise UserAlreadyExistsError("User already exists")
           
           user = User.create(
               email=email,
               password=password,
               first_name=first_name,
               last_name=last_name
           )
           
           created_user = await self._user_repository.create(user)
           return RegisterUserResponse.from_entity(created_user)

   # Data Transfer Object
   @dataclass
   class UserDTO:
       id: str
       email: str
       first_name: str
       last_name: str
       is_active: bool

Presentation Layer
~~~~~~~~~~~~~~~~~~

**Location**: ``app/presentation/``

**Responsibilities**:
- Handles HTTP requests and responses
- Validates input data (Pydantic schemas)
- Manages authentication and authorization
- Formats output data

**Key Components**:

.. code-block:: python

   # API Endpoint
   @router.post("/register", response_model=AuthResponse)
   async def register(
       request: UserRegistrationRequest,
       use_case: RegisterUserUseCase = Depends(get_register_user_use_case)
   ) -> AuthResponse:
       try:
           result = await use_case.execute(
               email=request.email,
               password=request.password,
               first_name=request.first_name,
               last_name=request.last_name
           )
           return AuthResponse.from_use_case_response(result)
       except UserAlreadyExistsError as e:
           raise HTTPException(status_code=409, detail=str(e))

   # Pydantic Schema
   class UserRegistrationRequest(BaseModel):
       email: EmailStr
       password: str = Field(..., min_length=8)
       first_name: str = Field(..., min_length=1)
       last_name: str = Field(..., min_length=1)

Infrastructure Layer
~~~~~~~~~~~~~~~~~~~~

**Location**: ``app/infrastructure/``

**Responsibilities**:
- Implements repository interfaces
- Handles database operations
- Manages external service integrations
- Provides authentication and security services

**Key Components**:

.. code-block:: python

   # Repository Implementation
   class UserRepositoryImpl(UserRepository):
       def __init__(self, session: AsyncSession):
           self._session = session
       
       async def create(self, user: User) -> User:
           user_model = self._entity_to_model(user)
           self._session.add(user_model)
           await self._session.commit()
           return self._model_to_entity(user_model)
       
       async def get_by_email(self, email: str) -> Optional[User]:
           result = await self._session.execute(
               select(UserModel).where(UserModel.email == email)
           )
           user_model = result.scalar_one_or_none()
           return self._model_to_entity(user_model) if user_model else None

   # Authentication Service
   class JWTHandler:
       def create_access_token(self, user_id: str) -> str:
           payload = {
               "sub": user_id,
               "exp": datetime.utcnow() + timedelta(minutes=30),
               "type": "access"
           }
           return jwt.encode(payload, self._secret_key, algorithm="HS256")

Design Patterns
---------------

The application employs several design patterns to ensure maintainability and flexibility:

Repository Pattern
~~~~~~~~~~~~~~~~~~

**Purpose**: Abstracts data access logic and provides a uniform interface for data operations.

**Implementation**:
- Abstract repository interfaces in the domain layer
- Concrete implementations in the infrastructure layer
- Dependency injection to provide implementations

**Benefits**:
- Testability (can mock repositories)
- Database independence
- Centralized data access logic

Dependency Injection
~~~~~~~~~~~~~~~~~~~~

**Purpose**: Manages dependencies and promotes loose coupling between components.

**Implementation**:

.. code-block:: python

   # Dependency Provider
   def get_user_repository(
       session: AsyncSession = Depends(get_database_session)
   ) -> UserRepository:
       return UserRepositoryImpl(session)
   
   def get_register_user_use_case(
       user_repository: UserRepository = Depends(get_user_repository),
       password_handler: PasswordHandler = Depends(get_password_handler)
   ) -> RegisterUserUseCase:
       return RegisterUserUseCase(user_repository, password_handler)

**Benefits**:
- Loose coupling
- Easy testing with mocks
- Configuration flexibility

Use Case Pattern
~~~~~~~~~~~~~~~~

**Purpose**: Encapsulates business workflows and application-specific logic.

**Implementation**:
- Each use case represents a single business operation
- Use cases coordinate between domain and infrastructure
- Clear input/output contracts

**Benefits**:
- Single responsibility
- Testable business logic
- Clear application boundaries

Factory Pattern
~~~~~~~~~~~~~~~

**Purpose**: Creates objects without specifying their concrete classes.

**Implementation**:

.. code-block:: python

   class UserFactory:
       @staticmethod
       def create_user(
           email: str,
           password: str,
           first_name: str,
           last_name: str
       ) -> User:
           # Validation and business rules
           return User.create(email, password, first_name, last_name)

**Benefits**:
- Encapsulates object creation logic
- Consistent object initialization
- Easy to extend and modify

Component Architecture
---------------------

The system is composed of well-defined components with clear responsibilities:

.. mermaid::

   graph LR
       subgraph "API Layer"
           AUTH_API[Authentication API]
           PROFILE_API[Profile API]
           HEALTH_API[Health API]
       end

       subgraph "Business Logic"
           AUTH_UC[Authentication Use Cases]
           PROFILE_UC[Profile Use Cases]
           HEALTH_UC[Health Use Cases]
       end

       subgraph "Domain Core"
           USER_ENT[User Entity]
           AUTH_SRV[Auth Service]
           VALID_SRV[Validation Service]
       end

       subgraph "Data Access"
           USER_REPO[User Repository]
           HEALTH_REPO[Health Repository]
       end

       subgraph "Infrastructure"
           DB[Database]
           JWT[JWT Handler]
           HASH[Password Handler]
           MONITOR[Health Monitor]
       end

       %% API to Use Cases
       AUTH_API --> AUTH_UC
       PROFILE_API --> PROFILE_UC
       HEALTH_API --> HEALTH_UC

       %% Use Cases to Domain
       AUTH_UC --> USER_ENT
       AUTH_UC --> AUTH_SRV
       PROFILE_UC --> USER_ENT
       PROFILE_UC --> VALID_SRV
       HEALTH_UC --> MONITOR

       %% Domain to Repositories
       AUTH_SRV --> USER_REPO
       USER_ENT --> USER_REPO
       HEALTH_UC --> HEALTH_REPO

       %% Repositories to Infrastructure
       USER_REPO --> DB
       HEALTH_REPO --> MONITOR
       AUTH_SRV --> JWT
       AUTH_SRV --> HASH

       %% Styling
       classDef api fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef business fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef data fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
       classDef infra fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff

       class AUTH_API,PROFILE_API,HEALTH_API api
       class AUTH_UC,PROFILE_UC,HEALTH_UC business
       class USER_ENT,AUTH_SRV,VALID_SRV domain
       class USER_REPO,HEALTH_REPO data
       class DB,JWT,HASH,MONITOR infra

Data Flow
---------

The typical data flow through the application follows this pattern:

.. mermaid::

   sequenceDiagram
       participant Client
       participant API as FastAPI Endpoint
       participant Schema as Pydantic Schema
       participant UseCase as Use Case
       participant Domain as Domain Entity
       participant Repo as Repository
       participant DB as Database

       Client->>API: HTTP Request
       API->>Schema: Validate Input
       Schema-->>API: Validated Data
       API->>UseCase: Execute Business Logic
       UseCase->>Domain: Apply Business Rules
       Domain-->>UseCase: Domain Result
       UseCase->>Repo: Persist/Retrieve Data
       Repo->>DB: Database Operation
       DB-->>Repo: Data Result
       Repo-->>UseCase: Repository Result
       UseCase-->>API: Use Case Result
       API->>Schema: Format Response
       Schema-->>API: Formatted Response
       API-->>Client: HTTP Response

Technology Stack
----------------

Core Framework
~~~~~~~~~~~~~~

- **FastAPI**: Modern, fast web framework for building APIs
- **Python 3.11+**: Latest Python features and performance improvements
- **Pydantic**: Data validation and serialization
- **SQLAlchemy 2.0**: Modern ORM with async support

Database
~~~~~~~~

- **SQLite**: Development database (file-based)
- **PostgreSQL**: Production database (when deployed)
- **Alembic**: Database migrations

Authentication & Security
~~~~~~~~~~~~~~~~~~~~~~~~~

- **JWT**: Stateless authentication tokens
- **bcrypt**: Secure password hashing
- **CORS**: Cross-origin resource sharing
- **Security Headers**: Protection against common attacks

Testing
~~~~~~~

- **pytest**: Unit and integration testing
- **Behave**: Behavior-driven development
- **Factory Boy**: Test data generation
- **httpx**: Async HTTP client for testing

Development Tools
~~~~~~~~~~~~~~~~~

- **Nix**: Reproducible development environments
- **Black**: Code formatting
- **isort**: Import sorting
- **mypy**: Static type checking

Quality Metrics
---------------

The architecture supports high-quality software development:

**Test Coverage**: 88.24%
- Unit tests: 240+ tests
- Integration tests: 15+ tests
- BDD scenarios: 100+ scenarios

**Code Quality**:
- Type hints throughout codebase
- Comprehensive error handling
- Consistent code formatting
- Clear separation of concerns

**Performance**:
- Async/await for I/O operations
- Connection pooling for database
- Efficient query patterns
- Minimal dependencies

**Security**:
- JWT-based authentication
- Password hashing with bcrypt
- Input validation with Pydantic
- SQL injection prevention

Scalability Considerations
--------------------------

The architecture is designed to support future growth:

**Horizontal Scaling**:
- Stateless application design
- Database connection pooling
- External session storage capability

**Vertical Scaling**:
- Async I/O for better resource utilization
- Efficient database queries
- Minimal memory footprint

**Microservices Ready**:
- Clear layer boundaries
- Interface-based design
- Independent deployability

**Extensibility**:
- Plugin architecture through dependency injection
- Easy to add new features
- Minimal coupling between components

Future Enhancements
-------------------

The architecture supports these planned enhancements:

1. **Caching Layer**: Redis for session and data caching
2. **Message Queue**: Async task processing
3. **API Gateway**: Rate limiting and request routing
4. **Monitoring**: Observability and metrics collection
5. **Multi-tenancy**: Support for multiple organizations
