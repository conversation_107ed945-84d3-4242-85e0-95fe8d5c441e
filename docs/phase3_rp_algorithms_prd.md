# Phase 3: RP Scientific Algorithms - Detailed PRD

## Overview
Implement the core Renaissance Periodisation scientific algorithms that form the intelligence of the training platform, including MEV Stimulus Estimation, Set Progression Calculator, Stimulus-to-Fatigue Ratio (SFR), and comprehensive feedback systems.

## Status: 🔮 PLANNED
**Start Date**: TBD (After Phase 2 completion)
**Target Completion**: TBD
**Current Progress**: 0% (Awaiting Phase 2 completion)

## Objectives

### Primary Goals
1. **MEV Stimulus Estimator**: Intelligent volume recommendations based on post-workout feedback
2. **Set Progression Calculator**: Automated weekly volume adjustments using recovery metrics
3. **Stimulus-to-Fatigue Ratio (SFR)**: Exercise effectiveness analysis for optimal selection
4. **Post-workout Feedback System**: Comprehensive data collection for algorithm inputs
5. **Weekly Progress Tracking**: Automated progression monitoring and recommendations

### Success Criteria
- [ ] MEV recommendations match RP book calculations within 5% accuracy
- [ ] Set progression logic working with real user feedback
- [ ] SFR calculations validated against RP reference standards
- [ ] Feedback system collecting comprehensive data
- [ ] Weekly progress tracking functional and actionable
- [ ] Algorithm performance < 10ms for all calculations
- [ ] User feedback integration seamless and intuitive
- [ ] Recommendation confidence scores > 80% accuracy

## Technical Requirements

### Database Schema Extensions

#### MEV Feedback Table
```sql
CREATE TABLE mev_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workout_id UUID REFERENCES workouts(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    muscle_group muscle_group_enum NOT NULL,
    mind_muscle_connection INTEGER CHECK (mind_muscle_connection BETWEEN 0 AND 3),
    pump_rating INTEGER CHECK (pump_rating BETWEEN 0 AND 3),
    muscle_disruption INTEGER CHECK (muscle_disruption BETWEEN 0 AND 3),
    total_score INTEGER GENERATED ALWAYS AS (mind_muscle_connection + pump_rating + muscle_disruption) STORED,
    recommendation mev_recommendation_enum,
    confidence_score DECIMAL(3,2) CHECK (confidence_score BETWEEN 0.0 AND 1.0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_mev_feedback_workout ON mev_feedback(workout_id);
CREATE INDEX idx_mev_feedback_muscle_group ON mev_feedback(muscle_group);
CREATE INDEX idx_mev_feedback_date ON mev_feedback(created_at);
```

#### Weekly Progress Table
```sql
CREATE TABLE weekly_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    muscle_group muscle_group_enum NOT NULL,
    week_start_date DATE NOT NULL,
    week_number INTEGER NOT NULL,
    soreness_score INTEGER CHECK (soreness_score BETWEEN 0 AND 3),
    performance_score INTEGER CHECK (performance_score BETWEEN 0 AND 3),
    recovery_score INTEGER CHECK (recovery_score BETWEEN 0 AND 3),
    current_weekly_sets INTEGER NOT NULL,
    recommended_sets_change INTEGER,
    actual_sets_added INTEGER,
    volume_change_percent DECIMAL(5,2),
    progression_type progression_type_enum,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, muscle_group, week_start_date)
);

CREATE INDEX idx_weekly_progress_user ON weekly_progress(user_id);
CREATE INDEX idx_weekly_progress_week ON weekly_progress(week_start_date);
CREATE INDEX idx_weekly_progress_muscle ON weekly_progress(muscle_group);
```

#### Exercise SFR Ratings Table
```sql
CREATE TABLE exercise_sfr_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    raw_stimulus_magnitude DECIMAL(3,1) CHECK (raw_stimulus_magnitude BETWEEN 0.0 AND 10.0),
    fatigue_score DECIMAL(3,1) CHECK (fatigue_score BETWEEN 0.0 AND 10.0),
    sfr_ratio DECIMAL(4,2) GENERATED ALWAYS AS (
        CASE 
            WHEN fatigue_score > 0 THEN raw_stimulus_magnitude / fatigue_score
            ELSE 0
        END
    ) STORED,
    joint_stress_rating INTEGER CHECK (joint_stress_rating BETWEEN 0 AND 3),
    systemic_fatigue_rating INTEGER CHECK (systemic_fatigue_rating BETWEEN 0 AND 3),
    injury_risk_rating INTEGER CHECK (injury_risk_rating BETWEEN 0 AND 3),
    effectiveness_tier sfr_tier_enum,
    sample_size INTEGER DEFAULT 1,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_sfr_ratings_user_exercise ON exercise_sfr_ratings(user_id, exercise_id);
CREATE INDEX idx_sfr_ratings_ratio ON exercise_sfr_ratings(sfr_ratio DESC);
CREATE INDEX idx_sfr_ratings_tier ON exercise_sfr_ratings(effectiveness_tier);
```

#### Algorithm Configuration Table
```sql
CREATE TABLE algorithm_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    algorithm_type algorithm_type_enum NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, algorithm_type, config_key)
);

CREATE INDEX idx_algorithm_config_user_type ON algorithm_config(user_id, algorithm_type);
```

### Enums and Types
```sql
CREATE TYPE mev_recommendation_enum AS ENUM (
    'increase_2_4_sets', 'progress_normally', 'maintain_volume', 
    'reduce_volume', 'deload_recommended', 'recovery_session'
);

CREATE TYPE progression_type_enum AS ENUM (
    'volume_increase', 'volume_maintain', 'volume_decrease', 
    'intensity_increase', 'frequency_increase', 'deload'
);

CREATE TYPE sfr_tier_enum AS ENUM (
    'tier_1_excellent', 'tier_2_good', 'tier_3_average', 
    'tier_4_poor', 'tier_5_avoid'
);

CREATE TYPE algorithm_type_enum AS ENUM (
    'mev_estimator', 'set_progression', 'sfr_calculator', 
    'volume_landmarks', 'deload_predictor'
);
```

## Core Algorithm Implementations

### MEV Stimulus Estimator
```python
class MEVEstimator:
    """
    Estimates Minimum Effective Volume based on post-workout feedback.
    
    Implements RP methodology for determining if current volume meets
    the minimum threshold for hypertrophy stimulus.
    """
    
    def calculate_stimulus_score(
        self,
        mind_muscle_connection: int,  # 0-3 scale
        pump_rating: int,             # 0-3 scale
        muscle_disruption: int        # 0-3 scale
    ) -> MEVRecommendation:
        """
        Calculate MEV recommendation based on stimulus feedback.
        
        Total Score Interpretation:
        - 0-1: Below MEV, increase volume by 2-4 sets
        - 2-3: At or slightly below MEV, increase volume by 2-4 sets
        - 4-6: At or just above MEV, progress normally
        - 7-9: Between MAV and MRV, consider reducing volume
        """
        
    def get_personalised_mev(
        self,
        user_id: UUID,
        muscle_group: MuscleGroup,
        training_history: TrainingHistory
    ) -> int:
        """Calculate personalised MEV based on individual factors."""
```

### Set Progression Calculator
```python
class SetProgressionCalculator:
    """
    Autoregulates weekly volume increases based on recovery indicators.
    
    Uses soreness, performance, and recovery metrics to determine
    optimal volume progression for the following week.
    """
    
    def calculate_weekly_sets(
        self,
        soreness_score: int,      # 0-3 scale
        performance_score: int,   # 0-3 scale
        recovery_score: int,      # 0-3 scale
        current_sets: int
    ) -> SetProgressionResult:
        """
        Calculate recommended set changes for next week.
        
        Algorithm considers:
        - Recovery capacity (soreness + recovery scores)
        - Performance trends
        - Current volume relative to landmarks
        - Individual response patterns
        """
        
    def predict_deload_timing(
        self,
        weekly_progress: List[WeeklyProgress],
        current_week: int
    ) -> DeloadPrediction:
        """Predict when deload should be scheduled."""
```

### Stimulus-to-Fatigue Ratio Calculator
```python
class SFRCalculator:
    """
    Quantifies exercise effectiveness by comparing stimulus to fatigue cost.
    
    Helps users select the most efficient exercises for their goals
    and individual response patterns.
    """
    
    def calculate_sfr(
        self,
        raw_stimulus_magnitude: float,  # 0-10 scale
        fatigue_score: float,           # 0-10 scale
        exercise_context: ExerciseContext
    ) -> SFRResult:
        """
        Calculate stimulus-to-fatigue ratio.
        
        SFR = Raw Stimulus Magnitude / Total Fatigue Score
        
        Where:
        - Raw Stimulus = Mind-muscle connection + Pump + Disruption
        - Fatigue = Joint stress + Systemic fatigue + Recovery impact
        """
        
    def rank_exercises_by_sfr(
        self,
        user_id: UUID,
        muscle_group: MuscleGroup,
        available_equipment: List[Equipment]
    ) -> List[ExerciseRanking]:
        """Rank exercises by SFR for optimal selection."""
```

## API Endpoints (Phase 3)

### MEV Feedback
```
POST   /api/v1/feedback/mev                    # Submit MEV feedback
GET    /api/v1/feedback/mev/{workout_id}       # Get workout MEV feedback
PUT    /api/v1/feedback/mev/{feedback_id}      # Update MEV feedback
GET    /api/v1/recommendations/mev/{muscle_group} # Get MEV recommendations
```

### Weekly Progress
```
POST   /api/v1/progress/weekly                 # Submit weekly progress
GET    /api/v1/progress/weekly                 # Get weekly progress history
PUT    /api/v1/progress/weekly/{progress_id}   # Update weekly progress
GET    /api/v1/recommendations/volume          # Get volume recommendations
```

### SFR Analysis
```
GET    /api/v1/analytics/sfr/exercises         # Get exercise SFR rankings
POST   /api/v1/analytics/sfr/rate             # Rate exercise effectiveness
GET    /api/v1/analytics/sfr/user/{user_id}   # Get user's SFR data
GET    /api/v1/recommendations/exercises       # Get exercise recommendations
```

### Algorithm Configuration
```
GET    /api/v1/algorithms/config               # Get algorithm settings
PUT    /api/v1/algorithms/config               # Update algorithm settings
POST   /api/v1/algorithms/calibrate            # Calibrate algorithms
GET    /api/v1/algorithms/performance          # Get algorithm performance metrics
```

## Implementation Tasks

### Task 1: MEV Estimation System
**Estimated Time**: 14 hours
- [ ] Implement MEV feedback data models
- [ ] Create MEV calculation algorithms
- [ ] Build feedback collection interfaces
- [ ] Add personalisation based on training history
- [ ] Implement confidence scoring
- [ ] Create recommendation engine

### Task 2: Set Progression Calculator
**Estimated Time**: 12 hours
- [ ] Implement weekly progress tracking
- [ ] Create set progression algorithms
- [ ] Build autoregulation logic
- [ ] Add deload prediction system
- [ ] Implement volume landmark integration
- [ ] Create progression visualisation

### Task 3: SFR Calculator System
**Estimated Time**: 10 hours
- [ ] Implement SFR calculation algorithms
- [ ] Create exercise effectiveness tracking
- [ ] Build ranking and recommendation system
- [ ] Add personalised SFR profiles
- [ ] Implement tier classification
- [ ] Create comparative analysis tools

### Task 4: Feedback Collection System
**Estimated Time**: 8 hours
- [ ] Design intuitive feedback interfaces
- [ ] Implement data validation and storage
- [ ] Create feedback reminder system
- [ ] Add bulk feedback entry options
- [ ] Implement feedback analytics
- [ ] Create feedback quality scoring

### Task 5: Algorithm Integration
**Estimated Time**: 10 hours
- [ ] Integrate algorithms with workout system
- [ ] Create real-time recommendation engine
- [ ] Implement algorithm configuration system
- [ ] Add performance monitoring
- [ ] Create algorithm testing framework
- [ ] Implement fallback mechanisms

### Task 6: Testing & Validation
**Estimated Time**: 12 hours
- [ ] Create algorithm unit tests
- [ ] Implement integration tests
- [ ] Add performance benchmarks
- [ ] Create validation against RP standards
- [ ] Implement user acceptance tests
- [ ] Add algorithm accuracy monitoring

## Quality Gates

### Algorithm Accuracy
- [ ] MEV calculations match RP book within 5%
- [ ] Set progression logic validated with test data
- [ ] SFR calculations consistent with RP methodology
- [ ] Recommendation confidence > 80%
- [ ] Algorithm performance < 10ms
- [ ] Edge cases handled gracefully

### Data Quality
- [ ] Feedback validation prevents invalid entries
- [ ] Data consistency across all algorithms
- [ ] Historical data migration successful
- [ ] Algorithm configuration properly versioned
- [ ] Performance metrics accurately tracked

### User Experience
- [ ] Feedback collection intuitive and fast
- [ ] Recommendations clearly explained
- [ ] Algorithm transparency maintained
- [ ] User can override recommendations
- [ ] Progress tracking visually clear

## Dependencies

### New Dependencies
```
# Scientific computing
numpy>=1.24.0                # Numerical computations
scipy>=1.10.0                # Statistical functions

# Machine learning (optional)
scikit-learn>=1.3.0          # Algorithm validation and tuning

# Data analysis
pandas>=2.0.0                # Data manipulation and analysis

# Mathematical operations
sympy>=1.12                  # Symbolic mathematics (optional)
```

## Mathematical Models

### MEV Calculation Formula
```
Total Stimulus Score = Mind-Muscle Connection + Pump + Muscle Disruption

Recommendation Logic:
- Score 0-1: increase_2_4_sets
- Score 2-3: progress_normally  
- Score 4-6: maintain_volume
- Score 7-9: reduce_volume
```

### Set Progression Formula
```
Readiness Score = (Performance + Recovery - Soreness) / 3

Volume Change = Base Increment × Readiness Score × Individual Factor

Where:
- Base Increment = 1-3 sets per muscle group
- Individual Factor = 0.5-1.5 based on training history
```

### SFR Calculation Formula
```
SFR = Raw Stimulus Magnitude / Total Fatigue Score

Tier Classification:
- Tier 1 (Excellent): SFR > 2.0
- Tier 2 (Good): SFR 1.5-2.0
- Tier 3 (Average): SFR 1.0-1.5
- Tier 4 (Poor): SFR 0.5-1.0
- Tier 5 (Avoid): SFR < 0.5
```

## Risks & Mitigation

### Algorithm Risks
1. **Accuracy Concerns**: Validate against RP reference book extensively
2. **Individual Variation**: Implement personalisation and learning
3. **Data Quality**: Robust validation and user education
4. **Performance**: Optimise calculations and implement caching

### User Adoption Risks
1. **Feedback Fatigue**: Make feedback collection quick and intuitive
2. **Trust in Algorithms**: Provide transparency and override options
3. **Complexity**: Start simple, add sophistication gradually

## Definition of Done

### Phase 3 Complete When:
- [ ] All core algorithms implemented and tested
- [ ] MEV recommendations accurate per RP methodology
- [ ] Set progression logic working with real user data
- [ ] SFR calculations validated and functional
- [ ] Feedback system collecting comprehensive data
- [ ] Algorithm performance meets benchmarks
- [ ] User interface for feedback intuitive
- [ ] Integration with Phase 2 workout system complete
- [ ] Algorithm accuracy monitoring in place

**Next Phase**: Phase 4 - Mesocycle Planning

## User Stories

### Epic: Intelligent Volume Recommendations
**As a user, I want intelligent volume recommendations so that I can optimise my training stimulus.**

#### User Story 1: MEV Feedback
- **Given** I complete a workout
- **When** I provide stimulus feedback
- **Then** I should receive personalised volume recommendations
- **And** the system should explain the reasoning
- **And** I should see confidence in the recommendation

#### User Story 2: Weekly Progression
- **Given** I complete a training week
- **When** I provide recovery feedback
- **Then** the system should recommend volume changes
- **And** predict when I might need a deload
- **And** track my progression over time

### Epic: Exercise Optimisation
**As a user, I want to select the most effective exercises so that I can maximise my training efficiency.**

#### User Story 3: SFR Analysis
- **Given** I want to choose exercises
- **When** I view exercise recommendations
- **Then** I should see exercises ranked by effectiveness
- **And** understand why certain exercises are recommended
- **And** see how exercises compare for my goals
