# Phase 4: Mesocycle Planning - Detailed PRD

## Overview
Build a comprehensive mesocycle planning system that enables users to create structured training programs with automated volume progression, deload scheduling, and template-based program design following RP periodisation principles.

## Status: 🔮 PLANNED
**Start Date**: TBD (After Phase 3 completion)
**Target Completion**: TBD
**Current Progress**: 0% (Awaiting Phase 3 completion)

## Objectives

### Primary Goals
1. **Mesocycle Creation**: Complete system for planning 4-8 week training blocks
2. **Training Templates**: Pre-built RP-certified programs (Black Adam, Superman, etc.)
3. **Volume Landmarks**: MEV/MAV/MRV configuration per muscle group
4. **Deload Scheduling**: Automatic and manual deload timing and implementation
5. **Program Customisation**: Flexible modification of templates and custom program creation

### Success Criteria
- [ ] Users can create custom mesocycles with volume progression
- [ ] Training templates available and fully functional
- [ ] Volume progression working automatically from MEV to MAV
- [ ] Deload scheduling automatic based on fatigue indicators
- [ ] Template customisation preserves RP principles
- [ ] Mesocycle analytics and progress tracking functional
- [ ] Program sharing and copying capabilities
- [ ] Integration with Phase 3 algorithms seamless

## Technical Requirements

### Database Schema Extensions

#### Mesocycles Table
```sql
CREATE TABLE mesocycles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_id UUID REFERENCES training_templates(id),
    start_date DATE NOT NULL,
    end_date DATE,
    planned_weeks INTEGER NOT NULL CHECK (planned_weeks BETWEEN 4 AND 12),
    current_week INTEGER DEFAULT 1,
    status mesocycle_status_enum DEFAULT 'planning',
    goal mesocycle_goal_enum NOT NULL,
    experience_level experience_level_enum NOT NULL,
    training_frequency INTEGER CHECK (training_frequency BETWEEN 3 AND 7),
    mev_multiplier DECIMAL(3,2) DEFAULT 1.0 CHECK (mev_multiplier BETWEEN 0.5 AND 2.0),
    mav_multiplier DECIMAL(3,2) DEFAULT 1.0 CHECK (mav_multiplier BETWEEN 0.5 AND 2.0),
    mrv_multiplier DECIMAL(3,2) DEFAULT 1.0 CHECK (mrv_multiplier BETWEEN 0.5 AND 2.0),
    deload_week INTEGER,
    auto_deload BOOLEAN DEFAULT TRUE,
    notes TEXT,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

CREATE INDEX idx_mesocycles_user ON mesocycles(user_id);
CREATE INDEX idx_mesocycles_status ON mesocycles(status);
CREATE INDEX idx_mesocycles_template ON mesocycles(template_id);
CREATE INDEX idx_mesocycles_dates ON mesocycles(start_date, end_date);
```

#### Training Templates Table
```sql
CREATE TABLE training_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    author VARCHAR(255),
    category template_category_enum NOT NULL,
    experience_level experience_level_enum NOT NULL,
    training_frequency INTEGER CHECK (training_frequency BETWEEN 3 AND 7),
    duration_weeks INTEGER CHECK (duration_weeks BETWEEN 4 AND 12),
    primary_goal template_goal_enum NOT NULL,
    tags TEXT[],
    is_official BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    rating DECIMAL(2,1) CHECK (rating BETWEEN 1.0 AND 5.0),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_templates_category ON training_templates(category);
CREATE INDEX idx_templates_level ON training_templates(experience_level);
CREATE INDEX idx_templates_goal ON training_templates(primary_goal);
CREATE INDEX idx_templates_rating ON training_templates(rating DESC);
```

#### Template Days Table
```sql
CREATE TABLE template_days (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id UUID REFERENCES training_templates(id) ON DELETE CASCADE,
    day_number INTEGER NOT NULL CHECK (day_number BETWEEN 1 AND 7),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    muscle_groups muscle_group_enum[] NOT NULL,
    estimated_duration_minutes INTEGER,
    difficulty_rating INTEGER CHECK (difficulty_rating BETWEEN 1 AND 5),
    sort_order INTEGER DEFAULT 0,
    is_rest_day BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_template_days_template ON template_days(template_id);
CREATE INDEX idx_template_days_number ON template_days(day_number);
```

#### Template Exercises Table
```sql
CREATE TABLE template_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_day_id UUID REFERENCES template_days(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    exercise_order INTEGER NOT NULL,
    target_sets_min INTEGER NOT NULL,
    target_sets_max INTEGER NOT NULL,
    target_reps_min INTEGER,
    target_reps_max INTEGER,
    target_rir_min INTEGER CHECK (target_rir_min BETWEEN 0 AND 5),
    target_rir_max INTEGER CHECK (target_rir_max BETWEEN 0 AND 5),
    rest_seconds INTEGER,
    progression_scheme progression_scheme_enum,
    notes TEXT,
    is_optional BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_template_exercises_day ON template_exercises(template_day_id);
CREATE INDEX idx_template_exercises_order ON template_exercises(exercise_order);
```

#### Volume Landmarks Table
```sql
CREATE TABLE volume_landmarks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    muscle_group muscle_group_enum NOT NULL,
    mev_sets INTEGER NOT NULL CHECK (mev_sets >= 0),
    mav_sets INTEGER NOT NULL CHECK (mav_sets >= mev_sets),
    mrv_sets INTEGER NOT NULL CHECK (mrv_sets >= mav_sets),
    mv_sets INTEGER NOT NULL CHECK (mv_sets >= 0 AND mv_sets <= mev_sets),
    confidence_level confidence_level_enum DEFAULT 'estimated',
    last_validated DATE,
    source landmark_source_enum DEFAULT 'user_input',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, muscle_group)
);

CREATE INDEX idx_volume_landmarks_user ON volume_landmarks(user_id);
CREATE INDEX idx_volume_landmarks_muscle ON volume_landmarks(muscle_group);
```

#### Mesocycle Weeks Table
```sql
CREATE TABLE mesocycle_weeks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mesocycle_id UUID REFERENCES mesocycles(id) ON DELETE CASCADE,
    week_number INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    week_type week_type_enum DEFAULT 'accumulation',
    volume_multiplier DECIMAL(3,2) DEFAULT 1.0,
    intensity_multiplier DECIMAL(3,2) DEFAULT 1.0,
    planned_sets_per_muscle JSONB, -- {muscle_group: sets_count}
    actual_sets_per_muscle JSONB,
    completion_percentage DECIMAL(5,2) DEFAULT 0.0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(mesocycle_id, week_number)
);

CREATE INDEX idx_mesocycle_weeks_mesocycle ON mesocycle_weeks(mesocycle_id);
CREATE INDEX idx_mesocycle_weeks_dates ON mesocycle_weeks(start_date, end_date);
```

### Enums and Types
```sql
CREATE TYPE mesocycle_status_enum AS ENUM (
    'planning', 'active', 'paused', 'completed', 'cancelled'
);

CREATE TYPE mesocycle_goal_enum AS ENUM (
    'hypertrophy', 'strength', 'endurance', 'general_fitness', 'cutting', 'bulking'
);

CREATE TYPE experience_level_enum AS ENUM (
    'beginner', 'intermediate', 'advanced', 'expert'
);

CREATE TYPE template_category_enum AS ENUM (
    'hypertrophy', 'strength', 'powerlifting', 'bodybuilding', 
    'general_fitness', 'sport_specific', 'rehabilitation'
);

CREATE TYPE template_goal_enum AS ENUM (
    'muscle_gain', 'strength_gain', 'fat_loss', 'performance', 'maintenance'
);

CREATE TYPE week_type_enum AS ENUM (
    'accumulation', 'intensification', 'deload', 'taper', 'test'
);

CREATE TYPE progression_scheme_enum AS ENUM (
    'linear', 'double_progression', 'wave_loading', 'autoregulated'
);

CREATE TYPE confidence_level_enum AS ENUM (
    'estimated', 'calculated', 'validated', 'expert_set'
);

CREATE TYPE landmark_source_enum AS ENUM (
    'user_input', 'algorithm_calculated', 'template_default', 'coach_set'
);
```

## Core System Components

### Mesocycle Planning Engine
```python
class MesocyclePlanner:
    """
    Core engine for creating and managing mesocycles.
    
    Handles volume progression, deload scheduling, and template application
    while maintaining RP periodisation principles.
    """
    
    def create_mesocycle(
        self,
        user_id: UUID,
        template_id: Optional[UUID],
        config: MesocycleConfig
    ) -> Mesocycle:
        """Create new mesocycle from template or custom configuration."""
        
    def calculate_volume_progression(
        self,
        mesocycle: Mesocycle,
        volume_landmarks: Dict[MuscleGroup, VolumeLandmarks]
    ) -> List[WeeklyVolume]:
        """Calculate weekly volume progression from MEV to MAV."""
        
    def schedule_deload(
        self,
        mesocycle: Mesocycle,
        fatigue_indicators: FatigueIndicators
    ) -> DeloadSchedule:
        """Determine optimal deload timing and implementation."""
```

### Template Management System
```python
class TemplateManager:
    """
    Manages training templates and customisation.
    
    Provides pre-built RP programs and allows user customisation
    while preserving scientific principles.
    """
    
    def get_recommended_templates(
        self,
        user_profile: UserProfile,
        goals: List[TrainingGoal]
    ) -> List[TemplateRecommendation]:
        """Get templates recommended for user's profile and goals."""
        
    def customise_template(
        self,
        template_id: UUID,
        customisations: TemplateCustomisation
    ) -> CustomTemplate:
        """Apply user customisations while preserving RP principles."""
        
    def validate_template(
        self,
        template: TrainingTemplate
    ) -> TemplateValidation:
        """Validate template against RP methodology."""
```

### Volume Landmark Calculator
```python
class VolumeLandmarkCalculator:
    """
    Calculates personalised volume landmarks for each muscle group.
    
    Uses training history, individual response patterns, and RP guidelines
    to estimate MEV, MAV, and MRV for optimal programming.
    """
    
    def calculate_landmarks(
        self,
        user_id: UUID,
        muscle_group: MuscleGroup,
        training_history: TrainingHistory
    ) -> VolumeLandmarks:
        """Calculate personalised volume landmarks."""
        
    def update_landmarks_from_data(
        self,
        user_id: UUID,
        mesocycle_data: MesocycleData
    ) -> Dict[MuscleGroup, VolumeLandmarks]:
        """Update landmarks based on mesocycle performance data."""
```

## API Endpoints (Phase 4)

### Mesocycle Management
```
GET    /api/v1/mesocycles                     # List user's mesocycles
POST   /api/v1/mesocycles                     # Create new mesocycle
GET    /api/v1/mesocycles/{mesocycle_id}      # Get mesocycle details
PUT    /api/v1/mesocycles/{mesocycle_id}      # Update mesocycle
DELETE /api/v1/mesocycles/{mesocycle_id}      # Delete mesocycle
POST   /api/v1/mesocycles/{mesocycle_id}/start # Start mesocycle
POST   /api/v1/mesocycles/{mesocycle_id}/pause # Pause mesocycle
POST   /api/v1/mesocycles/{mesocycle_id}/complete # Complete mesocycle
GET    /api/v1/mesocycles/{mesocycle_id}/progress # Get progress analytics
```

### Template Management
```
GET    /api/v1/templates                      # List available templates
GET    /api/v1/templates/{template_id}        # Get template details
GET    /api/v1/templates/recommended          # Get recommended templates
POST   /api/v1/templates/{template_id}/customise # Customise template
POST   /api/v1/templates                      # Create custom template
GET    /api/v1/templates/categories           # Get template categories
```

### Volume Landmarks
```
GET    /api/v1/volume-landmarks               # Get user's volume landmarks
PUT    /api/v1/volume-landmarks               # Update volume landmarks
POST   /api/v1/volume-landmarks/calculate     # Calculate landmarks
GET    /api/v1/volume-landmarks/recommendations # Get landmark recommendations
```

### Deload Management
```
GET    /api/v1/mesocycles/{mesocycle_id}/deload-status # Check deload status
POST   /api/v1/mesocycles/{mesocycle_id}/deload # Schedule manual deload
GET    /api/v1/deload/recommendations          # Get deload recommendations
```

## Implementation Tasks

### Task 1: Mesocycle Core System
**Estimated Time**: 16 hours
- [ ] Implement mesocycle data models and repositories
- [ ] Create mesocycle planning engine
- [ ] Build volume progression algorithms
- [ ] Add mesocycle lifecycle management
- [ ] Implement mesocycle validation rules
- [ ] Create mesocycle analytics system

### Task 2: Training Templates System
**Estimated Time**: 14 hours
- [ ] Implement template data models
- [ ] Create template management system
- [ ] Build template customisation engine
- [ ] Add official RP templates (Black Adam, Superman, etc.)
- [ ] Implement template validation and rating
- [ ] Create template recommendation engine

### Task 3: Volume Landmarks Management
**Estimated Time**: 10 hours
- [ ] Implement volume landmarks calculator
- [ ] Create landmark personalisation algorithms
- [ ] Build landmark validation system
- [ ] Add landmark update mechanisms
- [ ] Implement landmark confidence scoring
- [ ] Create landmark recommendation system

### Task 4: Deload Scheduling System
**Estimated Time**: 8 hours
- [ ] Implement deload prediction algorithms
- [ ] Create automatic deload scheduling
- [ ] Build manual deload override system
- [ ] Add deload implementation strategies
- [ ] Implement deload effectiveness tracking
- [ ] Create deload recommendation system

### Task 5: Program Customisation Engine
**Estimated Time**: 12 hours
- [ ] Build template customisation interface
- [ ] Implement exercise substitution system
- [ ] Create program modification validation
- [ ] Add custom program creation tools
- [ ] Implement program sharing capabilities
- [ ] Create program comparison tools

### Task 6: Integration & Testing
**Estimated Time**: 10 hours
- [ ] Integrate with Phase 3 algorithms
- [ ] Create comprehensive test suite
- [ ] Add performance optimisation
- [ ] Implement data migration tools
- [ ] Create user acceptance tests
- [ ] Add monitoring and analytics

## Quality Gates

### Functional Requirements
- [ ] Mesocycle creation and management working
- [ ] Template system fully functional
- [ ] Volume progression algorithms accurate
- [ ] Deload scheduling working automatically
- [ ] Program customisation preserves RP principles
- [ ] Integration with algorithms seamless

### Data Integrity
- [ ] Volume landmarks validation working
- [ ] Template data consistency maintained
- [ ] Mesocycle progression data accurate
- [ ] User customisations properly stored
- [ ] Historical data preserved correctly

### Performance Requirements
- [ ] Mesocycle creation < 500ms
- [ ] Template loading < 200ms
- [ ] Volume calculations < 100ms
- [ ] Deload predictions < 50ms
- [ ] Database queries optimised
- [ ] Caching implemented effectively

## Official RP Templates

### Black Adam Template
- **Target**: Intermediate hypertrophy
- **Frequency**: 6 days/week
- **Duration**: 6 weeks + deload
- **Focus**: High volume, moderate intensity

### Superman Template  
- **Target**: Advanced hypertrophy
- **Frequency**: 5 days/week
- **Duration**: 8 weeks + deload
- **Focus**: Progressive overload, high SFR exercises

### Wonder Woman Template
- **Target**: Female-specific hypertrophy
- **Frequency**: 4-5 days/week
- **Duration**: 6 weeks + deload
- **Focus**: Lower body emphasis, glute development

### Captain America Template
- **Target**: Strength and hypertrophy
- **Frequency**: 4 days/week
- **Duration**: 8 weeks + deload
- **Focus**: Compound movements, strength progression

## Dependencies

### New Dependencies
```
# Date/time handling
python-dateutil>=2.8.0        # Advanced date calculations

# Data validation
marshmallow>=3.20.0           # Complex data serialisation

# Mathematical operations
numpy>=1.24.0                 # Volume progression calculations

# Template processing
jinja2>=3.1.0                 # Template rendering and customisation
```

## Risks & Mitigation

### Technical Risks
1. **Algorithm Complexity**: Start with simplified versions, add sophistication gradually
2. **Data Consistency**: Implement robust validation and transaction management
3. **Performance**: Optimise queries and implement caching strategies
4. **Template Quality**: Validate all templates against RP principles

### User Experience Risks
1. **Complexity Overwhelm**: Provide guided setup and sensible defaults
2. **Template Rigidity**: Allow customisation while preserving principles
3. **Progress Tracking**: Make progression visible and motivating

## Definition of Done

### Phase 4 Complete When:
- [ ] Users can create and manage mesocycles
- [ ] Training templates available and functional
- [ ] Volume progression working automatically
- [ ] Deload scheduling operational
- [ ] Program customisation preserves RP principles
- [ ] Integration with Phase 3 algorithms complete
- [ ] Template library includes official RP programs
- [ ] User testing completed successfully
- [ ] Performance benchmarks met

**Next Phase**: Phase 5 - Progress Analytics

## User Stories

### Epic: Mesocycle Planning
**As a user, I want to create structured training programs so that I can follow evidence-based periodisation.**

#### User Story 1: Template Selection
- **Given** I want to start a new mesocycle
- **When** I browse available templates
- **Then** I should see templates categorised by experience and goals
- **And** I should see detailed descriptions and requirements
- **And** I should get personalised recommendations

#### User Story 2: Volume Configuration
- **Given** I have selected a template
- **When** I configure volume landmarks
- **Then** I should be able to set MEV, MAV, and MRV per muscle group
- **And** the system should validate that MEV < MAV < MRV
- **And** I should receive recommendations based on my history

### Epic: Program Customisation
**As a user, I want to customise training programs so that they fit my specific needs and preferences.**

#### User Story 3: Exercise Substitution
- **Given** I want to modify a template
- **When** I substitute exercises
- **Then** the system should suggest equivalent alternatives
- **And** maintain the same muscle group targeting
- **And** preserve the training stimulus
