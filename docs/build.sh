#!/bin/bash

# Forge Protocol Documentation Build Script
# This script builds the Sphinx documentation and provides various build options

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${DOCS_DIR}/_build"
SOURCE_DIR="${DOCS_DIR}"
VENV_DIR="${DOCS_DIR}/.venv"

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  Forge Protocol Documentation Builder${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_info "Using Python $python_version"
}

# Setup virtual environment
setup_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        print_info "Creating virtual environment..."
        python3 -m venv "$VENV_DIR"
        print_success "Virtual environment created"
    fi
    
    print_info "Activating virtual environment..."
    source "$VENV_DIR/bin/activate"
    
    print_info "Installing/updating dependencies..."
    pip install --upgrade pip
    pip install -r "$DOCS_DIR/requirements.txt"
    print_success "Dependencies installed"
}

# Clean build directory
clean_build() {
    if [ -d "$BUILD_DIR" ]; then
        print_info "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
        print_success "Build directory cleaned"
    fi
}

# Generate API documentation
generate_api_docs() {
    print_info "Generating API documentation..."
    
    # Check if app directory exists
    if [ -d "${DOCS_DIR}/../app" ]; then
        sphinx-apidoc -o "${DOCS_DIR}/api" "${DOCS_DIR}/../app" --force --module-first
        print_success "API documentation generated"
    else
        print_warning "App directory not found, skipping API documentation generation"
    fi
}

# Build HTML documentation
build_html() {
    print_info "Building HTML documentation..."
    
    sphinx-build -b html "$SOURCE_DIR" "$BUILD_DIR/html" -W --keep-going
    
    if [ $? -eq 0 ]; then
        print_success "HTML documentation built successfully"
        print_info "Documentation available at: $BUILD_DIR/html/index.html"
    else
        print_error "HTML build failed"
        exit 1
    fi
}

# Build PDF documentation
build_pdf() {
    print_info "Building PDF documentation..."
    
    if command -v rst2pdf &> /dev/null; then
        sphinx-build -b pdf "$SOURCE_DIR" "$BUILD_DIR/pdf"
        print_success "PDF documentation built successfully"
    else
        print_warning "rst2pdf not installed, skipping PDF build"
        print_info "Install with: pip install rst2pdf"
    fi
}

# Check for broken links
check_links() {
    print_info "Checking for broken links..."
    
    sphinx-build -b linkcheck "$SOURCE_DIR" "$BUILD_DIR/linkcheck"
    
    if [ $? -eq 0 ]; then
        print_success "Link check completed"
    else
        print_warning "Some links may be broken, check the linkcheck report"
    fi
}

# Spell check
spell_check() {
    print_info "Running spell check..."
    
    if pip list | grep -q sphinxcontrib-spelling; then
        sphinx-build -b spelling "$SOURCE_DIR" "$BUILD_DIR/spelling"
        print_success "Spell check completed"
    else
        print_warning "sphinxcontrib-spelling not installed, skipping spell check"
        print_info "Install with: pip install sphinxcontrib-spelling"
    fi
}

# Coverage report
coverage_report() {
    print_info "Generating coverage report..."
    
    sphinx-build -b coverage "$SOURCE_DIR" "$BUILD_DIR/coverage"
    print_success "Coverage report generated"
}

# Live reload server
serve_docs() {
    print_info "Starting live reload server..."
    print_info "Documentation will be available at http://localhost:8080"
    print_info "Press Ctrl+C to stop the server"
    
    sphinx-autobuild "$SOURCE_DIR" "$BUILD_DIR/html" \
        --host 0.0.0.0 \
        --port 8080 \
        --open-browser \
        --watch "${DOCS_DIR}/../app" \
        --ignore "${DOCS_DIR}/_build" \
        --ignore "${DOCS_DIR}/.venv"
}

# Show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  html        Build HTML documentation (default)"
    echo "  pdf         Build PDF documentation"
    echo "  clean       Clean build directory"
    echo "  api         Generate API documentation"
    echo "  linkcheck   Check for broken links"
    echo "  spelling    Run spell check"
    echo "  coverage    Generate coverage report"
    echo "  serve       Start live reload server"
    echo "  all         Build all formats and run checks"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0              # Build HTML documentation"
    echo "  $0 html         # Build HTML documentation"
    echo "  $0 serve        # Start live reload server"
    echo "  $0 all          # Build everything and run checks"
    echo
}

# Main execution
main() {
    print_header
    
    # Parse command line arguments
    COMMAND=${1:-html}
    
    case $COMMAND in
        html)
            check_python
            setup_venv
            generate_api_docs
            build_html
            ;;
        pdf)
            check_python
            setup_venv
            generate_api_docs
            build_pdf
            ;;
        clean)
            clean_build
            ;;
        api)
            check_python
            setup_venv
            generate_api_docs
            ;;
        linkcheck)
            check_python
            setup_venv
            check_links
            ;;
        spelling)
            check_python
            setup_venv
            spell_check
            ;;
        coverage)
            check_python
            setup_venv
            coverage_report
            ;;
        serve)
            check_python
            setup_venv
            generate_api_docs
            serve_docs
            ;;
        all)
            check_python
            setup_venv
            clean_build
            generate_api_docs
            build_html
            build_pdf
            check_links
            spell_check
            coverage_report
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $COMMAND"
            echo
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
