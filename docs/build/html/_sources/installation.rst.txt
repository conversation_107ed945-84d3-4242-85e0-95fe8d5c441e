Installation Guide
==================

This guide covers installation and setup of the Forge Protocol development environment using multiple approaches.

.. contents:: Table of Contents
   :local:
   :depth: 2

Quick Start (Recommended)
-------------------------

The fastest way to get started using Nix:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

   # Enter Nix development environment
   nix develop

   # Install Python dependencies
   pip install -r requirements/dev.txt

   # Start services
   docker compose -f docker/docker-compose.yml up -d

   # Run migrations
   python -m alembic upgrade head

   # Start development server
   python -m uvicorn app.main:app --reload

The API will be available at http://localhost:8000

Nix Development Environment
---------------------------

**Prerequisites:**

* Nix package manager (recommended)
* direnv (optional, for automatic environment loading)

**Installation:**

1. **Install Nix** (if not already installed):

   .. code-block:: bash

      # Multi-user installation (recommended)
      sh <(curl -L https://nixos.org/nix/install) --daemon

      # Enable flakes (required)
      echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf

2. **Install direnv** (optional but recommended):

   .. code-block:: bash

      # On macOS
      brew install direnv

      # On Ubuntu/Debian
      sudo apt install direnv

      # Add to shell (bash/zsh)
      echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
      # or for zsh
      echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc

3. **Setup automatic environment loading**:

   .. code-block:: bash

      # Allow direnv in the project directory
      cd forge-protocol
      direnv allow

**Benefits of Nix Environment:**

* **Reproducible**: Identical environment across all machines
* **Isolated**: No global package pollution
* **Complete**: All dependencies included (Python, PostgreSQL, Redis, etc.)
* **Automatic**: Environment loads when entering directory (with direnv)

Manual Installation
-------------------

For systems without Nix support:

**Prerequisites:**

* Python 3.11 or higher
* PostgreSQL 15 or higher
* Redis 6 or higher
* Docker and Docker Compose

**System Dependencies:**

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3.11 python3.11-venv python3-pip postgresql-15 redis-server

   # macOS (using Homebrew)
   brew install python@3.11 postgresql@15 redis

   # Arch Linux
   sudo pacman -S python postgresql redis

**Python Environment Setup:**

.. code-block:: bash

   # Create virtual environment
   python3.11 -m venv venv
   source venv/bin/activate

   # Upgrade pip
   pip install --upgrade pip

   # Install dependencies
   pip install -r requirements/dev.txt

**Database Setup:**

.. code-block:: bash

   # Start PostgreSQL service
   sudo systemctl start postgresql  # Linux
   brew services start postgresql@15  # macOS

   # Create database and user
   sudo -u postgres psql
   CREATE DATABASE forge_protocol;
   CREATE USER forge_user WITH PASSWORD 'forge_password';
   GRANT ALL PRIVILEGES ON DATABASE forge_protocol TO forge_user;
   \q

   # Start Redis service
   sudo systemctl start redis  # Linux
   brew services start redis  # macOS

**Environment Configuration:**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env

   # Edit configuration
   nano .env

Example `.env` file:

.. code-block:: bash

   # Database
   DATABASE_URL=postgresql+asyncpg://forge_user:forge_password@localhost:5432/forge_protocol
   
   # Security
   SECRET_KEY=your-secret-key-minimum-32-characters-long
   
   # Environment
   ENVIRONMENT=development
   DEBUG=true
   
   # Redis
   REDIS_URL=redis://localhost:6379/0

Docker Development Environment
------------------------------

Using Docker for isolated development:

**Prerequisites:**

* Docker 20.10 or higher
* Docker Compose 2.0 or higher

**Setup:**

.. code-block:: bash

   # Clone repository
   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

   # Start all services
   docker compose -f docker/docker-compose.yml up -d

   # Check service status
   docker compose -f docker/docker-compose.yml ps

   # View logs
   docker compose -f docker/docker-compose.yml logs -f

**Services Included:**

* **PostgreSQL 15**: Primary database
* **Redis 7**: Caching and session storage
* **pgAdmin**: Database administration interface

**Database Migration:**

.. code-block:: bash

   # Run migrations
   docker compose -f docker/docker-compose.yml exec app python -m alembic upgrade head

   # Create new migration
   docker compose -f docker/docker-compose.yml exec app python -m alembic revision --autogenerate -m "Description"

Development Server
------------------

**Start Development Server:**

.. code-block:: bash

   # With auto-reload (recommended for development)
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

   # With specific configuration
   python -m uvicorn app.main:app --reload --log-level debug

   # Production-like mode
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

**API Documentation:**

Once the server is running, access:

* **Interactive API docs**: http://localhost:8000/docs
* **Alternative docs**: http://localhost:8000/redoc
* **OpenAPI schema**: http://localhost:8000/openapi.json

Testing Installation
---------------------

**Verify Installation:**

.. code-block:: bash

   # Run health check
   curl http://localhost:8000/api/v1/health

   # Expected response:
   {
     "status": "healthy",
     "timestamp": "2025-01-13T12:00:00Z",
     "version": "1.0.0"
   }

**Run Test Suite:**

.. code-block:: bash

   # Quick test
   python -m pytest tests/unit/domain/test_user_entity.py -v

   # Full test suite
   ./scripts/run-comprehensive-tests.sh

   # Specific test categories
   python -m pytest -m unit        # Unit tests
   python -m pytest -m integration # Integration tests
   python -m pytest -m security    # Security tests

IDE Configuration
-----------------

**Visual Studio Code:**

Recommended extensions:

.. code-block:: json

   {
     "recommendations": [
       "ms-python.python",
       "ms-python.black-formatter",
       "ms-python.isort",
       "ms-python.flake8",
       "ms-python.mypy-type-checker",
       "bradlc.vscode-tailwindcss",
       "ms-vscode.vscode-json"
     ]
   }

**PyCharm:**

1. Open project directory
2. Configure Python interpreter to use virtual environment
3. Enable code formatting with Black
4. Configure import sorting with isort

**Vim/Neovim:**

Install language server support:

.. code-block:: bash

   # Install Python language server
   pip install python-lsp-server[all]

   # Configure with your preferred plugin manager

Troubleshooting
---------------

**Common Issues:**

1. **Database Connection Error:**

   .. code-block:: bash

      # Check PostgreSQL is running
      sudo systemctl status postgresql
      
      # Check connection
      psql -h localhost -U forge_user -d forge_protocol

2. **Permission Denied:**

   .. code-block:: bash

      # Fix file permissions
      chmod +x scripts/*.sh
      
      # Fix Python virtual environment
      source venv/bin/activate

3. **Port Already in Use:**

   .. code-block:: bash

      # Find process using port 8000
      lsof -i :8000
      
      # Kill process
      kill -9 <PID>

4. **Migration Errors:**

   .. code-block:: bash

      # Reset database (development only)
      python -m alembic downgrade base
      python -m alembic upgrade head

5. **Nix Environment Issues:**

   .. code-block:: bash

      # Rebuild Nix environment
      nix develop --rebuild
      
      # Clear Nix cache
      nix-collect-garbage

**Getting Help:**

* Check the logs: ``docker compose logs -f``
* Review configuration: ``cat .env``
* Validate environment: ``python -c "import app; print('OK')"``
* Test database: ``python -c "from app.infrastructure.database import db_manager; print('DB OK')"``

**Performance Optimization:**

.. code-block:: bash

   # Enable PostgreSQL performance monitoring
   echo "shared_preload_libraries = 'pg_stat_statements'" >> postgresql.conf
   
   # Restart PostgreSQL
   sudo systemctl restart postgresql

Next Steps
----------

After successful installation:

1. **Explore the API**: Visit http://localhost:8000/docs
2. **Run Tests**: Execute ``./scripts/run-comprehensive-tests.sh``
3. **Read Documentation**: Browse the full documentation
4. **Start Development**: Begin implementing new features

The installation provides a complete development environment optimized for mobile-first hypertrophy training application development.
