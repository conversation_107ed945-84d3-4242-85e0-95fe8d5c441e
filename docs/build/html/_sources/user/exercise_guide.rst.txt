Exercise Management Guide
=========================

This guide provides comprehensive instructions for managing exercises in the Forge Protocol platform, including creation, editing, approval workflows, and media management.

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

Before managing exercises, ensure you have:

1. **Valid Account**: Registered user account with appropriate permissions
2. **Authentication**: Valid JWT token for API access
3. **Permissions**: 
   - Basic users: Create and edit own exercises
   - Moderators: Approve/reject exercises
   - Admins: Full exercise management access

API Access
~~~~~~~~~~

All exercise operations require authentication:

.. code-block:: bash

   # Set your authentication token
   export AUTH_TOKEN="your-jwt-token-here"
   
   # Use in API calls
   curl -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        https://api.forgeprotocol.com/api/v1/exercises

Creating Exercises
------------------

Basic Exercise Creation
~~~~~~~~~~~~~~~~~~~~~~~

To create a new exercise, provide the required fields:

.. code-block:: bash

   curl -X POST https://api.forgeprotocol.com/api/v1/exercises \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Barbell Bench Press",
       "description": "Classic compound chest exercise using a barbell",
       "primary_muscle_group": "chest",
       "secondary_muscle_groups": ["triceps", "shoulders"],
       "movement_pattern": "push",
       "equipment_required": ["barbell"],
       "difficulty_level": "intermediate"
     }'

**Required Fields:**

- ``name``: Unique exercise name
- ``primary_muscle_group``: Main muscle targeted
- ``movement_pattern``: Type of movement
- ``difficulty_level``: Beginner, intermediate, or advanced

**Optional Fields:**

- ``description``: Detailed exercise description
- ``secondary_muscle_groups``: Additional muscles worked
- ``equipment_required``: List of required equipment
- ``form_cues``: Array of technique tips
- ``setup_instructions``: How to set up the exercise
- ``execution_steps``: Step-by-step instructions
- ``common_mistakes``: What to avoid
- ``safety_notes``: Important safety information
- ``video_url``: Primary demonstration video
- ``thumbnail_url``: Exercise thumbnail image

Comprehensive Exercise Example
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "name": "Barbell Bench Press",
     "description": "The barbell bench press is a fundamental compound exercise that primarily targets the chest muscles while also engaging the triceps and shoulders. It's considered one of the 'big three' powerlifting movements.",
     "primary_muscle_group": "chest",
     "secondary_muscle_groups": ["triceps", "shoulders"],
     "movement_pattern": "push",
     "equipment_required": ["barbell", "bench"],
     "difficulty_level": "intermediate",
     "form_cues": [
       "Keep shoulder blades retracted and depressed",
       "Maintain a slight arch in the lower back",
       "Control the descent - don't bounce off chest",
       "Drive through the heels",
       "Keep wrists straight and elbows at 45-degree angle"
     ],
     "setup_instructions": "Lie on the bench with eyes directly under the barbell. Feet should be flat on the floor. Grip the bar with hands slightly wider than shoulder-width apart.",
     "execution_steps": [
       "Unrack the bar with straight arms directly over the chest",
       "Lower the bar in a controlled manner to the chest",
       "Pause briefly when the bar touches the chest",
       "Press the bar back to the starting position",
       "Maintain tension throughout the entire movement"
     ],
     "common_mistakes": [
       "Bouncing the bar off the chest",
       "Flaring the elbows too wide (90 degrees)",
       "Not using full range of motion",
       "Lifting the head off the bench",
       "Using too much arch in the back"
     ],
     "safety_notes": "Always use a spotter when lifting heavy weights. Ensure the safety bars are set at an appropriate height. Never train to failure without a spotter.",
     "version_notes": "Initial creation with comprehensive form guidance based on RP methodology"
   }

Searching Exercises
-------------------

Basic Search
~~~~~~~~~~~~

Search for exercises using various filters:

.. code-block:: bash

   # Search by muscle group
   curl "https://api.forgeprotocol.com/api/v1/exercises?primary_muscle_group=chest" \
     -H "Authorization: Bearer $AUTH_TOKEN"
   
   # Search by equipment
   curl "https://api.forgeprotocol.com/api/v1/exercises?equipment=barbell&equipment=dumbbell" \
     -H "Authorization: Bearer $AUTH_TOKEN"
   
   # Search by difficulty
   curl "https://api.forgeprotocol.com/api/v1/exercises?difficulty_level=beginner" \
     -H "Authorization: Bearer $AUTH_TOKEN"

Advanced Search
~~~~~~~~~~~~~~~

Combine multiple filters for precise results:

.. code-block:: bash

   curl "https://api.forgeprotocol.com/api/v1/exercises?primary_muscle_group=chest&equipment=barbell&difficulty_level=intermediate&is_approved=true&limit=10&offset=0" \
     -H "Authorization: Bearer $AUTH_TOKEN"

**Available Filters:**

- ``name``: Search by exercise name (partial match)
- ``primary_muscle_group``: Filter by primary muscle
- ``movement_pattern``: Filter by movement type
- ``equipment``: Filter by required equipment (can specify multiple)
- ``difficulty_level``: Filter by difficulty
- ``is_active``: Filter by active status
- ``is_approved``: Filter by approval status
- ``current_version_only``: Only show current versions (default: true)
- ``include_deleted``: Include soft-deleted exercises (default: false)
- ``limit``: Maximum results (1-100, default: 50)
- ``offset``: Results to skip for pagination

Updating Exercises
------------------

Exercise Updates
~~~~~~~~~~~~~~~~

Update an existing exercise by providing the fields to change:

.. code-block:: bash

   curl -X PUT https://api.forgeprotocol.com/api/v1/exercises/{exercise_id} \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "description": "Updated description with more detail about muscle activation",
       "form_cues": [
         "Keep shoulder blades retracted",
         "Maintain arch in lower back", 
         "Control the descent",
         "Drive through heels for leg engagement"
       ],
       "version_notes": "Added heel drive cue for better leg engagement",
       "change_reason": "content_update"
     }'

**Versioning Options:**

- ``create_new_version=true`` (default): Creates a new version
- ``create_new_version=false``: Updates the current version in place

Version Management
~~~~~~~~~~~~~~~~~~

**View All Versions:**

.. code-block:: bash

   curl "https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/versions" \
     -H "Authorization: Bearer $AUTH_TOKEN"

**Get Specific Version:**

.. code-block:: bash

   curl "https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/version/2" \
     -H "Authorization: Bearer $AUTH_TOKEN"

**Set Current Version:**

.. code-block:: bash

   curl -X POST "https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/set-current-version/1" \
     -H "Authorization: Bearer $AUTH_TOKEN"

Approval Workflow
-----------------

Exercise Approval Process
~~~~~~~~~~~~~~~~~~~~~~~~~

All exercises follow an approval workflow:

1. **Creation**: New exercises start with status "pending"
2. **Review**: Moderators review exercise content
3. **Approval/Rejection**: Moderators approve or reject with notes
4. **Publication**: Approved exercises become publicly visible

Approving Exercises
~~~~~~~~~~~~~~~~~~~

Moderators can approve exercises:

.. code-block:: bash

   curl -X POST https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/approve \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "notes": "Exercise form and instructions are comprehensive and accurate. Approved for publication."
     }'

Rejecting Exercises
~~~~~~~~~~~~~~~~~~~

Moderators can reject exercises with feedback:

.. code-block:: bash

   curl -X POST https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/reject \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "notes": "Form cues need more detail for safety. Please add information about proper elbow positioning."
     }'

Media Management
----------------

Adding Media
~~~~~~~~~~~~

Add videos, images, or other media to exercises:

.. code-block:: bash

   curl -X POST https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/media \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "media_type": "video",
       "url": "https://cdn.example.com/videos/bench-press-form.mp4",
       "title": "Bench Press Form Demonstration",
       "description": "Complete demonstration of proper bench press technique",
       "thumbnail_url": "https://cdn.example.com/thumbnails/bench-press.jpg",
       "duration_seconds": 120,
       "width_pixels": 1920,
       "height_pixels": 1080,
       "mime_type": "video/mp4",
       "is_primary": true
     }'

**Media Types:**

- ``video``: Exercise demonstrations, form videos
- ``image``: Setup photos, form checkpoints
- ``gif``: Short movement loops
- ``audio``: Coaching cues, breathing instructions

**Primary Media:**

Each exercise can have one primary media item per type:
- One primary video
- One primary image
- One primary GIF
- One primary audio

Viewing Media
~~~~~~~~~~~~~

Get all media for an exercise:

.. code-block:: bash

   # All media
   curl "https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/media" \
     -H "Authorization: Bearer $AUTH_TOKEN"
   
   # Filter by type
   curl "https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/media?media_type=video" \
     -H "Authorization: Bearer $AUTH_TOKEN"

Updating Media
~~~~~~~~~~~~~~

Update media metadata:

.. code-block:: bash

   curl -X PUT https://api.forgeprotocol.com/api/v1/exercises/media/{media_id} \
     -H "Authorization: Bearer $AUTH_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "title": "Updated Video Title",
       "description": "Updated description with more detail",
       "is_primary": false,
       "sort_order": 1
     }'

Deleting Media
~~~~~~~~~~~~~~

Remove media from an exercise:

.. code-block:: bash

   curl -X DELETE https://api.forgeprotocol.com/api/v1/exercises/media/{media_id} \
     -H "Authorization: Bearer $AUTH_TOKEN"

Exercise Statistics
-------------------

Get comprehensive statistics for an exercise:

.. code-block:: bash

   curl "https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/statistics" \
     -H "Authorization: Bearer $AUTH_TOKEN"

**Statistics Include:**

- Version information (current version, total versions)
- Approval status and history
- Media counts by type
- Muscle group analysis
- Equipment requirements
- Audit trail summary

Soft Delete and Recovery
------------------------

Deleting Exercises
~~~~~~~~~~~~~~~~~~

Exercises are soft-deleted (can be recovered):

.. code-block:: bash

   curl -X DELETE https://api.forgeprotocol.com/api/v1/exercises/{exercise_id} \
     -H "Authorization: Bearer $AUTH_TOKEN"

Restoring Exercises
~~~~~~~~~~~~~~~~~~~

Restore a previously deleted exercise:

.. code-block:: bash

   curl -X POST https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/restore \
     -H "Authorization: Bearer $AUTH_TOKEN"

Best Practices
--------------

Exercise Creation
~~~~~~~~~~~~~~~~~

1. **Descriptive Names**: Use clear, specific exercise names
2. **Comprehensive Instructions**: Provide detailed setup and execution steps
3. **Safety First**: Always include relevant safety notes
4. **Form Cues**: Add specific technique cues for proper form
5. **Equipment Accuracy**: List all required equipment

Content Quality
~~~~~~~~~~~~~~~

1. **Evidence-Based**: Base instructions on scientific principles
2. **Clear Language**: Use simple, understandable language
3. **Progressive Detail**: Start with basics, add advanced details
4. **Visual Support**: Include high-quality media when possible
5. **Regular Updates**: Keep content current with latest research

Media Guidelines
~~~~~~~~~~~~~~~~

1. **High Quality**: Use clear, well-lit videos and images
2. **Multiple Angles**: Show exercises from different viewpoints
3. **Proper Framing**: Ensure full movement is visible
4. **Consistent Style**: Maintain consistent visual style
5. **Accessibility**: Provide alternative formats when possible

Version Management
~~~~~~~~~~~~~~~~~~

1. **Meaningful Notes**: Always include clear version notes
2. **Logical Progression**: Make incremental improvements
3. **Backup Strategy**: Keep important versions as backups
4. **Change Tracking**: Document reasons for changes
5. **Review Process**: Have changes reviewed before approval

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**403 Forbidden**
- Check authentication token validity
- Verify user permissions for the operation
- Ensure user owns the exercise (for modifications)

**409 Conflict**
- Exercise name already exists
- Primary media of same type already exists
- Business rule violation (check error message)

**422 Validation Error**
- Invalid enum values (muscle group, equipment, etc.)
- Missing required fields
- Invalid data format

**404 Not Found**
- Exercise ID doesn't exist
- Exercise UUID/version combination not found
- Media ID doesn't exist

Getting Help
~~~~~~~~~~~~

For additional support:

1. **API Documentation**: Comprehensive API reference available
2. **Error Messages**: Check detailed error responses
3. **Support Team**: Contact support for complex issues
4. **Community**: Join the Forge Protocol community forums
