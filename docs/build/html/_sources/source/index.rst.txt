Forge Protocol Documentation
============================

Welcome to the Forge Protocol documentation! This is a mobile-first hypertrophy training platform implementing evidence-based Renaissance Periodization principles with Clean Architecture and comprehensive testing.

.. image:: https://img.shields.io/badge/Python-3.11+-blue.svg
   :target: https://python.org
   :alt: Python Version

.. image:: https://img.shields.io/badge/FastAPI-Latest-green.svg
   :target: https://fastapi.tiangolo.com
   :alt: FastAPI

.. image:: https://img.shields.io/badge/PostgreSQL-15+-blue.svg
   :target: https://postgresql.org
   :alt: PostgreSQL

.. image:: https://img.shields.io/badge/Testing-Comprehensive-brightgreen.svg
   :alt: Testing

.. image:: https://img.shields.io/badge/Architecture-Clean-orange.svg
   :alt: Clean Architecture

Overview
--------

The Forge Protocol is a cutting-edge fitness application that combines:

* **Evidence-Based Training**: Renaissance Periodization principles
* **Mobile-First Design**: Optimized for mobile devices and networks
* **Clean Architecture**: Domain-driven design with clear separation of concerns
* **Comprehensive Testing**: No-mocking philosophy with real service testing
* **Modern Technology Stack**: FastAPI, PostgreSQL, Docker, Nix

Key Features
------------

* **User Authentication**: Secure JWT-based authentication with refresh tokens
* **Profile Management**: Comprehensive user profiles with training experience tracking
* **Mobile Optimization**: Response times < 200ms, payload sizes < 2KB
* **Security First**: Protection against common attack vectors
* **Scalable Architecture**: Clean separation of domain, application, and infrastructure layers

Quick Start
-----------

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

   # Enter Nix development environment (recommended)
   nix develop

   # Start services
   docker compose -f docker/docker-compose.yml up -d

   # Run migrations
   python -m alembic upgrade head

   # Start development server
   python -m uvicorn app.main:app --reload

Documentation Contents
----------------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   installation
   quickstart
   development-environment

.. toctree::
   :maxdepth: 2
   :caption: Architecture

   architecture/overview
   architecture/clean-architecture
   architecture/domain-model
   architecture/api-design

.. toctree::
   :maxdepth: 2
   :caption: Database

   database/schema
   database/models
   database/migrations
   database/performance

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/authentication
   api/users
   api/health
   api/errors

.. toctree::
   :maxdepth: 2
   :caption: Testing

   testing/strategy
   testing/unit-tests
   testing/integration-tests
   testing/security-tests
   testing/performance-tests

.. toctree::
   :maxdepth: 2
   :caption: Deployment

   deployment/docker
   deployment/production
   deployment/monitoring

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/contributing
   development/code-style
   development/testing-guide
   development/nix-environment

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
