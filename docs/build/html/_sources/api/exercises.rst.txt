Exercise API
============

The Exercise API provides comprehensive functionality for managing exercises, including creation, versioning, approval workflows, and media management.

Overview
--------

The Exercise API is built around the concept of versioned exercises with soft-delete capabilities and comprehensive audit trails. Each exercise can have multiple versions, with one designated as the current version.

Key Features:
- **Versioning**: Full version control with rollback capabilities
- **Soft Delete**: Exercises are never permanently deleted, allowing for recovery
- **Approval Workflow**: Multi-stage approval process for exercise publication
- **Media Management**: Support for videos, images, and other multimedia content
- **Advanced Search**: Filter by muscle groups, equipment, difficulty, and more
- **Audit Trail**: Complete history of all changes and actions

Base URL
--------

All exercise endpoints are available under:

.. code-block:: text

   /api/v1/exercises

Authentication
--------------

All exercise endpoints require authentication via JW<PERSON> token in the Authorization header:

.. code-block:: text

   Authorization: Bearer <your-jwt-token>

Core Endpoints
--------------

Create Exercise
~~~~~~~~~~~~~~~

Create a new exercise with comprehensive metadata.

.. code-block:: http

   POST /api/v1/exercises
   Content-Type: application/json
   Authorization: Bearer <token>

   {
     "name": "Barbell Bench Press",
     "description": "Classic compound chest exercise using a barbell",
     "primary_muscle_group": "chest",
     "secondary_muscle_groups": ["triceps", "shoulders"],
     "movement_pattern": "push",
     "equipment_required": ["barbell"],
     "difficulty_level": "intermediate",
     "form_cues": [
       "Keep shoulder blades retracted",
       "Maintain arch in lower back",
       "Control the descent"
     ],
     "setup_instructions": "Lie on bench with eyes under the bar",
     "execution_steps": [
       "Unrack the bar with straight arms",
       "Lower bar to chest with control",
       "Press bar back to starting position"
     ],
     "common_mistakes": [
       "Bouncing bar off chest",
       "Flaring elbows too wide",
       "Not using full range of motion"
     ],
     "safety_notes": "Always use a spotter for heavy weights",
     "version_notes": "Initial creation with comprehensive form guidance"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "exercise_uuid": "987fcdeb-51a2-43d1-9c4e-123456789abc",
     "version": 1,
     "is_current_version": true,
     "name": "Barbell Bench Press",
     "description": "Classic compound chest exercise using a barbell",
     "primary_muscle_group": "chest",
     "secondary_muscle_groups": ["triceps", "shoulders"],
     "movement_pattern": "push",
     "equipment_required": ["barbell"],
     "difficulty_level": "intermediate",
     "is_active": true,
     "is_approved": false,
     "approval_status": "pending",
     "created_at": "2024-01-15T10:30:00Z",
     "updated_at": "2024-01-15T10:30:00Z",
     "change_reason": "initial_creation"
   }

Search Exercises
~~~~~~~~~~~~~~~~

Search and filter exercises with comprehensive query parameters.

.. code-block:: http

   GET /api/v1/exercises?primary_muscle_group=chest&equipment=barbell&difficulty_level=intermediate&limit=20&offset=0

**Query Parameters:**

- ``name`` (string, optional): Search by exercise name
- ``primary_muscle_group`` (string, optional): Filter by primary muscle group
- ``movement_pattern`` (string, optional): Filter by movement pattern
- ``equipment`` (array, optional): Filter by required equipment
- ``difficulty_level`` (string, optional): Filter by difficulty level
- ``is_active`` (boolean, default: true): Filter by active status
- ``is_approved`` (boolean, optional): Filter by approval status
- ``current_version_only`` (boolean, default: true): Only return current versions
- ``include_deleted`` (boolean, default: false): Include soft-deleted exercises
- ``limit`` (integer, 1-100, default: 50): Maximum results to return
- ``offset`` (integer, default: 0): Number of results to skip

**Response (200 OK):**

.. code-block:: json

   {
     "exercises": [
       {
         "id": "123e4567-e89b-12d3-a456-************",
         "name": "Barbell Bench Press",
         "primary_muscle_group": "chest",
         "difficulty_level": "intermediate",
         "is_approved": true,
         "created_at": "2024-01-15T10:30:00Z"
       }
     ],
     "total_count": 1,
     "limit": 20,
     "offset": 0,
     "has_more": false
   }

Get Exercise
~~~~~~~~~~~~

Retrieve a specific exercise by ID.

.. code-block:: http

   GET /api/v1/exercises/{exercise_id}

**Response (200 OK):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "exercise_uuid": "987fcdeb-51a2-43d1-9c4e-123456789abc",
     "version": 1,
     "is_current_version": true,
     "name": "Barbell Bench Press",
     "description": "Classic compound chest exercise using a barbell",
     "primary_muscle_group": "chest",
     "secondary_muscle_groups": ["triceps", "shoulders"],
     "movement_pattern": "push",
     "equipment_required": ["barbell"],
     "difficulty_level": "intermediate",
     "form_cues": [
       "Keep shoulder blades retracted",
       "Maintain arch in lower back",
       "Control the descent"
     ],
     "setup_instructions": "Lie on bench with eyes under the bar",
     "execution_steps": [
       "Unrack the bar with straight arms",
       "Lower bar to chest with control",
       "Press bar back to starting position"
     ],
     "common_mistakes": [
       "Bouncing bar off chest",
       "Flaring elbows too wide",
       "Not using full range of motion"
     ],
     "safety_notes": "Always use a spotter for heavy weights",
     "is_active": true,
     "is_approved": true,
     "approval_status": "approved",
     "created_at": "2024-01-15T10:30:00Z",
     "updated_at": "2024-01-15T10:30:00Z",
     "media": []
   }

Update Exercise
~~~~~~~~~~~~~~~

Update an exercise, optionally creating a new version.

.. code-block:: http

   PUT /api/v1/exercises/{exercise_id}?create_new_version=true
   Content-Type: application/json

   {
     "description": "Updated description with more detail",
     "form_cues": [
       "Keep shoulder blades retracted",
       "Maintain arch in lower back",
       "Control the descent",
       "Drive through heels"
     ],
     "version_notes": "Added heel drive cue for better leg engagement",
     "change_reason": "content_update"
   }

**Query Parameters:**

- ``create_new_version`` (boolean, default: true): Whether to create a new version

**Response (200 OK):**

.. code-block:: json

   {
     "id": "456e7890-e89b-12d3-a456-************",
     "exercise_uuid": "987fcdeb-51a2-43d1-9c4e-123456789abc",
     "version": 2,
     "is_current_version": true,
     "parent_version_id": "123e4567-e89b-12d3-a456-************",
     "name": "Barbell Bench Press",
     "description": "Updated description with more detail",
     "is_approved": false,
     "approval_status": "pending",
     "change_reason": "content_update",
     "version_notes": "Added heel drive cue for better leg engagement"
   }

Delete Exercise
~~~~~~~~~~~~~~~

Soft delete an exercise (can be restored later).

.. code-block:: http

   DELETE /api/v1/exercises/{exercise_id}

**Response (204 No Content)**

Restore Exercise
~~~~~~~~~~~~~~~~

Restore a previously deleted exercise.

.. code-block:: http

   POST /api/v1/exercises/{exercise_id}/restore

**Response (200 OK):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "name": "Barbell Bench Press",
     "is_active": true,
     "deleted_at": null
   }

Approval Workflow
-----------------

Approve Exercise
~~~~~~~~~~~~~~~~

Approve an exercise for publication.

.. code-block:: http

   POST /api/v1/exercises/{exercise_id}/approve
   Content-Type: application/json

   {
     "notes": "Exercise form and instructions are comprehensive and accurate"
   }

**Response (200 OK):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "is_approved": true,
     "approval_status": "approved",
     "approved_at": "2024-01-15T11:00:00Z",
     "approved_by": "admin-user-id"
   }

Reject Exercise
~~~~~~~~~~~~~~~

Reject an exercise submission.

.. code-block:: http

   POST /api/v1/exercises/{exercise_id}/reject
   Content-Type: application/json

   {
     "notes": "Form cues need more detail for safety"
   }

**Response (200 OK):**

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "is_approved": false,
     "approval_status": "rejected"
   }

Version Management
------------------

Get Exercise Versions
~~~~~~~~~~~~~~~~~~~~~

Get all versions of an exercise.

.. code-block:: http

   GET /api/v1/exercises/uuid/{exercise_uuid}/versions

**Response (200 OK):**

.. code-block:: json

   {
     "exercise_uuid": "987fcdeb-51a2-43d1-9c4e-123456789abc",
     "current_version": 2,
     "total_versions": 2,
     "versions": [
       {
         "id": "123e4567-e89b-12d3-a456-************",
         "version": 1,
         "is_current_version": false,
         "created_at": "2024-01-15T10:30:00Z"
       },
       {
         "id": "456e7890-e89b-12d3-a456-************",
         "version": 2,
         "is_current_version": true,
         "created_at": "2024-01-15T11:00:00Z"
       }
     ]
   }

Get Specific Version
~~~~~~~~~~~~~~~~~~~~

Get a specific version of an exercise.

.. code-block:: http

   GET /api/v1/exercises/uuid/{exercise_uuid}/version/{version}

Set Current Version
~~~~~~~~~~~~~~~~~~~

Set a specific version as the current version.

.. code-block:: http

   POST /api/v1/exercises/uuid/{exercise_uuid}/set-current-version/{version}

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Current version updated successfully"
   }
