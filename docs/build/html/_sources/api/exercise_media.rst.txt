Exercise Media API
==================

The Exercise Media API provides functionality for managing multimedia content associated with exercises, including videos, images, GIFs, and audio files.

Overview
--------

Exercise media management supports:
- **Multiple Media Types**: Videos, images, GIFs, and audio files
- **Primary Media Designation**: Mark one media item per type as primary
- **Sort Ordering**: Custom ordering of media items
- **Metadata Management**: Titles, descriptions, and technical metadata
- **File Information**: Size, dimensions, duration tracking

Base URL
--------

All exercise media endpoints are available under:

.. code-block:: text

   /api/v1/exercises/{exercise_id}/media

Authentication
--------------

All media endpoints require authentication via JWT token:

.. code-block:: text

   Authorization: Bearer <your-jwt-token>

Media Management
----------------

Add Exercise Media
~~~~~~~~~~~~~~~~~~

Add media (video, image, etc.) to an exercise.

.. code-block:: http

   POST /api/v1/exercises/{exercise_id}/media
   Content-Type: application/json

   {
     "media_type": "video",
     "url": "https://example.com/videos/bench-press.mp4",
     "title": "Bench Press Form Video",
     "description": "Demonstration of proper bench press technique",
     "thumbnail_url": "https://example.com/thumbnails/bench-press.jpg",
     "duration_seconds": 120,
     "width_pixels": 1920,
     "height_pixels": 1080,
     "mime_type": "video/mp4",
     "is_primary": true
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "media-uuid-123",
     "exercise_id": "123e4567-e89b-12d3-a456-************",
     "media_type": "video",
     "url": "https://example.com/videos/bench-press.mp4",
     "title": "Bench Press Form Video",
     "description": "Demonstration of proper bench press technique",
     "thumbnail_url": "https://example.com/thumbnails/bench-press.jpg",
     "duration_seconds": 120,
     "width_pixels": 1920,
     "height_pixels": 1080,
     "mime_type": "video/mp4",
     "file_size_bytes": 15728640,
     "is_primary": true,
     "is_active": true,
     "sort_order": 0,
     "created_at": "2024-01-15T12:00:00Z",
     "updated_at": "2024-01-15T12:00:00Z"
   }

Get Exercise Media
~~~~~~~~~~~~~~~~~~

Get all media for an exercise.

.. code-block:: http

   GET /api/v1/exercises/{exercise_id}/media?media_type=video

**Query Parameters:**

- ``media_type`` (string, optional): Filter by media type (video, image, gif, audio)

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "media-uuid-123",
       "exercise_id": "123e4567-e89b-12d3-a456-************",
       "media_type": "video",
       "url": "https://example.com/videos/bench-press.mp4",
       "title": "Bench Press Form Video",
       "is_primary": true,
       "sort_order": 0,
       "created_at": "2024-01-15T12:00:00Z"
     },
     {
       "id": "media-uuid-456",
       "exercise_id": "123e4567-e89b-12d3-a456-************",
       "media_type": "image",
       "url": "https://example.com/images/bench-press-setup.jpg",
       "title": "Bench Press Setup Position",
       "is_primary": true,
       "sort_order": 1,
       "created_at": "2024-01-15T12:05:00Z"
     }
   ]

Update Exercise Media
~~~~~~~~~~~~~~~~~~~~~

Update exercise media metadata.

.. code-block:: http

   PUT /api/v1/exercises/media/{media_id}
   Content-Type: application/json

   {
     "title": "Updated Bench Press Form Video",
     "description": "Updated description with more detail",
     "is_primary": false,
     "sort_order": 1
   }

**Response (200 OK):**

.. code-block:: json

   {
     "id": "media-uuid-123",
     "exercise_id": "123e4567-e89b-12d3-a456-************",
     "media_type": "video",
     "url": "https://example.com/videos/bench-press.mp4",
     "title": "Updated Bench Press Form Video",
     "description": "Updated description with more detail",
     "is_primary": false,
     "sort_order": 1,
     "updated_at": "2024-01-15T12:30:00Z"
   }

Delete Exercise Media
~~~~~~~~~~~~~~~~~~~~~

Delete exercise media.

.. code-block:: http

   DELETE /api/v1/exercises/media/{media_id}

**Response (204 No Content)**

Reorder Exercise Media
~~~~~~~~~~~~~~~~~~~~~~

Reorder exercise media by providing new sort order.

.. code-block:: http

   POST /api/v1/exercises/{exercise_id}/media/reorder
   Content-Type: application/json

   {
     "media_order": [
       "media-uuid-456",
       "media-uuid-123",
       "media-uuid-789"
     ]
   }

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "media-uuid-456",
       "sort_order": 0
     },
     {
       "id": "media-uuid-123",
       "sort_order": 1
     },
     {
       "id": "media-uuid-789",
       "sort_order": 2
     }
   ]

Media Types
-----------

Supported Media Types
~~~~~~~~~~~~~~~~~~~~~

The API supports the following media types:

**Video**
- Primary use: Exercise demonstrations, form videos
- Supported formats: MP4, WebM, MOV
- Required fields: ``duration_seconds``, ``width_pixels``, ``height_pixels``
- Optional fields: ``mime_type``, ``file_size_bytes``

**Image**
- Primary use: Setup positions, form checkpoints, equipment photos
- Supported formats: JPEG, PNG, WebP
- Required fields: ``width_pixels``, ``height_pixels``
- Optional fields: ``mime_type``, ``file_size_bytes``

**GIF**
- Primary use: Short movement loops, quick form demonstrations
- Supported formats: GIF, WebP (animated)
- Required fields: ``width_pixels``, ``height_pixels``
- Optional fields: ``duration_seconds``, ``mime_type``, ``file_size_bytes``

**Audio**
- Primary use: Coaching cues, breathing instructions
- Supported formats: MP3, WAV, OGG
- Required fields: ``duration_seconds``
- Optional fields: ``mime_type``, ``file_size_bytes``

Primary Media Rules
~~~~~~~~~~~~~~~~~~~

Each exercise can have one primary media item per media type:
- One primary video
- One primary image  
- One primary GIF
- One primary audio

Attempting to set a second primary media of the same type will result in a 409 Conflict error.

File Size Limits
~~~~~~~~~~~~~~~~~

Recommended file size limits:
- **Videos**: Maximum 50MB
- **Images**: Maximum 5MB
- **GIFs**: Maximum 10MB
- **Audio**: Maximum 10MB

Data Models
-----------

ExerciseMedia Schema
~~~~~~~~~~~~~~~~~~~~

.. code-block:: typescript

   interface ExerciseMedia {
     id: string;                    // UUID
     exercise_id: string;           // Exercise UUID
     media_type: MediaType;         // Type of media
     url: string;                   // Media URL
     thumbnail_url?: string;        // Thumbnail URL
     title?: string;                // Media title
     description?: string;          // Media description
     file_size_bytes?: number;      // File size in bytes
     duration_seconds?: number;     // Duration for videos/audio
     width_pixels?: number;         // Width for images/videos
     height_pixels?: number;        // Height for images/videos
     mime_type?: string;            // MIME type
     sort_order: number;            // Sort order
     is_primary: boolean;           // Whether this is primary media
     is_active: boolean;            // Whether media is active
     created_at: string;            // ISO 8601 timestamp
     updated_at: string;            // ISO 8601 timestamp
   }

   enum MediaType {
     VIDEO = "video",
     IMAGE = "image", 
     GIF = "gif",
     AUDIO = "audio"
   }

Error Responses
---------------

**400 Bad Request - Invalid Media Data**

.. code-block:: json

   {
     "error": "Validation failed",
     "message": "Video media should have duration"
   }

**404 Not Found - Exercise Not Found**

.. code-block:: json

   {
     "error": "Exercise not found",
     "exercise_id": "123e4567-e89b-12d3-a456-************"
   }

**404 Not Found - Media Not Found**

.. code-block:: json

   {
     "error": "Media not found",
     "media_id": "media-uuid-123"
   }

**409 Conflict - Primary Media Exists**

.. code-block:: json

   {
     "error": "Business rule violation",
     "message": "Exercise already has primary video media"
   }

Best Practices
--------------

Media Organization
~~~~~~~~~~~~~~~~~~

1. **Use Primary Media Effectively**: Set the most important/representative media as primary
2. **Logical Sort Order**: Order media by importance or viewing sequence
3. **Descriptive Titles**: Use clear, descriptive titles for all media
4. **Thumbnail Consistency**: Provide thumbnails for videos when possible

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Optimize File Sizes**: Compress media files appropriately
2. **Use Appropriate Formats**: Choose formats optimized for web delivery
3. **Implement Lazy Loading**: Load media on demand in client applications
4. **CDN Integration**: Use CDN for media delivery when possible

Content Guidelines
~~~~~~~~~~~~~~~~~~

1. **High Quality**: Ensure media is clear and well-lit
2. **Proper Framing**: Show full exercise movement and setup
3. **Multiple Angles**: Provide different viewing angles when helpful
4. **Safety Focus**: Highlight proper form and safety considerations
