Glossary
========

This glossary defines key terms and concepts used throughout Forge Protocol documentation and the Renaissance Periodisation methodology.

Scientific Terms
----------------

.. glossary::

   Hypertrophy
      The increase in muscle fiber size through resistance training. The primary goal of Forge Protocol's training algorithms.

   MEV (Minimum Effective Volume)
      The lowest training volume that produces measurable hypertrophy adaptations. Represents the threshold where training stimulus becomes sufficient to drive muscle growth.

   MAV (Maximum Adaptive Volume)
      The training volume that produces the greatest rate of hypertrophy adaptations. Represents the optimal training stimulus for maximum muscle growth rate.

   MRV (Maximum Recoverable Volume)
      The highest training volume from which an individual can recover within their normal recovery timeframe. Beyond this point, fatigue accumulation exceeds recovery capacity.

   MV (Maintenance Volume)
      The minimum training volume required to maintain current muscle mass and strength. Used during deload periods or maintenance phases.

   SFR (Stimulus-to-Fatigue Ratio)
      A measure of exercise effectiveness that compares the hypertrophic stimulus generated to the systemic fatigue cost incurred.

   Progressive Overload
      The gradual increase of stress placed on the body during exercise training. Can be achieved through increases in volume, intensity, frequency, or range of motion.

   Periodisation
      The systematic planning of athletic training. In hypertrophy training, this involves strategic organisation of training variables across time to optimise adaptation whilst managing fatigue.

   Mesocycle
      A training block typically lasting 4-8 weeks with specific volume progression and planned deload periods.

   Deload
      A planned reduction in training volume (typically 40-60% of peak volume) to allow for recovery and adaptation.

   RPE (Rate of Perceived Exertion)
      A subjective scale (typically 1-10) used to measure the intensity of exercise based on how hard the individual feels they are working.

   RIR (Reps in Reserve)
      The number of repetitions remaining before reaching muscular failure. Used to gauge training intensity and proximity to failure.

Training Terms
--------------

.. glossary::

   Volume Landmarks
      The key volume thresholds (MV, MEV, MAV, MRV) that guide training prescription and progression.

   Set Progression
      The systematic increase in training sets over time, typically 1-3 sets per muscle group per week.

   Muscle Group
      A collection of muscles that work together to perform specific movements. Examples include chest, back, shoulders, arms, and legs.

   Movement Pattern
      The fundamental movement categories used to classify exercises: push, pull, squat, hinge, and isolation.

   Training Frequency
      How often a muscle group is trained per week. Can range from 1x to 3x per week depending on volume requirements.

   Exercise Selection
      The process of choosing exercises based on effectiveness, equipment availability, and individual preferences.

   Form Cues
      Specific instructions for proper exercise technique to maximise effectiveness and minimise injury risk.

   Training Block
      A period of training with specific goals and characteristics, typically lasting 4-8 weeks.

Technical Terms
---------------

.. glossary::

   API (Application Programming Interface)
      A set of protocols and tools for building software applications. Forge Protocol provides a REST API for integration.

   REST (Representational State Transfer)
      An architectural style for designing networked applications, used by Forge Protocol's API.

   JWT (JSON Web Token)
      A compact, URL-safe means of representing claims between two parties. Used for authentication in Forge Protocol.

   Clean Architecture
      A software design philosophy that separates concerns and creates a system that is independent of frameworks, UI, database, and external agencies.

   Domain-Driven Design (DDD)
      An approach to software development that centers the development on programming a domain model that has a rich understanding of the processes and rules of a domain.

   Repository Pattern
      A design pattern that encapsulates the logic needed to access data sources, centralising common data access functionality.

   Dependency Injection
      A technique whereby one object supplies the dependencies of another object, promoting loose coupling and testability.

   ORM (Object-Relational Mapping)
      A programming technique for converting data between incompatible type systems using object-oriented programming languages.

   Migration
      A script that modifies the database schema, allowing for version control of database changes.

   Docker
      A platform that uses containerisation to package applications and their dependencies into lightweight, portable containers.

   Pydantic
      A data validation and settings management library for Python that uses Python type annotations.

Algorithm Terms
---------------

.. glossary::

   Baseline MEV
      The research-based starting point for MEV calculations before individual adjustments are applied.

   Adjustment Factor
      A multiplier used to modify baseline calculations based on individual characteristics like age, experience, and recovery capacity.

   Readiness Score
      A composite metric that combines performance, recovery, and soreness indicators to guide training progression.

   Fatigue Accumulation
      The progressive increase in systemic fatigue over the course of a training block, used to determine deload timing.

   Adaptive Algorithm
      An algorithm that modifies its behaviour based on user feedback and performance data over time.

   Validation Testing
      The process of verifying that algorithms produce expected results against known test cases and real-world data.

   Performance Metrics
      Quantitative measures used to assess the effectiveness of training algorithms and user progress.

   Machine Learning
      A subset of artificial intelligence that enables systems to automatically learn and improve from experience without being explicitly programmed.

User Interface Terms
-------------------

.. glossary::

   Dashboard
      The main interface that provides an overview of user progress, upcoming workouts, and key metrics.

   Workout Log
      A record of completed exercises, sets, reps, and performance metrics for a training session.

   Exercise Database
      A comprehensive collection of exercises with instructions, videos, and effectiveness ratings.

   Progress Tracking
      The systematic monitoring and visualisation of training progress over time.

   User Profile
      A collection of user information including personal details, training history, and preferences.

   Settings
      Configuration options that allow users to customise their experience and training parameters.

   Notifications
      Alerts and reminders sent to users about workouts, progress milestones, and system updates.

   Sync
      The process of updating data across multiple devices or platforms to ensure consistency.

Data Terms
----------

.. glossary::

   Training Data
      Information collected about user workouts, including exercises performed, sets, reps, weights, and subjective feedback.

   Performance Data
      Metrics that track user progress over time, including strength gains, volume progression, and goal achievement.

   Biometric Data
      Physical measurements and health indicators that may influence training recommendations.

   Feedback Data
      User-provided information about workout difficulty, soreness, recovery, and satisfaction.

   Analytics
      The systematic analysis of training data to identify patterns, trends, and opportunities for improvement.

   Data Export
      The ability to extract user data in standard formats for backup or analysis purposes.

   Data Privacy
      The protection of user information and compliance with data protection regulations.

   Data Retention
      Policies governing how long user data is stored and when it is deleted.

Integration Terms
-----------------

.. glossary::

   SDK (Software Development Kit)
      A collection of software development tools and libraries for building applications that integrate with Forge Protocol.

   Webhook
      A method of augmenting or altering the behaviour of a web application with custom callbacks.

   Third-Party Integration
      The connection of Forge Protocol with external services and applications.

   API Key
      A unique identifier used to authenticate requests to the Forge Protocol API.

   Rate Limiting
      The practice of limiting the number of API requests a user can make within a specific time period.

   Endpoint
      A specific URL where an API can be accessed by a client application.

   Schema
      A description of the structure and format of API requests and responses.

   Authentication
      The process of verifying the identity of a user or application accessing the API.

Business Terms
--------------

.. glossary::

   SaaS (Software as a Service)
      A software distribution model where applications are hosted by a service provider and made available to customers over the internet.

   Subscription
      A recurring payment model that provides ongoing access to Forge Protocol services.

   Enterprise
      Large-scale deployment of Forge Protocol for organisations with advanced requirements.

   Support Tier
      Different levels of customer support based on subscription level and requirements.

   SLA (Service Level Agreement)
      A commitment between Forge Protocol and users regarding service availability and performance.

   Compliance
      Adherence to relevant regulations and standards, including data protection and security requirements.

   Scalability
      The ability of Forge Protocol to handle increased load and user growth.

   Uptime
      The percentage of time that Forge Protocol services are operational and accessible.

Abbreviations and Acronyms
--------------------------

.. glossary::

   RP
      Renaissance Periodisation - The scientific methodology underlying Forge Protocol.

   API
      Application Programming Interface

   REST
      Representational State Transfer

   JWT
      JSON Web Token

   ORM
      Object-Relational Mapping

   SQL
      Structured Query Language

   HTTP
      Hypertext Transfer Protocol

   HTTPS
      Hypertext Transfer Protocol Secure

   JSON
      JavaScript Object Notation

   UUID
      Universally Unique Identifier

   CORS
      Cross-Origin Resource Sharing

   CRUD
      Create, Read, Update, Delete

   CI/CD
      Continuous Integration/Continuous Deployment

   TDD
      Test-Driven Development

   BDD
      Behaviour-Driven Development

   DRY
      Don't Repeat Yourself

   SOLID
      Single Responsibility, Open-Closed, Liskov Substitution, Interface Segregation, Dependency Inversion

   GDPR
      General Data Protection Regulation

   HIPAA
      Health Insurance Portability and Accountability Act

   SSL/TLS
      Secure Sockets Layer/Transport Layer Security

   AWS
      Amazon Web Services

   GCP
      Google Cloud Platform

   CDN
      Content Delivery Network

   DNS
      Domain Name System

   VPC
      Virtual Private Cloud

   IAM
      Identity and Access Management

.. note::
   This glossary is continuously updated as new terms and concepts are introduced to Forge Protocol. 
   If you encounter unfamiliar terms not listed here, please refer to the specific documentation 
   sections or contact our support team.

.. tip::
   Use Ctrl+F (Cmd+F on Mac) to quickly search for specific terms within this glossary. 
   Many terms are also linked throughout the documentation for easy reference.
