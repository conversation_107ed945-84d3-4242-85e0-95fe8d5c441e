Exercise System Architecture
=============================

The Exercise System is a core component of the Forge Protocol platform, implementing a comprehensive exercise management system with versioning, approval workflows, and media management capabilities.

Overview
--------

The Exercise System follows Clean Architecture principles with clear separation of concerns across multiple layers:

.. code-block:: text

   ┌─────────────────────────────────────────────────────────────┐
   │                    Presentation Layer                       │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
   │  │   FastAPI       │  │   Pydantic      │  │   OpenAPI   │ │
   │  │   Routes        │  │   Schemas       │  │   Docs      │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
   └─────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────┐
   │                   Application Layer                         │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
   │  │   Use Cases     │  │    Services     │  │ Exceptions  │ │
   │  │                 │  │                 │  │             │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
   └─────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────┐
   │                     Domain Layer                            │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
   │  │   Entities      │  │  Repositories   │  │   Value     │ │
   │  │                 │  │  (Interfaces)   │  │   Objects   │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
   └─────────────────────────────────────────────────────────────┘
                                    │
   ┌─────────────────────────────────────────────────────────────┐
   │                 Infrastructure Layer                        │
   │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
   │  │   Database      │  │   Repository    │  │   External  │ │
   │  │   Models        │  │ Implementations │  │   Services  │ │
   │  └─────────────────┘  └─────────────────┘  └─────────────┘ │
   └─────────────────────────────────────────────────────────────┘

Core Components
---------------

Domain Layer
~~~~~~~~~~~~

**Exercise Entity**

The Exercise entity is the core domain model representing a single exercise with comprehensive metadata:

.. code-block:: python

   class Exercise(BaseModel):
       # Identity and versioning
       id: UUID
       exercise_uuid: UUID
       version: int
       is_current_version: bool
       parent_version_id: Optional[UUID]
       
       # Core exercise data
       name: str
       description: Optional[str]
       primary_muscle_group: MuscleGroup
       secondary_muscle_groups: Optional[List[MuscleGroup]]
       movement_pattern: MovementPattern
       equipment_required: Optional[List[Equipment]]
       difficulty_level: DifficultyLevel
       
       # Instructions and media
       form_cues: Optional[List[str]]
       setup_instructions: Optional[str]
       execution_steps: Optional[List[str]]
       common_mistakes: Optional[List[str]]
       safety_notes: Optional[str]
       video_url: Optional[str]
       thumbnail_url: Optional[str]
       
       # Status and workflow
       is_active: bool
       is_approved: bool
       approval_status: ApprovalStatus
       
       # Audit trail
       created_by: Optional[UUID]
       updated_by: Optional[UUID]
       approved_by: Optional[UUID]
       created_at: datetime
       updated_at: datetime
       deleted_at: Optional[datetime]
       approved_at: Optional[datetime]
       
       # Versioning metadata
       version_notes: Optional[str]
       change_reason: ChangeReason
       
       # Related entities
       media: Optional[List[ExerciseMedia]]

**Key Domain Rules**

1. **Versioning**: Each exercise has a unique UUID that remains constant across versions
2. **Soft Delete**: Exercises are never permanently deleted, only marked as deleted
3. **Approval Workflow**: New versions require re-approval
4. **Media Management**: Each exercise can have multiple media items with type-specific primary designation

**Repository Interface**

.. code-block:: python

   class ExerciseRepository(ABC):
       @abstractmethod
       async def create(self, exercise: Exercise) -> Exercise:
           """Create a new exercise."""
           
       @abstractmethod
       async def get_by_id(self, exercise_id: UUID) -> Optional[Exercise]:
           """Get exercise by ID."""
           
       @abstractmethod
       async def get_by_uuid(self, exercise_uuid: UUID, version: Optional[int] = None) -> Optional[Exercise]:
           """Get exercise by UUID and optionally version."""
           
       @abstractmethod
       async def search(self, filters: ExerciseSearchFilters, limit: int, offset: int) -> List[Exercise]:
           """Search exercises with filters."""
           
       @abstractmethod
       async def update(self, exercise: Exercise) -> Exercise:
           """Update an exercise."""
           
       @abstractmethod
       async def soft_delete(self, exercise_id: UUID, deleted_by: Optional[UUID]) -> bool:
           """Soft delete an exercise."""
           
       @abstractmethod
       async def create_version(self, exercise_uuid: UUID, updated_exercise: Exercise) -> Exercise:
           """Create a new version of an existing exercise."""

Application Layer
~~~~~~~~~~~~~~~~~

**Use Cases**

The application layer implements business logic through use cases:

1. **ExerciseUseCases**: Core exercise management operations
2. **ExerciseMediaUseCases**: Media management operations

**Key Use Cases**

.. code-block:: python

   class ExerciseUseCases:
       async def create_exercise(self, request: ExerciseCreateRequest, created_by: UUID) -> Exercise:
           """Create a new exercise with validation."""
           
       async def update_exercise(self, exercise_id: UUID, request: ExerciseUpdateRequest, 
                               updated_by: UUID, create_new_version: bool) -> Exercise:
           """Update exercise, optionally creating new version."""
           
       async def approve_exercise(self, exercise_id: UUID, approved_by: UUID, notes: str) -> Exercise:
           """Approve an exercise for publication."""
           
       async def search_exercises(self, filters: ExerciseSearchFilters) -> Tuple[List[Exercise], int]:
           """Search exercises with comprehensive filtering."""

**Service Layer**

The ExerciseService orchestrates use cases and provides a unified interface:

.. code-block:: python

   class ExerciseService:
       def __init__(self, exercise_repository: ExerciseRepository):
           self.exercise_repository = exercise_repository
           self.exercise_use_cases = ExerciseUseCases(exercise_repository)
           self.media_use_cases = ExerciseMediaUseCases(exercise_repository)
           
       async def create_exercise(self, request: ExerciseCreateRequest, created_by: UUID) -> Exercise:
           """Create exercise through use cases."""
           
       async def get_exercise_statistics(self, exercise_id: UUID) -> dict:
           """Get comprehensive exercise statistics."""

Infrastructure Layer
~~~~~~~~~~~~~~~~~~~~

**Database Models**

SQLAlchemy models implement the persistence layer:

.. code-block:: python

   class ExerciseModel(Base):
       __tablename__ = "exercises"
       
       # Primary key and versioning
       id: Mapped[UUID] = mapped_column(PostgresUUID(as_uuid=True), primary_key=True)
       exercise_uuid: Mapped[UUID] = mapped_column(PostgresUUID(as_uuid=True), nullable=False, index=True)
       version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
       is_current_version: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
       
       # Core data
       name: Mapped[str] = mapped_column(String(255), nullable=False)
       primary_muscle_group: Mapped[str] = mapped_column(Enum(...), nullable=False, index=True)
       
       # Audit fields
       created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
       updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
       deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

**Repository Implementation**

.. code-block:: python

   class ExerciseRepositoryImpl(ExerciseRepository):
       def __init__(self, session: AsyncSession):
           self.session = session
           
       async def create(self, exercise: Exercise) -> Exercise:
           """Create exercise with audit logging."""
           model = self._entity_to_model(exercise)
           self.session.add(model)
           await self.session.flush()
           
           # Log audit trail
           await self.log_audit(model.id, model.exercise_uuid, AuditAction.CREATED)
           
           return self._model_to_entity(model)

Versioning System
-----------------

Architecture
~~~~~~~~~~~~

The versioning system implements a sophisticated approach to exercise evolution:

.. code-block:: text

   Exercise UUID: 123e4567-e89b-12d3-a456-************
   
   Version 1 (ID: abc123...)  ←─ parent_version_id: null
   │                             is_current_version: false
   │
   ├─ Version 2 (ID: def456...)  ←─ parent_version_id: abc123...
   │                                is_current_version: true
   │
   └─ Version 3 (ID: ghi789...)  ←─ parent_version_id: def456...
                                    is_current_version: false

**Key Features**

1. **Immutable Versions**: Once created, versions are never modified
2. **Current Version Tracking**: Only one version per exercise UUID is current
3. **Parent-Child Relationships**: Each version (except first) has a parent
4. **Rollback Capability**: Any version can be set as current
5. **Audit Trail**: Complete history of all changes

**Version Creation Process**

.. code-block:: python

   async def create_version(self, exercise_uuid: UUID, updated_exercise: Exercise) -> Exercise:
       # 1. Get current version
       current = await self.get_current_version(exercise_uuid)
       
       # 2. Mark current version as not current
       await self._mark_not_current(exercise_uuid)
       
       # 3. Create new version
       new_version = Exercise(
           id=uuid4(),
           exercise_uuid=exercise_uuid,
           version=current.version + 1,
           is_current_version=True,
           parent_version_id=current.id,
           # ... updated fields
       )
       
       # 4. Save and return
       return await self.create(new_version)

Approval Workflow
-----------------

State Machine
~~~~~~~~~~~~~

The approval workflow implements a state machine:

.. code-block:: text

   ┌─────────────┐    approve()    ┌─────────────┐
   │   PENDING   │ ──────────────→ │  APPROVED   │
   │             │                 │             │
   └─────────────┘                 └─────────────┘
          │                               │
          │ reject()                      │ update()
          ↓                               ↓
   ┌─────────────┐                ┌─────────────┐
   │  REJECTED   │                │   PENDING   │
   │             │                │ (new version)│
   └─────────────┘                └─────────────┘

**Business Rules**

1. **New Exercises**: Start in PENDING status
2. **Updates**: Create new version in PENDING status
3. **Approval Required**: Only APPROVED exercises are publicly visible
4. **Re-approval**: Updates require re-approval even for approved exercises

Media Management
----------------

Architecture
~~~~~~~~~~~~

Media management supports multiple file types with metadata:

.. code-block:: text

   Exercise
   ├── Video Media (Primary)
   │   ├── URL: https://cdn.example.com/video.mp4
   │   ├── Thumbnail: https://cdn.example.com/thumb.jpg
   │   ├── Duration: 120 seconds
   │   └── Dimensions: 1920x1080
   │
   ├── Image Media (Primary)
   │   ├── URL: https://cdn.example.com/setup.jpg
   │   ├── Dimensions: 800x600
   │   └── File Size: 245KB
   │
   └── Additional Media
       ├── Secondary Video
       ├── Multiple Images
       └── Audio Instructions

**Primary Media Rules**

- One primary media item per type (video, image, gif, audio)
- Primary media is featured prominently in UI
- Attempting to set duplicate primary media returns 409 Conflict

Search and Filtering
--------------------

The search system provides comprehensive filtering capabilities:

**Supported Filters**

.. code-block:: python

   class ExerciseSearchFilters(BaseModel):
       name: Optional[str]                          # Text search
       primary_muscle_group: Optional[MuscleGroup]  # Exact match
       secondary_muscle_groups: Optional[List[MuscleGroup]]  # Any match
       movement_pattern: Optional[MovementPattern]  # Exact match
       equipment_required: Optional[List[Equipment]]  # Any match
       difficulty_level: Optional[DifficultyLevel]  # Exact match
       is_active: Optional[bool]                    # Status filter
       is_approved: Optional[bool]                  # Approval filter
       current_version_only: bool = True            # Version filter
       include_deleted: bool = False                # Soft delete filter

**Query Optimization**

1. **Database Indexes**: Strategic indexes on commonly filtered fields
2. **Pagination**: Limit/offset pagination with total count
3. **Selective Loading**: Load related data only when needed
4. **Caching**: Repository-level caching for frequently accessed data

Performance Considerations
--------------------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

1. **Indexes**: Comprehensive indexing strategy for search performance
2. **Partitioning**: Consider partitioning by exercise_uuid for large datasets
3. **Archival**: Archive old versions to maintain performance
4. **Connection Pooling**: Async connection pooling for scalability

Caching Strategy
~~~~~~~~~~~~~~~~

1. **Application Cache**: Cache frequently accessed exercises
2. **CDN**: Use CDN for media delivery
3. **Database Query Cache**: Cache expensive search queries
4. **Version Cache**: Cache current version lookups

Scalability
~~~~~~~~~~~

1. **Horizontal Scaling**: Stateless design supports horizontal scaling
2. **Read Replicas**: Use read replicas for search operations
3. **Event Sourcing**: Consider event sourcing for audit trail
4. **Microservices**: Exercise system can be extracted as microservice

Security Considerations
-----------------------

Authentication & Authorization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **JWT Authentication**: All endpoints require valid JWT tokens
2. **Role-Based Access**: Different permissions for create/approve/admin
3. **Resource Ownership**: Users can only modify their own exercises
4. **Approval Permissions**: Only authorized users can approve exercises

Data Protection
~~~~~~~~~~~~~~~

1. **Input Validation**: Comprehensive validation at all layers
2. **SQL Injection Prevention**: Parameterized queries and ORM
3. **XSS Prevention**: Output encoding and CSP headers
4. **File Upload Security**: Validate media files and use secure storage

Audit Trail
~~~~~~~~~~~

1. **Complete History**: All changes logged with user and timestamp
2. **Immutable Logs**: Audit logs cannot be modified
3. **Compliance**: Supports regulatory compliance requirements
4. **Forensics**: Detailed trail for security investigations
