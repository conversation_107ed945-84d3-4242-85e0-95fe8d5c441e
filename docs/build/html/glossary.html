

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Glossary &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="_static/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="_static/copybutton.js?v=30646c52"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="prev" title="License" href="license.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Forge Protocol
              <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="license.html">License</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Glossary</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#scientific-terms">Scientific Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#training-terms">Training Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#technical-terms">Technical Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#algorithm-terms">Algorithm Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#user-interface-terms">User Interface Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#data-terms">Data Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#integration-terms">Integration Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#business-terms">Business Terms</a></li>
<li class="toctree-l2"><a class="reference internal" href="#abbreviations-and-acronyms">Abbreviations and Acronyms</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Glossary</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/glossary.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="glossary">
<h1>Glossary<a class="headerlink" href="#glossary" title="Link to this heading"></a></h1>
<p>This glossary defines key terms and concepts used throughout Forge Protocol documentation and the Renaissance Periodisation methodology.</p>
<section id="scientific-terms">
<h2>Scientific Terms<a class="headerlink" href="#scientific-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-Hypertrophy">Hypertrophy<a class="headerlink" href="#term-Hypertrophy" title="Link to this term"></a></dt><dd><p>The increase in muscle fiber size through resistance training. The primary goal of Forge Protocol’s training algorithms.</p>
</dd>
<dt id="term-MEV-Minimum-Effective-Volume">MEV (Minimum Effective Volume)<a class="headerlink" href="#term-MEV-Minimum-Effective-Volume" title="Link to this term"></a></dt><dd><p>The lowest training volume that produces measurable hypertrophy adaptations. Represents the threshold where training stimulus becomes sufficient to drive muscle growth.</p>
</dd>
<dt id="term-MAV-Maximum-Adaptive-Volume">MAV (Maximum Adaptive Volume)<a class="headerlink" href="#term-MAV-Maximum-Adaptive-Volume" title="Link to this term"></a></dt><dd><p>The training volume that produces the greatest rate of hypertrophy adaptations. Represents the optimal training stimulus for maximum muscle growth rate.</p>
</dd>
<dt id="term-MRV-Maximum-Recoverable-Volume">MRV (Maximum Recoverable Volume)<a class="headerlink" href="#term-MRV-Maximum-Recoverable-Volume" title="Link to this term"></a></dt><dd><p>The highest training volume from which an individual can recover within their normal recovery timeframe. Beyond this point, fatigue accumulation exceeds recovery capacity.</p>
</dd>
<dt id="term-MV-Maintenance-Volume">MV (Maintenance Volume)<a class="headerlink" href="#term-MV-Maintenance-Volume" title="Link to this term"></a></dt><dd><p>The minimum training volume required to maintain current muscle mass and strength. Used during deload periods or maintenance phases.</p>
</dd>
<dt id="term-SFR-Stimulus-to-Fatigue-Ratio">SFR (Stimulus-to-Fatigue Ratio)<a class="headerlink" href="#term-SFR-Stimulus-to-Fatigue-Ratio" title="Link to this term"></a></dt><dd><p>A measure of exercise effectiveness that compares the hypertrophic stimulus generated to the systemic fatigue cost incurred.</p>
</dd>
<dt id="term-Progressive-Overload">Progressive Overload<a class="headerlink" href="#term-Progressive-Overload" title="Link to this term"></a></dt><dd><p>The gradual increase of stress placed on the body during exercise training. Can be achieved through increases in volume, intensity, frequency, or range of motion.</p>
</dd>
<dt id="term-Periodisation">Periodisation<a class="headerlink" href="#term-Periodisation" title="Link to this term"></a></dt><dd><p>The systematic planning of athletic training. In hypertrophy training, this involves strategic organisation of training variables across time to optimise adaptation whilst managing fatigue.</p>
</dd>
<dt id="term-Mesocycle">Mesocycle<a class="headerlink" href="#term-Mesocycle" title="Link to this term"></a></dt><dd><p>A training block typically lasting 4-8 weeks with specific volume progression and planned deload periods.</p>
</dd>
<dt id="term-Deload">Deload<a class="headerlink" href="#term-Deload" title="Link to this term"></a></dt><dd><p>A planned reduction in training volume (typically 40-60% of peak volume) to allow for recovery and adaptation.</p>
</dd>
<dt id="term-RPE-Rate-of-Perceived-Exertion">RPE (Rate of Perceived Exertion)<a class="headerlink" href="#term-RPE-Rate-of-Perceived-Exertion" title="Link to this term"></a></dt><dd><p>A subjective scale (typically 1-10) used to measure the intensity of exercise based on how hard the individual feels they are working.</p>
</dd>
<dt id="term-RIR-Reps-in-Reserve">RIR (Reps in Reserve)<a class="headerlink" href="#term-RIR-Reps-in-Reserve" title="Link to this term"></a></dt><dd><p>The number of repetitions remaining before reaching muscular failure. Used to gauge training intensity and proximity to failure.</p>
</dd>
</dl>
</section>
<section id="training-terms">
<h2>Training Terms<a class="headerlink" href="#training-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-Volume-Landmarks">Volume Landmarks<a class="headerlink" href="#term-Volume-Landmarks" title="Link to this term"></a></dt><dd><p>The key volume thresholds (MV, MEV, MAV, MRV) that guide training prescription and progression.</p>
</dd>
<dt id="term-Set-Progression">Set Progression<a class="headerlink" href="#term-Set-Progression" title="Link to this term"></a></dt><dd><p>The systematic increase in training sets over time, typically 1-3 sets per muscle group per week.</p>
</dd>
<dt id="term-Muscle-Group">Muscle Group<a class="headerlink" href="#term-Muscle-Group" title="Link to this term"></a></dt><dd><p>A collection of muscles that work together to perform specific movements. Examples include chest, back, shoulders, arms, and legs.</p>
</dd>
<dt id="term-Movement-Pattern">Movement Pattern<a class="headerlink" href="#term-Movement-Pattern" title="Link to this term"></a></dt><dd><p>The fundamental movement categories used to classify exercises: push, pull, squat, hinge, and isolation.</p>
</dd>
<dt id="term-Training-Frequency">Training Frequency<a class="headerlink" href="#term-Training-Frequency" title="Link to this term"></a></dt><dd><p>How often a muscle group is trained per week. Can range from 1x to 3x per week depending on volume requirements.</p>
</dd>
<dt id="term-Exercise-Selection">Exercise Selection<a class="headerlink" href="#term-Exercise-Selection" title="Link to this term"></a></dt><dd><p>The process of choosing exercises based on effectiveness, equipment availability, and individual preferences.</p>
</dd>
<dt id="term-Form-Cues">Form Cues<a class="headerlink" href="#term-Form-Cues" title="Link to this term"></a></dt><dd><p>Specific instructions for proper exercise technique to maximise effectiveness and minimise injury risk.</p>
</dd>
<dt id="term-Training-Block">Training Block<a class="headerlink" href="#term-Training-Block" title="Link to this term"></a></dt><dd><p>A period of training with specific goals and characteristics, typically lasting 4-8 weeks.</p>
</dd>
</dl>
</section>
<section id="technical-terms">
<h2>Technical Terms<a class="headerlink" href="#technical-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-API-Application-Programming-Interface">API (Application Programming Interface)<a class="headerlink" href="#term-API-Application-Programming-Interface" title="Link to this term"></a></dt><dd><p>A set of protocols and tools for building software applications. Forge Protocol provides a REST API for integration.</p>
</dd>
<dt id="term-REST-Representational-State-Transfer">REST (Representational State Transfer)<a class="headerlink" href="#term-REST-Representational-State-Transfer" title="Link to this term"></a></dt><dd><p>An architectural style for designing networked applications, used by Forge Protocol’s API.</p>
</dd>
<dt id="term-JWT-JSON-Web-Token">JWT (JSON Web Token)<a class="headerlink" href="#term-JWT-JSON-Web-Token" title="Link to this term"></a></dt><dd><p>A compact, URL-safe means of representing claims between two parties. Used for authentication in Forge Protocol.</p>
</dd>
<dt id="term-Clean-Architecture">Clean Architecture<a class="headerlink" href="#term-Clean-Architecture" title="Link to this term"></a></dt><dd><p>A software design philosophy that separates concerns and creates a system that is independent of frameworks, UI, database, and external agencies.</p>
</dd>
<dt id="term-Domain-Driven-Design-DDD">Domain-Driven Design (DDD)<a class="headerlink" href="#term-Domain-Driven-Design-DDD" title="Link to this term"></a></dt><dd><p>An approach to software development that centers the development on programming a domain model that has a rich understanding of the processes and rules of a domain.</p>
</dd>
<dt id="term-Repository-Pattern">Repository Pattern<a class="headerlink" href="#term-Repository-Pattern" title="Link to this term"></a></dt><dd><p>A design pattern that encapsulates the logic needed to access data sources, centralising common data access functionality.</p>
</dd>
<dt id="term-Dependency-Injection">Dependency Injection<a class="headerlink" href="#term-Dependency-Injection" title="Link to this term"></a></dt><dd><p>A technique whereby one object supplies the dependencies of another object, promoting loose coupling and testability.</p>
</dd>
<dt id="term-ORM-Object-Relational-Mapping">ORM (Object-Relational Mapping)<a class="headerlink" href="#term-ORM-Object-Relational-Mapping" title="Link to this term"></a></dt><dd><p>A programming technique for converting data between incompatible type systems using object-oriented programming languages.</p>
</dd>
<dt id="term-Migration">Migration<a class="headerlink" href="#term-Migration" title="Link to this term"></a></dt><dd><p>A script that modifies the database schema, allowing for version control of database changes.</p>
</dd>
<dt id="term-Docker">Docker<a class="headerlink" href="#term-Docker" title="Link to this term"></a></dt><dd><p>A platform that uses containerisation to package applications and their dependencies into lightweight, portable containers.</p>
</dd>
<dt id="term-Pydantic">Pydantic<a class="headerlink" href="#term-Pydantic" title="Link to this term"></a></dt><dd><p>A data validation and settings management library for Python that uses Python type annotations.</p>
</dd>
</dl>
</section>
<section id="algorithm-terms">
<h2>Algorithm Terms<a class="headerlink" href="#algorithm-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-Baseline-MEV">Baseline MEV<a class="headerlink" href="#term-Baseline-MEV" title="Link to this term"></a></dt><dd><p>The research-based starting point for MEV calculations before individual adjustments are applied.</p>
</dd>
<dt id="term-Adjustment-Factor">Adjustment Factor<a class="headerlink" href="#term-Adjustment-Factor" title="Link to this term"></a></dt><dd><p>A multiplier used to modify baseline calculations based on individual characteristics like age, experience, and recovery capacity.</p>
</dd>
<dt id="term-Readiness-Score">Readiness Score<a class="headerlink" href="#term-Readiness-Score" title="Link to this term"></a></dt><dd><p>A composite metric that combines performance, recovery, and soreness indicators to guide training progression.</p>
</dd>
<dt id="term-Fatigue-Accumulation">Fatigue Accumulation<a class="headerlink" href="#term-Fatigue-Accumulation" title="Link to this term"></a></dt><dd><p>The progressive increase in systemic fatigue over the course of a training block, used to determine deload timing.</p>
</dd>
<dt id="term-Adaptive-Algorithm">Adaptive Algorithm<a class="headerlink" href="#term-Adaptive-Algorithm" title="Link to this term"></a></dt><dd><p>An algorithm that modifies its behaviour based on user feedback and performance data over time.</p>
</dd>
<dt id="term-Validation-Testing">Validation Testing<a class="headerlink" href="#term-Validation-Testing" title="Link to this term"></a></dt><dd><p>The process of verifying that algorithms produce expected results against known test cases and real-world data.</p>
</dd>
<dt id="term-Performance-Metrics">Performance Metrics<a class="headerlink" href="#term-Performance-Metrics" title="Link to this term"></a></dt><dd><p>Quantitative measures used to assess the effectiveness of training algorithms and user progress.</p>
</dd>
<dt id="term-Machine-Learning">Machine Learning<a class="headerlink" href="#term-Machine-Learning" title="Link to this term"></a></dt><dd><p>A subset of artificial intelligence that enables systems to automatically learn and improve from experience without being explicitly programmed.</p>
</dd>
</dl>
</section>
<section id="user-interface-terms">
<h2>User Interface Terms<a class="headerlink" href="#user-interface-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-Dashboard">Dashboard<a class="headerlink" href="#term-Dashboard" title="Link to this term"></a></dt><dd><p>The main interface that provides an overview of user progress, upcoming workouts, and key metrics.</p>
</dd>
<dt id="term-Workout-Log">Workout Log<a class="headerlink" href="#term-Workout-Log" title="Link to this term"></a></dt><dd><p>A record of completed exercises, sets, reps, and performance metrics for a training session.</p>
</dd>
<dt id="term-Exercise-Database">Exercise Database<a class="headerlink" href="#term-Exercise-Database" title="Link to this term"></a></dt><dd><p>A comprehensive collection of exercises with instructions, videos, and effectiveness ratings.</p>
</dd>
<dt id="term-Progress-Tracking">Progress Tracking<a class="headerlink" href="#term-Progress-Tracking" title="Link to this term"></a></dt><dd><p>The systematic monitoring and visualisation of training progress over time.</p>
</dd>
<dt id="term-User-Profile">User Profile<a class="headerlink" href="#term-User-Profile" title="Link to this term"></a></dt><dd><p>A collection of user information including personal details, training history, and preferences.</p>
</dd>
<dt id="term-Settings">Settings<a class="headerlink" href="#term-Settings" title="Link to this term"></a></dt><dd><p>Configuration options that allow users to customise their experience and training parameters.</p>
</dd>
<dt id="term-Notifications">Notifications<a class="headerlink" href="#term-Notifications" title="Link to this term"></a></dt><dd><p>Alerts and reminders sent to users about workouts, progress milestones, and system updates.</p>
</dd>
<dt id="term-Sync">Sync<a class="headerlink" href="#term-Sync" title="Link to this term"></a></dt><dd><p>The process of updating data across multiple devices or platforms to ensure consistency.</p>
</dd>
</dl>
</section>
<section id="data-terms">
<h2>Data Terms<a class="headerlink" href="#data-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-Training-Data">Training Data<a class="headerlink" href="#term-Training-Data" title="Link to this term"></a></dt><dd><p>Information collected about user workouts, including exercises performed, sets, reps, weights, and subjective feedback.</p>
</dd>
<dt id="term-Performance-Data">Performance Data<a class="headerlink" href="#term-Performance-Data" title="Link to this term"></a></dt><dd><p>Metrics that track user progress over time, including strength gains, volume progression, and goal achievement.</p>
</dd>
<dt id="term-Biometric-Data">Biometric Data<a class="headerlink" href="#term-Biometric-Data" title="Link to this term"></a></dt><dd><p>Physical measurements and health indicators that may influence training recommendations.</p>
</dd>
<dt id="term-Feedback-Data">Feedback Data<a class="headerlink" href="#term-Feedback-Data" title="Link to this term"></a></dt><dd><p>User-provided information about workout difficulty, soreness, recovery, and satisfaction.</p>
</dd>
<dt id="term-Analytics">Analytics<a class="headerlink" href="#term-Analytics" title="Link to this term"></a></dt><dd><p>The systematic analysis of training data to identify patterns, trends, and opportunities for improvement.</p>
</dd>
<dt id="term-Data-Export">Data Export<a class="headerlink" href="#term-Data-Export" title="Link to this term"></a></dt><dd><p>The ability to extract user data in standard formats for backup or analysis purposes.</p>
</dd>
<dt id="term-Data-Privacy">Data Privacy<a class="headerlink" href="#term-Data-Privacy" title="Link to this term"></a></dt><dd><p>The protection of user information and compliance with data protection regulations.</p>
</dd>
<dt id="term-Data-Retention">Data Retention<a class="headerlink" href="#term-Data-Retention" title="Link to this term"></a></dt><dd><p>Policies governing how long user data is stored and when it is deleted.</p>
</dd>
</dl>
</section>
<section id="integration-terms">
<h2>Integration Terms<a class="headerlink" href="#integration-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-SDK-Software-Development-Kit">SDK (Software Development Kit)<a class="headerlink" href="#term-SDK-Software-Development-Kit" title="Link to this term"></a></dt><dd><p>A collection of software development tools and libraries for building applications that integrate with Forge Protocol.</p>
</dd>
<dt id="term-Webhook">Webhook<a class="headerlink" href="#term-Webhook" title="Link to this term"></a></dt><dd><p>A method of augmenting or altering the behaviour of a web application with custom callbacks.</p>
</dd>
<dt id="term-Third-Party-Integration">Third-Party Integration<a class="headerlink" href="#term-Third-Party-Integration" title="Link to this term"></a></dt><dd><p>The connection of Forge Protocol with external services and applications.</p>
</dd>
<dt id="term-API-Key">API Key<a class="headerlink" href="#term-API-Key" title="Link to this term"></a></dt><dd><p>A unique identifier used to authenticate requests to the Forge Protocol API.</p>
</dd>
<dt id="term-Rate-Limiting">Rate Limiting<a class="headerlink" href="#term-Rate-Limiting" title="Link to this term"></a></dt><dd><p>The practice of limiting the number of API requests a user can make within a specific time period.</p>
</dd>
<dt id="term-Endpoint">Endpoint<a class="headerlink" href="#term-Endpoint" title="Link to this term"></a></dt><dd><p>A specific URL where an API can be accessed by a client application.</p>
</dd>
<dt id="term-Schema">Schema<a class="headerlink" href="#term-Schema" title="Link to this term"></a></dt><dd><p>A description of the structure and format of API requests and responses.</p>
</dd>
<dt id="term-Authentication">Authentication<a class="headerlink" href="#term-Authentication" title="Link to this term"></a></dt><dd><p>The process of verifying the identity of a user or application accessing the API.</p>
</dd>
</dl>
</section>
<section id="business-terms">
<h2>Business Terms<a class="headerlink" href="#business-terms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-SaaS-Software-as-a-Service">SaaS (Software as a Service)<a class="headerlink" href="#term-SaaS-Software-as-a-Service" title="Link to this term"></a></dt><dd><p>A software distribution model where applications are hosted by a service provider and made available to customers over the internet.</p>
</dd>
<dt id="term-Subscription">Subscription<a class="headerlink" href="#term-Subscription" title="Link to this term"></a></dt><dd><p>A recurring payment model that provides ongoing access to Forge Protocol services.</p>
</dd>
<dt id="term-Enterprise">Enterprise<a class="headerlink" href="#term-Enterprise" title="Link to this term"></a></dt><dd><p>Large-scale deployment of Forge Protocol for organisations with advanced requirements.</p>
</dd>
<dt id="term-Support-Tier">Support Tier<a class="headerlink" href="#term-Support-Tier" title="Link to this term"></a></dt><dd><p>Different levels of customer support based on subscription level and requirements.</p>
</dd>
<dt id="term-SLA-Service-Level-Agreement">SLA (Service Level Agreement)<a class="headerlink" href="#term-SLA-Service-Level-Agreement" title="Link to this term"></a></dt><dd><p>A commitment between Forge Protocol and users regarding service availability and performance.</p>
</dd>
<dt id="term-Compliance">Compliance<a class="headerlink" href="#term-Compliance" title="Link to this term"></a></dt><dd><p>Adherence to relevant regulations and standards, including data protection and security requirements.</p>
</dd>
<dt id="term-Scalability">Scalability<a class="headerlink" href="#term-Scalability" title="Link to this term"></a></dt><dd><p>The ability of Forge Protocol to handle increased load and user growth.</p>
</dd>
<dt id="term-Uptime">Uptime<a class="headerlink" href="#term-Uptime" title="Link to this term"></a></dt><dd><p>The percentage of time that Forge Protocol services are operational and accessible.</p>
</dd>
</dl>
</section>
<section id="abbreviations-and-acronyms">
<h2>Abbreviations and Acronyms<a class="headerlink" href="#abbreviations-and-acronyms" title="Link to this heading"></a></h2>
<dl class="simple glossary">
<dt id="term-RP">RP<a class="headerlink" href="#term-RP" title="Link to this term"></a></dt><dd><p>Renaissance Periodisation - The scientific methodology underlying Forge Protocol.</p>
</dd>
<dt id="term-API">API<a class="headerlink" href="#term-API" title="Link to this term"></a></dt><dd><p>Application Programming Interface</p>
</dd>
<dt id="term-REST">REST<a class="headerlink" href="#term-REST" title="Link to this term"></a></dt><dd><p>Representational State Transfer</p>
</dd>
<dt id="term-JWT">JWT<a class="headerlink" href="#term-JWT" title="Link to this term"></a></dt><dd><p>JSON Web Token</p>
</dd>
<dt id="term-ORM">ORM<a class="headerlink" href="#term-ORM" title="Link to this term"></a></dt><dd><p>Object-Relational Mapping</p>
</dd>
<dt id="term-SQL">SQL<a class="headerlink" href="#term-SQL" title="Link to this term"></a></dt><dd><p>Structured Query Language</p>
</dd>
<dt id="term-HTTP">HTTP<a class="headerlink" href="#term-HTTP" title="Link to this term"></a></dt><dd><p>Hypertext Transfer Protocol</p>
</dd>
<dt id="term-HTTPS">HTTPS<a class="headerlink" href="#term-HTTPS" title="Link to this term"></a></dt><dd><p>Hypertext Transfer Protocol Secure</p>
</dd>
<dt id="term-JSON">JSON<a class="headerlink" href="#term-JSON" title="Link to this term"></a></dt><dd><p>JavaScript Object Notation</p>
</dd>
<dt id="term-UUID">UUID<a class="headerlink" href="#term-UUID" title="Link to this term"></a></dt><dd><p>Universally Unique Identifier</p>
</dd>
<dt id="term-CORS">CORS<a class="headerlink" href="#term-CORS" title="Link to this term"></a></dt><dd><p>Cross-Origin Resource Sharing</p>
</dd>
<dt id="term-CRUD">CRUD<a class="headerlink" href="#term-CRUD" title="Link to this term"></a></dt><dd><p>Create, Read, Update, Delete</p>
</dd>
<dt id="term-CI-CD">CI/CD<a class="headerlink" href="#term-CI-CD" title="Link to this term"></a></dt><dd><p>Continuous Integration/Continuous Deployment</p>
</dd>
<dt id="term-TDD">TDD<a class="headerlink" href="#term-TDD" title="Link to this term"></a></dt><dd><p>Test-Driven Development</p>
</dd>
<dt id="term-BDD">BDD<a class="headerlink" href="#term-BDD" title="Link to this term"></a></dt><dd><p>Behaviour-Driven Development</p>
</dd>
<dt id="term-DRY">DRY<a class="headerlink" href="#term-DRY" title="Link to this term"></a></dt><dd><p>Don’t Repeat Yourself</p>
</dd>
<dt id="term-SOLID">SOLID<a class="headerlink" href="#term-SOLID" title="Link to this term"></a></dt><dd><p>Single Responsibility, Open-Closed, Liskov Substitution, Interface Segregation, Dependency Inversion</p>
</dd>
<dt id="term-GDPR">GDPR<a class="headerlink" href="#term-GDPR" title="Link to this term"></a></dt><dd><p>General Data Protection Regulation</p>
</dd>
<dt id="term-HIPAA">HIPAA<a class="headerlink" href="#term-HIPAA" title="Link to this term"></a></dt><dd><p>Health Insurance Portability and Accountability Act</p>
</dd>
<dt id="term-SSL-TLS">SSL/TLS<a class="headerlink" href="#term-SSL-TLS" title="Link to this term"></a></dt><dd><p>Secure Sockets Layer/Transport Layer Security</p>
</dd>
<dt id="term-AWS">AWS<a class="headerlink" href="#term-AWS" title="Link to this term"></a></dt><dd><p>Amazon Web Services</p>
</dd>
<dt id="term-GCP">GCP<a class="headerlink" href="#term-GCP" title="Link to this term"></a></dt><dd><p>Google Cloud Platform</p>
</dd>
<dt id="term-CDN">CDN<a class="headerlink" href="#term-CDN" title="Link to this term"></a></dt><dd><p>Content Delivery Network</p>
</dd>
<dt id="term-DNS">DNS<a class="headerlink" href="#term-DNS" title="Link to this term"></a></dt><dd><p>Domain Name System</p>
</dd>
<dt id="term-VPC">VPC<a class="headerlink" href="#term-VPC" title="Link to this term"></a></dt><dd><p>Virtual Private Cloud</p>
</dd>
<dt id="term-IAM">IAM<a class="headerlink" href="#term-IAM" title="Link to this term"></a></dt><dd><p>Identity and Access Management</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This glossary is continuously updated as new terms and concepts are introduced to Forge Protocol.
If you encounter unfamiliar terms not listed here, please refer to the specific documentation
sections or contact our support team.</p>
</div>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p>Use Ctrl+F (Cmd+F on Mac) to quickly search for specific terms within this glossary.
Many terms are also linked throughout the documentation for easy reference.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="license.html" class="btn btn-neutral float-left" title="License" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>