

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="_static/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="_static/copybutton.js?v=30646c52"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Forge Protocol
              <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#J"><strong>J</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#V"><strong>V</strong></a>
 | <a href="#W"><strong>W</strong></a>
 
</div>
<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Adaptive-Algorithm"><strong>Adaptive Algorithm</strong></a>
</li>
      <li><a href="glossary.html#term-Adjustment-Factor"><strong>Adjustment Factor</strong></a>
</li>
      <li><a href="glossary.html#term-Analytics"><strong>Analytics</strong></a>
</li>
      <li><a href="glossary.html#term-API"><strong>API</strong></a>

      <ul>
        <li><a href="glossary.html#term-API-Application-Programming-Interface"><strong>(Application Programming Interface)</strong></a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-API-Key"><strong>API Key</strong></a>
</li>
      <li><a href="glossary.html#term-Authentication"><strong>Authentication</strong></a>
</li>
      <li><a href="glossary.html#term-AWS"><strong>AWS</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Baseline-MEV"><strong>Baseline MEV</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-BDD"><strong>BDD</strong></a>
</li>
      <li><a href="glossary.html#term-Biometric-Data"><strong>Biometric Data</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-CDN"><strong>CDN</strong></a>
</li>
      <li><a href="glossary.html#term-CI-CD"><strong>CI/CD</strong></a>
</li>
      <li><a href="glossary.html#term-Clean-Architecture"><strong>Clean Architecture</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Compliance"><strong>Compliance</strong></a>
</li>
      <li><a href="glossary.html#term-CORS"><strong>CORS</strong></a>
</li>
      <li><a href="glossary.html#term-CRUD"><strong>CRUD</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Dashboard"><strong>Dashboard</strong></a>
</li>
      <li><a href="glossary.html#term-Data-Export"><strong>Data Export</strong></a>
</li>
      <li><a href="glossary.html#term-Data-Privacy"><strong>Data Privacy</strong></a>
</li>
      <li><a href="glossary.html#term-Data-Retention"><strong>Data Retention</strong></a>
</li>
      <li><a href="glossary.html#term-Deload"><strong>Deload</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Dependency-Injection"><strong>Dependency Injection</strong></a>
</li>
      <li><a href="glossary.html#term-DNS"><strong>DNS</strong></a>
</li>
      <li><a href="glossary.html#term-Docker"><strong>Docker</strong></a>
</li>
      <li><a href="glossary.html#term-Domain-Driven-Design-DDD"><strong>Domain-Driven Design (DDD)</strong></a>
</li>
      <li><a href="glossary.html#term-DRY"><strong>DRY</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Endpoint"><strong>Endpoint</strong></a>
</li>
      <li><a href="glossary.html#term-Enterprise"><strong>Enterprise</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Exercise-Database"><strong>Exercise Database</strong></a>
</li>
      <li><a href="glossary.html#term-Exercise-Selection"><strong>Exercise Selection</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Fatigue-Accumulation"><strong>Fatigue Accumulation</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Feedback-Data"><strong>Feedback Data</strong></a>
</li>
      <li><a href="glossary.html#term-Form-Cues"><strong>Form Cues</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-GCP"><strong>GCP</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-GDPR"><strong>GDPR</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-HIPAA"><strong>HIPAA</strong></a>
</li>
      <li><a href="glossary.html#term-HTTP"><strong>HTTP</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-HTTPS"><strong>HTTPS</strong></a>
</li>
      <li><a href="glossary.html#term-Hypertrophy"><strong>Hypertrophy</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-IAM"><strong>IAM</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-JSON"><strong>JSON</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-JWT"><strong>JWT</strong></a>

      <ul>
        <li><a href="glossary.html#term-JWT-JSON-Web-Token"><strong>(JSON Web Token)</strong></a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Machine-Learning"><strong>Machine Learning</strong></a>
</li>
      <li><a href="glossary.html#term-MAV-Maximum-Adaptive-Volume"><strong>MAV (Maximum Adaptive Volume)</strong></a>
</li>
      <li><a href="glossary.html#term-Mesocycle"><strong>Mesocycle</strong></a>
</li>
      <li><a href="glossary.html#term-MEV-Minimum-Effective-Volume"><strong>MEV (Minimum Effective Volume)</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Migration"><strong>Migration</strong></a>
</li>
      <li><a href="glossary.html#term-Movement-Pattern"><strong>Movement Pattern</strong></a>
</li>
      <li><a href="glossary.html#term-MRV-Maximum-Recoverable-Volume"><strong>MRV (Maximum Recoverable Volume)</strong></a>
</li>
      <li><a href="glossary.html#term-Muscle-Group"><strong>Muscle Group</strong></a>
</li>
      <li><a href="glossary.html#term-MV-Maintenance-Volume"><strong>MV (Maintenance Volume)</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Notifications"><strong>Notifications</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-ORM"><strong>ORM</strong></a>

      <ul>
        <li><a href="glossary.html#term-ORM-Object-Relational-Mapping"><strong>(Object-Relational Mapping)</strong></a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Performance-Data"><strong>Performance Data</strong></a>
</li>
      <li><a href="glossary.html#term-Performance-Metrics"><strong>Performance Metrics</strong></a>
</li>
      <li><a href="glossary.html#term-Periodisation"><strong>Periodisation</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Progress-Tracking"><strong>Progress Tracking</strong></a>
</li>
      <li><a href="glossary.html#term-Progressive-Overload"><strong>Progressive Overload</strong></a>
</li>
      <li><a href="glossary.html#term-Pydantic"><strong>Pydantic</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Rate-Limiting"><strong>Rate Limiting</strong></a>
</li>
      <li><a href="glossary.html#term-Readiness-Score"><strong>Readiness Score</strong></a>
</li>
      <li><a href="glossary.html#term-Repository-Pattern"><strong>Repository Pattern</strong></a>
</li>
      <li><a href="glossary.html#term-REST"><strong>REST</strong></a>

      <ul>
        <li><a href="glossary.html#term-REST-Representational-State-Transfer"><strong>(Representational State Transfer)</strong></a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-RIR-Reps-in-Reserve"><strong>RIR (Reps in Reserve)</strong></a>
</li>
      <li><a href="glossary.html#term-RP"><strong>RP</strong></a>
</li>
      <li><a href="glossary.html#term-RPE-Rate-of-Perceived-Exertion"><strong>RPE (Rate of Perceived Exertion)</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-SaaS-Software-as-a-Service"><strong>SaaS (Software as a Service)</strong></a>
</li>
      <li><a href="glossary.html#term-Scalability"><strong>Scalability</strong></a>
</li>
      <li><a href="glossary.html#term-Schema"><strong>Schema</strong></a>
</li>
      <li><a href="glossary.html#term-SDK-Software-Development-Kit"><strong>SDK (Software Development Kit)</strong></a>
</li>
      <li><a href="glossary.html#term-Set-Progression"><strong>Set Progression</strong></a>
</li>
      <li><a href="glossary.html#term-Settings"><strong>Settings</strong></a>
</li>
      <li><a href="glossary.html#term-SFR-Stimulus-to-Fatigue-Ratio"><strong>SFR (Stimulus-to-Fatigue Ratio)</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-SLA-Service-Level-Agreement"><strong>SLA (Service Level Agreement)</strong></a>
</li>
      <li><a href="glossary.html#term-SOLID"><strong>SOLID</strong></a>
</li>
      <li><a href="glossary.html#term-SQL"><strong>SQL</strong></a>
</li>
      <li><a href="glossary.html#term-SSL-TLS"><strong>SSL/TLS</strong></a>
</li>
      <li><a href="glossary.html#term-Subscription"><strong>Subscription</strong></a>
</li>
      <li><a href="glossary.html#term-Support-Tier"><strong>Support Tier</strong></a>
</li>
      <li><a href="glossary.html#term-Sync"><strong>Sync</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-TDD"><strong>TDD</strong></a>
</li>
      <li><a href="glossary.html#term-Third-Party-Integration"><strong>Third-Party Integration</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Training-Block"><strong>Training Block</strong></a>
</li>
      <li><a href="glossary.html#term-Training-Data"><strong>Training Data</strong></a>
</li>
      <li><a href="glossary.html#term-Training-Frequency"><strong>Training Frequency</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Uptime"><strong>Uptime</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-User-Profile"><strong>User Profile</strong></a>
</li>
      <li><a href="glossary.html#term-UUID"><strong>UUID</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Validation-Testing"><strong>Validation Testing</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Volume-Landmarks"><strong>Volume Landmarks</strong></a>
</li>
      <li><a href="glossary.html#term-VPC"><strong>VPC</strong></a>
</li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Webhook"><strong>Webhook</strong></a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="glossary.html#term-Workout-Log"><strong>Workout Log</strong></a>
</li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>