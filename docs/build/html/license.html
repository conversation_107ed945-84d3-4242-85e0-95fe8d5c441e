

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>License &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="_static/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="_static/copybutton.js?v=30646c52"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Glossary" href="glossary.html" />
    <link rel="prev" title="Contributing to Forge Protocol" href="contributing.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Forge Protocol
              <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">License</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#mit-license">MIT License</a></li>
<li class="toctree-l2"><a class="reference internal" href="#third-party-licenses">Third-Party Licenses</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#fastapi">FastAPI</a></li>
<li class="toctree-l3"><a class="reference internal" href="#sqlalchemy">SQLAlchemy</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pydantic">Pydantic</a></li>
<li class="toctree-l3"><a class="reference internal" href="#postgresql">PostgreSQL</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker">Docker</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pytest">pytest</a></li>
<li class="toctree-l3"><a class="reference internal" href="#alembic">Alembic</a></li>
<li class="toctree-l3"><a class="reference internal" href="#bcrypt">bcrypt</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pyjwt">PyJWT</a></li>
<li class="toctree-l3"><a class="reference internal" href="#uvicorn">uvicorn</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#scientific-references">Scientific References</a></li>
<li class="toctree-l2"><a class="reference internal" href="#data-and-privacy">Data and Privacy</a></li>
<li class="toctree-l2"><a class="reference internal" href="#disclaimer">Disclaimer</a></li>
<li class="toctree-l2"><a class="reference internal" href="#attribution">Attribution</a></li>
<li class="toctree-l2"><a class="reference internal" href="#contact-information">Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="#updates-to-license">Updates to License</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">License</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/license.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="license">
<h1>License<a class="headerlink" href="#license" title="Link to this heading"></a></h1>
<section id="mit-license">
<h2>MIT License<a class="headerlink" href="#mit-license" title="Link to this heading"></a></h2>
<p>Copyright (c) 2025 Forge Protocol Team</p>
<p>Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the “Software”), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:</p>
<p>The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.</p>
<p>THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.</p>
</section>
<section id="third-party-licenses">
<h2>Third-Party Licenses<a class="headerlink" href="#third-party-licenses" title="Link to this heading"></a></h2>
<p>Forge Protocol includes and depends on several open-source libraries and frameworks.
Below are the licenses for the major dependencies:</p>
<section id="fastapi">
<h3>FastAPI<a class="headerlink" href="#fastapi" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2018 Sebastián Ramírez
<strong>URL</strong>: <a class="reference external" href="https://github.com/tiangolo/fastapi">https://github.com/tiangolo/fastapi</a></p>
</section>
<section id="sqlalchemy">
<h3>SQLAlchemy<a class="headerlink" href="#sqlalchemy" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2006-2023 the SQLAlchemy authors and contributors
<strong>URL</strong>: <a class="reference external" href="https://github.com/sqlalchemy/sqlalchemy">https://github.com/sqlalchemy/sqlalchemy</a></p>
</section>
<section id="pydantic">
<h3>Pydantic<a class="headerlink" href="#pydantic" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2017 to present Pydantic Services Inc. and individual contributors
<strong>URL</strong>: <a class="reference external" href="https://github.com/pydantic/pydantic">https://github.com/pydantic/pydantic</a></p>
</section>
<section id="postgresql">
<h3>PostgreSQL<a class="headerlink" href="#postgresql" title="Link to this heading"></a></h3>
<p><strong>License</strong>: PostgreSQL License (similar to BSD or MIT)
<strong>Copyright</strong>: Copyright (c) 1996-2023, The PostgreSQL Global Development Group
<strong>URL</strong>: <a class="reference external" href="https://www.postgresql.org/">https://www.postgresql.org/</a></p>
</section>
<section id="docker">
<h3>Docker<a class="headerlink" href="#docker" title="Link to this heading"></a></h3>
<p><strong>License</strong>: Apache License 2.0
<strong>Copyright</strong>: Copyright 2013-2023 Docker, Inc.
<strong>URL</strong>: <a class="reference external" href="https://github.com/docker/docker">https://github.com/docker/docker</a></p>
</section>
<section id="pytest">
<h3>pytest<a class="headerlink" href="#pytest" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2004 Holger Krekel and others
<strong>URL</strong>: <a class="reference external" href="https://github.com/pytest-dev/pytest">https://github.com/pytest-dev/pytest</a></p>
</section>
<section id="alembic">
<h3>Alembic<a class="headerlink" href="#alembic" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2009-2023 by the Alembic authors and contributors
<strong>URL</strong>: <a class="reference external" href="https://github.com/sqlalchemy/alembic">https://github.com/sqlalchemy/alembic</a></p>
</section>
<section id="bcrypt">
<h3>bcrypt<a class="headerlink" href="#bcrypt" title="Link to this heading"></a></h3>
<p><strong>License</strong>: Apache License 2.0
<strong>Copyright</strong>: Copyright (c) 2013 Donald Stufft and individual contributors
<strong>URL</strong>: <a class="reference external" href="https://github.com/pyca/bcrypt">https://github.com/pyca/bcrypt</a></p>
</section>
<section id="pyjwt">
<h3>PyJWT<a class="headerlink" href="#pyjwt" title="Link to this heading"></a></h3>
<p><strong>License</strong>: MIT License
<strong>Copyright</strong>: Copyright (c) 2015 José Padilla
<strong>URL</strong>: <a class="reference external" href="https://github.com/jpadilla/pyjwt">https://github.com/jpadilla/pyjwt</a></p>
</section>
<section id="uvicorn">
<h3>uvicorn<a class="headerlink" href="#uvicorn" title="Link to this heading"></a></h3>
<p><strong>License</strong>: BSD 3-Clause License
<strong>Copyright</strong>: Copyright (c) 2017-present, Encode OSS Ltd.
<strong>URL</strong>: <a class="reference external" href="https://github.com/encode/uvicorn">https://github.com/encode/uvicorn</a></p>
</section>
</section>
<section id="scientific-references">
<h2>Scientific References<a class="headerlink" href="#scientific-references" title="Link to this heading"></a></h2>
<p>The scientific methodology implemented in Forge Protocol is based on research and principles
developed by Renaissance Periodisation and the broader exercise science community. While the
code implementation is licensed under MIT, the scientific principles and methodologies are
based on published research and established training principles.</p>
<p><strong>Key Scientific Sources:</strong></p>
<ul class="simple">
<li><p>Renaissance Periodisation methodology and principles</p></li>
<li><p>“Scientific Principles of Hypertrophy Training” by Dr. Mike Israetel et al.</p></li>
<li><p>Peer-reviewed research in exercise science and sports medicine</p></li>
<li><p>Evidence-based training principles from the scientific community</p></li>
</ul>
</section>
<section id="data-and-privacy">
<h2>Data and Privacy<a class="headerlink" href="#data-and-privacy" title="Link to this heading"></a></h2>
<p><strong>User Data</strong>
User data collected and processed by Forge Protocol is subject to our Privacy Policy
and Terms of Service. The software license does not grant rights to user data.</p>
<p><strong>Training Data</strong>
Aggregated and anonymised training data may be used for research purposes in accordance
with our Privacy Policy and applicable data protection regulations.</p>
<p><strong>Scientific Data</strong>
Exercise databases, training algorithms, and scientific methodologies are provided
under the MIT license for research and educational purposes.</p>
</section>
<section id="disclaimer">
<h2>Disclaimer<a class="headerlink" href="#disclaimer" title="Link to this heading"></a></h2>
<p><strong>No Warranty</strong>
This software is provided “as is” without warranty of any kind. The authors and
contributors are not liable for any damages arising from the use of this software.</p>
<p><strong>Medical Disclaimer</strong>
Forge Protocol is not intended to provide medical advice. Users should consult with
qualified healthcare professionals before beginning any exercise program. The software
is for informational and educational purposes only.</p>
<p><strong>Fitness Disclaimer</strong>
While based on scientific principles, individual responses to training may vary.
Users should listen to their bodies and adjust training as needed. The software
provides general guidance and should not replace professional coaching or medical advice.</p>
<p><strong>Research Disclaimer</strong>
The algorithms and methodologies implemented are based on current scientific understanding
and may be updated as new research emerges. Results may vary based on individual factors
and adherence to the program.</p>
</section>
<section id="attribution">
<h2>Attribution<a class="headerlink" href="#attribution" title="Link to this heading"></a></h2>
<p>If you use Forge Protocol in your research, applications, or publications, we appreciate
attribution. Suggested citation:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Forge Protocol Team. (2025). Forge Protocol: Evidence-based hypertrophy training platform.
GitHub. https://github.com/forkrul/forge-protocol
</pre></div>
</div>
<p>For academic publications, please also cite the relevant scientific sources that inform
the methodology implemented in the platform.</p>
</section>
<section id="contact-information">
<h2>Contact Information<a class="headerlink" href="#contact-information" title="Link to this heading"></a></h2>
<p>For questions about licensing, usage rights, or commercial applications:</p>
<ul class="simple">
<li><p><strong>General Inquiries</strong>: <a class="reference external" href="mailto:hello&#37;&#52;&#48;forgeprotocol&#46;com">hello<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a></p></li>
<li><p><strong>Legal Questions</strong>: <a class="reference external" href="mailto:legal&#37;&#52;&#48;forgeprotocol&#46;com">legal<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a></p></li>
<li><p><strong>Commercial Licensing</strong>: <a class="reference external" href="mailto:business&#37;&#52;&#48;forgeprotocol&#46;com">business<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a></p></li>
<li><p><strong>Research Collaboration</strong>: <a class="reference external" href="mailto:research&#37;&#52;&#48;forgeprotocol&#46;com">research<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a></p></li>
</ul>
<p><strong>Mailing Address</strong>:
Forge Protocol Team
[Address to be provided]</p>
<p><strong>GitHub Repository</strong>:
<a class="reference external" href="https://github.com/forkrul/forge-protocol">https://github.com/forkrul/forge-protocol</a></p>
</section>
<section id="updates-to-license">
<h2>Updates to License<a class="headerlink" href="#updates-to-license" title="Link to this heading"></a></h2>
<p>This license may be updated from time to time. Users will be notified of significant
changes through the project repository and official communication channels.</p>
<p><strong>Version History</strong>:
* v1.0 (2025-01-06): Initial MIT license
* Future versions will be documented here</p>
<p>For the most current version of this license, please refer to the LICENSE file in the
project repository: <a class="reference external" href="https://github.com/forkrul/forge-protocol/blob/main/LICENSE">https://github.com/forkrul/forge-protocol/blob/main/LICENSE</a></p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="contributing.html" class="btn btn-neutral float-left" title="Contributing to Forge Protocol" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="glossary.html" class="btn btn-neutral float-right" title="Glossary" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>