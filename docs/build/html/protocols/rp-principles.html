

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Renaissance Periodisation Principles &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
      <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="⚙️ Getting Started (Admin)" href="../admin/index.html" />
    <link rel="prev" title="🔬 Protocols &amp; Science" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">🔬 Protocols &amp; Science</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#overview">Overview</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#core-scientific-principles">🧬 Core Scientific Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#implementation-approach">📊 Implementation Approach</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#key-algorithms">🎯 Key Algorithms</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#documentation-structure">📚 Documentation Structure</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Renaissance Periodisation Principles</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#research-foundation">🔬 Research Foundation</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#validation-methodology">📈 Validation Methodology</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#educational-resources">🎓 Educational Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#getting-started">Getting Started</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Renaissance Periodisation Principles</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-principles">🎯 Core Principles</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#volume-landmarks">Volume Landmarks</a></li>
<li class="toctree-l3"><a class="reference internal" href="#stimulus-to-fatigue-ratio-sfr">Stimulus-to-Fatigue Ratio (SFR)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#progressive-overload-strategies">Progressive Overload Strategies</a></li>
<li class="toctree-l3"><a class="reference internal" href="#periodisation-framework">Periodisation Framework</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#mathematical-models">🧮 Mathematical Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#volume-landmark-calculations">Volume Landmark Calculations</a></li>
<li class="toctree-l3"><a class="reference internal" href="#sfr-calculation">SFR Calculation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#volume-progression-algorithm">Volume Progression Algorithm</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#practical-implementation">🎯 Practical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#individual-assessment">Individual Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#programme-design-process">Programme Design Process</a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-and-adjustment">Monitoring and Adjustment</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">🔬 Protocols &amp; Science</a></li>
      <li class="breadcrumb-item active">Renaissance Periodisation Principles</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/protocols/rp-principles.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="renaissance-periodisation-principles">
<h1>Renaissance Periodisation Principles<a class="headerlink" href="#renaissance-periodisation-principles" title="Link to this heading"></a></h1>
<p>This section details the core Renaissance Periodisation (RP) principles that form the scientific foundation of Forge Protocol.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Renaissance Periodisation represents a systematic, evidence-based approach to hypertrophy training developed by Dr. Mike Israetel and the RP team. The methodology emphasises precise volume management, intelligent exercise selection, and strategic periodisation to optimise muscle growth whilst minimising injury risk and overtraining.</p>
</section>
<section id="core-principles">
<h2>🎯 Core Principles<a class="headerlink" href="#core-principles" title="Link to this heading"></a></h2>
<section id="volume-landmarks">
<h3>Volume Landmarks<a class="headerlink" href="#volume-landmarks" title="Link to this heading"></a></h3>
<p>The foundation of RP methodology centres on understanding and applying volume landmarks for each muscle group:</p>
<dl class="simple">
<dt><strong>Maintenance Volume (MV)</strong></dt><dd><p>The minimum training volume required to maintain current muscle mass and strength.</p>
</dd>
<dt><strong>Minimum Effective Volume (MEV)</strong></dt><dd><p>The lowest training volume that produces measurable hypertrophy adaptations.</p>
</dd>
<dt><strong>Maximum Adaptive Volume (MAV)</strong></dt><dd><p>The training volume that produces the greatest rate of hypertrophy adaptations.</p>
</dd>
<dt><strong>Maximum Recoverable Volume (MRV)</strong></dt><dd><p>The highest training volume from which an individual can recover within their normal recovery timeframe.</p>
</dd>
</dl>
</section>
<section id="stimulus-to-fatigue-ratio-sfr">
<h3>Stimulus-to-Fatigue Ratio (SFR)<a class="headerlink" href="#stimulus-to-fatigue-ratio-sfr" title="Link to this heading"></a></h3>
<p>The SFR concept evaluates exercise effectiveness by comparing the hypertrophic stimulus generated to the systemic fatigue cost incurred.</p>
<dl class="simple">
<dt><strong>High SFR Exercises</strong></dt><dd><ul class="simple">
<li><p>Generate significant muscle activation with minimal systemic fatigue</p></li>
<li><p>Allow for higher training frequencies and volumes</p></li>
<li><p>Examples: Isolation exercises, machine-based movements</p></li>
</ul>
</dd>
<dt><strong>Moderate SFR Exercises</strong></dt><dd><ul class="simple">
<li><p>Provide good muscle stimulus with moderate fatigue cost</p></li>
<li><p>Form the backbone of most training programmes</p></li>
<li><p>Examples: Compound movements with moderate loading</p></li>
</ul>
</dd>
<dt><strong>Low SFR Exercises</strong></dt><dd><ul class="simple">
<li><p>Generate high systemic fatigue relative to muscle-specific stimulus</p></li>
<li><p>Used strategically for strength and movement quality</p></li>
<li><p>Examples: Heavy compound lifts, high-skill movements</p></li>
</ul>
</dd>
</dl>
</section>
<section id="progressive-overload-strategies">
<h3>Progressive Overload Strategies<a class="headerlink" href="#progressive-overload-strategies" title="Link to this heading"></a></h3>
<p>RP emphasises multiple vectors of progressive overload:</p>
<dl class="simple">
<dt><strong>Volume Progression</strong></dt><dd><ul class="simple">
<li><p>Primary driver of hypertrophy adaptations</p></li>
<li><p>Systematic increase in weekly training volume</p></li>
<li><p>Progression rate: 1-3 sets per muscle group per week</p></li>
</ul>
</dd>
<dt><strong>Intensity Progression</strong></dt><dd><ul class="simple">
<li><p>Secondary driver through load increases</p></li>
<li><p>Maintained within hypertrophy-specific rep ranges</p></li>
<li><p>Progression rate: 2.5-5% load increases when rep targets exceeded</p></li>
</ul>
</dd>
<dt><strong>Frequency Progression</strong></dt><dd><ul class="simple">
<li><p>Increased training frequency as volume requirements grow</p></li>
<li><p>Allows for higher weekly volumes whilst managing session fatigue</p></li>
<li><p>Progression: From 1x to 3x per week per muscle group</p></li>
</ul>
</dd>
<dt><strong>Range of Motion Progression</strong></dt><dd><ul class="simple">
<li><p>Emphasis on full range of motion for maximum stimulus</p></li>
<li><p>Progressive lengthening of range where possible</p></li>
<li><p>Focus on stretched position emphasis</p></li>
</ul>
</dd>
</dl>
</section>
<section id="periodisation-framework">
<h3>Periodisation Framework<a class="headerlink" href="#periodisation-framework" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Mesocycle Structure</strong></dt><dd><ul class="simple">
<li><p>4-8 week training blocks with specific volume progression</p></li>
<li><p>Systematic increase from MEV towards MAV/MRV</p></li>
<li><p>Planned deload every 4-8 weeks based on fatigue accumulation</p></li>
</ul>
</dd>
<dt><strong>Volume Progression Pattern</strong></dt><dd><ul class="simple">
<li><p>Week 1: Start near MEV (conservative beginning)</p></li>
<li><p>Week 2-4: Progressive volume increases</p></li>
<li><p>Week 5-6: Approach MAV/MRV (peak volume)</p></li>
<li><p>Week 7: Deload to MV (recovery and adaptation)</p></li>
</ul>
</dd>
<dt><strong>Deload Strategies</strong></dt><dd><ul class="simple">
<li><p>Volume reduction: 40-60% of peak volume</p></li>
<li><p>Intensity maintenance: Keep loads similar</p></li>
<li><p>Duration: 1 week typically sufficient</p></li>
<li><p>Indicators: Performance decline, excessive soreness, poor recovery</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="mathematical-models">
<h2>🧮 Mathematical Models<a class="headerlink" href="#mathematical-models" title="Link to this heading"></a></h2>
<section id="volume-landmark-calculations">
<h3>Volume Landmark Calculations<a class="headerlink" href="#volume-landmark-calculations" title="Link to this heading"></a></h3>
<p>The platform implements sophisticated algorithms to estimate individual volume landmarks:</p>
<div class="math notranslate nohighlight">
\[MEV_{individual} = MEV_{baseline} \times (1 + experience_{factor} + genetics_{factor})\]</div>
<dl class="simple">
<dt>Where:</dt><dd><ul class="simple">
<li><p><span class="math notranslate nohighlight">\(MEV_{baseline}\)</span> = Research-based starting point</p></li>
<li><p><span class="math notranslate nohighlight">\(experience_{factor}\)</span> = Training history adjustment (-0.2 to +0.3)</p></li>
<li><p><span class="math notranslate nohighlight">\(genetics_{factor}\)</span> = Individual response modifier (-0.3 to +0.5)</p></li>
</ul>
</dd>
</dl>
</section>
<section id="sfr-calculation">
<h3>SFR Calculation<a class="headerlink" href="#sfr-calculation" title="Link to this heading"></a></h3>
<p>Exercise effectiveness is quantified using the SFR formula:</p>
<div class="math notranslate nohighlight">
\[SFR = \frac{Stimulus_{score}}{Fatigue_{cost}} \times Recovery_{factor}\]</div>
<dl class="simple">
<dt>Where:</dt><dd><ul class="simple">
<li><p><span class="math notranslate nohighlight">\(Stimulus_{score}\)</span> = Muscle activation × Range of motion × Load</p></li>
<li><p><span class="math notranslate nohighlight">\(Fatigue_{cost}\)</span> = Neural demand + Metabolic cost + Joint stress</p></li>
<li><p><span class="math notranslate nohighlight">\(Recovery_{factor}\)</span> = Individual recovery capacity modifier</p></li>
</ul>
</dd>
</dl>
</section>
<section id="volume-progression-algorithm">
<h3>Volume Progression Algorithm<a class="headerlink" href="#volume-progression-algorithm" title="Link to this heading"></a></h3>
<p>Weekly volume increases follow an adaptive progression model:</p>
<div class="math notranslate nohighlight">
\[Volume_{week+1} = Volume_{week} + \Delta V \times Readiness_{score}\]</div>
<dl class="simple">
<dt>Where:</dt><dd><ul class="simple">
<li><p><span class="math notranslate nohighlight">\(\Delta V\)</span> = Base progression increment (1-3 sets)</p></li>
<li><p><span class="math notranslate nohighlight">\(Readiness_{score}\)</span> = Recovery and performance indicator (0.5-1.5)</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="practical-implementation">
<h2>🎯 Practical Implementation<a class="headerlink" href="#practical-implementation" title="Link to this heading"></a></h2>
<section id="individual-assessment">
<h3>Individual Assessment<a class="headerlink" href="#individual-assessment" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Training History Evaluation</strong></dt><dd><ul class="simple">
<li><p>Years of consistent training experience</p></li>
<li><p>Previous volume tolerance and response patterns</p></li>
<li><p>Injury history and movement limitations</p></li>
<li><p>Current fitness and strength levels</p></li>
</ul>
</dd>
<dt><strong>Response Pattern Analysis</strong></dt><dd><ul class="simple">
<li><p>Rate of adaptation to volume increases</p></li>
<li><p>Recovery capacity and sleep quality</p></li>
<li><p>Stress levels and lifestyle factors</p></li>
<li><p>Genetic indicators (family history, body type)</p></li>
</ul>
</dd>
<dt><strong>Goal-Specific Adjustments</strong></dt><dd><ul class="simple">
<li><p>Hypertrophy vs. strength emphasis</p></li>
<li><p>Time availability and training frequency</p></li>
<li><p>Equipment access and exercise preferences</p></li>
<li><p>Competition or event preparation requirements</p></li>
</ul>
</dd>
</dl>
</section>
<section id="programme-design-process">
<h3>Programme Design Process<a class="headerlink" href="#programme-design-process" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Baseline Assessment</strong>: Determine current volume landmarks</p></li>
<li><p><strong>Goal Setting</strong>: Define specific hypertrophy objectives</p></li>
<li><p><strong>Exercise Selection</strong>: Choose high-SFR movements for each muscle group</p></li>
<li><p><strong>Volume Planning</strong>: Map mesocycle progression from MEV to MAV</p></li>
<li><p><strong>Frequency Distribution</strong>: Optimise training frequency for volume targets</p></li>
<li><p><strong>Progression Monitoring</strong>: Track performance and adjust accordingly</p></li>
<li><p><strong>Deload Planning</strong>: Schedule recovery periods based on fatigue indicators</p></li>
</ol>
</section>
<section id="monitoring-and-adjustment">
<h3>Monitoring and Adjustment<a class="headerlink" href="#monitoring-and-adjustment" title="Link to this heading"></a></h3>
<dl class="simple">
<dt><strong>Performance Indicators</strong></dt><dd><ul class="simple">
<li><p>Rep performance at given loads</p></li>
<li><p>Rate of Perceived Exertion (RPE) trends</p></li>
<li><p>Muscle soreness patterns and duration</p></li>
<li><p>Sleep quality and recovery metrics</p></li>
</ul>
</dd>
<dt><strong>Adjustment Triggers</strong></dt><dd><ul class="simple">
<li><p>Performance decline over 2+ sessions</p></li>
<li><p>Excessive soreness lasting &gt;48 hours</p></li>
<li><p>RPE increases without load progression</p></li>
<li><p>Poor sleep or elevated resting heart rate</p></li>
</ul>
</dd>
<dt><strong>Modification Strategies</strong></dt><dd><ul class="simple">
<li><p>Volume reduction (10-20% decrease)</p></li>
<li><p>Exercise substitution (lower SFR alternatives)</p></li>
<li><p>Frequency adjustment (redistribute volume)</p></li>
<li><p>Early deload implementation</p></li>
</ul>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These principles represent general guidelines that must be individualised based on personal response patterns,
training history, and lifestyle factors. The algorithms in Forge Protocol automatically adjust these
principles based on user feedback and performance data.</p>
</div>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p>Start conservatively with volume recommendations and allow the adaptive algorithms to guide progression.
The platform learns from your responses and becomes more accurate over time.</p>
</div>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="🔬 Protocols &amp; Science" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../admin/index.html" class="btn btn-neutral float-right" title="⚙️ Getting Started (Admin)" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>