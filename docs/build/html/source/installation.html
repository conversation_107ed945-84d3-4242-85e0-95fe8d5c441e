

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Installation Guide &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Installation Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/source/installation.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="installation-guide">
<h1>Installation Guide<a class="headerlink" href="#installation-guide" title="Link to this heading"></a></h1>
<p>This guide covers installation and setup of the Forge Protocol development environment using multiple approaches.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#quick-start-recommended" id="id1">Quick Start (Recommended)</a></p></li>
<li><p><a class="reference internal" href="#nix-development-environment" id="id2">Nix Development Environment</a></p></li>
<li><p><a class="reference internal" href="#manual-installation" id="id3">Manual Installation</a></p></li>
<li><p><a class="reference internal" href="#docker-development-environment" id="id4">Docker Development Environment</a></p></li>
<li><p><a class="reference internal" href="#development-server" id="id5">Development Server</a></p></li>
<li><p><a class="reference internal" href="#testing-installation" id="id6">Testing Installation</a></p></li>
<li><p><a class="reference internal" href="#ide-configuration" id="id7">IDE Configuration</a></p></li>
<li><p><a class="reference internal" href="#troubleshooting" id="id8">Troubleshooting</a></p></li>
<li><p><a class="reference internal" href="#next-steps" id="id9">Next Steps</a></p></li>
</ul>
</nav>
<section id="quick-start-recommended">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Quick Start (Recommended)</a><a class="headerlink" href="#quick-start-recommended" title="Link to this heading"></a></h2>
<p>The fastest way to get started using Nix:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol

<span class="c1"># Enter Nix development environment</span>
nix<span class="w"> </span>develop

<span class="c1"># Install Python dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements/dev.txt

<span class="c1"># Start services</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Run migrations</span>
python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Start development server</span>
python<span class="w"> </span>-m<span class="w"> </span>uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--reload
</pre></div>
</div>
<p>The API will be available at <a class="reference external" href="http://localhost:8000">http://localhost:8000</a></p>
</section>
<section id="nix-development-environment">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Nix Development Environment</a><a class="headerlink" href="#nix-development-environment" title="Link to this heading"></a></h2>
<p><strong>Prerequisites:</strong></p>
<ul class="simple">
<li><p>Nix package manager (recommended)</p></li>
<li><p>direnv (optional, for automatic environment loading)</p></li>
</ul>
<p><strong>Installation:</strong></p>
<ol class="arabic">
<li><p><strong>Install Nix</strong> (if not already installed):</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Multi-user installation (recommended)</span>
sh<span class="w"> </span>&lt;<span class="o">(</span>curl<span class="w"> </span>-L<span class="w"> </span>https://nixos.org/nix/install<span class="o">)</span><span class="w"> </span>--daemon

<span class="c1"># Enable flakes (required)</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;experimental-features = nix-command flakes&quot;</span><span class="w"> </span>&gt;&gt;<span class="w"> </span>~/.config/nix/nix.conf
</pre></div>
</div>
</li>
<li><p><strong>Install direnv</strong> (optional but recommended):</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># On macOS</span>
brew<span class="w"> </span>install<span class="w"> </span>direnv

<span class="c1"># On Ubuntu/Debian</span>
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>direnv

<span class="c1"># Add to shell (bash/zsh)</span>
<span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;eval &quot;$(direnv hook bash)&quot;&#39;</span><span class="w"> </span>&gt;&gt;<span class="w"> </span>~/.bashrc
<span class="c1"># or for zsh</span>
<span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;eval &quot;$(direnv hook zsh)&quot;&#39;</span><span class="w"> </span>&gt;&gt;<span class="w"> </span>~/.zshrc
</pre></div>
</div>
</li>
<li><p><strong>Setup automatic environment loading</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Allow direnv in the project directory</span>
<span class="nb">cd</span><span class="w"> </span>forge-protocol
direnv<span class="w"> </span>allow
</pre></div>
</div>
</li>
</ol>
<p><strong>Benefits of Nix Environment:</strong></p>
<ul class="simple">
<li><p><strong>Reproducible</strong>: Identical environment across all machines</p></li>
<li><p><strong>Isolated</strong>: No global package pollution</p></li>
<li><p><strong>Complete</strong>: All dependencies included (Python, PostgreSQL, Redis, etc.)</p></li>
<li><p><strong>Automatic</strong>: Environment loads when entering directory (with direnv)</p></li>
</ul>
</section>
<section id="manual-installation">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Manual Installation</a><a class="headerlink" href="#manual-installation" title="Link to this heading"></a></h2>
<p>For systems without Nix support:</p>
<p><strong>Prerequisites:</strong></p>
<ul class="simple">
<li><p>Python 3.11 or higher</p></li>
<li><p>PostgreSQL 15 or higher</p></li>
<li><p>Redis 6 or higher</p></li>
<li><p>Docker and Docker Compose</p></li>
</ul>
<p><strong>System Dependencies:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Ubuntu/Debian</span>
sudo<span class="w"> </span>apt<span class="w"> </span>update
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>python3.11<span class="w"> </span>python3.11-venv<span class="w"> </span>python3-pip<span class="w"> </span>postgresql-15<span class="w"> </span>redis-server

<span class="c1"># macOS (using Homebrew)</span>
brew<span class="w"> </span>install<span class="w"> </span>python@3.11<span class="w"> </span>postgresql@15<span class="w"> </span>redis

<span class="c1"># Arch Linux</span>
sudo<span class="w"> </span>pacman<span class="w"> </span>-S<span class="w"> </span>python<span class="w"> </span>postgresql<span class="w"> </span>redis
</pre></div>
</div>
<p><strong>Python Environment Setup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create virtual environment</span>
python3.11<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate

<span class="c1"># Upgrade pip</span>
pip<span class="w"> </span>install<span class="w"> </span>--upgrade<span class="w"> </span>pip

<span class="c1"># Install dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements/dev.txt
</pre></div>
</div>
<p><strong>Database Setup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start PostgreSQL service</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>start<span class="w"> </span>postgresql<span class="w">  </span><span class="c1"># Linux</span>
brew<span class="w"> </span>services<span class="w"> </span>start<span class="w"> </span>postgresql@15<span class="w">  </span><span class="c1"># macOS</span>

<span class="c1"># Create database and user</span>
sudo<span class="w"> </span>-u<span class="w"> </span>postgres<span class="w"> </span>psql
CREATE<span class="w"> </span>DATABASE<span class="w"> </span>forge_protocol<span class="p">;</span>
CREATE<span class="w"> </span>USER<span class="w"> </span>forge_user<span class="w"> </span>WITH<span class="w"> </span>PASSWORD<span class="w"> </span><span class="s1">&#39;forge_password&#39;</span><span class="p">;</span>
GRANT<span class="w"> </span>ALL<span class="w"> </span>PRIVILEGES<span class="w"> </span>ON<span class="w"> </span>DATABASE<span class="w"> </span>forge_protocol<span class="w"> </span>TO<span class="w"> </span>forge_user<span class="p">;</span>
<span class="se">\q</span>

<span class="c1"># Start Redis service</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>start<span class="w"> </span>redis<span class="w">  </span><span class="c1"># Linux</span>
brew<span class="w"> </span>services<span class="w"> </span>start<span class="w"> </span>redis<span class="w">  </span><span class="c1"># macOS</span>
</pre></div>
</div>
<p><strong>Environment Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Copy environment template</span>
cp<span class="w"> </span>.env.example<span class="w"> </span>.env

<span class="c1"># Edit configuration</span>
nano<span class="w"> </span>.env
</pre></div>
</div>
<p>Example <cite>.env</cite> file:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Database</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span>postgresql+asyncpg://forge_user:forge_password@localhost:5432/forge_protocol

<span class="c1"># Security</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span>your-secret-key-minimum-32-characters-long

<span class="c1"># Environment</span>
<span class="nv">ENVIRONMENT</span><span class="o">=</span>development
<span class="nv">DEBUG</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># Redis</span>
<span class="nv">REDIS_URL</span><span class="o">=</span>redis://localhost:6379/0
</pre></div>
</div>
</section>
<section id="docker-development-environment">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Docker Development Environment</a><a class="headerlink" href="#docker-development-environment" title="Link to this heading"></a></h2>
<p>Using Docker for isolated development:</p>
<p><strong>Prerequisites:</strong></p>
<ul class="simple">
<li><p>Docker 20.10 or higher</p></li>
<li><p>Docker Compose 2.0 or higher</p></li>
</ul>
<p><strong>Setup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol

<span class="c1"># Start all services</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Check service status</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span>ps

<span class="c1"># View logs</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span>logs<span class="w"> </span>-f
</pre></div>
</div>
<p><strong>Services Included:</strong></p>
<ul class="simple">
<li><p><strong>PostgreSQL 15</strong>: Primary database</p></li>
<li><p><strong>Redis 7</strong>: Caching and session storage</p></li>
<li><p><strong>pgAdmin</strong>: Database administration interface</p></li>
</ul>
<p><strong>Database Migration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run migrations</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>app<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Create new migration</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>app<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>revision<span class="w"> </span>--autogenerate<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;Description&quot;</span>
</pre></div>
</div>
</section>
<section id="development-server">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Development Server</a><a class="headerlink" href="#development-server" title="Link to this heading"></a></h2>
<p><strong>Start Development Server:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># With auto-reload (recommended for development)</span>
python<span class="w"> </span>-m<span class="w"> </span>uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--reload<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span>

<span class="c1"># With specific configuration</span>
python<span class="w"> </span>-m<span class="w"> </span>uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--reload<span class="w"> </span>--log-level<span class="w"> </span>debug

<span class="c1"># Production-like mode</span>
python<span class="w"> </span>-m<span class="w"> </span>uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span><span class="w"> </span>--workers<span class="w"> </span><span class="m">4</span>
</pre></div>
</div>
<p><strong>API Documentation:</strong></p>
<p>Once the server is running, access:</p>
<ul class="simple">
<li><p><strong>Interactive API docs</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>Alternative docs</strong>: <a class="reference external" href="http://localhost:8000/redoc">http://localhost:8000/redoc</a></p></li>
<li><p><strong>OpenAPI schema</strong>: <a class="reference external" href="http://localhost:8000/openapi.json">http://localhost:8000/openapi.json</a></p></li>
</ul>
</section>
<section id="testing-installation">
<h2><a class="toc-backref" href="#id6" role="doc-backlink">Testing Installation</a><a class="headerlink" href="#testing-installation" title="Link to this heading"></a></h2>
<p><strong>Verify Installation:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run health check</span>
curl<span class="w"> </span>http://localhost:8000/api/v1/health

<span class="c1"># Expected response:</span>
<span class="o">{</span>
<span class="w">  </span><span class="s2">&quot;status&quot;</span>:<span class="w"> </span><span class="s2">&quot;healthy&quot;</span>,
<span class="w">  </span><span class="s2">&quot;timestamp&quot;</span>:<span class="w"> </span><span class="s2">&quot;2025-01-13T12:00:00Z&quot;</span>,
<span class="w">  </span><span class="s2">&quot;version&quot;</span>:<span class="w"> </span><span class="s2">&quot;1.0.0&quot;</span>
<span class="o">}</span>
</pre></div>
</div>
<p><strong>Run Test Suite:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Quick test</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/unit/domain/test_user_entity.py<span class="w"> </span>-v

<span class="c1"># Full test suite</span>
./scripts/run-comprehensive-tests.sh

<span class="c1"># Specific test categories</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span>unit<span class="w">        </span><span class="c1"># Unit tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span>integration<span class="w"> </span><span class="c1"># Integration tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>-m<span class="w"> </span>security<span class="w">    </span><span class="c1"># Security tests</span>
</pre></div>
</div>
</section>
<section id="ide-configuration">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">IDE Configuration</a><a class="headerlink" href="#ide-configuration" title="Link to this heading"></a></h2>
<p><strong>Visual Studio Code:</strong></p>
<p>Recommended extensions:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;ms-python.python&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-python.black-formatter&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-python.isort&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-python.flake8&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-python.mypy-type-checker&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;bradlc.vscode-tailwindcss&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-json&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>PyCharm:</strong></p>
<ol class="arabic simple">
<li><p>Open project directory</p></li>
<li><p>Configure Python interpreter to use virtual environment</p></li>
<li><p>Enable code formatting with Black</p></li>
<li><p>Configure import sorting with isort</p></li>
</ol>
<p><strong>Vim/Neovim:</strong></p>
<p>Install language server support:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install Python language server</span>
pip<span class="w"> </span>install<span class="w"> </span>python-lsp-server<span class="o">[</span>all<span class="o">]</span>

<span class="c1"># Configure with your preferred plugin manager</span>
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Troubleshooting</a><a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p><strong>Common Issues:</strong></p>
<ol class="arabic">
<li><p><strong>Database Connection Error:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check PostgreSQL is running</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>status<span class="w"> </span>postgresql

<span class="c1"># Check connection</span>
psql<span class="w"> </span>-h<span class="w"> </span>localhost<span class="w"> </span>-U<span class="w"> </span>forge_user<span class="w"> </span>-d<span class="w"> </span>forge_protocol
</pre></div>
</div>
</li>
<li><p><strong>Permission Denied:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Fix file permissions</span>
chmod<span class="w"> </span>+x<span class="w"> </span>scripts/*.sh

<span class="c1"># Fix Python virtual environment</span>
<span class="nb">source</span><span class="w"> </span>venv/bin/activate
</pre></div>
</div>
</li>
<li><p><strong>Port Already in Use:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Find process using port 8000</span>
lsof<span class="w"> </span>-i<span class="w"> </span>:8000

<span class="c1"># Kill process</span>
<span class="nb">kill</span><span class="w"> </span>-9<span class="w"> </span>&lt;PID&gt;
</pre></div>
</div>
</li>
<li><p><strong>Migration Errors:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reset database (development only)</span>
python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>downgrade<span class="w"> </span>base
python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head
</pre></div>
</div>
</li>
<li><p><strong>Nix Environment Issues:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Rebuild Nix environment</span>
nix<span class="w"> </span>develop<span class="w"> </span>--rebuild

<span class="c1"># Clear Nix cache</span>
nix-collect-garbage
</pre></div>
</div>
</li>
</ol>
<p><strong>Getting Help:</strong></p>
<ul class="simple">
<li><p>Check the logs: <code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">compose</span> <span class="pre">logs</span> <span class="pre">-f</span></code></p></li>
<li><p>Review configuration: <code class="docutils literal notranslate"><span class="pre">cat</span> <span class="pre">.env</span></code></p></li>
<li><p>Validate environment: <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-c</span> <span class="pre">&quot;import</span> <span class="pre">app;</span> <span class="pre">print('OK')&quot;</span></code></p></li>
<li><p>Test database: <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-c</span> <span class="pre">&quot;from</span> <span class="pre">app.infrastructure.database</span> <span class="pre">import</span> <span class="pre">db_manager;</span> <span class="pre">print('DB</span> <span class="pre">OK')&quot;</span></code></p></li>
</ul>
<p><strong>Performance Optimization:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Enable PostgreSQL performance monitoring</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;shared_preload_libraries = &#39;pg_stat_statements&#39;&quot;</span><span class="w"> </span>&gt;&gt;<span class="w"> </span>postgresql.conf

<span class="c1"># Restart PostgreSQL</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>restart<span class="w"> </span>postgresql
</pre></div>
</div>
</section>
<section id="next-steps">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Next Steps</a><a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful installation:</p>
<ol class="arabic simple">
<li><p><strong>Explore the API</strong>: Visit <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>Run Tests</strong>: Execute <code class="docutils literal notranslate"><span class="pre">./scripts/run-comprehensive-tests.sh</span></code></p></li>
<li><p><strong>Read Documentation</strong>: Browse the full documentation</p></li>
<li><p><strong>Start Development</strong>: Begin implementing new features</p></li>
</ol>
<p>The installation provides a complete development environment optimized for mobile-first hypertrophy training application development.</p>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>