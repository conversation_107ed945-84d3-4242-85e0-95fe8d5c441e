

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Forge Protocol Documentation &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Forge Protocol Documentation</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/source/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="forge-protocol-documentation">
<h1>Forge Protocol Documentation<a class="headerlink" href="#forge-protocol-documentation" title="Link to this heading"></a></h1>
<p>Welcome to the Forge Protocol documentation! This is a mobile-first hypertrophy training platform implementing evidence-based Renaissance Periodization principles with Clean Architecture and comprehensive testing.</p>
<a class="reference external image-reference" href="https://python.org"><img alt="Python Version" src="https://img.shields.io/badge/Python-3.11+-blue.svg" />
</a>
<a class="reference external image-reference" href="https://fastapi.tiangolo.com"><img alt="FastAPI" src="https://img.shields.io/badge/FastAPI-Latest-green.svg" />
</a>
<a class="reference external image-reference" href="https://postgresql.org"><img alt="PostgreSQL" src="https://img.shields.io/badge/PostgreSQL-15+-blue.svg" />
</a>
<img alt="Testing" src="https://img.shields.io/badge/Testing-Comprehensive-brightgreen.svg" />
<img alt="Clean Architecture" src="https://img.shields.io/badge/Architecture-Clean-orange.svg" />
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Forge Protocol is a cutting-edge fitness application that combines:</p>
<ul class="simple">
<li><p><strong>Evidence-Based Training</strong>: Renaissance Periodization principles</p></li>
<li><p><strong>Mobile-First Design</strong>: Optimized for mobile devices and networks</p></li>
<li><p><strong>Clean Architecture</strong>: Domain-driven design with clear separation of concerns</p></li>
<li><p><strong>Comprehensive Testing</strong>: No-mocking philosophy with real service testing</p></li>
<li><p><strong>Modern Technology Stack</strong>: FastAPI, PostgreSQL, Docker, Nix</p></li>
</ul>
</section>
<section id="key-features">
<h2>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>User Authentication</strong>: Secure JWT-based authentication with refresh tokens</p></li>
<li><p><strong>Profile Management</strong>: Comprehensive user profiles with training experience tracking</p></li>
<li><p><strong>Mobile Optimization</strong>: Response times &lt; 200ms, payload sizes &lt; 2KB</p></li>
<li><p><strong>Security First</strong>: Protection against common attack vectors</p></li>
<li><p><strong>Scalable Architecture</strong>: Clean separation of domain, application, and infrastructure layers</p></li>
</ul>
</section>
<section id="quick-start">
<h2>Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h2>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol

<span class="c1"># Enter Nix development environment (recommended)</span>
nix<span class="w"> </span>develop

<span class="c1"># Start services</span>
docker<span class="w"> </span>compose<span class="w"> </span>-f<span class="w"> </span>docker/docker-compose.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Run migrations</span>
python<span class="w"> </span>-m<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Start development server</span>
python<span class="w"> </span>-m<span class="w"> </span>uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--reload
</pre></div>
</div>
</section>
<section id="documentation-contents">
<h2>Documentation Contents<a class="headerlink" href="#documentation-contents" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation.html#quick-start-recommended">Quick Start (Recommended)</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#nix-development-environment">Nix Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#docker-development-environment">Docker Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#development-server">Development Server</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#testing-installation">Testing Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#ide-configuration">IDE Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="../genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="../py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="../search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>