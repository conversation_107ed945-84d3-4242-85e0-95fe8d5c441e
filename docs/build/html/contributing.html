

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Contributing to Forge Protocol &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="_static/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="_static/copybutton.js?v=30646c52"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="License" href="license.html" />
    <link rel="prev" title="Changelog" href="changelog.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Forge Protocol
              <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Contributing to Forge Protocol</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#project-vision">🎯 Project Vision</a></li>
<li class="toctree-l2"><a class="reference internal" href="#getting-started">🚀 Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#development-setup">Development Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="#development-workflow">Development Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#coding-standards">📝 Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#code-style">Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="#architecture-guidelines">Architecture Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#testing-guidelines">🧪 Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#test-categories">Test Categories</a></li>
<li class="toctree-l3"><a class="reference internal" href="#test-requirements">Test Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#documentation-standards">📚 Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="#documentation-guidelines">Documentation Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#scientific-contributions">🔬 Scientific Contributions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#algorithm-development">Algorithm Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="#research-contributions">Research Contributions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bug-reports-and-issues">🐛 Bug Reports and Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#reporting-bugs">Reporting Bugs</a></li>
<li class="toctree-l3"><a class="reference internal" href="#feature-requests">Feature Requests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#recognition">🎉 Recognition</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#contributors">Contributors</a></li>
<li class="toctree-l3"><a class="reference internal" href="#attribution">Attribution</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#getting-help">📞 Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#communication-channels">Communication Channels</a></li>
<li class="toctree-l3"><a class="reference internal" href="#office-hours">Office Hours</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Contributing to Forge Protocol</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/contributing.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="contributing-to-forge-protocol">
<h1>Contributing to Forge Protocol<a class="headerlink" href="#contributing-to-forge-protocol" title="Link to this heading"></a></h1>
<p>Thank you for your interest in contributing to Forge Protocol! This document provides guidelines and information for contributors to help maintain code quality and project consistency.</p>
<section id="project-vision">
<h2>🎯 Project Vision<a class="headerlink" href="#project-vision" title="Link to this heading"></a></h2>
<p>We’re building an evidence-based hypertrophy training platform that implements Renaissance Periodisation principles with scientific rigour and clean architecture. Every contribution should align with these core values:</p>
<dl class="simple">
<dt><strong>Scientific Accuracy</strong></dt><dd><p>All training algorithms and recommendations must be based on peer-reviewed research and validated methodologies.</p>
</dd>
<dt><strong>Code Quality</strong></dt><dd><p>We maintain high standards for code quality, testing, and documentation to ensure long-term maintainability.</p>
</dd>
<dt><strong>User Experience</strong></dt><dd><p>Features should be intuitive, reliable, and provide clear value to users seeking evidence-based training guidance.</p>
</dd>
<dt><strong>Open Collaboration</strong></dt><dd><p>We welcome contributions from developers, exercise scientists, and fitness professionals worldwide.</p>
</dd>
</dl>
</section>
<section id="getting-started">
<h2>🚀 Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="development-setup">
<h3>Development Setup<a class="headerlink" href="#development-setup" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Fork and Clone</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Fork the repository on GitHub, then clone your fork</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/YOUR_USERNAME/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol
</pre></div>
</div>
</li>
<li><p><strong>Set Up Development Environment</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create virtual environment</span>
python3.11<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># Linux/macOS</span>
<span class="c1"># venv\Scripts\activate   # Windows</span>

<span class="c1"># Install development dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements/dev.txt
</pre></div>
</div>
</li>
<li><p><strong>Start Development Services</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start database and supporting services</span>
<span class="nb">cd</span><span class="w"> </span>docker
docker-compose<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>db<span class="w"> </span>redis<span class="w"> </span>pgadmin

<span class="c1"># Run database migrations</span>
<span class="nb">cd</span><span class="w"> </span>..
alembic<span class="w"> </span>upgrade<span class="w"> </span>head
</pre></div>
</div>
</li>
<li><p><strong>Run Tests</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run the full test suite</span>
pytest

<span class="c1"># Run with coverage</span>
pytest<span class="w"> </span>--cov<span class="o">=</span>app<span class="w"> </span>--cov-report<span class="o">=</span>html
</pre></div>
</div>
</li>
<li><p><strong>Start Development Server</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start with hot reload</span>
uvicorn<span class="w"> </span>app.main:app<span class="w"> </span>--reload<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span>
</pre></div>
</div>
</li>
</ol>
</section>
<section id="development-workflow">
<h3>Development Workflow<a class="headerlink" href="#development-workflow" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Create Feature Branch</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create and switch to feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/your-feature-name

<span class="c1"># Use descriptive branch names:</span>
<span class="c1"># feature/exercise-database</span>
<span class="c1"># bugfix/auth-token-refresh</span>
<span class="c1"># docs/api-documentation</span>
</pre></div>
</div>
</li>
<li><p><strong>Make Changes</strong>
- Follow the coding standards outlined below
- Write tests for new functionality
- Update documentation as needed
- Ensure all tests pass</p></li>
<li><p><strong>Commit Changes</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Stage your changes</span>
git<span class="w"> </span>add<span class="w"> </span>.

<span class="c1"># Commit with descriptive message</span>
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add exercise database with search functionality&quot;</span>
</pre></div>
</div>
</li>
<li><p><strong>Push and Create Pull Request</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Push to your fork</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/your-feature-name

<span class="c1"># Create pull request on GitHub</span>
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="coding-standards">
<h2>📝 Coding Standards<a class="headerlink" href="#coding-standards" title="Link to this heading"></a></h2>
<section id="code-style">
<h3>Code Style<a class="headerlink" href="#code-style" title="Link to this heading"></a></h3>
<p>We use automated tools to maintain consistent code style:</p>
<p><strong>Python Code Formatting</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Format code with Black</span>
black<span class="w"> </span>.

<span class="c1"># Sort imports with isort</span>
isort<span class="w"> </span>.

<span class="c1"># Run all formatting tools</span>
make<span class="w"> </span>format
</pre></div>
</div>
<p><strong>Type Hints</strong>
All functions and classes must include complete type annotations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">List</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">uuid</span><span class="w"> </span><span class="kn">import</span> <span class="n">UUID</span>

<span class="k">def</span><span class="w"> </span><span class="nf">get_user_by_id</span><span class="p">(</span><span class="n">user_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">User</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Retrieve user by ID.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">user_repository</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>

<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_workout</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span>
    <span class="n">exercises</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ExerciseSelection</span><span class="p">]</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Workout</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create new workout session.&quot;&quot;&quot;</span>
    <span class="c1"># Implementation here</span>
</pre></div>
</div>
<p><strong>Docstrings</strong>
Use Google-style docstrings for all public functions and classes:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">calculate_mev</span><span class="p">(</span>
    <span class="n">user_profile</span><span class="p">:</span> <span class="n">UserProfile</span><span class="p">,</span>
    <span class="n">muscle_group</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Calculate Minimum Effective Volume for user and muscle group.</span>

<span class="sd">    Args:</span>
<span class="sd">        user_profile: User&#39;s training history and characteristics</span>
<span class="sd">        muscle_group: Target muscle group for MEV calculation</span>

<span class="sd">    Returns:</span>
<span class="sd">        Estimated MEV in sets per week</span>

<span class="sd">    Raises:</span>
<span class="sd">        ValueError: If muscle group is not supported</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; profile = UserProfile(training_years=3, age=25)</span>
<span class="sd">        &gt;&gt;&gt; mev = calculate_mev(profile, &quot;chest&quot;)</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;MEV for chest: {mev} sets/week&quot;)</span>
<span class="sd">    &quot;&quot;&quot;</span>
</pre></div>
</div>
</section>
<section id="architecture-guidelines">
<h3>Architecture Guidelines<a class="headerlink" href="#architecture-guidelines" title="Link to this heading"></a></h3>
<p><strong>Clean Architecture Principles</strong>
- Respect layer boundaries and dependency rules
- Domain layer should not depend on infrastructure
- Use dependency injection for external dependencies
- Keep business logic in domain services</p>
<p><strong>Repository Pattern</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Abstract repository interface (domain layer)</span>
<span class="k">class</span><span class="w"> </span><span class="nc">UserRepository</span><span class="p">(</span><span class="n">ABC</span><span class="p">):</span>
    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">user</span><span class="p">:</span> <span class="n">User</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create new user.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_by_id</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">User</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get user by ID.&quot;&quot;&quot;</span>

<span class="c1"># Concrete implementation (infrastructure layer)</span>
<span class="k">class</span><span class="w"> </span><span class="nc">SQLUserRepository</span><span class="p">(</span><span class="n">UserRepository</span><span class="p">):</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">user</span><span class="p">:</span> <span class="n">User</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
        <span class="c1"># Database implementation</span>
        <span class="k">pass</span>
</pre></div>
</div>
<p><strong>Error Handling</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Domain exceptions</span>
<span class="k">class</span><span class="w"> </span><span class="nc">UserNotFoundError</span><span class="p">(</span><span class="n">DomainException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when user cannot be found.&quot;&quot;&quot;</span>

<span class="k">class</span><span class="w"> </span><span class="nc">InvalidCredentialsError</span><span class="p">(</span><span class="n">DomainException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when authentication fails.&quot;&quot;&quot;</span>

<span class="c1"># Use specific exceptions</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">authenticate_user</span><span class="p">(</span><span class="n">email</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">password</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
    <span class="n">user</span> <span class="o">=</span> <span class="k">await</span> <span class="n">user_repository</span><span class="o">.</span><span class="n">get_by_email</span><span class="p">(</span><span class="n">email</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">user</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">UserNotFoundError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;User with email </span><span class="si">{</span><span class="n">email</span><span class="si">}</span><span class="s2"> not found&quot;</span><span class="p">)</span>

    <span class="k">if</span> <span class="ow">not</span> <span class="n">verify_password</span><span class="p">(</span><span class="n">password</span><span class="p">,</span> <span class="n">user</span><span class="o">.</span><span class="n">hashed_password</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">InvalidCredentialsError</span><span class="p">(</span><span class="s2">&quot;Invalid password&quot;</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">user</span>
</pre></div>
</div>
</section>
</section>
<section id="testing-guidelines">
<h2>🧪 Testing Guidelines<a class="headerlink" href="#testing-guidelines" title="Link to this heading"></a></h2>
<section id="test-categories">
<h3>Test Categories<a class="headerlink" href="#test-categories" title="Link to this heading"></a></h3>
<p><strong>Unit Tests</strong>
Test individual components in isolation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/unit/test_mev_calculator.py</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">pytest</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">app.domain.services.mev_calculator</span><span class="w"> </span><span class="kn">import</span> <span class="n">MEVCalculator</span>

<span class="k">class</span><span class="w"> </span><span class="nc">TestMEVCalculator</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">test_calculate_mev_beginner</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test MEV calculation for beginner user.&quot;&quot;&quot;</span>
        <span class="n">calculator</span> <span class="o">=</span> <span class="n">MEVCalculator</span><span class="p">()</span>
        <span class="n">profile</span> <span class="o">=</span> <span class="n">UserProfile</span><span class="p">(</span><span class="n">training_years</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">age</span><span class="o">=</span><span class="mi">25</span><span class="p">)</span>

        <span class="n">mev</span> <span class="o">=</span> <span class="n">calculator</span><span class="o">.</span><span class="n">calculate_mev</span><span class="p">(</span><span class="n">profile</span><span class="p">,</span> <span class="s2">&quot;chest&quot;</span><span class="p">)</span>

        <span class="k">assert</span> <span class="mi">6</span> <span class="o">&lt;=</span> <span class="n">mev</span> <span class="o">&lt;=</span> <span class="mi">8</span>  <span class="c1"># Expected range for beginners</span>
</pre></div>
</div>
<p><strong>Integration Tests</strong>
Test component interactions:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/integration/test_auth_endpoints.py</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">pytest</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">httpx</span><span class="w"> </span><span class="kn">import</span> <span class="n">AsyncClient</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">asyncio</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_user_registration_flow</span><span class="p">(</span><span class="n">client</span><span class="p">:</span> <span class="n">AsyncClient</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test complete user registration flow.&quot;&quot;&quot;</span>
    <span class="c1"># Register user</span>
    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/auth/register&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;email&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
        <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;SecurePassword123!&quot;</span><span class="p">,</span>
        <span class="s2">&quot;first_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Test&quot;</span><span class="p">,</span>
        <span class="s2">&quot;last_name&quot;</span><span class="p">:</span> <span class="s2">&quot;User&quot;</span>
    <span class="p">})</span>

    <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">201</span>
    <span class="n">user_data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
    <span class="k">assert</span> <span class="n">user_data</span><span class="p">[</span><span class="s2">&quot;email&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;<EMAIL>&quot;</span>
</pre></div>
</div>
<p><strong>Behavioral Tests</strong>
Test user scenarios with Gherkin:</p>
<div class="highlight-gherkin notranslate"><div class="highlight"><pre><span></span><span class="c"># tests/behavioral/features/user_registration.feature</span>
<span class="k">Feature:</span><span class="nf"> User Registration</span>
<span class="nf">  As a new user</span>
<span class="nf">  I want to register for an account</span>
<span class="nf">  So that I can access the training platform</span>

<span class="nf">  </span><span class="k">Scenario:</span><span class="nf"> Successful registration with valid data</span>
<span class="k">    Given </span><span class="nf">I am on the registration page</span>
<span class="nf">    </span><span class="k">When </span><span class="nf">I enter valid registration details</span>
<span class="nf">    </span><span class="k">And </span><span class="nf">I submit the registration form</span>
<span class="nf">    </span><span class="k">Then </span><span class="nf">I should receive a success confirmation</span>
<span class="nf">    </span><span class="k">And </span><span class="nf">my account should be created in the system</span>
</pre></div>
</div>
</section>
<section id="test-requirements">
<h3>Test Requirements<a class="headerlink" href="#test-requirements" title="Link to this heading"></a></h3>
<p><strong>Coverage Requirements</strong>
- Minimum 90% overall test coverage
- 100% coverage for critical paths (authentication, algorithms)
- All public methods must have tests
- Edge cases and error conditions must be tested</p>
<p><strong>Test Naming</strong>
Use descriptive test names that explain the scenario:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">test_mev_calculation_increases_with_training_experience</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;MEV should increase for users with more training experience.&quot;&quot;&quot;</span>

<span class="k">def</span><span class="w"> </span><span class="nf">test_authentication_fails_with_invalid_password</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Authentication should fail when password is incorrect.&quot;&quot;&quot;</span>

<span class="k">def</span><span class="w"> </span><span class="nf">test_workout_creation_requires_authenticated_user</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Workout creation should require valid authentication.&quot;&quot;&quot;</span>
</pre></div>
</div>
<p><strong>Test Data</strong>
Use factories for test data creation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/factories.py</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">factory</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">app.domain.entities.user</span><span class="w"> </span><span class="kn">import</span> <span class="n">User</span>

<span class="k">class</span><span class="w"> </span><span class="nc">UserFactory</span><span class="p">(</span><span class="n">factory</span><span class="o">.</span><span class="n">Factory</span><span class="p">):</span>
    <span class="k">class</span><span class="w"> </span><span class="nc">Meta</span><span class="p">:</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">User</span>

    <span class="n">email</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Sequence</span><span class="p">(</span><span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;user</span><span class="si">{</span><span class="n">n</span><span class="si">}</span><span class="s2">@example.com&quot;</span><span class="p">)</span>
    <span class="n">first_name</span> <span class="o">=</span> <span class="s2">&quot;Test&quot;</span>
    <span class="n">last_name</span> <span class="o">=</span> <span class="s2">&quot;User&quot;</span>
    <span class="n">training_experience_years</span> <span class="o">=</span> <span class="mi">2</span>
    <span class="n">is_active</span> <span class="o">=</span> <span class="kc">True</span>

<span class="c1"># Usage in tests</span>
<span class="k">def</span><span class="w"> </span><span class="nf">test_user_profile_update</span><span class="p">():</span>
    <span class="n">user</span> <span class="o">=</span> <span class="n">UserFactory</span><span class="p">()</span>
    <span class="c1"># Test implementation</span>
</pre></div>
</div>
</section>
</section>
<section id="documentation-standards">
<h2>📚 Documentation Standards<a class="headerlink" href="#documentation-standards" title="Link to this heading"></a></h2>
<section id="documentation-types">
<h3>Documentation Types<a class="headerlink" href="#documentation-types" title="Link to this heading"></a></h3>
<p><strong>API Documentation</strong>
- All endpoints must have OpenAPI documentation
- Include request/response examples
- Document error responses and status codes
- Provide usage examples in multiple languages</p>
<p><strong>Code Documentation</strong>
- Comprehensive docstrings for all public APIs
- Inline comments for complex algorithms
- Architecture decision records (ADRs) for major decisions
- README files for each major component</p>
<p><strong>User Documentation</strong>
- Getting started guides
- API reference with examples
- Integration guides for common use cases
- Troubleshooting and FAQ sections</p>
</section>
<section id="documentation-guidelines">
<h3>Documentation Guidelines<a class="headerlink" href="#documentation-guidelines" title="Link to this heading"></a></h3>
<p><strong>Writing Style</strong>
- Use clear, concise language
- Write for your audience (developers, users, admins)
- Include practical examples and code snippets
- Use British English spelling consistently</p>
<p><strong>Structure</strong>
- Start with overview and key concepts
- Provide step-by-step instructions
- Include troubleshooting sections
- Add cross-references to related topics</p>
<p><strong>Code Examples</strong>
- Test all code examples before publishing
- Include complete, runnable examples
- Show both success and error cases
- Provide examples in multiple programming languages</p>
</section>
</section>
<section id="scientific-contributions">
<h2>🔬 Scientific Contributions<a class="headerlink" href="#scientific-contributions" title="Link to this heading"></a></h2>
<section id="algorithm-development">
<h3>Algorithm Development<a class="headerlink" href="#algorithm-development" title="Link to this heading"></a></h3>
<p><strong>Research Requirements</strong>
- All algorithms must be based on peer-reviewed research
- Include citations and references for scientific claims
- Document assumptions and limitations
- Provide validation methodology</p>
<p><strong>Implementation Standards</strong>
- Include mathematical formulations in documentation
- Implement comprehensive unit tests for algorithms
- Validate against known test cases
- Document parameter tuning and sensitivity analysis</p>
<p><strong>Review Process</strong>
- Scientific review by qualified exercise physiologists
- Code review by senior developers
- Validation testing with real-world data
- Performance benchmarking and optimization</p>
</section>
<section id="research-contributions">
<h3>Research Contributions<a class="headerlink" href="#research-contributions" title="Link to this heading"></a></h3>
<p><strong>Literature Reviews</strong>
- Systematic review of relevant research
- Critical analysis of methodology and findings
- Identification of research gaps and opportunities
- Regular updates as new research emerges</p>
<p><strong>Validation Studies</strong>
- Design and conduct validation studies
- Statistical analysis of algorithm performance
- Comparison with existing methods
- Publication of findings in peer-reviewed journals</p>
</section>
</section>
<section id="bug-reports-and-issues">
<h2>🐛 Bug Reports and Issues<a class="headerlink" href="#bug-reports-and-issues" title="Link to this heading"></a></h2>
<section id="reporting-bugs">
<h3>Reporting Bugs<a class="headerlink" href="#reporting-bugs" title="Link to this heading"></a></h3>
<p>When reporting bugs, please include:</p>
<p><strong>Environment Information</strong>
- Operating system and version
- Python version and virtual environment
- Docker version (if applicable)
- Browser and version (for web interface issues)</p>
<p><strong>Reproduction Steps</strong>
1. Clear steps to reproduce the issue
2. Expected behavior vs actual behavior
3. Screenshots or error messages
4. Minimal code example (if applicable)</p>
<p><strong>Additional Context</strong>
- When did the issue first occur?
- Does it happen consistently?
- Any recent changes to your setup?
- Relevant log files or error traces</p>
</section>
<section id="feature-requests">
<h3>Feature Requests<a class="headerlink" href="#feature-requests" title="Link to this heading"></a></h3>
<p>For feature requests, please provide:</p>
<p><strong>Use Case Description</strong>
- What problem does this solve?
- Who would benefit from this feature?
- How does it align with project goals?</p>
<p><strong>Proposed Solution</strong>
- Detailed description of the feature
- User interface mockups (if applicable)
- Technical implementation considerations
- Alternative solutions considered</p>
<p><strong>Acceptance Criteria</strong>
- Clear definition of “done”
- Success metrics and validation criteria
- Performance and security requirements
- Documentation and testing requirements</p>
</section>
</section>
<section id="recognition">
<h2>🎉 Recognition<a class="headerlink" href="#recognition" title="Link to this heading"></a></h2>
<section id="contributors">
<h3>Contributors<a class="headerlink" href="#contributors" title="Link to this heading"></a></h3>
<p>We recognize and appreciate all forms of contribution:</p>
<p><strong>Code Contributors</strong>
- Feature development and bug fixes
- Performance optimizations
- Security improvements
- Test coverage enhancements</p>
<p><strong>Documentation Contributors</strong>
- API documentation improvements
- User guide enhancements
- Translation and localization
- Video tutorials and examples</p>
<p><strong>Scientific Contributors</strong>
- Algorithm development and validation
- Research reviews and analysis
- Expert consultation and review
- Educational content creation</p>
<p><strong>Community Contributors</strong>
- Issue reporting and triage
- Community support and mentoring
- Event organization and speaking
- Feedback and user experience testing</p>
</section>
<section id="attribution">
<h3>Attribution<a class="headerlink" href="#attribution" title="Link to this heading"></a></h3>
<p>All contributors are recognized in:
- Project README and documentation
- Release notes and changelogs
- Annual contributor reports
- Conference presentations and papers</p>
</section>
</section>
<section id="getting-help">
<h2>📞 Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<section id="communication-channels">
<h3>Communication Channels<a class="headerlink" href="#communication-channels" title="Link to this heading"></a></h3>
<p><strong>GitHub Issues</strong>
- Bug reports and feature requests
- Technical discussions and questions
- Project planning and roadmap discussions</p>
<p><strong>Development Chat</strong>
- Real-time development discussions
- Quick questions and clarifications
- Pair programming coordination</p>
<p><strong>Community Forum</strong>
- User support and questions
- Best practices sharing
- Integration examples and tutorials</p>
<p><strong>Email Contact</strong>
- Security vulnerability reports: <a class="reference external" href="mailto:security&#37;&#52;&#48;forgeprotocol&#46;com">security<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a>
- Partnership inquiries: <a class="reference external" href="mailto:partnerships&#37;&#52;&#48;forgeprotocol&#46;com">partnerships<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a>
- General questions: <a class="reference external" href="mailto:hello&#37;&#52;&#48;forgeprotocol&#46;com">hello<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a></p>
</section>
<section id="office-hours">
<h3>Office Hours<a class="headerlink" href="#office-hours" title="Link to this heading"></a></h3>
<p><strong>Weekly Developer Office Hours</strong>
- Tuesdays 2:00-3:00 PM UTC
- Open discussion and Q&amp;A
- Code review and architecture discussions</p>
<p><strong>Monthly Community Calls</strong>
- First Friday of each month, 3:00-4:00 PM UTC
- Project updates and roadmap discussions
- Community showcase and feedback</p>
<p>Thank you for contributing to Forge Protocol! Together, we’re building the future of evidence-based training technology. 🏋️‍♂️</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="changelog.html" class="btn btn-neutral float-left" title="Changelog" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="license.html" class="btn btn-neutral float-right" title="License" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>