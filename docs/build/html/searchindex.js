Search.setIndex({"alltitles": {"API Access": [[14, "api-access"]], "API Reference": [[3, null]], "Abbreviations and Acronyms": [[7, "abbreviations-and-acronyms"]], "Add Exercise Media": [[1, "add-exercise-media"]], "Adding Media": [[14, "adding-media"]], "Advanced Search": [[14, "advanced-search"]], "Alembic": [[9, "alembic"]], "Algorithm Development": [[6, "algorithm-development"]], "Algorithm Terms": [[7, "algorithm-terms"]], "Application Layer": [[4, "application-layer"]], "Approval Workflow": [[2, "approval-workflow"], [4, "approval-workflow"], [14, "approval-workflow"]], "Approve Exercise": [[2, "approve-exercise"]], "Approving Exercises": [[14, "approving-exercises"]], "Architecture": [[4, "architecture"], [4, "id1"]], "Architecture Guidelines": [[6, "architecture-guidelines"]], "Attribution": [[6, "attribution"], [9, "attribution"]], "Audit Trail": [[4, "audit-trail"]], "Authentication": [[1, "authentication"], [2, "authentication"]], "Authentication & Authorization": [[4, "authentication-authorization"]], "Base URL": [[1, "base-url"], [2, "base-url"]], "Basic Exercise Creation": [[14, "basic-exercise-creation"]], "Basic Search": [[14, "basic-search"]], "Best Practices": [[1, "best-practices"], [14, "best-practices"]], "Business Terms": [[7, "business-terms"]], "Caching Strategy": [[4, "caching-strategy"]], "Changelog": [[5, null]], "Code Style": [[6, "code-style"]], "Common Issues": [[14, "common-issues"]], "Communication Channels": [[6, "communication-channels"]], "Comprehensive Exercise Example": [[14, "comprehensive-exercise-example"]], "Contact Information": [[9, "contact-information"]], "Content Guidelines": [[1, "content-guidelines"]], "Content Quality": [[14, "content-quality"]], "Contributing to Forge Protocol": [[6, null]], "Contributors": [[6, "contributors"]], "Core API Modules": [[3, null]], "Core Components": [[4, "core-components"]], "Core Endpoints": [[2, "core-endpoints"]], "Create Exercise": [[2, "create-exercise"]], "Creating Exercises": [[14, "creating-exercises"]], "Data Models": [[1, "data-models"]], "Data Protection": [[4, "data-protection"]], "Data Terms": [[7, "data-terms"]], "Data and Privacy": [[9, "data-and-privacy"]], "Database Optimization": [[4, "database-optimization"]], "Delete Exercise": [[2, "delete-exercise"]], "Delete Exercise Media": [[1, "delete-exercise-media"]], "Deleting Exercises": [[14, "deleting-exercises"]], "Deleting Media": [[14, "deleting-media"]], "Development Server": [[13, "development-server"]], "Development Setup": [[6, "development-setup"]], "Development Workflow": [[6, "development-workflow"]], "Disclaimer": [[9, "disclaimer"]], "Docker": [[9, "docker"]], "Docker Development Environment": [[13, "docker-development-environment"]], "Documentation Contents": [[12, "documentation-contents"]], "Documentation Guidelines": [[6, "documentation-guidelines"]], "Documentation Types": [[6, "documentation-types"]], "Domain Layer": [[4, "domain-layer"]], "Error Responses": [[1, "error-responses"]], "Exercise API": [[2, null]], "Exercise Approval Process": [[14, "exercise-approval-process"]], "Exercise Creation": [[14, "exercise-creation"]], "Exercise Management Guide": [[14, null]], "Exercise Media API": [[1, null]], "Exercise Statistics": [[14, "exercise-statistics"]], "Exercise System Architecture": [[4, null]], "Exercise Updates": [[14, "exercise-updates"]], "ExerciseMedia Schema": [[1, "exercisemedia-schema"]], "FastAPI": [[9, "fastapi"]], "Feature Requests": [[6, "feature-requests"]], "File Size Limits": [[1, "file-size-limits"]], "Forge Protocol Documentation": [[12, null]], "Get Exercise": [[2, "get-exercise"]], "Get Exercise Media": [[1, "get-exercise-media"]], "Get Exercise Versions": [[2, "get-exercise-versions"]], "Get Specific Version": [[2, "get-specific-version"]], "Getting Help": [[0, "getting-help"], [14, "getting-help"]], "Getting Started": [[10, "getting-started"], [12, null], [14, "getting-started"]], "Glossary": [[7, null]], "IDE Configuration": [[13, "ide-configuration"]], "Indices and Tables": [[8, "indices-and-tables"]], "Indices and tables": [[12, "indices-and-tables"]], "Individual Assessment": [[11, "individual-assessment"]], "Infrastructure Layer": [[4, "infrastructure-layer"]], "Installation Guide": [[13, null]], "Integration Terms": [[7, "integration-terms"]], "Key Features": [[12, "key-features"]], "License": [[9, null]], "MIT License": [[9, "mit-license"]], "Manual Installation": [[13, "manual-installation"]], "Media Guidelines": [[14, "media-guidelines"]], "Media Management": [[1, "media-management"], [4, "media-management"], [14, "media-management"]], "Media Organization": [[1, "media-organization"]], "Media Types": [[1, "media-types"]], "Monitoring and Adjustment": [[11, "monitoring-and-adjustment"]], "Next Steps": [[13, "next-steps"]], "Nix Development Environment": [[13, "nix-development-environment"]], "Office Hours": [[6, "office-hours"]], "Overview": [[0, "overview"], [1, "overview"], [2, "overview"], [4, "overview"], [10, "overview"], [11, "overview"], [12, "overview"]], "Performance Considerations": [[4, "performance-considerations"]], "Performance Optimization": [[1, "performance-optimization"]], "Periodisation Framework": [[11, "periodisation-framework"]], "PostgreSQL": [[9, "postgresql"]], "Prerequisites": [[14, "prerequisites"]], "Primary Media Rules": [[1, "primary-media-rules"]], "Programme Design Process": [[11, "programme-design-process"]], "Progressive Overload Strategies": [[11, "progressive-overload-strategies"]], "PyJWT": [[9, "pyjwt"]], "Pydantic": [[9, "pydantic"]], "Quick Start": [[12, "quick-start"]], "Quick Start (Recommended)": [[13, "quick-start-recommended"]], "Reject Exercise": [[2, "reject-exercise"]], "Rejecting Exercises": [[14, "rejecting-exercises"]], "Renaissance Periodisation Principles": [[11, null]], "Reorder Exercise Media": [[1, "reorder-exercise-media"]], "Reporting Bugs": [[6, "reporting-bugs"]], "Research Contributions": [[6, "research-contributions"]], "Restore Exercise": [[2, "restore-exercise"]], "Restoring Exercises": [[14, "restoring-exercises"]], "SFR Calculation": [[11, "sfr-calculation"]], "SQLAlchemy": [[9, "sqlalchemy"]], "Scalability": [[4, "scalability"]], "Scientific References": [[9, "scientific-references"]], "Scientific Terms": [[7, "scientific-terms"]], "Search Exercises": [[2, "search-exercises"]], "Search and Filtering": [[4, "search-and-filtering"]], "Searching Exercises": [[14, "searching-exercises"]], "Security Considerations": [[4, "security-considerations"]], "Set Current Version": [[2, "set-current-version"]], "Soft Delete and Recovery": [[14, "soft-delete-and-recovery"]], "State Machine": [[4, "state-machine"]], "Stimulus-to-Fatigue Ratio (SFR)": [[11, "stimulus-to-fatigue-ratio-sfr"]], "Supported Media Types": [[1, "supported-media-types"]], "Table of Contents": [[13, "table-of-contents"]], "Technical Terms": [[7, "technical-terms"]], "Test Categories": [[6, "test-categories"]], "Test Requirements": [[6, "test-requirements"]], "Testing Installation": [[13, "testing-installation"]], "Third-Party Licenses": [[9, "third-party-licenses"]], "Training Terms": [[7, "training-terms"]], "Troubleshooting": [[13, "troubleshooting"], [14, "troubleshooting"]], "Update Exercise": [[2, "update-exercise"]], "Update Exercise Media": [[1, "update-exercise-media"]], "Updates to License": [[9, "updates-to-license"]], "Updating Exercises": [[14, "updating-exercises"]], "Updating Media": [[14, "updating-media"]], "User Interface Terms": [[7, "user-interface-terms"]], "Version Management": [[2, "version-management"], [14, "version-management"], [14, "id1"]], "Versioning System": [[4, "versioning-system"]], "Viewing Media": [[14, "viewing-media"]], "Volume Landmark Calculations": [[11, "volume-landmark-calculations"]], "Volume Landmarks": [[11, "volume-landmarks"]], "Volume Progression Algorithm": [[11, "volume-progression-algorithm"]], "Welcome to Forge Protocol": [[8, "welcome-to-forge-protocol"]], "[1.0.0] - 2025-01-06": [[5, "id2"]], "[2.0.0] - 2025-06-14": [[5, "id1"]], "[Unreleased]": [[5, "unreleased"]], "bcrypt": [[9, "bcrypt"]], "pytest": [[9, "pytest"]], "uvicorn": [[9, "uvicorn"]], "\u2699\ufe0f Configuration Management": [[0, "configuration-management"]], "\u2699\ufe0f Getting Started (Admin)": [[0, null], [8, null]], "\u2728 Key Features": [[8, "key-features"]], "\ud83c\udf89 Recognition": [[6, "recognition"]], "\ud83c\udf93 Educational Resources": [[10, "educational-resources"]], "\ud83c\udfaf Core Principles": [[11, "core-principles"]], "\ud83c\udfaf Key Algorithms": [[10, "key-algorithms"]], "\ud83c\udfaf Key Objectives": [[8, "key-objectives"]], "\ud83c\udfaf Practical Implementation": [[11, "practical-implementation"]], "\ud83c\udfaf Prerequisites": [[0, "prerequisites"]], "\ud83c\udfaf Project Vision": [[6, "project-vision"]], "\ud83c\udfcb\ufe0f\u200d\u2642\ufe0f Forge Protocol Documentation": [[8, null]], "\ud83c\udfd7\ufe0f Architecture & Development": [[8, null]], "\ud83c\udfd7\ufe0f Architecture Overview": [[0, "architecture-overview"]], "\ud83d\udc1b Bug Reports and Issues": [[6, "bug-reports-and-issues"]], "\ud83d\udc65 Target Users": [[8, "target-users"]], "\ud83d\udcc8 Validation Methodology": [[10, "validation-methodology"]], "\ud83d\udcca Implementation Approach": [[10, "implementation-approach"]], "\ud83d\udcca Monitoring and Observability": [[0, "monitoring-and-observability"]], "\ud83d\udccb Table of Contents": [[8, "table-of-contents"]], "\ud83d\udcd6 User Guide": [[8, null]], "\ud83d\udcda API Reference": [[8, null]], "\ud83d\udcda Documentation Standards": [[6, "documentation-standards"]], "\ud83d\udcda Documentation Structure": [[0, "documentation-structure"], [8, "documentation-structure"], [10, "documentation-structure"]], "\ud83d\udcdd Additional Resources": [[8, null]], "\ud83d\udcdd Coding Standards": [[6, "coding-standards"]], "\ud83d\udcde Getting Help": [[6, "getting-help"]], "\ud83d\udd12 Security and Compliance": [[0, "security-and-compliance"]], "\ud83d\udd27 Installation Options": [[0, "installation-options"]], "\ud83d\udd2c Protocols & Science": [[8, null], [10, null]], "\ud83d\udd2c Research Foundation": [[10, "research-foundation"]], "\ud83d\udd2c Scientific Contributions": [[6, "scientific-contributions"]], "\ud83d\uddc4\ufe0f Database Management": [[0, "database-management"]], "\ud83d\ude80 Deployment Strategies": [[0, "deployment-strategies"]], "\ud83d\ude80 Getting Started": [[6, "getting-started"]], "\ud83d\ude80 Quick Start": [[0, "quick-start"], [8, "quick-start"]], "\ud83e\uddea Testing Guidelines": [[6, "testing-guidelines"]], "\ud83e\uddea Testing and Quality Assurance": [[0, "testing-and-quality-assurance"]], "\ud83e\uddec Core Scientific Principles": [[10, "core-scientific-principles"]], "\ud83e\uddee Mathematical Models": [[11, "mathematical-models"]]}, "docnames": ["admin/index", "api/exercise_media", "api/exercises", "api/modules", "architecture/exercise_system", "changelog", "contributing", "glossary", "index", "license", "protocols/index", "protocols/rp-principles", "source/index", "source/installation", "user/exercise_guide"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["admin/index.rst", "api/exercise_media.rst", "api/exercises.rst", "api/modules.rst", "architecture/exercise_system.rst", "changelog.rst", "contributing.rst", "glossary.rst", "index.rst", "license.rst", "protocols/index.rst", "protocols/rp-principles.rst", "source/index.rst", "source/installation.rst", "user/exercise_guide.rst"], "indexentries": {"adaptive algorithm": [[7, "term-Adaptive-Algorithm", true]], "adjustment factor": [[7, "term-Adjustment-Factor", true]], "analytics": [[7, "term-Analytics", true]], "api": [[7, "term-API", true]], "api (application programming interface)": [[7, "term-API-Application-Programming-Interface", true]], "api key": [[7, "term-API-Key", true]], "authentication": [[7, "term-Authentication", true]], "aws": [[7, "term-AWS", true]], "baseline mev": [[7, "term-Baseline-MEV", true]], "bdd": [[7, "term-BDD", true]], "biometric data": [[7, "term-Biometric-Data", true]], "cdn": [[7, "term-CDN", true]], "ci/cd": [[7, "term-CI-CD", true]], "clean architecture": [[7, "term-Clean-Architecture", true]], "compliance": [[7, "term-Compliance", true]], "cors": [[7, "term-CORS", true]], "crud": [[7, "term-CRUD", true]], "dashboard": [[7, "term-Dashboard", true]], "data export": [[7, "term-Data-Export", true]], "data privacy": [[7, "term-Data-Privacy", true]], "data retention": [[7, "term-Data-Retention", true]], "deload": [[7, "term-Deload", true]], "dependency injection": [[7, "term-Dependency-Injection", true]], "dns": [[7, "term-DNS", true]], "docker": [[7, "term-Docker", true]], "domain-driven design (ddd)": [[7, "term-Domain-Driven-Design-DDD", true]], "dry": [[7, "term-DRY", true]], "endpoint": [[7, "term-Endpoint", true]], "enterprise": [[7, "term-Enterprise", true]], "exercise database": [[7, "term-Exercise-Database", true]], "exercise selection": [[7, "term-Exercise-Selection", true]], "fatigue accumulation": [[7, "term-Fatigue-Accumulation", true]], "feedback data": [[7, "term-Feedback-Data", true]], "form cues": [[7, "term-Form-Cues", true]], "gcp": [[7, "term-GCP", true]], "gdpr": [[7, "term-GDPR", true]], "hipaa": [[7, "term-HIPAA", true]], "http": [[7, "term-HTTP", true]], "https": [[7, "term-HTTPS", true]], "hypertrophy": [[7, "term-Hypertrophy", true]], "iam": [[7, "term-IAM", true]], "json": [[7, "term-JSON", true]], "jwt": [[7, "term-JWT", true]], "jwt (json web token)": [[7, "term-JWT-JSON-Web-Token", true]], "machine learning": [[7, "term-Machine-Learning", true]], "mav (maximum adaptive volume)": [[7, "term-MAV-Maximum-Adaptive-Volume", true]], "mesocycle": [[7, "term-Mesocycle", true]], "mev (minimum effective volume)": [[7, "term-MEV-Minimum-Effective-Volume", true]], "migration": [[7, "term-Migration", true]], "movement pattern": [[7, "term-Movement-Pattern", true]], "mrv (maximum recoverable volume)": [[7, "term-MRV-Maximum-Recoverable-Volume", true]], "muscle group": [[7, "term-Muscle-Group", true]], "mv (maintenance volume)": [[7, "term-MV-Maintenance-Volume", true]], "notifications": [[7, "term-Notifications", true]], "orm": [[7, "term-ORM", true]], "orm (object-relational mapping)": [[7, "term-ORM-Object-Relational-Mapping", true]], "performance data": [[7, "term-Performance-Data", true]], "performance metrics": [[7, "term-Performance-Metrics", true]], "periodisation": [[7, "term-Periodisation", true]], "progress tracking": [[7, "term-Progress-Tracking", true]], "progressive overload": [[7, "term-Progressive-Overload", true]], "pydantic": [[7, "term-Pydantic", true]], "rate limiting": [[7, "term-Rate-Limiting", true]], "readiness score": [[7, "term-Readiness-Score", true]], "repository pattern": [[7, "term-Repository-Pattern", true]], "rest": [[7, "term-REST", true]], "rest (representational state transfer)": [[7, "term-REST-Representational-State-Transfer", true]], "rir (reps in reserve)": [[7, "term-RIR-Reps-in-Reserve", true]], "rp": [[7, "term-RP", true]], "rpe (rate of perceived exertion)": [[7, "term-RPE-Rate-of-Perceived-Exertion", true]], "saas (software as a service)": [[7, "term-SaaS-Software-as-a-Service", true]], "scalability": [[7, "term-Scalability", true]], "schema": [[7, "term-Schema", true]], "sdk (software development kit)": [[7, "term-SDK-Software-Development-Kit", true]], "set progression": [[7, "term-Set-Progression", true]], "settings": [[7, "term-Settings", true]], "sfr (stimulus-to-fatigue ratio)": [[7, "term-SFR-Stimulus-to-Fatigue-Ratio", true]], "sla (service level agreement)": [[7, "term-SLA-Service-Level-Agreement", true]], "solid": [[7, "term-SOLID", true]], "sql": [[7, "term-SQL", true]], "ssl/tls": [[7, "term-SSL-TLS", true]], "subscription": [[7, "term-Subscription", true]], "support tier": [[7, "term-Support-Tier", true]], "sync": [[7, "term-Sync", true]], "tdd": [[7, "term-TDD", true]], "third-party integration": [[7, "term-Third-Party-Integration", true]], "training block": [[7, "term-Training-Block", true]], "training data": [[7, "term-Training-Data", true]], "training frequency": [[7, "term-Training-Frequency", true]], "uptime": [[7, "term-Uptime", true]], "user profile": [[7, "term-User-Profile", true]], "uuid": [[7, "term-UUID", true]], "validation testing": [[7, "term-Validation-Testing", true]], "volume landmarks": [[7, "term-Volume-Landmarks", true]], "vpc": [[7, "term-VPC", true]], "webhook": [[7, "term-Webhook", true]], "workout log": [[7, "term-Workout-Log", true]]}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [6, 7, 8, 13, 14], "0": [1, 2, 6, 8, 9, 11, 13, 14], "00": [1, 2, 6, 13], "00z": [1, 2, 13], "01": [1, 2, 9, 13], "04": 0, "05": 1, "06": 9, "1": [1, 2, 4, 6, 7, 11, 13, 14], "10": [0, 7, 11, 13, 14], "100": [2, 6, 8, 14], "1080": [1, 14], "10m": 5, "10mb": 1, "11": [0, 6, 13], "120": [1, 4, 14], "123": 1, "123456789abc": 2, "123e4567": [1, 2, 4], "12d3": [1, 2, 4], "13t12": 13, "15": [0, 13], "15728640": 1, "15t10": 2, "15t11": 2, "15t12": 1, "1920": [1, 14], "1920x1080": 4, "1996": 9, "1x": [7, 11], "2": [0, 1, 2, 4, 6, 9, 11, 13, 14], "20": [0, 2, 11, 13], "200": [1, 2], "2004": 9, "2006": 9, "2009": 9, "200m": [0, 5, 12], "201": [1, 2, 6], "2013": 9, "2015": 9, "2017": 9, "2018": 9, "2023": 9, "2024": [1, 2], "2025": [9, 13], "204": [1, 2], "20gb": 0, "245kb": 4, "25": 6, "255": 4, "2kb": 12, "3": [0, 4, 5, 6, 7, 9, 11, 13], "30": [0, 1, 2], "32": 13, "3x": [7, 11], "4": [4, 5, 6, 7, 11, 13], "40": [7, 11], "400": 1, "403": 14, "404": [1, 14], "409": [1, 4, 14], "422": 14, "426614174000": [1, 2, 4], "426614174001": 2, "43d1": 2, "45": 14, "456": 1, "456e7890": 2, "48": 11, "4gb": 0, "5": [0, 5, 6, 11], "50": [2, 14], "50m": 5, "50mb": 1, "51a2": 2, "5432": 13, "5mb": 1, "6": [6, 11, 13], "60": [7, 11], "6379": 13, "7": [11, 13], "789": 1, "8": [6, 7, 11], "8000": [0, 5, 6, 8, 13], "800x600": 4, "8601": 1, "8gb": 0, "9": [0, 13], "90": [0, 5, 6, 14], "95th": [0, 5], "987fcdeb": 2, "99": 0, "9c4e": 2, "A": [6, 7, 9], "AND": 9, "AS": 9, "And": 6, "As": [6, 10], "BE": 9, "BUT": 9, "FOR": 9, "For": [5, 6, 8, 9, 13, 14], "IN": 9, "If": [0, 6, 7, 9], "In": 7, "It": 14, "NO": 9, "NOT": 9, "No": [1, 2, 9, 12, 13], "Not": [1, 2, 14], "OF": 9, "ON": 13, "OR": 9, "On": 13, "One": [1, 4, 14], "THE": 9, "TO": [9, 13], "The": [0, 1, 2, 4, 5, 7, 9, 10, 11, 12, 13, 14], "Then": 6, "These": 11, "To": [10, 14], "WITH": [9, 13], "With": 13, "__init__": 4, "__tablename__": 4, "_entity_to_model": 4, "_mark_not_curr": 4, "_model_to_ent": 4, "a456": [1, 2, 4], "abc": [4, 6], "abc123": 4, "abil": 7, "about": [7, 9, 14], "abov": 9, "abstract": [5, 6], "abstractmethod": [4, 6], "academ": 9, "accept": 6, "access": [0, 4, 5, 6, 7, 8, 11, 13], "accord": 9, "accordingli": 11, "account": [5, 6, 7, 14], "accumul": [7, 10, 11], "accur": [2, 11, 14], "accuraci": [6, 8, 14], "achiev": 7, "acknowledg": 5, "across": [0, 4, 7, 10, 13], "act": 7, "action": [2, 9, 10], "activ": [0, 1, 2, 5, 6, 10, 11, 13, 14], "actual": 6, "ad": [2, 5], "adapt": [5, 7, 8, 10, 11], "add": [4, 5, 6, 13, 14], "addit": [4, 5, 6, 14], "address": 9, "adher": [5, 7, 9], "adjust": [7, 8, 9, 10], "admin": [2, 4, 6, 14], "administr": [0, 8, 13], "adr": 6, "advanc": [2, 5, 7, 8], "advic": 9, "after": 13, "ag": [6, 7], "against": [5, 6, 7, 10, 12], "agenc": 7, "aggreg": 9, "agreement": 7, "al": 9, "alemb": [0, 5, 6, 8, 12, 13], "alert": [0, 5, 7], "algorithm": [0, 5, 8, 9], "align": 6, "all": [0, 1, 2, 3, 4, 5, 6, 8, 9, 10, 13, 14], "allow": [2, 7, 8, 11, 13], "alreadi": [1, 13, 14], "also": [7, 9, 14], "alter": 7, "altern": [6, 11, 13, 14], "alwai": [0, 2, 10, 14], "am": 6, "amazon": 7, "an": [0, 1, 2, 4, 6, 7, 9, 11, 14], "analysi": [5, 6, 7, 8, 10, 11, 14], "analyt": [5, 7, 8], "angl": [1, 14], "ani": [4, 6, 9, 10], "anim": 1, "annot": [6, 7], "annual": 6, "anonymis": [0, 9], "anoth": 7, "apach": 9, "apart": 14, "api": [0, 5, 6, 7, 13], "app": [0, 5, 6, 8, 12, 13], "appli": [7, 11], "applic": [0, 1, 2, 5, 6, 7, 8, 9, 10, 12, 13, 14], "appreci": [6, 9], "approach": [4, 7, 8, 11, 13], "appropri": [1, 14], "approv": [3, 5, 8], "approval_statu": [2, 4], "approvalstatu": 4, "approve_exercis": 4, "approved_at": [2, 4], "approved_bi": [2, 4], "apt": 13, "ar": [1, 2, 4, 6, 7, 8, 9, 10, 14], "arch": [2, 13, 14], "architectur": [5, 7, 12], "archiv": 4, "area": 8, "arg": 6, "aris": 9, "arm": [2, 7, 14], "around": 2, "arrai": [2, 14], "artifici": 7, "as_uuid": 4, "assert": 6, "assess": [0, 7], "assist": 0, "associ": [1, 5, 9], "assum": 0, "assumpt": 6, "assur": [5, 8], "async": [4, 5, 6], "asynccli": 6, "asyncio": 6, "asyncpg": 13, "asyncsess": 4, "athlet": 7, "attack": [5, 12], "attempt": [1, 4], "audienc": 6, "audio": [1, 4, 5, 8, 14], "audit": [0, 2, 5, 14], "auditact": 4, "augment": 7, "auth": [5, 6], "auth_token": 14, "authent": [0, 3, 5, 6, 7, 8, 12, 14], "authenticate_us": 6, "author": [1, 2, 9, 14], "authoris": 0, "auto": 13, "autogener": 13, "autom": [0, 5, 6, 8, 10], "automat": [0, 5, 7, 10, 11, 13], "avail": [0, 1, 2, 5, 7, 11, 13, 14], "averag": 5, "avoid": 14, "aw": 7, "await": [4, 5, 6], "b": 6, "back": [2, 7, 8, 14], "backbon": 11, "backend": 0, "backup": [0, 7, 14], "bad": 1, "balanc": 0, "bar": [2, 14], "barbel": [2, 14], "base": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "baselin": [7, 11], "basemodel": 4, "bash": 13, "bashrc": 13, "basic": [0, 5], "bcrypt": [5, 8], "bdd": [0, 5, 7], "bearer": [1, 2, 14], "becom": [7, 11, 14], "been": 10, "befor": [0, 6, 7, 9, 14], "begin": [0, 9, 11, 13], "beginn": [6, 14], "behavior": [5, 6], "behaviour": [0, 7], "being": 7, "below": [6, 9], "bench": [1, 2, 14], "benchmark": [0, 5, 6], "benefit": [6, 13], "best": [0, 5, 6, 8], "better": [2, 14], "between": 7, "beyond": 7, "big": 14, "bin": [6, 13], "biometr": 7, "black": [0, 5, 6, 13], "blade": [2, 14], "blob": 9, "block": [5, 7, 10, 11], "blue": 0, "bodi": [7, 9, 10, 11], "bompa": 10, "bool": 4, "boolean": [1, 2, 4], "both": 6, "bounc": [2, 14], "boundari": 6, "brad": 10, "bradlc": 13, "branch": [0, 6], "breath": [1, 14], "brew": 13, "briefli": 14, "british": 6, "broader": 9, "brows": 13, "browser": 6, "brute": 5, "bsd": 9, "bugfix": 6, "build": [0, 6, 7, 10], "built": [0, 2, 8, 10], "busi": [0, 1, 4, 5, 6, 8, 9, 14], "byte": 1, "c": [9, 13], "cach": [0, 13], "calcul": [5, 6, 7, 8, 10], "calculate_mev": 6, "call": [0, 6, 14], "callback": 7, "can": [0, 1, 2, 4, 6, 7, 10, 11, 14], "cannot": [4, 6], "capabl": [0, 2, 4, 5, 8], "capac": [0, 7, 11], "cascad": 5, "case": [0, 4, 5, 6, 7, 8], "cat": 13, "categori": [0, 7, 13], "categoris": 8, "cd": [0, 6, 7, 8, 12, 13], "cdn": [1, 4, 7, 14], "center": 7, "centr": 11, "centralis": 7, "certif": 0, "certifi": 10, "chang": [0, 2, 4, 5, 6, 7, 8, 9, 14], "change_reason": [2, 4, 14], "changelog": [6, 8], "changereason": 4, "channel": [0, 9], "charact": 13, "characterist": [6, 7, 10], "charg": 9, "chat": 6, "check": [0, 5, 13, 14], "checker": 13, "checkout": 6, "checkpoint": [1, 14], "chest": [2, 6, 7, 14], "child": [4, 5], "chmod": 13, "choic": 10, "choos": [0, 1, 7, 11], "ci": 7, "citat": [6, 9], "cite": 9, "claim": [6, 7, 9], "clarif": 6, "class": [4, 6], "classic": [2, 14], "classif": 8, "classifi": 7, "claus": 9, "clean": [0, 4, 5, 6, 7, 8, 12], "cleanup": 5, "clear": [0, 1, 4, 5, 6, 12, 13, 14], "client": [1, 6, 7], "clone": [0, 5, 6, 8, 12, 13], "close": 7, "cloud": 7, "cmd": 7, "coach": [1, 8, 9, 10, 14], "code": [0, 5, 9, 10, 13], "codebas": 5, "collabor": [6, 9], "collect": [0, 7, 9, 13], "com": [0, 1, 4, 6, 8, 9, 12, 13, 14], "combin": [7, 12, 14], "command": 13, "comment": 6, "commerci": 9, "commit": [0, 5, 6, 7], "common": [0, 6, 7, 12, 13], "common_mistak": [2, 4, 14], "commonli": 4, "commun": [5, 9, 14], "compact": 7, "compar": [7, 11], "comparison": 6, "competit": 11, "complet": [2, 4, 5, 6, 7, 8, 13, 14], "complex": [5, 6, 10, 14], "complianc": [4, 5, 7], "compon": [3, 5, 6, 8], "compos": [0, 5, 6, 8, 12, 13], "composit": 7, "compound": [2, 11, 14], "comprehens": [0, 2, 3, 4, 5, 6, 7, 8, 10, 12, 13], "compress": 1, "comput": 10, "concept": [0, 2, 6, 7, 10, 11], "concern": [0, 4, 7, 8, 12], "concis": 6, "concret": 6, "condit": [6, 9], "conduct": 6, "conf": 13, "confer": 6, "config": 13, "configur": [5, 7, 8, 12], "confirm": 6, "conflict": [1, 4, 14], "connect": [0, 4, 5, 7, 9, 13], "conserv": 11, "consid": [0, 4, 6, 10, 14], "consider": [0, 1, 6, 8], "consist": [0, 1, 6, 7, 11, 14], "constant": 4, "constraint": 5, "consult": [0, 6, 9, 10], "contact": [0, 6, 7, 14], "contain": 7, "container": 5, "containeris": [0, 7], "content": [2, 5, 6, 7], "content_upd": [2, 14], "context": 6, "continu": [0, 7, 8, 10], "contract": 9, "contribut": [5, 8], "contributor": [5, 9], "control": [0, 2, 5, 7, 8, 14], "convert": 7, "coordin": 6, "copi": [5, 9, 13], "copyright": 9, "cor": [5, 7, 8], "core": [5, 6, 8], "correl": 0, "cost": [7, 10, 11], "count": [4, 14], "coupl": 7, "cours": 7, "cov": [5, 6], "cover": [8, 13], "coverag": [0, 5, 6], "cp": [5, 13], "creat": [1, 4, 5, 6, 7, 8, 13], "create_exercis": 4, "create_new_vers": [2, 4, 14], "create_vers": 4, "create_workout": 6, "created_at": [1, 2, 4], "created_bi": 4, "creation": [2, 4, 5, 6], "criteria": 6, "critic": 6, "cross": [5, 6, 7], "crud": [5, 7], "csp": 4, "ctrl": 7, "cue": [1, 2, 7, 14], "curl": [0, 5, 13, 14], "current": [4, 5, 7, 9, 10, 11, 14], "current_vers": 2, "current_version_onli": [2, 4, 14], "custom": [0, 1, 5, 7], "customis": 7, "cut": 12, "d": [0, 5, 6, 8, 12, 13, 14], "daemon": 13, "damag": 9, "dashboard": 7, "data": [0, 5, 6, 8, 10, 11, 14], "databas": [5, 6, 7, 8, 9, 13], "database_url": 13, "dataset": [4, 5], "datetim": 4, "db": [5, 6, 13], "db_manag": 13, "ddd": 7, "deal": 9, "debian": 13, "debt": 5, "debug": [0, 13], "decis": 6, "declin": 11, "decreas": 11, "deep": 8, "def": [4, 6], "def456": 4, "default": [0, 2, 4, 5, 14], "defin": [7, 11], "definit": 6, "degre": 14, "delet": [0, 4, 5, 7, 8], "deleted_at": [2, 4, 5], "deleted_bi": 4, "deliveri": [1, 4, 7], "deload": [7, 8, 10, 11], "delta": 11, "demand": [1, 10, 11], "demonstr": [1, 8, 14], "deni": 13, "depend": [0, 5, 6, 7, 9, 13], "deploy": [5, 7, 8], "depress": 14, "descent": [2, 14], "descript": [1, 2, 4, 5, 6, 7, 13, 14], "design": [0, 1, 2, 4, 5, 6, 7, 12], "detail": [0, 1, 2, 4, 5, 6, 7, 8, 10, 11, 14], "detect": 0, "determin": [7, 11], "dev": [6, 9, 13], "develop": [0, 5, 7, 9, 10, 11, 12], "devic": [7, 12], "devop": 0, "diagram": 5, "dict": 4, "did": 6, "differ": [0, 1, 4, 7, 8, 14], "difficulti": [2, 5, 7, 8, 14], "difficulty_level": [2, 4, 14], "difficultylevel": 4, "dimens": [1, 4], "direct": 0, "directli": 14, "directori": 13, "direnv": 13, "disast": 0, "discuss": 6, "disk": 0, "dissip": 10, "distribut": [7, 9, 11], "dive": 8, "dn": 7, "do": 9, "doc": [0, 4, 5, 6, 8, 13], "docker": [0, 5, 6, 7, 8, 12], "docstr": 6, "document": [3, 5, 7, 9, 13, 14], "doe": [6, 9], "doesn": 14, "domain": [0, 5, 6, 7, 8, 12, 13], "domainexcept": 6, "don": [7, 14], "donald": 9, "done": 6, "downgrad": 13, "download": 0, "dr": [9, 10, 11], "drive": [2, 7, 10, 14], "driven": [5, 7, 8, 12], "driver": 11, "dry": 7, "dumbbel": 14, "duplic": 4, "durat": [1, 4, 11], "duration_second": [1, 14], "dure": [0, 7], "e89b": [1, 2, 4], "each": [1, 2, 4, 6, 10, 11, 14], "earli": 11, "easi": [0, 7], "echo": 13, "edg": [6, 12], "edit": [13, 14], "educ": [6, 9], "effect": [1, 6, 7, 8, 10, 11], "elbow": [2, 14], "elev": 11, "email": [5, 6], "emerg": [6, 9, 10], "emphasi": 11, "emphasis": 11, "empir": 10, "enabl": [7, 13], "encapsul": 7, "encod": [4, 9], "encount": [0, 7], "encrypt": 0, "end": 8, "endpoint": [0, 1, 3, 4, 5, 6, 7, 8], "enforc": 5, "engag": [2, 14], "engin": [8, 10], "english": 6, "enhanc": [5, 6, 10], "ensur": [0, 1, 6, 7, 14], "enter": [6, 12, 13], "enterpris": [0, 7, 8], "enthusiast": 8, "entir": 14, "entiti": [0, 4, 5, 6, 8], "enum": [1, 4, 5, 14], "env": [5, 13], "environ": [0, 5, 6, 12], "equip": [1, 2, 4, 5, 7, 8, 11, 14], "equipment_requir": [2, 4, 14], "eric": 10, "error": [0, 5, 6, 13, 14], "escal": 0, "establish": [9, 10], "estim": [5, 6, 8, 10, 11], "et": 9, "etc": [1, 13, 14], "eval": 13, "evalu": 11, "even": 4, "event": [4, 5, 6, 9, 11], "everi": [0, 6, 11], "evid": [6, 8, 9, 10, 11, 12, 14], "evolut": 4, "evolv": 0, "exact": 4, "exampl": [1, 4, 5, 6, 7, 8, 11, 13], "exce": 7, "exceed": 11, "excel": 0, "except": [4, 5, 6], "excess": 11, "exec": [0, 5, 8, 13], "execut": [13, 14], "execution_step": [2, 4, 14], "exercis": [3, 5, 6, 7, 8, 9, 10, 11], "exercise_id": [1, 2, 4, 14], "exercise_repositori": 4, "exercise_use_cas": 4, "exercise_uuid": [2, 4, 14], "exercisecreaterequest": 4, "exercisemedia": 4, "exercisemediausecas": 4, "exercisemodel": 4, "exerciserepositori": 4, "exerciserepositoryimpl": 4, "exercisesearchfilt": 4, "exerciseselect": 6, "exerciseservic": 4, "exerciseupdaterequest": 4, "exerciseusecas": 4, "exert": [7, 11], "exist": [1, 4, 6, 14], "expand": 5, "expans": 5, "expect": [6, 7, 13], "expens": 4, "experi": [0, 5, 6, 7, 11, 12], "experienc": 10, "experience_": 11, "experiment": 13, "expert": [6, 10], "expir": 5, "explain": 6, "explan": 10, "explicitli": 7, "explor": [10, 13], "export": [7, 14], "express": 9, "extend": 0, "extens": [10, 13], "extern": [0, 4, 6, 7, 8], "extract": [4, 7], "ey": [2, 14], "f": [6, 7, 12, 13], "factor": [0, 7, 9, 11], "factori": 6, "fail": [1, 6], "failur": [7, 14], "fals": [1, 2, 4, 14], "famili": 11, "familiar": 0, "faq": [5, 6], "fastapi": [0, 4, 5, 12], "fastest": [0, 13], "fatigu": [5, 7, 8, 10], "fatigue_": 11, "feat": 6, "featur": [0, 2, 4, 5, 13], "feedback": [5, 6, 7, 8, 10, 11, 14], "feel": 7, "feet": 14, "fiber": 7, "field": [1, 4, 10, 14], "file": [0, 4, 5, 6, 9, 13], "file_size_byt": 1, "filter": [1, 2, 5, 8, 14], "find": [6, 13], "first": [4, 6, 12, 13, 14], "first_nam": 6, "fit": [0, 6, 8, 9, 11, 12], "fix": [5, 6, 13], "flag": 0, "flake": 13, "flake8": [0, 5, 13], "flare": [2, 14], "flat": 14, "flexibl": [0, 8], "float": 6, "floor": 14, "flow": [5, 6], "flush": 4, "focu": [1, 11], "focus": 8, "follow": [0, 1, 4, 6, 8, 9, 10, 11, 14], "forbidden": 14, "forc": 5, "foreign": 5, "forens": 4, "forg": [0, 3, 4, 5, 7, 9, 10, 11, 13, 14], "forge_password": 13, "forge_protocol": 13, "forge_us": 13, "forgeprotocol": [0, 6, 9, 14], "fork": 6, "forkrul": [0, 8, 9, 12, 13], "form": [1, 2, 5, 6, 7, 8, 10, 11, 14], "form_cu": [2, 4, 14], "format": [1, 5, 6, 7, 13, 14], "formatt": 13, "formul": 6, "formula": 11, "forum": [6, 14], "found": [1, 6, 10, 14], "foundat": [5, 11], "frac": 11, "frame": [1, 14], "framework": [0, 5, 7, 8, 9, 10], "free": 9, "frequenc": [7, 11], "frequent": 4, "fridai": 6, "from": [0, 5, 6, 7, 8, 9, 10, 11, 13, 14], "full": [1, 2, 5, 6, 11, 13, 14], "func": 4, "function": [1, 2, 5, 6, 7], "fundament": [7, 14], "furnish": 9, "futur": [6, 9], "gain": 7, "gap": 6, "garbag": 13, "gaug": 7, "gcp": 7, "gdpr": [0, 7], "gener": [5, 6, 7, 9, 10, 11], "genet": 11, "genetics_": 11, "get": [4, 5, 13], "get_by_email": 6, "get_by_id": [4, 6], "get_by_uuid": 4, "get_current_vers": 4, "get_exercise_statist": 4, "get_user_by_id": 6, "gherkin": [0, 5, 6], "ghi789": 4, "gif": [1, 4, 5, 8, 14], "git": [0, 5, 6, 8, 12, 13], "github": [0, 5, 6, 8, 9, 12, 13], "given": [6, 11], "global": [9, 13], "glossari": 8, "goal": [6, 7, 11], "good": 11, "googl": [6, 7], "govern": 7, "grade": [0, 8], "gradual": 7, "grant": [9, 13], "greatest": [7, 11], "green": 0, "greg": 10, "grip": 14, "group": [2, 5, 6, 7, 8, 9, 10, 11, 14], "grow": 11, "growth": [7, 8, 10, 11], "guid": [0, 5, 6, 7, 10, 11, 12], "guidanc": [0, 2, 6, 8, 9, 14], "guidelin": [5, 11], "gym": 8, "h": [13, 14], "ha": [1, 4, 7], "hand": 14, "handl": [5, 6, 7], "handler": 5, "happen": 6, "hard": 7, "has_mor": 2, "hash": [5, 8], "hashed_password": 6, "have": [0, 1, 2, 4, 6, 10, 14], "head": [0, 5, 6, 8, 12, 13, 14], "header": [2, 4, 5], "health": [0, 5, 7, 13], "healthcar": 9, "healthi": 13, "heart": 11, "heavi": [2, 11, 14], "heel": [2, 14], "height": [1, 14], "height_pixel": [1, 14], "hello": [6, 9], "helm": 10, "help": [1, 13], "here": [6, 7, 9, 10, 14], "herebi": 9, "hierarchi": 5, "high": [0, 1, 6, 11, 14], "higher": [11, 13], "highest": [7, 11], "highlight": 1, "hing": [7, 8], "hint": [0, 5, 6], "hipaa": 7, "histori": [2, 4, 5, 6, 7, 9, 10, 11, 14], "hoffmann": 10, "holder": 9, "holger": 9, "homebrew": 13, "hook": [5, 13], "horizont": [0, 4, 5], "host": [6, 7, 13], "hot": [0, 5, 6], "hour": 11, "how": [6, 7, 10, 14], "html": 6, "http": [0, 1, 4, 5, 6, 7, 8, 9, 12, 13, 14], "httpx": 6, "hypertext": 7, "hypertroph": [7, 10, 11], "hypertrophi": [6, 7, 8, 9, 10, 11, 12, 13], "i": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "iam": 7, "id": [0, 1, 2, 4, 5, 6, 12, 14], "ident": [4, 7, 13], "identif": 6, "identifi": [7, 8], "imag": [1, 2, 4, 5, 8, 14], "immut": [4, 5], "implement": [0, 1, 4, 5, 6, 8, 9, 12, 13], "impli": 9, "import": [1, 6, 13, 14], "improv": [5, 6, 7, 8, 10, 14], "inc": 9, "incid": 0, "includ": [0, 1, 2, 5, 6, 7, 8, 9, 10, 13, 14], "include_delet": [2, 4, 14], "incompat": 7, "incorrect": 6, "increas": [6, 7, 10, 11], "increment": [11, 14], "incur": [7, 11], "independ": 7, "index": [0, 4, 5, 8, 12], "indic": [7, 10, 11], "individu": [6, 7, 8, 9, 10], "individualis": 11, "influenc": 7, "inform": [0, 1, 6, 7, 14], "infrastructur": [0, 5, 6, 8, 12, 13], "initi": [2, 5, 9, 14], "initial_cr": 2, "inject": [4, 5, 6, 7, 8], "injuri": [7, 10, 11], "inlin": 6, "input": [4, 5, 8], "inquiri": [6, 9], "insight": 8, "instal": [5, 6, 8, 12], "institut": 10, "instruct": [1, 2, 4, 5, 6, 7, 8, 14], "insur": 7, "int": 4, "integ": [2, 4], "integr": [0, 1, 5, 6, 8, 10, 13], "intellig": [7, 8, 11], "intend": 9, "intens": [7, 11], "interact": [6, 13], "interest": [6, 8], "interfac": [1, 4, 5, 6, 13], "intermedi": [2, 8, 14], "internation": 5, "internet": [0, 7], "interpret": 13, "introduc": 7, "intrus": 0, "intuit": 6, "invalid": [1, 6, 14], "invalidcredentialserror": 6, "invers": [5, 7], "investig": 4, "involv": 7, "is_act": [1, 2, 4, 6, 14], "is_approv": [2, 4, 14], "is_current_vers": [2, 4], "is_primari": [1, 14], "iso": 1, "isol": [0, 6, 7, 8, 11, 13], "isort": [0, 5, 6, 13], "israetel": [9, 10, 11], "issu": [0, 5, 13], "item": [1, 4, 5, 14], "its": 7, "jame": 10, "javascript": 7, "join": 14, "joint": 11, "jos\u00e9": 9, "journal": 6, "jpadilla": 9, "jpeg": 1, "jpg": [1, 4, 14], "json": [1, 2, 6, 7, 13, 14], "june": 5, "jwt": [0, 1, 2, 4, 5, 7, 8, 12, 14], "keep": [2, 5, 6, 11, 14], "kei": [2, 4, 5, 6, 7, 9, 13], "kill": 13, "kind": 9, "kit": 7, "knowledg": 10, "known": [5, 6, 7, 10], "krekel": 9, "l": 13, "lambda": 6, "landmark": [7, 10], "languag": [6, 7, 13, 14], "larg": [4, 5, 7], "last": [7, 11], "last_nam": 6, "later": [0, 2], "latest": [5, 8, 10, 14], "layer": [0, 5, 6, 7, 8, 12], "lazi": 1, "lead": 10, "learn": [5, 7, 10, 11], "leg": [2, 7, 14], "legal": 9, "lengthen": 11, "level": [2, 4, 7, 11, 13], "liabil": 9, "liabl": 9, "librari": [7, 9], "licens": 8, "lie": [2, 14], "lifestyl": 11, "lift": [11, 14], "lightweight": 7, "like": [0, 7, 13], "limit": [2, 4, 5, 6, 7, 8, 9, 11, 14], "link": 7, "lint": [0, 5], "linux": [0, 6, 13], "liskov": 7, "list": [4, 6, 7, 14], "listen": [9, 10], "lit": [1, 14], "literatur": [6, 10], "load": [0, 1, 4, 5, 7, 8, 11, 13], "local": [0, 6], "localhost": [0, 5, 8, 13], "lockout": 5, "log": [0, 4, 5, 6, 7, 13], "log_audit": 4, "logic": [0, 1, 4, 5, 6, 7, 8, 14], "login": 5, "logout": 5, "long": [6, 7, 13], "lookup": 4, "loon": 10, "loop": [1, 14], "loos": 7, "lost": 8, "low": 11, "lower": [2, 11, 14], "lowest": [7, 11], "lsof": 13, "lsp": 13, "ltd": 9, "luc": 10, "m": [6, 12, 13], "mac": 7, "machin": [5, 7, 10, 11, 13], "maco": [0, 6, 13], "made": 7, "mai": [7, 9], "mail": 9, "main": [0, 6, 7, 8, 9, 12, 13, 14], "maintain": [0, 2, 4, 5, 6, 7, 8, 11, 14], "mainten": [7, 11], "major": [6, 9], "make": [0, 6, 7, 14], "manag": [3, 5, 7, 8, 10, 11, 12, 13], "mani": 7, "manner": 14, "manual": 12, "map": [4, 7, 11], "mapped_column": 4, "mark": [1, 4, 6], "mass": [7, 11], "match": [4, 14], "materi": 10, "mathemat": [6, 8, 10], "mav": [7, 10, 11], "maximis": 7, "maximum": [1, 2, 7, 10, 11, 14], "me": 5, "mean": 7, "meaning": 14, "measur": [7, 10, 11], "media": [2, 5, 8], "media_id": [1, 14], "media_ord": 1, "media_typ": [1, 14], "media_use_cas": 4, "mediatyp": 1, "medic": 9, "medicin": 9, "memori": 0, "mentor": 6, "merchant": 9, "merg": 9, "mesocycl": [5, 7, 8, 10, 11], "messag": [1, 2, 5, 6, 14], "meta": 6, "metabol": 11, "metadata": [1, 2, 4, 5, 14], "method": [0, 6, 7], "methodologi": [5, 6, 7, 8, 9, 11, 14], "metric": [0, 5, 6, 7, 8, 10, 11], "mev": [5, 6, 7, 8, 10, 11], "mev_": 11, "mev_calcul": 6, "mevcalcul": 6, "microservic": 4, "migrat": [0, 5, 6, 7, 8, 12, 13], "mike": [9, 10, 11], "mileston": 7, "mime": 1, "mime_typ": [1, 14], "mind": 0, "minim": [6, 11], "minimis": [7, 11], "minimum": [0, 6, 7, 10, 11, 13], "minut": [0, 8], "miss": 14, "mobil": [12, 13], "mock": 12, "mockup": 6, "mode": 13, "model": [4, 5, 6, 7, 8, 10], "moder": [11, 14], "modern": [0, 8, 12], "modif": [11, 14], "modifi": [4, 7, 9, 11], "modul": [8, 12], "monitor": [5, 7, 10, 13], "month": 6, "monthli": 6, "more": [1, 2, 6, 11, 14], "mortem": 0, "most": [1, 9, 11], "motion": [2, 7, 11, 14], "mov": 1, "movement": [1, 2, 7, 8, 11, 14], "movement_pattern": [2, 4, 14], "movementpattern": 4, "mp3": 1, "mp4": [1, 4, 14], "mrv": [7, 10, 11], "much": 14, "multi": [0, 2, 5, 8, 13], "multimedia": [1, 2], "multipl": [0, 1, 2, 4, 5, 6, 7, 10, 11, 13, 14], "multipli": 7, "muscl": [2, 5, 6, 7, 8, 10, 11, 14], "muscle_group": 6, "musclegroup": 4, "muscular": 7, "must": [6, 11], "mv": [7, 11], "my": 6, "mypi": [0, 5, 13], "n": 6, "name": [2, 4, 6, 7, 14], "nano": 13, "need": [0, 2, 4, 5, 6, 7, 9, 14], "neovim": 13, "network": [0, 7, 12], "neural": 11, "never": [2, 4, 8, 14], "new": [0, 1, 2, 4, 5, 6, 7, 9, 10, 13, 14], "new_vers": 4, "next": 12, "nix": 12, "nixo": 13, "non": 5, "none": 4, "noninfring": 9, "normal": [7, 11], "notabl": 5, "notat": 7, "note": [2, 4, 5, 6, 14], "notic": 9, "notif": [0, 7], "notifi": 9, "now": 4, "nuckol": 10, "null": [2, 4], "nullabl": 4, "number": [1, 2, 7], "object": [4, 7, 11], "obtain": 9, "occur": 6, "off": [2, 14], "offici": [0, 9], "offset": [2, 4, 14], "often": 7, "ogg": 1, "ok": [1, 2, 13], "old": 4, "onc": [4, 13], "one": [1, 2, 4, 7, 14], "ongo": [7, 10], "onli": [2, 4, 9, 13, 14], "open": [0, 5, 6, 7, 8, 9, 13], "openapi": [4, 5, 6, 13], "oper": [0, 4, 5, 6, 7, 14], "opportun": [6, 7], "optim": [5, 6, 7, 8, 10, 12, 13], "optimis": [0, 7, 8, 10, 11], "option": [1, 2, 4, 5, 6, 7, 13, 14], "orchestr": [0, 4, 5], "order": 1, "org": [9, 13], "organ": 6, "organis": [0, 7, 8, 10], "orient": 7, "origin": [5, 6, 7], "orm": [0, 4, 5, 7], "oss": 9, "other": [2, 9, 14], "otherwis": 9, "our": [0, 5, 7, 8, 9, 10], "out": 9, "outcom": 10, "outlin": 6, "output": 4, "over": [7, 8, 11, 14], "overal": 6, "overload": [7, 8, 10], "overrid": 0, "overtrain": 11, "overview": [3, 6, 7, 8], "own": [4, 14], "ownership": 4, "p": 13, "packag": [0, 7, 13], "pacman": 13, "padilla": 9, "page": [6, 8, 12], "pagin": [4, 5, 14], "pair": 6, "paper": 6, "paramet": [0, 1, 2, 6, 7], "parameter": [4, 5], "parameteris": 8, "parent": [4, 5], "parent_version_id": [2, 4], "parti": 7, "partial": 14, "particular": 9, "partit": 4, "partnership": 6, "pass": [5, 6], "password": [5, 6, 8, 13], "patch": 0, "path": 6, "pattern": [2, 5, 6, 7, 8, 10, 11], "paus": 14, "payload": 12, "payment": 7, "peak": [7, 11], "peer": [6, 9, 10], "pend": [2, 4, 5, 14], "penetr": 0, "per": [1, 4, 6, 7, 11, 14], "perceiv": [7, 11], "percentag": 7, "percentil": [0, 5], "perform": [0, 5, 6, 7, 8, 10, 11, 13], "period": [7, 11, 12], "periodis": [5, 6, 7, 8, 9, 10], "perman": [2, 4, 8], "permiss": [4, 9, 13, 14], "permit": 9, "persist": 4, "person": [0, 7, 8, 9, 11], "personalis": [5, 8, 10], "pg_stat_stat": 13, "pgadmin": [0, 5, 6, 13], "phase": [5, 7], "phillip": 10, "philosophi": [7, 12], "photo": [1, 14], "physic": 7, "physiolog": 10, "physiologi": 10, "physiologist": [6, 10], "pid": 13, "pip": [6, 13], "place": [7, 14], "placehold": 5, "plan": [0, 5, 6, 7, 8, 10, 11], "platform": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14], "pleas": [6, 7, 9, 14], "plugin": 13, "pm": 6, "png": 1, "point": [0, 7, 11], "polici": [0, 7, 9], "pollut": 13, "pool": [0, 4, 5], "poor": 11, "port": [6, 13], "portabl": 7, "portion": 9, "posit": [1, 2, 11, 14], "possibl": [1, 11, 14], "post": [0, 1, 2, 5, 6, 8, 10, 14], "postgr": 13, "postgresql": [0, 5, 12, 13], "postgresuuid": 4, "power": 8, "powerlift": 14, "practic": [0, 5, 6, 7, 8, 10], "practition": 10, "pre": 5, "precis": [8, 11, 14], "predict": 5, "prefer": [7, 11, 13], "prepar": 11, "prerequisit": 13, "prescript": 7, "present": [0, 4, 5, 6, 8, 9], "preserv": 5, "press": [1, 2, 14], "prevent": [0, 4], "previou": [10, 11], "previous": [2, 14], "primari": [2, 4, 5, 7, 8, 11, 13, 14], "primarili": 14, "primary_kei": 4, "primary_muscle_group": [2, 4, 14], "principl": [0, 4, 5, 6, 8, 9, 12, 14], "print": [6, 13], "privaci": 7, "privat": 7, "privileg": 13, "problem": 6, "procedur": 0, "proceed": 0, "process": [0, 2, 4, 5, 6, 7, 8, 9, 13], "produc": [7, 11], "product": [0, 5, 13], "profession": [6, 9, 10], "profil": [5, 6, 7, 12], "program": [6, 7, 8, 9], "programm": [8, 10], "progress": [5, 7, 8, 10, 14], "project": [5, 9, 13], "promin": 4, "promot": 7, "proper": [1, 5, 7, 8, 14], "propos": 6, "protect": [0, 5, 7, 8, 9, 12], "protocol": [0, 3, 4, 5, 7, 9, 11, 13, 14], "provid": [0, 1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14], "proxim": 7, "psql": 13, "public": [2, 4, 5, 6, 9, 14], "publicli": [4, 14], "publish": [6, 9], "pull": [5, 6, 7, 8], "purpos": [7, 9, 10], "push": [2, 6, 7, 8, 14], "put": [1, 2, 5, 14], "py": [6, 13], "pyca": 9, "pycharm": 13, "pydant": [0, 4, 5, 7, 8], "pytest": [0, 5, 6, 13], "python": [0, 6, 7, 12, 13], "python3": [6, 13], "q": [6, 13], "q3": 5, "q4": 5, "qualifi": [6, 9, 10], "qualiti": [1, 5, 6, 8, 11], "quantifi": [10, 11], "quantit": 7, "queri": [0, 1, 2, 4, 5, 7, 8], "question": [6, 9], "quick": [1, 5, 6], "quickli": 7, "r": [6, 13], "rais": 6, "ram": 0, "ram\u00edrez": 9, "rang": [2, 6, 7, 11, 14], "rank": [8, 10], "rate": [5, 7, 8, 10, 11], "ratio": [5, 7, 8, 10], "rbac": 0, "re": [0, 4, 6], "reach": 7, "read": [4, 7, 13], "readi": [5, 7], "readiness_": 11, "readm": 6, "real": [6, 7, 10, 12], "reason": 14, "rebuild": 13, "receiv": 6, "recent": 6, "recogn": 6, "recommend": [0, 1, 5, 6, 7, 8, 10, 11, 12], "record": [6, 7], "recov": [7, 11, 14], "recover": [7, 10, 11], "recoveri": [0, 2, 5, 7, 8, 10, 11], "recovery_": 11, "recur": 7, "redi": [0, 5, 6, 13], "redis_url": 13, "redistribut": 11, "redoc": 13, "reduct": [7, 10, 11], "refer": [5, 6, 7, 10, 14], "refin": 10, "reflect": 10, "refresh": [0, 5, 6, 8, 12], "regard": 7, "regist": [5, 6, 14], "registr": [5, 6], "regul": [7, 9], "regular": [0, 6, 10, 14], "regulatori": 4, "reject": [4, 5], "rel": [10, 11], "relat": [4, 6, 7], "relationship": [4, 5], "releas": [5, 6], "relev": [6, 7, 9, 14], "reliabl": [0, 5, 6], "reload": [0, 5, 6, 12, 13], "remain": [4, 7], "remind": 7, "remov": 14, "renaiss": [5, 6, 7, 8, 9, 10, 12], "renown": 8, "reorder": 5, "rep": [7, 11], "repeat": 7, "repetit": 7, "replac": 9, "replica": 4, "report": [0, 5], "repositori": [0, 4, 5, 6, 7, 8, 9, 12, 13], "repres": [1, 4, 7, 10, 11], "represent": [7, 8], "reproduc": [6, 13], "reproduct": 6, "request": [1, 4, 5, 7], "requir": [0, 1, 2, 4, 5, 7, 8, 11, 13, 14], "research": [5, 7, 9, 11, 14], "reserv": 7, "reset": [0, 13], "resist": 7, "resourc": [0, 4, 5, 7], "respect": 6, "respons": [0, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "rest": [0, 5, 7, 11], "restart": [5, 13], "restor": 5, "restrict": 9, "result": [1, 2, 7, 9, 14], "retent": [0, 7], "retract": [2, 14], "retriev": [2, 6], "return": [2, 4, 6], "review": [0, 5, 6, 8, 9, 10, 13, 14], "revis": 13, "revoc": 5, "revok": 5, "rich": 7, "right": 9, "rigor": 10, "rigour": 6, "rir": 7, "risk": [7, 10, 11], "roadmap": 6, "role": [0, 4, 5], "rollback": [0, 2, 4, 5, 8], "rotat": [0, 5, 8], "round": [5, 8], "rout": 4, "rp": [7, 8, 11, 14], "rpe": [5, 7, 11], "rule": [0, 4, 5, 6, 7, 8, 14], "run": [0, 5, 6, 8, 12, 13], "runnabl": 6, "runtim": 0, "saa": 7, "safe": 7, "safeti": [1, 2, 5, 14], "safety_not": [2, 4, 14], "same": [1, 14], "sanit": 5, "satisfact": 7, "save": 4, "scalabl": [0, 5, 7, 8, 12], "scale": [0, 4, 5, 7], "scan": [0, 5], "scenario": [0, 5, 6], "schedul": [0, 8, 11], "schema": [0, 4, 5, 7, 8, 13], "schoenfeld": 10, "scienc": [5, 9], "scientif": [5, 8, 11, 14], "scientist": 6, "score": [7, 11], "screenshot": 6, "script": [6, 7, 13], "sdk": 7, "search": [5, 6, 7, 8, 12], "search_exercis": 4, "sebasti\u00e1n": 9, "second": [1, 4], "secondari": [4, 8, 11], "secondary_muscle_group": [2, 4, 14], "secret": [0, 5, 13], "secret_kei": 13, "section": [0, 3, 6, 7, 8, 10, 11], "secur": [5, 6, 7, 8, 12, 13], "securepassword123": 6, "see": 8, "seed": 0, "seek": [6, 8], "segreg": 7, "select": [4, 5, 7, 8, 10, 11], "self": [4, 6], "sell": 9, "semant": 5, "senior": 6, "sensibl": 0, "sensit": 6, "sent": 7, "separ": [0, 4, 5, 7, 12], "sequenc": [1, 6], "serv": [0, 10], "server": [6, 12], "server_default": 4, "servic": [0, 4, 5, 6, 7, 8, 9, 12, 13], "session": [0, 4, 5, 6, 7, 11, 13], "set": [0, 1, 4, 5, 6, 7, 8, 10, 11, 14], "setup": [0, 1, 4, 5, 8, 13, 14], "setup_instruct": [2, 4, 14], "sever": [9, 10], "sfr": [7, 10], "sh": 13, "shall": 9, "share": [6, 7], "shared_preload_librari": 13, "shell": 13, "short": [1, 14], "should": [1, 6, 9, 14], "shoulder": [2, 7, 14], "show": [1, 6, 14], "showcas": 6, "signific": [9, 11], "significantli": 10, "similar": [9, 11], "simpl": 14, "singl": [4, 7], "size": [4, 7, 12], "skill": 11, "skip": [2, 14], "sla": 7, "sleep": 11, "slight": 14, "slightli": 14, "snippet": 6, "so": [6, 9], "socket": 7, "soft": [2, 4, 5, 8], "soft_delet": 4, "softwar": [0, 7, 8, 9], "solid": 7, "solut": [0, 6], "solv": 6, "some": 5, "sophist": [4, 10, 11], "sore": [7, 8, 10, 11], "sort": [1, 6, 13], "sort_ord": [1, 14], "sourc": [4, 5, 6, 7, 9, 13], "space": 0, "speak": 6, "specif": [0, 4, 6, 7, 8, 11, 13, 14], "specifi": 14, "spell": 6, "sport": 9, "spotter": [2, 14], "sql": [4, 5, 7, 8], "sqlalchemi": [0, 4, 5], "sqluserrepositori": 6, "squat": [7, 8], "ssl": 7, "stabl": 0, "stack": [0, 5, 12], "stage": [0, 2, 5, 6, 8], "standard": [5, 7], "start": [2, 4, 5, 7, 11], "state": [7, 10], "stateless": 4, "statist": [4, 5, 6, 8, 10], "statu": [0, 2, 4, 5, 6, 8, 13, 14], "status_cod": 6, "step": [5, 6, 12, 14], "stimulu": [5, 7, 8, 10], "stimulus_": 11, "storag": [0, 4, 5, 13], "store": 7, "str": [4, 6], "straight": [2, 14], "strateg": [4, 5, 7, 10, 11], "strategi": [5, 14], "strength": [5, 7, 8, 11], "stress": [7, 11], "stretch": 11, "string": [1, 2, 4], "structur": [5, 6, 7, 11], "stuart": 10, "studi": [6, 10], "studio": 13, "stufft": 9, "style": [5, 7, 14], "subject": [7, 9, 10], "sublicens": 9, "submiss": 2, "submit": 6, "subscript": 7, "subsect": 10, "subset": 7, "substanti": 9, "substitut": [7, 11], "success": [6, 13], "successfulli": 2, "sudo": 13, "suffici": [7, 11], "suggest": 9, "suit": [6, 13], "summari": 14, "suppli": 7, "support": [0, 2, 4, 5, 6, 7, 8, 10, 13, 14], "switch": 6, "sync": 7, "system": [0, 5, 6, 7, 8, 10, 11, 13], "systemat": [6, 7, 10, 11], "systemctl": 13, "t": [7, 14], "tabl": 5, "tailor": 8, "tailwindcss": 13, "target": [0, 5, 6, 11, 14], "tdd": 7, "team": [5, 7, 9, 10, 11, 14], "technic": [1, 5, 6], "techniqu": [1, 7, 14], "technologi": [0, 6, 12], "templat": 13, "tension": 14, "term": [6, 9], "test": [5, 7, 10, 12], "test_auth_endpoint": 6, "test_authentication_fails_with_invalid_password": 6, "test_calculate_mev_beginn": 6, "test_mev_calcul": 6, "test_mev_calculation_increases_with_training_experi": 6, "test_user_ent": 13, "test_user_profile_upd": 6, "test_user_registration_flow": 6, "test_workout_creation_requires_authenticated_us": 6, "testabl": 7, "testmevcalcul": 6, "text": 4, "than": 14, "thank": 6, "thei": 7, "theoret": 10, "thi": [0, 1, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "third": 7, "three": [8, 14], "threshold": [0, 7], "through": [2, 4, 5, 7, 8, 9, 10, 11, 14], "throughout": [5, 7, 14], "thumb": 4, "thumbnail": [1, 4, 14], "thumbnail_url": [1, 4, 14], "tiangolo": 9, "tier": 7, "time": [0, 5, 6, 7, 8, 9, 10, 11, 12], "timefram": [7, 11], "timeout": 0, "timestamp": [1, 4, 5, 13], "timezon": 4, "tip": 14, "titl": [1, 14], "tl": 7, "togeth": [6, 7], "token": [0, 1, 2, 4, 5, 6, 7, 8, 12, 14], "toler": 11, "too": [2, 14], "tool": [0, 5, 6, 7, 8], "topic": 6, "tort": 9, "total": [4, 14], "total_count": 2, "total_vers": 2, "touch": 14, "toward": 11, "trace": 6, "track": [0, 1, 4, 5, 7, 8, 10, 11, 12, 14], "trail": [2, 5, 14], "train": [5, 6, 8, 9, 10, 11, 12, 13, 14], "traine": 8, "trainer": 8, "training_experience_year": 6, "training_year": 6, "transfer": 7, "transit": 0, "translat": [6, 10], "transport": 7, "trend": [5, 7, 8, 11], "triag": 6, "tricep": [2, 14], "trigger": 11, "troubleshoot": [0, 5, 6, 8, 12], "true": [1, 2, 4, 6, 13, 14], "tudor": 10, "tuesdai": 6, "tune": 6, "tupl": 4, "tutori": 6, "two": 7, "txt": [6, 13], "type": [0, 2, 4, 5, 7, 11, 13, 14], "typic": [7, 11], "u": 13, "ubuntu": [0, 13], "ui": [4, 7], "under": [0, 1, 2, 9, 14], "undergo": 10, "underli": [7, 10], "understand": [7, 9, 10, 11, 14], "unfamiliar": 7, "unifi": 4, "uniqu": [4, 5, 7, 14], "unit": [0, 5, 6, 13], "univers": 7, "unrack": [2, 14], "up": [0, 5, 6, 8, 12, 13, 14], "upcom": 7, "updat": [0, 4, 5, 6, 7, 8, 10, 13], "update_exercis": 4, "updated_at": [1, 2, 4], "updated_bi": 4, "updated_exercis": 4, "upgrad": [0, 5, 6, 8, 12, 13], "upload": 4, "upon": 10, "uptim": [0, 7], "url": [3, 4, 5, 7, 9, 14], "us": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14], "usag": [6, 8, 9], "user": [0, 2, 4, 5, 6, 9, 10, 11, 12, 13, 14], "user_data": 6, "user_id": 6, "user_profil": 6, "user_registr": 6, "user_repositori": 6, "userfactori": 6, "usernotfounderror": 6, "userprofil": 6, "userrepositori": 6, "utc": 6, "utilis": 0, "uuid": [1, 2, 4, 5, 6, 7, 14], "uuid4": 4, "uvicorn": [6, 12, 13], "v": [5, 6, 11, 13], "v1": [0, 1, 2, 5, 6, 8, 9, 13, 14], "v2": [0, 5], "valid": [0, 1, 4, 5, 6, 7, 8, 13, 14], "valu": [4, 6, 14], "valueerror": 6, "van": 10, "vari": [9, 10], "variabl": [0, 5, 7, 10], "variat": 8, "variou": 14, "vector": [11, 12], "venv": [6, 13], "verif": 0, "verifi": [0, 5, 7, 13, 14], "verify_password": 6, "version": [0, 3, 5, 6, 7, 8, 9, 13], "version_not": [2, 4, 14], "via": [1, 2], "video": [1, 2, 4, 5, 6, 7, 8, 14], "video_url": [4, 14], "view": [1, 13], "viewpoint": 14, "vim": 13, "violat": [1, 5, 14], "virtual": [6, 7, 13], "visibl": [4, 14], "visit": [5, 8, 13], "visual": [5, 8, 13, 14], "visualis": 7, "vladimir": 10, "volum": [6, 7, 8, 10], "volume_": 11, "vpc": 7, "vscode": 13, "vulner": [0, 5, 6], "wai": 13, "want": 6, "warranti": 9, "wav": 1, "we": [6, 9], "web": [1, 6, 7], "webhook": 7, "webm": 1, "webp": 1, "week": [6, 7, 11], "weekli": [6, 8, 10, 11], "weight": [2, 7, 14], "welcom": [6, 12], "well": [1, 14], "what": [6, 14], "when": [1, 4, 6, 7, 10, 11, 13, 14], "where": [7, 11], "wherebi": 7, "whether": [1, 2, 9], "which": [7, 11], "while": [9, 10, 14], "whilst": [7, 10, 11], "who": 6, "whom": 9, "wide": [2, 14], "wider": 14, "width": [1, 14], "width_pixel": [1, 14], "window": [0, 6], "within": [7, 11], "without": [7, 9, 11, 13, 14], "work": [7, 14], "worker": 13, "workflow": [3, 5, 8], "workout": [5, 6, 7, 8, 10], "world": [6, 7, 10], "worldwid": 6, "would": 6, "wrist": 14, "write": 6, "wsl2": 0, "www": 9, "x": [13, 14], "xss": 4, "year": 11, "yet": 5, "yml": [12, 13], "you": [0, 6, 7, 9, 14], "your": [0, 1, 2, 6, 9, 10, 11, 13, 14], "your_usernam": 6, "yourself": 7, "zatsiorski": 10, "zsh": 13, "zshrc": 13}, "titles": ["\u2699\ufe0f Getting Started (Admin)", "Exercise Media API", "Exercise API", "API Reference", "Exercise System Architecture", "Changelog", "Contributing to Forge Protocol", "Glossary", "\ud83c\udfcb\ufe0f\u200d\u2642\ufe0f Forge Protocol Documentation", "License", "\ud83d\udd2c Protocols &amp; Science", "Renaissance Periodisation Principles", "Forge Protocol Documentation", "Installation Guide", "Exercise Management Guide"], "titleterms": {"0": 5, "01": 5, "06": 5, "1": 5, "14": 5, "2": 5, "2025": 5, "abbrevi": 7, "access": 14, "acronym": 7, "ad": 14, "add": 1, "addit": 8, "adjust": 11, "admin": [0, 8], "advanc": 14, "alemb": 9, "algorithm": [6, 7, 10, 11], "api": [1, 2, 3, 8, 14], "applic": 4, "approach": 10, "approv": [2, 4, 14], "architectur": [0, 4, 6, 8], "assess": 11, "assur": 0, "attribut": [6, 9], "audit": 4, "authent": [1, 2, 4], "author": 4, "base": [1, 2], "basic": 14, "bcrypt": 9, "best": [1, 14], "bug": 6, "busi": 7, "cach": 4, "calcul": 11, "categori": 6, "changelog": 5, "channel": 6, "code": 6, "common": 14, "commun": 6, "complianc": 0, "compon": 4, "comprehens": 14, "configur": [0, 13], "consider": 4, "contact": 9, "content": [1, 8, 12, 13, 14], "contribut": 6, "contributor": 6, "core": [2, 3, 4, 10, 11], "creat": [2, 14], "creation": 14, "current": 2, "data": [1, 4, 7, 9], "databas": [0, 4], "delet": [1, 2, 14], "deploy": 0, "design": 11, "develop": [6, 8, 13], "disclaim": 9, "docker": [9, 13], "document": [0, 6, 8, 10, 12], "domain": 4, "educ": 10, "endpoint": 2, "environ": 13, "error": 1, "exampl": 14, "exercis": [1, 2, 4, 14], "exercisemedia": 1, "fastapi": 9, "fatigu": 11, "featur": [6, 8, 12], "file": 1, "filter": 4, "forg": [6, 8, 12], "foundat": 10, "framework": 11, "get": [0, 1, 2, 6, 8, 10, 12, 14], "glossari": 7, "guid": [8, 13, 14], "guidelin": [1, 6, 14], "help": [0, 6, 14], "hour": 6, "id": 13, "implement": [10, 11], "indic": [8, 12], "individu": 11, "inform": 9, "infrastructur": 4, "instal": [0, 13], "integr": 7, "interfac": 7, "issu": [6, 14], "kei": [8, 10, 12], "landmark": 11, "layer": 4, "licens": 9, "limit": 1, "machin": 4, "manag": [0, 1, 2, 4, 14], "manual": 13, "mathemat": 11, "media": [1, 4, 14], "methodologi": 10, "mit": 9, "model": [1, 11], "modul": 3, "monitor": [0, 11], "next": 13, "nix": 13, "object": 8, "observ": 0, "offic": 6, "optim": [1, 4], "option": 0, "organ": 1, "overload": 11, "overview": [0, 1, 2, 4, 10, 11, 12], "parti": 9, "perform": [1, 4], "periodis": 11, "postgresql": 9, "practic": [1, 11, 14], "prerequisit": [0, 14], "primari": 1, "principl": [10, 11], "privaci": 9, "process": [11, 14], "programm": 11, "progress": 11, "project": 6, "protect": 4, "protocol": [6, 8, 10, 12], "pydant": 9, "pyjwt": 9, "pytest": 9, "qualiti": [0, 14], "quick": [0, 8, 12, 13], "ratio": 11, "recognit": 6, "recommend": 13, "recoveri": 14, "refer": [3, 8, 9], "reject": [2, 14], "renaiss": 11, "reorder": 1, "report": 6, "request": 6, "requir": 6, "research": [6, 10], "resourc": [8, 10], "respons": 1, "restor": [2, 14], "rule": 1, "scalabl": 4, "schema": 1, "scienc": [8, 10], "scientif": [6, 7, 9, 10], "search": [2, 4, 14], "secur": [0, 4], "server": 13, "set": 2, "setup": 6, "sfr": 11, "size": 1, "soft": 14, "specif": 2, "sqlalchemi": 9, "standard": 6, "start": [0, 6, 8, 10, 12, 13, 14], "state": 4, "statist": 14, "step": 13, "stimulu": 11, "strategi": [0, 4, 11], "structur": [0, 8, 10], "style": 6, "support": 1, "system": 4, "tabl": [8, 12, 13], "target": 8, "technic": 7, "term": 7, "test": [0, 6, 13], "third": 9, "trail": 4, "train": 7, "troubleshoot": [13, 14], "type": [1, 6], "unreleas": 5, "updat": [1, 2, 9, 14], "url": [1, 2], "user": [7, 8], "uvicorn": 9, "valid": 10, "version": [2, 4, 14], "view": 14, "vision": 6, "volum": 11, "welcom": 8, "workflow": [2, 4, 6, 14]}})