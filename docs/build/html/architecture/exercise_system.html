

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Exercise System Architecture &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="API Reference" href="../api/modules.html" />
    <link rel="prev" title="Exercise Management Guide" href="../user/exercise_guide.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Exercise System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#domain-layer">Domain Layer</a></li>
<li class="toctree-l3"><a class="reference internal" href="#application-layer">Application Layer</a></li>
<li class="toctree-l3"><a class="reference internal" href="#infrastructure-layer">Infrastructure Layer</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#versioning-system">Versioning System</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#architecture">Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#approval-workflow">Approval Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#state-machine">State Machine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#media-management">Media Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id1">Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#search-and-filtering">Search and Filtering</a></li>
<li class="toctree-l2"><a class="reference internal" href="#performance-considerations">Performance Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#database-optimization">Database Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#caching-strategy">Caching Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scalability">Scalability</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-considerations">Security Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#authentication-authorization">Authentication &amp; Authorization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#data-protection">Data Protection</a></li>
<li class="toctree-l3"><a class="reference internal" href="#audit-trail">Audit Trail</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Exercise System Architecture</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/architecture/exercise_system.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="exercise-system-architecture">
<h1>Exercise System Architecture<a class="headerlink" href="#exercise-system-architecture" title="Link to this heading"></a></h1>
<p>The Exercise System is a core component of the Forge Protocol platform, implementing a comprehensive exercise management system with versioning, approval workflows, and media management capabilities.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Exercise System follows Clean Architecture principles with clear separation of concerns across multiple layers:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   FastAPI       │  │   Pydantic      │  │   OpenAPI   │ │
│  │   Routes        │  │   Schemas       │  │   Docs      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Use Cases     │  │    Services     │  │ Exceptions  │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Entities      │  │  Repositories   │  │   Value     │ │
│  │                 │  │  (Interfaces)   │  │   Objects   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Database      │  │   Repository    │  │   External  │ │
│  │   Models        │  │ Implementations │  │   Services  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
</pre></div>
</div>
</section>
<section id="core-components">
<h2>Core Components<a class="headerlink" href="#core-components" title="Link to this heading"></a></h2>
<section id="domain-layer">
<h3>Domain Layer<a class="headerlink" href="#domain-layer" title="Link to this heading"></a></h3>
<p><strong>Exercise Entity</strong></p>
<p>The Exercise entity is the core domain model representing a single exercise with comprehensive metadata:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">Exercise</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
    <span class="c1"># Identity and versioning</span>
    <span class="nb">id</span><span class="p">:</span> <span class="n">UUID</span>
    <span class="n">exercise_uuid</span><span class="p">:</span> <span class="n">UUID</span>
    <span class="n">version</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">is_current_version</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">parent_version_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span>

    <span class="c1"># Core exercise data</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">primary_muscle_group</span><span class="p">:</span> <span class="n">MuscleGroup</span>
    <span class="n">secondary_muscle_groups</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">MuscleGroup</span><span class="p">]]</span>
    <span class="n">movement_pattern</span><span class="p">:</span> <span class="n">MovementPattern</span>
    <span class="n">equipment_required</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">Equipment</span><span class="p">]]</span>
    <span class="n">difficulty_level</span><span class="p">:</span> <span class="n">DifficultyLevel</span>

    <span class="c1"># Instructions and media</span>
    <span class="n">form_cues</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span>
    <span class="n">setup_instructions</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">execution_steps</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span>
    <span class="n">common_mistakes</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span>
    <span class="n">safety_notes</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">video_url</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">thumbnail_url</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>

    <span class="c1"># Status and workflow</span>
    <span class="n">is_active</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">is_approved</span><span class="p">:</span> <span class="nb">bool</span>
    <span class="n">approval_status</span><span class="p">:</span> <span class="n">ApprovalStatus</span>

    <span class="c1"># Audit trail</span>
    <span class="n">created_by</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span>
    <span class="n">updated_by</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span>
    <span class="n">approved_by</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span>
    <span class="n">created_at</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">updated_at</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">deleted_at</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span>
    <span class="n">approved_at</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span>

    <span class="c1"># Versioning metadata</span>
    <span class="n">version_notes</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">change_reason</span><span class="p">:</span> <span class="n">ChangeReason</span>

    <span class="c1"># Related entities</span>
    <span class="n">media</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">ExerciseMedia</span><span class="p">]]</span>
</pre></div>
</div>
<p><strong>Key Domain Rules</strong></p>
<ol class="arabic simple">
<li><p><strong>Versioning</strong>: Each exercise has a unique UUID that remains constant across versions</p></li>
<li><p><strong>Soft Delete</strong>: Exercises are never permanently deleted, only marked as deleted</p></li>
<li><p><strong>Approval Workflow</strong>: New versions require re-approval</p></li>
<li><p><strong>Media Management</strong>: Each exercise can have multiple media items with type-specific primary designation</p></li>
</ol>
<p><strong>Repository Interface</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseRepository</span><span class="p">(</span><span class="n">ABC</span><span class="p">):</span>
    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise</span><span class="p">:</span> <span class="n">Exercise</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create a new exercise.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_by_id</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Exercise</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get exercise by ID.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_by_uuid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_uuid</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">version</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Exercise</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get exercise by UUID and optionally version.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">filters</span><span class="p">:</span> <span class="n">ExerciseSearchFilters</span><span class="p">,</span> <span class="n">limit</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">offset</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Exercise</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Search exercises with filters.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise</span><span class="p">:</span> <span class="n">Exercise</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Update an exercise.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">soft_delete</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">deleted_by</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UUID</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Soft delete an exercise.&quot;&quot;&quot;</span>

    <span class="nd">@abstractmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_version</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_uuid</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">updated_exercise</span><span class="p">:</span> <span class="n">Exercise</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create a new version of an existing exercise.&quot;&quot;&quot;</span>
</pre></div>
</div>
</section>
<section id="application-layer">
<h3>Application Layer<a class="headerlink" href="#application-layer" title="Link to this heading"></a></h3>
<p><strong>Use Cases</strong></p>
<p>The application layer implements business logic through use cases:</p>
<ol class="arabic simple">
<li><p><strong>ExerciseUseCases</strong>: Core exercise management operations</p></li>
<li><p><strong>ExerciseMediaUseCases</strong>: Media management operations</p></li>
</ol>
<p><strong>Key Use Cases</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseUseCases</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_exercise</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request</span><span class="p">:</span> <span class="n">ExerciseCreateRequest</span><span class="p">,</span> <span class="n">created_by</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create a new exercise with validation.&quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_exercise</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">request</span><span class="p">:</span> <span class="n">ExerciseUpdateRequest</span><span class="p">,</span>
                            <span class="n">updated_by</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">create_new_version</span><span class="p">:</span> <span class="nb">bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Update exercise, optionally creating new version.&quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">approve_exercise</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">approved_by</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">notes</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Approve an exercise for publication.&quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">search_exercises</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">filters</span><span class="p">:</span> <span class="n">ExerciseSearchFilters</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">Exercise</span><span class="p">],</span> <span class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Search exercises with comprehensive filtering.&quot;&quot;&quot;</span>
</pre></div>
</div>
<p><strong>Service Layer</strong></p>
<p>The ExerciseService orchestrates use cases and provides a unified interface:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseService</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_repository</span><span class="p">:</span> <span class="n">ExerciseRepository</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">exercise_repository</span> <span class="o">=</span> <span class="n">exercise_repository</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">exercise_use_cases</span> <span class="o">=</span> <span class="n">ExerciseUseCases</span><span class="p">(</span><span class="n">exercise_repository</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">media_use_cases</span> <span class="o">=</span> <span class="n">ExerciseMediaUseCases</span><span class="p">(</span><span class="n">exercise_repository</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_exercise</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request</span><span class="p">:</span> <span class="n">ExerciseCreateRequest</span><span class="p">,</span> <span class="n">created_by</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create exercise through use cases.&quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_exercise_statistics</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_id</span><span class="p">:</span> <span class="n">UUID</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">dict</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get comprehensive exercise statistics.&quot;&quot;&quot;</span>
</pre></div>
</div>
</section>
<section id="infrastructure-layer">
<h3>Infrastructure Layer<a class="headerlink" href="#infrastructure-layer" title="Link to this heading"></a></h3>
<p><strong>Database Models</strong></p>
<p>SQLAlchemy models implement the persistence layer:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseModel</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="n">__tablename__</span> <span class="o">=</span> <span class="s2">&quot;exercises&quot;</span>

    <span class="c1"># Primary key and versioning</span>
    <span class="nb">id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">PostgresUUID</span><span class="p">(</span><span class="n">as_uuid</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">exercise_uuid</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">UUID</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">PostgresUUID</span><span class="p">(</span><span class="n">as_uuid</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">index</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">version</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">is_current_version</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Boolean</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">index</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Core data</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">255</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">primary_muscle_group</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Enum</span><span class="p">(</span><span class="o">...</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">index</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Audit fields</span>
    <span class="n">created_at</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">server_default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    <span class="n">updated_at</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">server_default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    <span class="n">deleted_at</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Repository Implementation</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseRepositoryImpl</span><span class="p">(</span><span class="n">ExerciseRepository</span><span class="p">):</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">session</span><span class="p">:</span> <span class="n">AsyncSession</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session</span> <span class="o">=</span> <span class="n">session</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise</span><span class="p">:</span> <span class="n">Exercise</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create exercise with audit logging.&quot;&quot;&quot;</span>
        <span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_entity_to_model</span><span class="p">(</span><span class="n">exercise</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">model</span><span class="p">)</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">session</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>

        <span class="c1"># Log audit trail</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">log_audit</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">id</span><span class="p">,</span> <span class="n">model</span><span class="o">.</span><span class="n">exercise_uuid</span><span class="p">,</span> <span class="n">AuditAction</span><span class="o">.</span><span class="n">CREATED</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_model_to_entity</span><span class="p">(</span><span class="n">model</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="versioning-system">
<h2>Versioning System<a class="headerlink" href="#versioning-system" title="Link to this heading"></a></h2>
<section id="architecture">
<h3>Architecture<a class="headerlink" href="#architecture" title="Link to this heading"></a></h3>
<p>The versioning system implements a sophisticated approach to exercise evolution:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Exercise UUID: 123e4567-e89b-12d3-a456-************

Version 1 (ID: abc123...)  ←─ parent_version_id: null
│                             is_current_version: false
│
├─ Version 2 (ID: def456...)  ←─ parent_version_id: abc123...
│                                is_current_version: true
│
└─ Version 3 (ID: ghi789...)  ←─ parent_version_id: def456...
                                 is_current_version: false
</pre></div>
</div>
<p><strong>Key Features</strong></p>
<ol class="arabic simple">
<li><p><strong>Immutable Versions</strong>: Once created, versions are never modified</p></li>
<li><p><strong>Current Version Tracking</strong>: Only one version per exercise UUID is current</p></li>
<li><p><strong>Parent-Child Relationships</strong>: Each version (except first) has a parent</p></li>
<li><p><strong>Rollback Capability</strong>: Any version can be set as current</p></li>
<li><p><strong>Audit Trail</strong>: Complete history of all changes</p></li>
</ol>
<p><strong>Version Creation Process</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_version</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">exercise_uuid</span><span class="p">:</span> <span class="n">UUID</span><span class="p">,</span> <span class="n">updated_exercise</span><span class="p">:</span> <span class="n">Exercise</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Exercise</span><span class="p">:</span>
    <span class="c1"># 1. Get current version</span>
    <span class="n">current</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_current_version</span><span class="p">(</span><span class="n">exercise_uuid</span><span class="p">)</span>

    <span class="c1"># 2. Mark current version as not current</span>
    <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_mark_not_current</span><span class="p">(</span><span class="n">exercise_uuid</span><span class="p">)</span>

    <span class="c1"># 3. Create new version</span>
    <span class="n">new_version</span> <span class="o">=</span> <span class="n">Exercise</span><span class="p">(</span>
        <span class="nb">id</span><span class="o">=</span><span class="n">uuid4</span><span class="p">(),</span>
        <span class="n">exercise_uuid</span><span class="o">=</span><span class="n">exercise_uuid</span><span class="p">,</span>
        <span class="n">version</span><span class="o">=</span><span class="n">current</span><span class="o">.</span><span class="n">version</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span>
        <span class="n">is_current_version</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">parent_version_id</span><span class="o">=</span><span class="n">current</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
        <span class="c1"># ... updated fields</span>
    <span class="p">)</span>

    <span class="c1"># 4. Save and return</span>
    <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">new_version</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="approval-workflow">
<h2>Approval Workflow<a class="headerlink" href="#approval-workflow" title="Link to this heading"></a></h2>
<section id="state-machine">
<h3>State Machine<a class="headerlink" href="#state-machine" title="Link to this heading"></a></h3>
<p>The approval workflow implements a state machine:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>┌─────────────┐    approve()    ┌─────────────┐
│   PENDING   │ ──────────────→ │  APPROVED   │
│             │                 │             │
└─────────────┘                 └─────────────┘
       │                               │
       │ reject()                      │ update()
       ↓                               ↓
┌─────────────┐                ┌─────────────┐
│  REJECTED   │                │   PENDING   │
│             │                │ (new version)│
└─────────────┘                └─────────────┘
</pre></div>
</div>
<p><strong>Business Rules</strong></p>
<ol class="arabic simple">
<li><p><strong>New Exercises</strong>: Start in PENDING status</p></li>
<li><p><strong>Updates</strong>: Create new version in PENDING status</p></li>
<li><p><strong>Approval Required</strong>: Only APPROVED exercises are publicly visible</p></li>
<li><p><strong>Re-approval</strong>: Updates require re-approval even for approved exercises</p></li>
</ol>
</section>
</section>
<section id="media-management">
<h2>Media Management<a class="headerlink" href="#media-management" title="Link to this heading"></a></h2>
<section id="id1">
<h3>Architecture<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<p>Media management supports multiple file types with metadata:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Exercise
├── Video Media (Primary)
│   ├── URL: https://cdn.example.com/video.mp4
│   ├── Thumbnail: https://cdn.example.com/thumb.jpg
│   ├── Duration: 120 seconds
│   └── Dimensions: 1920x1080
│
├── Image Media (Primary)
│   ├── URL: https://cdn.example.com/setup.jpg
│   ├── Dimensions: 800x600
│   └── File Size: 245KB
│
└── Additional Media
    ├── Secondary Video
    ├── Multiple Images
    └── Audio Instructions
</pre></div>
</div>
<p><strong>Primary Media Rules</strong></p>
<ul class="simple">
<li><p>One primary media item per type (video, image, gif, audio)</p></li>
<li><p>Primary media is featured prominently in UI</p></li>
<li><p>Attempting to set duplicate primary media returns 409 Conflict</p></li>
</ul>
</section>
</section>
<section id="search-and-filtering">
<h2>Search and Filtering<a class="headerlink" href="#search-and-filtering" title="Link to this heading"></a></h2>
<p>The search system provides comprehensive filtering capabilities:</p>
<p><strong>Supported Filters</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ExerciseSearchFilters</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>                          <span class="c1"># Text search</span>
    <span class="n">primary_muscle_group</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">MuscleGroup</span><span class="p">]</span>  <span class="c1"># Exact match</span>
    <span class="n">secondary_muscle_groups</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">MuscleGroup</span><span class="p">]]</span>  <span class="c1"># Any match</span>
    <span class="n">movement_pattern</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">MovementPattern</span><span class="p">]</span>  <span class="c1"># Exact match</span>
    <span class="n">equipment_required</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">Equipment</span><span class="p">]]</span>  <span class="c1"># Any match</span>
    <span class="n">difficulty_level</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">DifficultyLevel</span><span class="p">]</span>  <span class="c1"># Exact match</span>
    <span class="n">is_active</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span>                    <span class="c1"># Status filter</span>
    <span class="n">is_approved</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span>                  <span class="c1"># Approval filter</span>
    <span class="n">current_version_only</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span>            <span class="c1"># Version filter</span>
    <span class="n">include_deleted</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>                <span class="c1"># Soft delete filter</span>
</pre></div>
</div>
<p><strong>Query Optimization</strong></p>
<ol class="arabic simple">
<li><p><strong>Database Indexes</strong>: Strategic indexes on commonly filtered fields</p></li>
<li><p><strong>Pagination</strong>: Limit/offset pagination with total count</p></li>
<li><p><strong>Selective Loading</strong>: Load related data only when needed</p></li>
<li><p><strong>Caching</strong>: Repository-level caching for frequently accessed data</p></li>
</ol>
</section>
<section id="performance-considerations">
<h2>Performance Considerations<a class="headerlink" href="#performance-considerations" title="Link to this heading"></a></h2>
<section id="database-optimization">
<h3>Database Optimization<a class="headerlink" href="#database-optimization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Indexes</strong>: Comprehensive indexing strategy for search performance</p></li>
<li><p><strong>Partitioning</strong>: Consider partitioning by exercise_uuid for large datasets</p></li>
<li><p><strong>Archival</strong>: Archive old versions to maintain performance</p></li>
<li><p><strong>Connection Pooling</strong>: Async connection pooling for scalability</p></li>
</ol>
</section>
<section id="caching-strategy">
<h3>Caching Strategy<a class="headerlink" href="#caching-strategy" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Application Cache</strong>: Cache frequently accessed exercises</p></li>
<li><p><strong>CDN</strong>: Use CDN for media delivery</p></li>
<li><p><strong>Database Query Cache</strong>: Cache expensive search queries</p></li>
<li><p><strong>Version Cache</strong>: Cache current version lookups</p></li>
</ol>
</section>
<section id="scalability">
<h3>Scalability<a class="headerlink" href="#scalability" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Horizontal Scaling</strong>: Stateless design supports horizontal scaling</p></li>
<li><p><strong>Read Replicas</strong>: Use read replicas for search operations</p></li>
<li><p><strong>Event Sourcing</strong>: Consider event sourcing for audit trail</p></li>
<li><p><strong>Microservices</strong>: Exercise system can be extracted as microservice</p></li>
</ol>
</section>
</section>
<section id="security-considerations">
<h2>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading"></a></h2>
<section id="authentication-authorization">
<h3>Authentication &amp; Authorization<a class="headerlink" href="#authentication-authorization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>JWT Authentication</strong>: All endpoints require valid JWT tokens</p></li>
<li><p><strong>Role-Based Access</strong>: Different permissions for create/approve/admin</p></li>
<li><p><strong>Resource Ownership</strong>: Users can only modify their own exercises</p></li>
<li><p><strong>Approval Permissions</strong>: Only authorized users can approve exercises</p></li>
</ol>
</section>
<section id="data-protection">
<h3>Data Protection<a class="headerlink" href="#data-protection" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Input Validation</strong>: Comprehensive validation at all layers</p></li>
<li><p><strong>SQL Injection Prevention</strong>: Parameterized queries and ORM</p></li>
<li><p><strong>XSS Prevention</strong>: Output encoding and CSP headers</p></li>
<li><p><strong>File Upload Security</strong>: Validate media files and use secure storage</p></li>
</ol>
</section>
<section id="audit-trail">
<h3>Audit Trail<a class="headerlink" href="#audit-trail" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Complete History</strong>: All changes logged with user and timestamp</p></li>
<li><p><strong>Immutable Logs</strong>: Audit logs cannot be modified</p></li>
<li><p><strong>Compliance</strong>: Supports regulatory compliance requirements</p></li>
<li><p><strong>Forensics</strong>: Detailed trail for security investigations</p></li>
</ol>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../user/exercise_guide.html" class="btn btn-neutral float-left" title="Exercise Management Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../api/modules.html" class="btn btn-neutral float-right" title="API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>