

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>⚙️ Getting Started (Admin) &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Exercise Management Guide" href="../user/exercise_guide.html" />
    <link rel="prev" title="Renaissance Periodisation Principles" href="../protocols/rp-principles.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">⚙️ Getting Started (Admin)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#prerequisites">🎯 Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="#quick-start">🚀 Quick Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="#architecture-overview">🏗️ Architecture Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="#documentation-structure">📚 Documentation Structure</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#installation-options">🔧 Installation Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="#configuration-management">⚙️ Configuration Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#database-management">🗄️ Database Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#testing-and-quality-assurance">🧪 Testing and Quality Assurance</a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-and-observability">📊 Monitoring and Observability</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-and-compliance">🔒 Security and Compliance</a></li>
<li class="toctree-l3"><a class="reference internal" href="#deployment-strategies">🚀 Deployment Strategies</a></li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">⚙️ Getting Started (Admin)</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/admin/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="getting-started-admin">
<h1>⚙️ Getting Started (Admin)<a class="headerlink" href="#getting-started-admin" title="Link to this heading"></a></h1>
<p>This section provides comprehensive guidance for administrators setting up, configuring, and maintaining Forge Protocol.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Forge Protocol is designed with modern DevOps practices in mind, featuring containerised deployment, automated testing, and comprehensive monitoring capabilities. The platform follows Clean Architecture principles, making it maintainable, scalable, and easy to extend.</p>
<section id="prerequisites">
<h3>🎯 Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Before beginning the installation process, ensure you have the following prerequisites:</p>
<dl class="simple">
<dt><strong>System Requirements</strong></dt><dd><ul class="simple">
<li><p><strong>Operating System</strong>: Linux (Ubuntu 20.04+ recommended), macOS 10.15+, or Windows 10+ with WSL2</p></li>
<li><p><strong>Memory</strong>: Minimum 4GB RAM (8GB+ recommended for production)</p></li>
<li><p><strong>Storage</strong>: 20GB+ available disk space</p></li>
<li><p><strong>Network</strong>: Stable internet connection for package downloads</p></li>
</ul>
</dd>
<dt><strong>Required Software</strong></dt><dd><ul class="simple">
<li><p><strong>Docker</strong>: Version 20.10+ with Docker Compose v2</p></li>
<li><p><strong>Git</strong>: Version 2.30+ for repository management</p></li>
<li><p><strong>Python</strong>: 3.11+ (for local development)</p></li>
<li><p><strong>PostgreSQL</strong>: 15+ (if not using Docker)</p></li>
</ul>
</dd>
</dl>
</section>
<section id="quick-start">
<h3>🚀 Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h3>
<p>Get Forge Protocol running in under 5 minutes:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol

<span class="c1"># Start all services</span>
<span class="nb">cd</span><span class="w"> </span>docker
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Run database migrations</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>app<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Verify installation</span>
curl<span class="w"> </span>http://localhost:8000/api/v1/health

<span class="c1"># Access API documentation</span>
open<span class="w"> </span>http://localhost:8000/docs
</pre></div>
</div>
</section>
<section id="architecture-overview">
<h3>🏗️ Architecture Overview<a class="headerlink" href="#architecture-overview" title="Link to this heading"></a></h3>
<p>Forge Protocol follows Clean Architecture principles with clear separation of concerns:</p>
<dl class="simple">
<dt><strong>Application Layers</strong></dt><dd><ul class="simple">
<li><p><strong>Domain Layer</strong>: Business logic, entities, and domain services</p></li>
<li><p><strong>Application Layer</strong>: Use cases and application-specific business rules</p></li>
<li><p><strong>Infrastructure Layer</strong>: Database, external services, and framework implementations</p></li>
<li><p><strong>Presentation Layer</strong>: API endpoints, schemas, and HTTP concerns</p></li>
</ul>
</dd>
<dt><strong>Technology Stack</strong></dt><dd><ul class="simple">
<li><p><strong>Backend Framework</strong>: FastAPI with Python 3.11+</p></li>
<li><p><strong>Database</strong>: PostgreSQL 15+ with SQLAlchemy ORM</p></li>
<li><p><strong>Authentication</strong>: JWT with refresh token rotation</p></li>
<li><p><strong>Validation</strong>: Pydantic v2 with comprehensive type hints</p></li>
<li><p><strong>Migrations</strong>: Alembic for database schema management</p></li>
<li><p><strong>Testing</strong>: pytest with coverage reporting and BDD support</p></li>
<li><p><strong>Containerisation</strong>: Docker with multi-stage builds</p></li>
<li><p><strong>Code Quality</strong>: Black, isort, flake8, mypy integration</p></li>
</ul>
</dd>
<dt><strong>Service Architecture</strong></dt><dd><ul class="simple">
<li><p><strong>API Service</strong>: FastAPI application serving REST endpoints</p></li>
<li><p><strong>Database Service</strong>: PostgreSQL with automated backups</p></li>
<li><p><strong>Cache Service</strong>: Redis for session management and caching</p></li>
<li><p><strong>Admin Service</strong>: pgAdmin for database administration</p></li>
<li><p><strong>Monitoring</strong>: Health checks and metrics collection</p></li>
</ul>
</dd>
</dl>
</section>
<section id="documentation-structure">
<h3>📚 Documentation Structure<a class="headerlink" href="#documentation-structure" title="Link to this heading"></a></h3>
<p>This admin section is organised into the following guides:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="installation-options">
<h3>🔧 Installation Options<a class="headerlink" href="#installation-options" title="Link to this heading"></a></h3>
<p>Choose the installation method that best fits your needs:</p>
<dl class="simple">
<dt><strong>Docker Deployment (Recommended)</strong></dt><dd><ul class="simple">
<li><p>Fastest setup with all dependencies included</p></li>
<li><p>Consistent environment across development and production</p></li>
<li><p>Automatic service orchestration and networking</p></li>
<li><p>Built-in database and cache services</p></li>
</ul>
</dd>
<dt><strong>Local Development Setup</strong></dt><dd><ul class="simple">
<li><p>Direct Python environment for development</p></li>
<li><p>Hot reloading and debugging capabilities</p></li>
<li><p>Custom database configuration options</p></li>
<li><p>Integration with local development tools</p></li>
</ul>
</dd>
<dt><strong>Production Deployment</strong></dt><dd><ul class="simple">
<li><p>Optimised for performance and security</p></li>
<li><p>Horizontal scaling capabilities</p></li>
<li><p>Load balancing and high availability</p></li>
<li><p>Comprehensive monitoring and logging</p></li>
</ul>
</dd>
</dl>
</section>
<section id="configuration-management">
<h3>⚙️ Configuration Management<a class="headerlink" href="#configuration-management" title="Link to this heading"></a></h3>
<p>Forge Protocol uses environment-based configuration with sensible defaults:</p>
<dl class="simple">
<dt><strong>Environment Variables</strong></dt><dd><ul class="simple">
<li><p>Database connection settings</p></li>
<li><p>Authentication and security parameters</p></li>
<li><p>Feature flags and operational settings</p></li>
<li><p>External service integrations</p></li>
</ul>
</dd>
<dt><strong>Configuration Files</strong></dt><dd><ul class="simple">
<li><p>Pydantic-based settings with validation</p></li>
<li><p>Environment-specific overrides</p></li>
<li><p>Secrets management integration</p></li>
<li><p>Runtime configuration updates</p></li>
</ul>
</dd>
<dt><strong>Security Considerations</strong></dt><dd><ul class="simple">
<li><p>Secure secret storage and rotation</p></li>
<li><p>Environment isolation and access controls</p></li>
<li><p>Audit logging and compliance features</p></li>
<li><p>Regular security updates and patches</p></li>
</ul>
</dd>
</dl>
</section>
<section id="database-management">
<h3>🗄️ Database Management<a class="headerlink" href="#database-management" title="Link to this heading"></a></h3>
<p>Comprehensive database administration capabilities:</p>
<dl class="simple">
<dt><strong>Schema Management</strong></dt><dd><ul class="simple">
<li><p>Automated migrations with Alembic</p></li>
<li><p>Version control for database changes</p></li>
<li><p>Rollback and recovery procedures</p></li>
<li><p>Schema validation and testing</p></li>
</ul>
</dd>
<dt><strong>Performance Optimisation</strong></dt><dd><ul class="simple">
<li><p>Query performance monitoring</p></li>
<li><p>Index optimisation strategies</p></li>
<li><p>Connection pooling configuration</p></li>
<li><p>Caching layer implementation</p></li>
</ul>
</dd>
<dt><strong>Backup and Recovery</strong></dt><dd><ul class="simple">
<li><p>Automated backup scheduling</p></li>
<li><p>Point-in-time recovery capabilities</p></li>
<li><p>Disaster recovery procedures</p></li>
<li><p>Data integrity verification</p></li>
</ul>
</dd>
</dl>
</section>
<section id="testing-and-quality-assurance">
<h3>🧪 Testing and Quality Assurance<a class="headerlink" href="#testing-and-quality-assurance" title="Link to this heading"></a></h3>
<p>Comprehensive testing framework ensuring code quality:</p>
<dl class="simple">
<dt><strong>Test Categories</strong></dt><dd><ul class="simple">
<li><p><strong>Unit Tests</strong>: Domain logic and algorithm validation</p></li>
<li><p><strong>Integration Tests</strong>: Database and API endpoint testing</p></li>
<li><p><strong>Behavioural Tests</strong>: User scenario validation with Gherkin</p></li>
<li><p><strong>Performance Tests</strong>: Load testing and benchmarking</p></li>
<li><p><strong>Security Tests</strong>: Vulnerability scanning and penetration testing</p></li>
</ul>
</dd>
<dt><strong>Quality Metrics</strong></dt><dd><ul class="simple">
<li><p><strong>Code Coverage</strong>: Minimum 90% requirement</p></li>
<li><p><strong>Performance</strong>: &lt;200ms API response time (95th percentile)</p></li>
<li><p><strong>Reliability</strong>: 99.9% uptime target</p></li>
<li><p><strong>Security</strong>: Regular vulnerability assessments</p></li>
</ul>
</dd>
<dt><strong>Continuous Integration</strong></dt><dd><ul class="simple">
<li><p>Automated testing on every commit</p></li>
<li><p>Code quality checks and linting</p></li>
<li><p>Security scanning and dependency updates</p></li>
<li><p>Automated deployment to staging environments</p></li>
</ul>
</dd>
</dl>
</section>
<section id="monitoring-and-observability">
<h3>📊 Monitoring and Observability<a class="headerlink" href="#monitoring-and-observability" title="Link to this heading"></a></h3>
<p>Comprehensive monitoring for operational excellence:</p>
<dl class="simple">
<dt><strong>Health Monitoring</strong></dt><dd><ul class="simple">
<li><p>Application health checks and status endpoints</p></li>
<li><p>Database connectivity and performance monitoring</p></li>
<li><p>External service dependency tracking</p></li>
<li><p>Resource utilisation and capacity planning</p></li>
</ul>
</dd>
<dt><strong>Logging and Metrics</strong></dt><dd><ul class="simple">
<li><p>Structured logging with correlation IDs</p></li>
<li><p>Performance metrics and alerting</p></li>
<li><p>Error tracking and notification</p></li>
<li><p>User activity and audit logging</p></li>
</ul>
</dd>
<dt><strong>Alerting and Notifications</strong></dt><dd><ul class="simple">
<li><p>Configurable alert thresholds</p></li>
<li><p>Multiple notification channels</p></li>
<li><p>Escalation procedures and on-call rotation</p></li>
<li><p>Incident response and post-mortem processes</p></li>
</ul>
</dd>
</dl>
</section>
<section id="security-and-compliance">
<h3>🔒 Security and Compliance<a class="headerlink" href="#security-and-compliance" title="Link to this heading"></a></h3>
<p>Enterprise-grade security features:</p>
<dl class="simple">
<dt><strong>Authentication and Authorisation</strong></dt><dd><ul class="simple">
<li><p>JWT-based authentication with refresh tokens</p></li>
<li><p>Role-based access control (RBAC)</p></li>
<li><p>Multi-factor authentication support</p></li>
<li><p>Session management and timeout policies</p></li>
</ul>
</dd>
<dt><strong>Data Protection</strong></dt><dd><ul class="simple">
<li><p>Encryption at rest and in transit</p></li>
<li><p>Personal data anonymisation</p></li>
<li><p>GDPR compliance features</p></li>
<li><p>Data retention and deletion policies</p></li>
</ul>
</dd>
<dt><strong>Security Monitoring</strong></dt><dd><ul class="simple">
<li><p>Intrusion detection and prevention</p></li>
<li><p>Vulnerability scanning and patching</p></li>
<li><p>Security audit logging</p></li>
<li><p>Compliance reporting and certification</p></li>
</ul>
</dd>
</dl>
</section>
<section id="deployment-strategies">
<h3>🚀 Deployment Strategies<a class="headerlink" href="#deployment-strategies" title="Link to this heading"></a></h3>
<p>Flexible deployment options for different environments:</p>
<dl class="simple">
<dt><strong>Development Environment</strong></dt><dd><ul class="simple">
<li><p>Local Docker Compose setup</p></li>
<li><p>Hot reloading and debugging</p></li>
<li><p>Test data seeding and reset</p></li>
<li><p>Development tool integration</p></li>
</ul>
</dd>
<dt><strong>Staging Environment</strong></dt><dd><ul class="simple">
<li><p>Production-like configuration</p></li>
<li><p>Automated deployment from main branch</p></li>
<li><p>Integration testing and validation</p></li>
<li><p>Performance testing and benchmarking</p></li>
</ul>
</dd>
<dt><strong>Production Environment</strong></dt><dd><ul class="simple">
<li><p>High availability and load balancing</p></li>
<li><p>Automated scaling and resource management</p></li>
<li><p>Blue-green deployment strategies</p></li>
<li><p>Disaster recovery and backup procedures</p></li>
</ul>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation assumes familiarity with basic system administration concepts.
If you’re new to Docker or PostgreSQL, consider reviewing their official documentation
before proceeding with the installation.</p>
</div>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p>Start with the Docker deployment for the fastest setup experience. You can always
migrate to a custom deployment later as your requirements evolve.</p>
</div>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<p>If you encounter issues during setup or administration:</p>
<ol class="arabic simple">
<li><p><strong>Check the</strong> <span class="xref std std-doc">troubleshooting</span> <strong>guide</strong> for common issues and solutions</p></li>
<li><p><strong>Review the logs</strong> using <code class="docutils literal notranslate"><span class="pre">docker-compose</span> <span class="pre">logs</span></code> for error details</p></li>
<li><p><strong>Consult the</strong> <span class="xref std std-doc">monitoring</span> <strong>section</strong> for health check procedures</p></li>
<li><p><strong>Open an issue</strong> on our GitHub repository with detailed error information</p></li>
<li><p><strong>Contact support</strong> at <a class="reference external" href="mailto:admin&#37;&#52;&#48;forgeprotocol&#46;com">admin<span>&#64;</span>forgeprotocol<span>&#46;</span>com</a> for enterprise assistance</p></li>
</ol>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../protocols/rp-principles.html" class="btn btn-neutral float-left" title="Renaissance Periodisation Principles" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../user/exercise_guide.html" class="btn btn-neutral float-right" title="Exercise Management Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>