

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Exercise Management Guide &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Exercise System Architecture" href="../architecture/exercise_system.html" />
    <link rel="prev" title="⚙️ Getting Started (Admin)" href="../admin/index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Exercise Management Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="#api-access">API Access</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#creating-exercises">Creating Exercises</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-exercise-creation">Basic Exercise Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#comprehensive-exercise-example">Comprehensive Exercise Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#searching-exercises">Searching Exercises</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-search">Basic Search</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-search">Advanced Search</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#updating-exercises">Updating Exercises</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#exercise-updates">Exercise Updates</a></li>
<li class="toctree-l3"><a class="reference internal" href="#version-management">Version Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#approval-workflow">Approval Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#exercise-approval-process">Exercise Approval Process</a></li>
<li class="toctree-l3"><a class="reference internal" href="#approving-exercises">Approving Exercises</a></li>
<li class="toctree-l3"><a class="reference internal" href="#rejecting-exercises">Rejecting Exercises</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#media-management">Media Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#adding-media">Adding Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#viewing-media">Viewing Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#updating-media">Updating Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#deleting-media">Deleting Media</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#exercise-statistics">Exercise Statistics</a></li>
<li class="toctree-l2"><a class="reference internal" href="#soft-delete-and-recovery">Soft Delete and Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#deleting-exercises">Deleting Exercises</a></li>
<li class="toctree-l3"><a class="reference internal" href="#restoring-exercises">Restoring Exercises</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#exercise-creation">Exercise Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#content-quality">Content Quality</a></li>
<li class="toctree-l3"><a class="reference internal" href="#media-guidelines">Media Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id1">Version Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Exercise Management Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/user/exercise_guide.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="exercise-management-guide">
<h1>Exercise Management Guide<a class="headerlink" href="#exercise-management-guide" title="Link to this heading"></a></h1>
<p>This guide provides comprehensive instructions for managing exercises in the Forge Protocol platform, including creation, editing, approval workflows, and media management.</p>
<section id="getting-started">
<h2>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Before managing exercises, ensure you have:</p>
<ol class="arabic simple">
<li><p><strong>Valid Account</strong>: Registered user account with appropriate permissions</p></li>
<li><p><strong>Authentication</strong>: Valid JWT token for API access</p></li>
<li><p><strong>Permissions</strong>:
- Basic users: Create and edit own exercises
- Moderators: Approve/reject exercises
- Admins: Full exercise management access</p></li>
</ol>
</section>
<section id="api-access">
<h3>API Access<a class="headerlink" href="#api-access" title="Link to this heading"></a></h3>
<p>All exercise operations require authentication:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Set your authentication token</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">AUTH_TOKEN</span><span class="o">=</span><span class="s2">&quot;your-jwt-token-here&quot;</span>

<span class="c1"># Use in API calls</span>
curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>https://api.forgeprotocol.com/api/v1/exercises
</pre></div>
</div>
</section>
</section>
<section id="creating-exercises">
<h2>Creating Exercises<a class="headerlink" href="#creating-exercises" title="Link to this heading"></a></h2>
<section id="basic-exercise-creation">
<h3>Basic Exercise Creation<a class="headerlink" href="#basic-exercise-creation" title="Link to this heading"></a></h3>
<p>To create a new exercise, provide the required fields:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;name&quot;: &quot;Barbell Bench Press&quot;,</span>
<span class="s1">    &quot;description&quot;: &quot;Classic compound chest exercise using a barbell&quot;,</span>
<span class="s1">    &quot;primary_muscle_group&quot;: &quot;chest&quot;,</span>
<span class="s1">    &quot;secondary_muscle_groups&quot;: [&quot;triceps&quot;, &quot;shoulders&quot;],</span>
<span class="s1">    &quot;movement_pattern&quot;: &quot;push&quot;,</span>
<span class="s1">    &quot;equipment_required&quot;: [&quot;barbell&quot;],</span>
<span class="s1">    &quot;difficulty_level&quot;: &quot;intermediate&quot;</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
<p><strong>Required Fields:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code>: Unique exercise name</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">primary_muscle_group</span></code>: Main muscle targeted</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">movement_pattern</span></code>: Type of movement</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">difficulty_level</span></code>: Beginner, intermediate, or advanced</p></li>
</ul>
<p><strong>Optional Fields:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">description</span></code>: Detailed exercise description</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">secondary_muscle_groups</span></code>: Additional muscles worked</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">equipment_required</span></code>: List of required equipment</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">form_cues</span></code>: Array of technique tips</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">setup_instructions</span></code>: How to set up the exercise</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">execution_steps</span></code>: Step-by-step instructions</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">common_mistakes</span></code>: What to avoid</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">safety_notes</span></code>: Important safety information</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">video_url</span></code>: Primary demonstration video</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">thumbnail_url</span></code>: Exercise thumbnail image</p></li>
</ul>
</section>
<section id="comprehensive-exercise-example">
<h3>Comprehensive Exercise Example<a class="headerlink" href="#comprehensive-exercise-example" title="Link to this heading"></a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;The barbell bench press is a fundamental compound exercise that primarily targets the chest muscles while also engaging the triceps and shoulders. It&#39;s considered one of the &#39;big three&#39; powerlifting movements.&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;primary_muscle_group&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;chest&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;secondary_muscle_groups&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;triceps&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;shoulders&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;movement_pattern&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;push&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;equipment_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;barbell&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;bench&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;difficulty_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;form_cues&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Keep shoulder blades retracted and depressed&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Maintain a slight arch in the lower back&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Control the descent - don&#39;t bounce off chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Drive through the heels&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Keep wrists straight and elbows at 45-degree angle&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;setup_instructions&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lie on the bench with eyes directly under the barbell. Feet should be flat on the floor. Grip the bar with hands slightly wider than shoulder-width apart.&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;execution_steps&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Unrack the bar with straight arms directly over the chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Lower the bar in a controlled manner to the chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Pause briefly when the bar touches the chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Press the bar back to the starting position&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Maintain tension throughout the entire movement&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;common_mistakes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Bouncing the bar off the chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Flaring the elbows too wide (90 degrees)&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Not using full range of motion&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Lifting the head off the bench&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Using too much arch in the back&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;safety_notes&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Always use a spotter when lifting heavy weights. Ensure the safety bars are set at an appropriate height. Never train to failure without a spotter.&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version_notes&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Initial creation with comprehensive form guidance based on RP methodology&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="searching-exercises">
<h2>Searching Exercises<a class="headerlink" href="#searching-exercises" title="Link to this heading"></a></h2>
<section id="basic-search">
<h3>Basic Search<a class="headerlink" href="#basic-search" title="Link to this heading"></a></h3>
<p>Search for exercises using various filters:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Search by muscle group</span>
curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises?primary_muscle_group=chest&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>

<span class="c1"># Search by equipment</span>
curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises?equipment=barbell&amp;equipment=dumbbell&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>

<span class="c1"># Search by difficulty</span>
curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises?difficulty_level=beginner&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
<section id="advanced-search">
<h3>Advanced Search<a class="headerlink" href="#advanced-search" title="Link to this heading"></a></h3>
<p>Combine multiple filters for precise results:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises?primary_muscle_group=chest&amp;equipment=barbell&amp;difficulty_level=intermediate&amp;is_approved=true&amp;limit=10&amp;offset=0&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p><strong>Available Filters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code>: Search by exercise name (partial match)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">primary_muscle_group</span></code>: Filter by primary muscle</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">movement_pattern</span></code>: Filter by movement type</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">equipment</span></code>: Filter by required equipment (can specify multiple)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">difficulty_level</span></code>: Filter by difficulty</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">is_active</span></code>: Filter by active status</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">is_approved</span></code>: Filter by approval status</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">current_version_only</span></code>: Only show current versions (default: true)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">include_deleted</span></code>: Include soft-deleted exercises (default: false)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">limit</span></code>: Maximum results (1-100, default: 50)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">offset</span></code>: Results to skip for pagination</p></li>
</ul>
</section>
</section>
<section id="updating-exercises">
<h2>Updating Exercises<a class="headerlink" href="#updating-exercises" title="Link to this heading"></a></h2>
<section id="exercise-updates">
<h3>Exercise Updates<a class="headerlink" href="#exercise-updates" title="Link to this heading"></a></h3>
<p>Update an existing exercise by providing the fields to change:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>PUT<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;description&quot;: &quot;Updated description with more detail about muscle activation&quot;,</span>
<span class="s1">    &quot;form_cues&quot;: [</span>
<span class="s1">      &quot;Keep shoulder blades retracted&quot;,</span>
<span class="s1">      &quot;Maintain arch in lower back&quot;,</span>
<span class="s1">      &quot;Control the descent&quot;,</span>
<span class="s1">      &quot;Drive through heels for leg engagement&quot;</span>
<span class="s1">    ],</span>
<span class="s1">    &quot;version_notes&quot;: &quot;Added heel drive cue for better leg engagement&quot;,</span>
<span class="s1">    &quot;change_reason&quot;: &quot;content_update&quot;</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
<p><strong>Versioning Options:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">create_new_version=true</span></code> (default): Creates a new version</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">create_new_version=false</span></code>: Updates the current version in place</p></li>
</ul>
</section>
<section id="version-management">
<h3>Version Management<a class="headerlink" href="#version-management" title="Link to this heading"></a></h3>
<p><strong>View All Versions:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/versions&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p><strong>Get Specific Version:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/version/2&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p><strong>Set Current Version:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/uuid/{exercise_uuid}/set-current-version/1&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="approval-workflow">
<h2>Approval Workflow<a class="headerlink" href="#approval-workflow" title="Link to this heading"></a></h2>
<section id="exercise-approval-process">
<h3>Exercise Approval Process<a class="headerlink" href="#exercise-approval-process" title="Link to this heading"></a></h3>
<p>All exercises follow an approval workflow:</p>
<ol class="arabic simple">
<li><p><strong>Creation</strong>: New exercises start with status “pending”</p></li>
<li><p><strong>Review</strong>: Moderators review exercise content</p></li>
<li><p><strong>Approval/Rejection</strong>: Moderators approve or reject with notes</p></li>
<li><p><strong>Publication</strong>: Approved exercises become publicly visible</p></li>
</ol>
</section>
<section id="approving-exercises">
<h3>Approving Exercises<a class="headerlink" href="#approving-exercises" title="Link to this heading"></a></h3>
<p>Moderators can approve exercises:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span>/approve<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;notes&quot;: &quot;Exercise form and instructions are comprehensive and accurate. Approved for publication.&quot;</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
</section>
<section id="rejecting-exercises">
<h3>Rejecting Exercises<a class="headerlink" href="#rejecting-exercises" title="Link to this heading"></a></h3>
<p>Moderators can reject exercises with feedback:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span>/reject<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;notes&quot;: &quot;Form cues need more detail for safety. Please add information about proper elbow positioning.&quot;</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
</section>
</section>
<section id="media-management">
<h2>Media Management<a class="headerlink" href="#media-management" title="Link to this heading"></a></h2>
<section id="adding-media">
<h3>Adding Media<a class="headerlink" href="#adding-media" title="Link to this heading"></a></h3>
<p>Add videos, images, or other media to exercises:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span>/media<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;media_type&quot;: &quot;video&quot;,</span>
<span class="s1">    &quot;url&quot;: &quot;https://cdn.example.com/videos/bench-press-form.mp4&quot;,</span>
<span class="s1">    &quot;title&quot;: &quot;Bench Press Form Demonstration&quot;,</span>
<span class="s1">    &quot;description&quot;: &quot;Complete demonstration of proper bench press technique&quot;,</span>
<span class="s1">    &quot;thumbnail_url&quot;: &quot;https://cdn.example.com/thumbnails/bench-press.jpg&quot;,</span>
<span class="s1">    &quot;duration_seconds&quot;: 120,</span>
<span class="s1">    &quot;width_pixels&quot;: 1920,</span>
<span class="s1">    &quot;height_pixels&quot;: 1080,</span>
<span class="s1">    &quot;mime_type&quot;: &quot;video/mp4&quot;,</span>
<span class="s1">    &quot;is_primary&quot;: true</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
<p><strong>Media Types:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">video</span></code>: Exercise demonstrations, form videos</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">image</span></code>: Setup photos, form checkpoints</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">gif</span></code>: Short movement loops</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">audio</span></code>: Coaching cues, breathing instructions</p></li>
</ul>
<p><strong>Primary Media:</strong></p>
<p>Each exercise can have one primary media item per type:
- One primary video
- One primary image
- One primary GIF
- One primary audio</p>
</section>
<section id="viewing-media">
<h3>Viewing Media<a class="headerlink" href="#viewing-media" title="Link to this heading"></a></h3>
<p>Get all media for an exercise:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># All media</span>
curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/media&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>

<span class="c1"># Filter by type</span>
curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/media?media_type=video&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
<section id="updating-media">
<h3>Updating Media<a class="headerlink" href="#updating-media" title="Link to this heading"></a></h3>
<p>Update media metadata:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>PUT<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/media/<span class="o">{</span>media_id<span class="o">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;title&quot;: &quot;Updated Video Title&quot;,</span>
<span class="s1">    &quot;description&quot;: &quot;Updated description with more detail&quot;,</span>
<span class="s1">    &quot;is_primary&quot;: false,</span>
<span class="s1">    &quot;sort_order&quot;: 1</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
</section>
<section id="deleting-media">
<h3>Deleting Media<a class="headerlink" href="#deleting-media" title="Link to this heading"></a></h3>
<p>Remove media from an exercise:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>DELETE<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/media/<span class="o">{</span>media_id<span class="o">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="exercise-statistics">
<h2>Exercise Statistics<a class="headerlink" href="#exercise-statistics" title="Link to this heading"></a></h2>
<p>Get comprehensive statistics for an exercise:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span><span class="s2">&quot;https://api.forgeprotocol.com/api/v1/exercises/{exercise_id}/statistics&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p><strong>Statistics Include:</strong></p>
<ul class="simple">
<li><p>Version information (current version, total versions)</p></li>
<li><p>Approval status and history</p></li>
<li><p>Media counts by type</p></li>
<li><p>Muscle group analysis</p></li>
<li><p>Equipment requirements</p></li>
<li><p>Audit trail summary</p></li>
</ul>
</section>
<section id="soft-delete-and-recovery">
<h2>Soft Delete and Recovery<a class="headerlink" href="#soft-delete-and-recovery" title="Link to this heading"></a></h2>
<section id="deleting-exercises">
<h3>Deleting Exercises<a class="headerlink" href="#deleting-exercises" title="Link to this heading"></a></h3>
<p>Exercises are soft-deleted (can be recovered):</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>DELETE<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
<section id="restoring-exercises">
<h3>Restoring Exercises<a class="headerlink" href="#restoring-exercises" title="Link to this heading"></a></h3>
<p>Restore a previously deleted exercise:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://api.forgeprotocol.com/api/v1/exercises/<span class="o">{</span>exercise_id<span class="o">}</span>/restore<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$AUTH_TOKEN</span><span class="s2">&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="exercise-creation">
<h3>Exercise Creation<a class="headerlink" href="#exercise-creation" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Descriptive Names</strong>: Use clear, specific exercise names</p></li>
<li><p><strong>Comprehensive Instructions</strong>: Provide detailed setup and execution steps</p></li>
<li><p><strong>Safety First</strong>: Always include relevant safety notes</p></li>
<li><p><strong>Form Cues</strong>: Add specific technique cues for proper form</p></li>
<li><p><strong>Equipment Accuracy</strong>: List all required equipment</p></li>
</ol>
</section>
<section id="content-quality">
<h3>Content Quality<a class="headerlink" href="#content-quality" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Evidence-Based</strong>: Base instructions on scientific principles</p></li>
<li><p><strong>Clear Language</strong>: Use simple, understandable language</p></li>
<li><p><strong>Progressive Detail</strong>: Start with basics, add advanced details</p></li>
<li><p><strong>Visual Support</strong>: Include high-quality media when possible</p></li>
<li><p><strong>Regular Updates</strong>: Keep content current with latest research</p></li>
</ol>
</section>
<section id="media-guidelines">
<h3>Media Guidelines<a class="headerlink" href="#media-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>High Quality</strong>: Use clear, well-lit videos and images</p></li>
<li><p><strong>Multiple Angles</strong>: Show exercises from different viewpoints</p></li>
<li><p><strong>Proper Framing</strong>: Ensure full movement is visible</p></li>
<li><p><strong>Consistent Style</strong>: Maintain consistent visual style</p></li>
<li><p><strong>Accessibility</strong>: Provide alternative formats when possible</p></li>
</ol>
</section>
<section id="id1">
<h3>Version Management<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Meaningful Notes</strong>: Always include clear version notes</p></li>
<li><p><strong>Logical Progression</strong>: Make incremental improvements</p></li>
<li><p><strong>Backup Strategy</strong>: Keep important versions as backups</p></li>
<li><p><strong>Change Tracking</strong>: Document reasons for changes</p></li>
<li><p><strong>Review Process</strong>: Have changes reviewed before approval</p></li>
</ol>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>403 Forbidden</strong>
- Check authentication token validity
- Verify user permissions for the operation
- Ensure user owns the exercise (for modifications)</p>
<p><strong>409 Conflict</strong>
- Exercise name already exists
- Primary media of same type already exists
- Business rule violation (check error message)</p>
<p><strong>422 Validation Error</strong>
- Invalid enum values (muscle group, equipment, etc.)
- Missing required fields
- Invalid data format</p>
<p><strong>404 Not Found</strong>
- Exercise ID doesn’t exist
- Exercise UUID/version combination not found
- Media ID doesn’t exist</p>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<p>For additional support:</p>
<ol class="arabic simple">
<li><p><strong>API Documentation</strong>: Comprehensive API reference available</p></li>
<li><p><strong>Error Messages</strong>: Check detailed error responses</p></li>
<li><p><strong>Support Team</strong>: Contact support for complex issues</p></li>
<li><p><strong>Community</strong>: Join the Forge Protocol community forums</p></li>
</ol>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../admin/index.html" class="btn btn-neutral float-left" title="⚙️ Getting Started (Admin)" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../architecture/exercise_system.html" class="btn btn-neutral float-right" title="Exercise System Architecture" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>