

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>🏋️‍♂️ Forge Protocol Documentation &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="_static/favicon.ico"/>
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="_static/copybutton.js?v=30646c52"></script>
      <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
      <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="🔬 Protocols &amp; Science" href="protocols/index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="#" class="icon icon-home">
            Forge Protocol
              <img src="_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">🏋️‍♂️ Forge Protocol Documentation</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="forge-protocol-documentation">
<h1>🏋️‍♂️ Forge Protocol Documentation<a class="headerlink" href="#forge-protocol-documentation" title="Link to this heading"></a></h1>
<p><em>Evidence-based hypertrophy training platform implementing Renaissance Periodisation scientific principles</em></p>
<a class="reference external image-reference" href="https://python.org"><img alt="Python Version" src="https://img.shields.io/badge/Python-3.11+-blue.svg" />
</a>
<a class="reference external image-reference" href="https://fastapi.tiangolo.com"><img alt="FastAPI Version" src="https://img.shields.io/badge/FastAPI-0.104+-green.svg" />
</a>
<a class="reference external image-reference" href="https://postgresql.org"><img alt="PostgreSQL Version" src="https://img.shields.io/badge/PostgreSQL-15+-blue.svg" />
</a>
<a class="reference external image-reference" href="https://github.com/forkrul/forge-protocol/blob/main/LICENSE"><img alt="License" src="https://img.shields.io/badge/License-MIT-yellow.svg" />
</a>
<section id="welcome-to-forge-protocol">
<h2>Welcome to Forge Protocol<a class="headerlink" href="#welcome-to-forge-protocol" title="Link to this heading"></a></h2>
<p><strong>Forge Protocol</strong> is a comprehensive hypertrophy-focused training platform that implements evidence-based periodisation principles from the renowned “Scientific Principles of Hypertrophy Training” methodology. Built with Clean Architecture principles, this platform provides intelligent, adaptive training programmes that optimise muscle growth through precise volume management and progressive overload.</p>
<section id="key-objectives">
<h3>🎯 Key Objectives<a class="headerlink" href="#key-objectives" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Scientific Accuracy</strong>: Implement RP’s core algorithms for MEV estimation, set progression, and stimulus-to-fatigue ratio calculations</p></li>
<li><p><strong>Intelligent Programming</strong>: Automated mesocycle planning with volume progression and deload scheduling</p></li>
<li><p><strong>Comprehensive Database</strong>: 100+ exercises with proper form guidance and video demonstrations</p></li>
<li><p><strong>Data-Driven Insights</strong>: Advanced analytics for continuous programme optimisation</p></li>
<li><p><strong>Enterprise-Grade</strong>: Scalable, maintainable platform following modern software engineering practices</p></li>
</ul>
</section>
<section id="target-users">
<h3>👥 Target Users<a class="headerlink" href="#target-users" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Intermediate to Advanced Trainees</strong> seeking evidence-based hypertrophy programming</p></li>
<li><p><strong>Personal Trainers &amp; Coaches</strong> requiring scientifically-backed programming tools</p></li>
<li><p><strong>Fitness Enthusiasts</strong> interested in optimising training through data-driven approaches</p></li>
</ul>
</section>
<section id="documentation-structure">
<h3>📚 Documentation Structure<a class="headerlink" href="#documentation-structure" title="Link to this heading"></a></h3>
<p>This documentation is organised into three main sections:</p>
<dl class="simple">
<dt><strong>🔬 Protocols &amp; Science</strong></dt><dd><p>Deep dive into the scientific principles, algorithms, and methodologies that power Forge Protocol.</p>
</dd>
<dt><strong>⚙️ Getting Started (Admin)</strong></dt><dd><p>Complete setup guide for administrators, including installation, configuration, and deployment.</p>
</dd>
<dt><strong>📖 User Guide</strong></dt><dd><p>Comprehensive guide for end users, covering API usage, authentication, and integration examples.</p>
</dd>
</dl>
</section>
<section id="key-features">
<h3>✨ Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h3>
<dl class="simple">
<dt>🧠 <strong>Scientific Training Intelligence</strong></dt><dd><ul class="simple">
<li><p>MEV Stimulus Estimation - Intelligent volume recommendations based on post-workout feedback</p></li>
<li><p>Automated Set Progression - Weekly volume adjustments using soreness and performance metrics</p></li>
<li><p>Stimulus-to-Fatigue Ratio - Exercise effectiveness analysis for optimal exercise selection</p></li>
<li><p>Mesocycle Planning - Complete periodisation with automated deload scheduling</p></li>
</ul>
</dd>
<dt>💪 <strong>Comprehensive Exercise Database</strong></dt><dd><ul class="simple">
<li><p>100+ Exercises with detailed form instructions and video demonstrations</p></li>
<li><p>Versioning System - Complete version control with rollback capabilities</p></li>
<li><p>Approval Workflow - Multi-stage review process for exercise quality assurance</p></li>
<li><p>Media Management - Support for videos, images, GIFs, and audio content</p></li>
<li><p>Muscle Group Targeting - Primary and secondary muscle group classifications</p></li>
<li><p>Equipment Flexibility - Exercise variations for different gym setups</p></li>
<li><p>Movement Pattern Analysis - Push, pull, squat, hinge, and isolation categorisation</p></li>
<li><p>Advanced Search - Filter by muscle groups, equipment, difficulty, and approval status</p></li>
<li><p>Soft Delete - Exercises are never permanently lost, allowing for recovery</p></li>
</ul>
</dd>
<dt>📊 <strong>Advanced Analytics &amp; Progress Tracking</strong></dt><dd><ul class="simple">
<li><p>Volume Progression Tracking - Visual representation of training load over time</p></li>
<li><p>Performance Trend Analysis - Identify strengths and areas for improvement</p></li>
<li><p>Exercise Effectiveness Ranking - Data-driven exercise selection recommendations</p></li>
<li><p>Personalised Insights - Tailored recommendations based on individual response patterns</p></li>
</ul>
</dd>
<dt>🔐 <strong>Enterprise-Grade Security</strong></dt><dd><ul class="simple">
<li><p>JWT Authentication with refresh token rotation</p></li>
<li><p>bcrypt Password Hashing with configurable rounds</p></li>
<li><p>Input Validation using Pydantic schemas</p></li>
<li><p>SQL Injection Protection through parameterised queries</p></li>
<li><p>CORS &amp; Rate Limiting for API security</p></li>
</ul>
</dd>
<dt>🏗️ <strong>Clean Architecture</strong></dt><dd><ul class="simple">
<li><p>Domain Layer: Business logic, entities, and domain services</p></li>
<li><p>Application Layer: Use cases and application-specific business rules</p></li>
<li><p>Infrastructure Layer: Database, external services, and framework implementations</p></li>
<li><p>Presentation Layer: API endpoints, schemas, and HTTP concerns</p></li>
</ul>
</dd>
</dl>
</section>
<section id="quick-start">
<h3>🚀 Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h3>
<p>Get started with Forge Protocol in minutes using Docker:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/forge-protocol.git
<span class="nb">cd</span><span class="w"> </span>forge-protocol

<span class="c1"># Start all services</span>
<span class="nb">cd</span><span class="w"> </span>docker
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Run database migrations</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>app<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Access the application</span>
open<span class="w"> </span>http://localhost:8000/docs
</pre></div>
</div>
<p>For detailed setup instructions, see the <span class="xref std std-doc">admin/installation</span> guide.</p>
</section>
<section id="table-of-contents">
<h3>📋 Table of Contents<a class="headerlink" href="#table-of-contents" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="protocols/index.html">🔬 Protocols &amp; Science</a><ul>
<li class="toctree-l2"><a class="reference internal" href="protocols/index.html#overview">Overview</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="protocols/rp-principles.html">Renaissance Periodisation Principles</a><ul>
<li class="toctree-l2"><a class="reference internal" href="protocols/rp-principles.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="protocols/rp-principles.html#core-principles">🎯 Core Principles</a></li>
<li class="toctree-l2"><a class="reference internal" href="protocols/rp-principles.html#mathematical-models">🧮 Mathematical Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="protocols/rp-principles.html#practical-implementation">🎯 Practical Implementation</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="admin/index.html">⚙️ Getting Started (Admin)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="admin/index.html#overview">Overview</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user/exercise_guide.html">Exercise Management Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#creating-exercises">Creating Exercises</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#searching-exercises">Searching Exercises</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#updating-exercises">Updating Exercises</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#approval-workflow">Approval Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#media-management">Media Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#exercise-statistics">Exercise Statistics</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#soft-delete-and-recovery">Soft Delete and Recovery</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="user/exercise_guide.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture/exercise_system.html">Exercise System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#core-components">Core Components</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#versioning-system">Versioning System</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#approval-workflow">Approval Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#media-management">Media Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#search-and-filtering">Search and Filtering</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture/exercise_system.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercises.html">Exercise API</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/exercise_media.html">Exercise Media API</a></li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>
</div>
</section>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and Tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation is for Forge Protocol v1.0. For the latest updates and changes,
see the <a class="reference internal" href="changelog.html"><span class="doc">Changelog</span></a> or visit our <a class="reference external" href="https://github.com/forkrul/forge-protocol">GitHub repository</a>.</p>
</div>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="protocols/index.html" class="btn btn-neutral float-right" title="🔬 Protocols &amp; Science" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>