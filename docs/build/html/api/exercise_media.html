

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Exercise Media API &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Changelog" href="../changelog.html" />
    <link rel="prev" title="Exercise API" href="exercises.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="exercises.html">Exercise API</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Exercise Media API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#media-management">Media Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#add-exercise-media">Add Exercise Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-exercise-media">Get Exercise Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-exercise-media">Update Exercise Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#delete-exercise-media">Delete Exercise Media</a></li>
<li class="toctree-l3"><a class="reference internal" href="#reorder-exercise-media">Reorder Exercise Media</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#media-types">Media Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#supported-media-types">Supported Media Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="#primary-media-rules">Primary Media Rules</a></li>
<li class="toctree-l3"><a class="reference internal" href="#file-size-limits">File Size Limits</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#data-models">Data Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#exercisemedia-schema">ExerciseMedia Schema</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-responses">Error Responses</a></li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#media-organization">Media Organization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#content-guidelines">Content Guidelines</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Exercise Media API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/exercise_media.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="exercise-media-api">
<h1>Exercise Media API<a class="headerlink" href="#exercise-media-api" title="Link to this heading"></a></h1>
<p>The Exercise Media API provides functionality for managing multimedia content associated with exercises, including videos, images, GIFs, and audio files.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Exercise media management supports:
- <strong>Multiple Media Types</strong>: Videos, images, GIFs, and audio files
- <strong>Primary Media Designation</strong>: Mark one media item per type as primary
- <strong>Sort Ordering</strong>: Custom ordering of media items
- <strong>Metadata Management</strong>: Titles, descriptions, and technical metadata
- <strong>File Information</strong>: Size, dimensions, duration tracking</p>
</section>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All exercise media endpoints are available under:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>/api/v1/exercises/{exercise_id}/media
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All media endpoints require authentication via JWT token:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Authorization: Bearer &lt;your-jwt-token&gt;
</pre></div>
</div>
</section>
<section id="media-management">
<h2>Media Management<a class="headerlink" href="#media-management" title="Link to this heading"></a></h2>
<section id="add-exercise-media">
<h3>Add Exercise Media<a class="headerlink" href="#add-exercise-media" title="Link to this heading"></a></h3>
<p>Add media (video, image, etc.) to an exercise.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/{exercise_id}/media</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;media_type&quot;: &quot;video&quot;,</span>
<span class="err">  &quot;url&quot;: &quot;https://example.com/videos/bench-press.mp4&quot;,</span>
<span class="err">  &quot;title&quot;: &quot;Bench Press Form Video&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Demonstration of proper bench press technique&quot;,</span>
<span class="err">  &quot;thumbnail_url&quot;: &quot;https://example.com/thumbnails/bench-press.jpg&quot;,</span>
<span class="err">  &quot;duration_seconds&quot;: 120,</span>
<span class="err">  &quot;width_pixels&quot;: 1920,</span>
<span class="err">  &quot;height_pixels&quot;: 1080,</span>
<span class="err">  &quot;mime_type&quot;: &quot;video/mp4&quot;,</span>
<span class="err">  &quot;is_primary&quot;: true</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (201 Created):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;media_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;video&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com/videos/bench-press.mp4&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bench Press Form Video&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Demonstration of proper bench press technique&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;thumbnail_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com/thumbnails/bench-press.jpg&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;duration_seconds&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">120</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;width_pixels&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1920</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;height_pixels&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1080</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;mime_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;video/mp4&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;file_size_bytes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15728640</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_primary&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_active&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T12:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T12:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-exercise-media">
<h3>Get Exercise Media<a class="headerlink" href="#get-exercise-media" title="Link to this heading"></a></h3>
<p>Get all media for an exercise.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/exercises/{exercise_id}/media?media_type=video</span>
</pre></div>
</div>
<p><strong>Query Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">media_type</span></code> (string, optional): Filter by media type (video, image, gif, audio)</p></li>
</ul>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-123&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;exercise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;media_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;video&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com/videos/bench-press.mp4&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bench Press Form Video&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;is_primary&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T12:00:00Z&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-456&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;exercise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;media_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;image&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com/images/bench-press-setup.jpg&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bench Press Setup Position&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;is_primary&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T12:05:00Z&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="update-exercise-media">
<h3>Update Exercise Media<a class="headerlink" href="#update-exercise-media" title="Link to this heading"></a></h3>
<p>Update exercise media metadata.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">PUT /api/v1/exercises/media/{media_id}</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;title&quot;: &quot;Updated Bench Press Form Video&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Updated description with more detail&quot;,</span>
<span class="err">  &quot;is_primary&quot;: false,</span>
<span class="err">  &quot;sort_order&quot;: 1</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;media_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;video&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com/videos/bench-press.mp4&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Updated Bench Press Form Video&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Updated description with more detail&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_primary&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T12:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="delete-exercise-media">
<h3>Delete Exercise Media<a class="headerlink" href="#delete-exercise-media" title="Link to this heading"></a></h3>
<p>Delete exercise media.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">DELETE /api/v1/exercises/media/{media_id}</span>
</pre></div>
</div>
<p><strong>Response (204 No Content)</strong></p>
</section>
<section id="reorder-exercise-media">
<h3>Reorder Exercise Media<a class="headerlink" href="#reorder-exercise-media" title="Link to this heading"></a></h3>
<p>Reorder exercise media by providing new sort order.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/{exercise_id}/media/reorder</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;media_order&quot;: [</span>
<span class="err">    &quot;media-uuid-456&quot;,</span>
<span class="err">    &quot;media-uuid-123&quot;,</span>
<span class="err">    &quot;media-uuid-789&quot;</span>
<span class="err">  ]</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-456&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-123&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-789&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;sort_order&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
</section>
<section id="media-types">
<h2>Media Types<a class="headerlink" href="#media-types" title="Link to this heading"></a></h2>
<section id="supported-media-types">
<h3>Supported Media Types<a class="headerlink" href="#supported-media-types" title="Link to this heading"></a></h3>
<p>The API supports the following media types:</p>
<p><strong>Video</strong>
- Primary use: Exercise demonstrations, form videos
- Supported formats: MP4, WebM, MOV
- Required fields: <code class="docutils literal notranslate"><span class="pre">duration_seconds</span></code>, <code class="docutils literal notranslate"><span class="pre">width_pixels</span></code>, <code class="docutils literal notranslate"><span class="pre">height_pixels</span></code>
- Optional fields: <code class="docutils literal notranslate"><span class="pre">mime_type</span></code>, <code class="docutils literal notranslate"><span class="pre">file_size_bytes</span></code></p>
<p><strong>Image</strong>
- Primary use: Setup positions, form checkpoints, equipment photos
- Supported formats: JPEG, PNG, WebP
- Required fields: <code class="docutils literal notranslate"><span class="pre">width_pixels</span></code>, <code class="docutils literal notranslate"><span class="pre">height_pixels</span></code>
- Optional fields: <code class="docutils literal notranslate"><span class="pre">mime_type</span></code>, <code class="docutils literal notranslate"><span class="pre">file_size_bytes</span></code></p>
<p><strong>GIF</strong>
- Primary use: Short movement loops, quick form demonstrations
- Supported formats: GIF, WebP (animated)
- Required fields: <code class="docutils literal notranslate"><span class="pre">width_pixels</span></code>, <code class="docutils literal notranslate"><span class="pre">height_pixels</span></code>
- Optional fields: <code class="docutils literal notranslate"><span class="pre">duration_seconds</span></code>, <code class="docutils literal notranslate"><span class="pre">mime_type</span></code>, <code class="docutils literal notranslate"><span class="pre">file_size_bytes</span></code></p>
<p><strong>Audio</strong>
- Primary use: Coaching cues, breathing instructions
- Supported formats: MP3, WAV, OGG
- Required fields: <code class="docutils literal notranslate"><span class="pre">duration_seconds</span></code>
- Optional fields: <code class="docutils literal notranslate"><span class="pre">mime_type</span></code>, <code class="docutils literal notranslate"><span class="pre">file_size_bytes</span></code></p>
</section>
<section id="primary-media-rules">
<h3>Primary Media Rules<a class="headerlink" href="#primary-media-rules" title="Link to this heading"></a></h3>
<p>Each exercise can have one primary media item per media type:
- One primary video
- One primary image
- One primary GIF
- One primary audio</p>
<p>Attempting to set a second primary media of the same type will result in a 409 Conflict error.</p>
</section>
<section id="file-size-limits">
<h3>File Size Limits<a class="headerlink" href="#file-size-limits" title="Link to this heading"></a></h3>
<p>Recommended file size limits:
- <strong>Videos</strong>: Maximum 50MB
- <strong>Images</strong>: Maximum 5MB
- <strong>GIFs</strong>: Maximum 10MB
- <strong>Audio</strong>: Maximum 10MB</p>
</section>
</section>
<section id="data-models">
<h2>Data Models<a class="headerlink" href="#data-models" title="Link to this heading"></a></h2>
<section id="exercisemedia-schema">
<h3>ExerciseMedia Schema<a class="headerlink" href="#exercisemedia-schema" title="Link to this heading"></a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">interface</span><span class="w"> </span><span class="nx">ExerciseMedia</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">                    </span><span class="c1">// UUID</span>
<span class="w">  </span><span class="nx">exercise_id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">           </span><span class="c1">// Exercise UUID</span>
<span class="w">  </span><span class="nx">media_type</span><span class="o">:</span><span class="w"> </span><span class="kt">MediaType</span><span class="p">;</span><span class="w">         </span><span class="c1">// Type of media</span>
<span class="w">  </span><span class="nx">url</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">                   </span><span class="c1">// Media URL</span>
<span class="w">  </span><span class="nx">thumbnail_url?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">        </span><span class="c1">// Thumbnail URL</span>
<span class="w">  </span><span class="nx">title?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">                </span><span class="c1">// Media title</span>
<span class="w">  </span><span class="nx">description?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">          </span><span class="c1">// Media description</span>
<span class="w">  </span><span class="nx">file_size_bytes?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">      </span><span class="c1">// File size in bytes</span>
<span class="w">  </span><span class="nx">duration_seconds?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">     </span><span class="c1">// Duration for videos/audio</span>
<span class="w">  </span><span class="nx">width_pixels?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">         </span><span class="c1">// Width for images/videos</span>
<span class="w">  </span><span class="nx">height_pixels?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">        </span><span class="c1">// Height for images/videos</span>
<span class="w">  </span><span class="nx">mime_type?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">            </span><span class="c1">// MIME type</span>
<span class="w">  </span><span class="nx">sort_order</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w">            </span><span class="c1">// Sort order</span>
<span class="w">  </span><span class="nx">is_primary</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span><span class="w">           </span><span class="c1">// Whether this is primary media</span>
<span class="w">  </span><span class="nx">is_active</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span><span class="w">            </span><span class="c1">// Whether media is active</span>
<span class="w">  </span><span class="nx">created_at</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">            </span><span class="c1">// ISO 8601 timestamp</span>
<span class="w">  </span><span class="nx">updated_at</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w">            </span><span class="c1">// ISO 8601 timestamp</span>
<span class="p">}</span>

<span class="kd">enum</span><span class="w"> </span><span class="nx">MediaType</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">VIDEO</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;video&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">IMAGE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;image&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">GIF</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;gif&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">AUDIO</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;audio&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-responses">
<h2>Error Responses<a class="headerlink" href="#error-responses" title="Link to this heading"></a></h2>
<p><strong>400 Bad Request - Invalid Media Data</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Validation failed&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Video media should have duration&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>404 Not Found - Exercise Not Found</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Exercise not found&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>404 Not Found - Media Not Found</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Media not found&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;media_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;media-uuid-123&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>409 Conflict - Primary Media Exists</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Business rule violation&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Exercise already has primary video media&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="media-organization">
<h3>Media Organization<a class="headerlink" href="#media-organization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Use Primary Media Effectively</strong>: Set the most important/representative media as primary</p></li>
<li><p><strong>Logical Sort Order</strong>: Order media by importance or viewing sequence</p></li>
<li><p><strong>Descriptive Titles</strong>: Use clear, descriptive titles for all media</p></li>
<li><p><strong>Thumbnail Consistency</strong>: Provide thumbnails for videos when possible</p></li>
</ol>
</section>
<section id="performance-optimization">
<h3>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Optimize File Sizes</strong>: Compress media files appropriately</p></li>
<li><p><strong>Use Appropriate Formats</strong>: Choose formats optimized for web delivery</p></li>
<li><p><strong>Implement Lazy Loading</strong>: Load media on demand in client applications</p></li>
<li><p><strong>CDN Integration</strong>: Use CDN for media delivery when possible</p></li>
</ol>
</section>
<section id="content-guidelines">
<h3>Content Guidelines<a class="headerlink" href="#content-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>High Quality</strong>: Ensure media is clear and well-lit</p></li>
<li><p><strong>Proper Framing</strong>: Show full exercise movement and setup</p></li>
<li><p><strong>Multiple Angles</strong>: Provide different viewing angles when helpful</p></li>
<li><p><strong>Safety Focus</strong>: Highlight proper form and safety considerations</p></li>
</ol>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="exercises.html" class="btn btn-neutral float-left" title="Exercise API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../changelog.html" class="btn btn-neutral float-right" title="Changelog" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>