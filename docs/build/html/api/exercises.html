

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Exercise API &mdash; Forge Protocol v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=42351169" />

  
    <link rel="shortcut icon" href="../_static/favicon.ico"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Exercise Media API" href="exercise_media.html" />
    <link rel="prev" title="API Reference" href="modules.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Forge Protocol
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🔬 Protocols &amp; Science</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../protocols/index.html">🔬 Protocols &amp; Science</a></li>
<li class="toctree-l1"><a class="reference internal" href="../protocols/rp-principles.html">Renaissance Periodisation Principles</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">⚙️ Getting Started (Admin)</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin/index.html">⚙️ Getting Started (Admin)</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📖 User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user/exercise_guide.html">Exercise Management Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🏗️ Architecture &amp; Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/exercise_system.html">Exercise System Architecture</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Exercise API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-endpoints">Core Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#create-exercise">Create Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#search-exercises">Search Exercises</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-exercise">Get Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#update-exercise">Update Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#delete-exercise">Delete Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#restore-exercise">Restore Exercise</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#approval-workflow">Approval Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#approve-exercise">Approve Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#reject-exercise">Reject Exercise</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#version-management">Version Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#get-exercise-versions">Get Exercise Versions</a></li>
<li class="toctree-l3"><a class="reference internal" href="#get-specific-version">Get Specific Version</a></li>
<li class="toctree-l3"><a class="reference internal" href="#set-current-version">Set Current Version</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="exercise_media.html">Exercise Media API</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📝 Additional Resources</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../changelog.html">Changelog</a></li>
<li class="toctree-l1"><a class="reference internal" href="../contributing.html">Contributing to Forge Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="../license.html">License</a></li>
<li class="toctree-l1"><a class="reference internal" href="../glossary.html">Glossary</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Forge Protocol</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Exercise API</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/exercises.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="exercise-api">
<h1>Exercise API<a class="headerlink" href="#exercise-api" title="Link to this heading"></a></h1>
<p>The Exercise API provides comprehensive functionality for managing exercises, including creation, versioning, approval workflows, and media management.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Exercise API is built around the concept of versioned exercises with soft-delete capabilities and comprehensive audit trails. Each exercise can have multiple versions, with one designated as the current version.</p>
<p>Key Features:
- <strong>Versioning</strong>: Full version control with rollback capabilities
- <strong>Soft Delete</strong>: Exercises are never permanently deleted, allowing for recovery
- <strong>Approval Workflow</strong>: Multi-stage approval process for exercise publication
- <strong>Media Management</strong>: Support for videos, images, and other multimedia content
- <strong>Advanced Search</strong>: Filter by muscle groups, equipment, difficulty, and more
- <strong>Audit Trail</strong>: Complete history of all changes and actions</p>
</section>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All exercise endpoints are available under:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>/api/v1/exercises
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All exercise endpoints require authentication via JWT token in the Authorization header:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Authorization: Bearer &lt;your-jwt-token&gt;
</pre></div>
</div>
</section>
<section id="core-endpoints">
<h2>Core Endpoints<a class="headerlink" href="#core-endpoints" title="Link to this heading"></a></h2>
<section id="create-exercise">
<h3>Create Exercise<a class="headerlink" href="#create-exercise" title="Link to this heading"></a></h3>
<p>Create a new exercise with comprehensive metadata.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer &lt;token&gt;</span>

<span class="err">{</span>
<span class="err">  &quot;name&quot;: &quot;Barbell Bench Press&quot;,</span>
<span class="err">  &quot;description&quot;: &quot;Classic compound chest exercise using a barbell&quot;,</span>
<span class="err">  &quot;primary_muscle_group&quot;: &quot;chest&quot;,</span>
<span class="err">  &quot;secondary_muscle_groups&quot;: [&quot;triceps&quot;, &quot;shoulders&quot;],</span>
<span class="err">  &quot;movement_pattern&quot;: &quot;push&quot;,</span>
<span class="err">  &quot;equipment_required&quot;: [&quot;barbell&quot;],</span>
<span class="err">  &quot;difficulty_level&quot;: &quot;intermediate&quot;,</span>
<span class="err">  &quot;form_cues&quot;: [</span>
<span class="err">    &quot;Keep shoulder blades retracted&quot;,</span>
<span class="err">    &quot;Maintain arch in lower back&quot;,</span>
<span class="err">    &quot;Control the descent&quot;</span>
<span class="err">  ],</span>
<span class="err">  &quot;setup_instructions&quot;: &quot;Lie on bench with eyes under the bar&quot;,</span>
<span class="err">  &quot;execution_steps&quot;: [</span>
<span class="err">    &quot;Unrack the bar with straight arms&quot;,</span>
<span class="err">    &quot;Lower bar to chest with control&quot;,</span>
<span class="err">    &quot;Press bar back to starting position&quot;</span>
<span class="err">  ],</span>
<span class="err">  &quot;common_mistakes&quot;: [</span>
<span class="err">    &quot;Bouncing bar off chest&quot;,</span>
<span class="err">    &quot;Flaring elbows too wide&quot;,</span>
<span class="err">    &quot;Not using full range of motion&quot;</span>
<span class="err">  ],</span>
<span class="err">  &quot;safety_notes&quot;: &quot;Always use a spotter for heavy weights&quot;,</span>
<span class="err">  &quot;version_notes&quot;: &quot;Initial creation with comprehensive form guidance&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (201 Created):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_uuid&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;987fcdeb-51a2-43d1-9c4e-123456789abc&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Classic compound chest exercise using a barbell&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;primary_muscle_group&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;chest&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;secondary_muscle_groups&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;triceps&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;shoulders&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;movement_pattern&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;push&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;equipment_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;barbell&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;difficulty_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_active&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approval_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;pending&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;change_reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;initial_creation&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="search-exercises">
<h3>Search Exercises<a class="headerlink" href="#search-exercises" title="Link to this heading"></a></h3>
<p>Search and filter exercises with comprehensive query parameters.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/exercises?primary_muscle_group=chest&amp;equipment=barbell&amp;difficulty_level=intermediate&amp;limit=20&amp;offset=0</span>
</pre></div>
</div>
<p><strong>Query Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code> (string, optional): Search by exercise name</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">primary_muscle_group</span></code> (string, optional): Filter by primary muscle group</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">movement_pattern</span></code> (string, optional): Filter by movement pattern</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">equipment</span></code> (array, optional): Filter by required equipment</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">difficulty_level</span></code> (string, optional): Filter by difficulty level</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">is_active</span></code> (boolean, default: true): Filter by active status</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">is_approved</span></code> (boolean, optional): Filter by approval status</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">current_version_only</span></code> (boolean, default: true): Only return current versions</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">include_deleted</span></code> (boolean, default: false): Include soft-deleted exercises</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">limit</span></code> (integer, 1-100, default: 50): Maximum results to return</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">offset</span></code> (integer, default: 0): Number of results to skip</p></li>
</ul>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;exercises&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;primary_muscle_group&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;chest&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;difficulty_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total_count&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;limit&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">20</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;offset&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;has_more&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-exercise">
<h3>Get Exercise<a class="headerlink" href="#get-exercise" title="Link to this heading"></a></h3>
<p>Retrieve a specific exercise by ID.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/exercises/{exercise_id}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_uuid&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;987fcdeb-51a2-43d1-9c4e-123456789abc&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Classic compound chest exercise using a barbell&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;primary_muscle_group&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;chest&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;secondary_muscle_groups&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;triceps&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;shoulders&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;movement_pattern&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;push&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;equipment_required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;barbell&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;difficulty_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;intermediate&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;form_cues&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Keep shoulder blades retracted&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Maintain arch in lower back&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Control the descent&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;setup_instructions&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lie on bench with eyes under the bar&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;execution_steps&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Unrack the bar with straight arms&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Lower bar to chest with control&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Press bar back to starting position&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;common_mistakes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;Bouncing bar off chest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Flaring elbows too wide&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;Not using full range of motion&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;safety_notes&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Always use a spotter for heavy weights&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_active&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approval_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;approved&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;updated_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;media&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="update-exercise">
<h3>Update Exercise<a class="headerlink" href="#update-exercise" title="Link to this heading"></a></h3>
<p>Update an exercise, optionally creating a new version.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">PUT /api/v1/exercises/{exercise_id}?create_new_version=true</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;description&quot;: &quot;Updated description with more detail&quot;,</span>
<span class="err">  &quot;form_cues&quot;: [</span>
<span class="err">    &quot;Keep shoulder blades retracted&quot;,</span>
<span class="err">    &quot;Maintain arch in lower back&quot;,</span>
<span class="err">    &quot;Control the descent&quot;,</span>
<span class="err">    &quot;Drive through heels&quot;</span>
<span class="err">  ],</span>
<span class="err">  &quot;version_notes&quot;: &quot;Added heel drive cue for better leg engagement&quot;,</span>
<span class="err">  &quot;change_reason&quot;: &quot;content_update&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Query Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">create_new_version</span></code> (boolean, default: true): Whether to create a new version</p></li>
</ul>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;456e7890-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exercise_uuid&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;987fcdeb-51a2-43d1-9c4e-123456789abc&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;parent_version_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Updated description with more detail&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approval_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;pending&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;change_reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;content_update&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version_notes&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Added heel drive cue for better leg engagement&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="delete-exercise">
<h3>Delete Exercise<a class="headerlink" href="#delete-exercise" title="Link to this heading"></a></h3>
<p>Soft delete an exercise (can be restored later).</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">DELETE /api/v1/exercises/{exercise_id}</span>
</pre></div>
</div>
<p><strong>Response (204 No Content)</strong></p>
</section>
<section id="restore-exercise">
<h3>Restore Exercise<a class="headerlink" href="#restore-exercise" title="Link to this heading"></a></h3>
<p>Restore a previously deleted exercise.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/{exercise_id}/restore</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Barbell Bench Press&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_active&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;deleted_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="approval-workflow">
<h2>Approval Workflow<a class="headerlink" href="#approval-workflow" title="Link to this heading"></a></h2>
<section id="approve-exercise">
<h3>Approve Exercise<a class="headerlink" href="#approve-exercise" title="Link to this heading"></a></h3>
<p>Approve an exercise for publication.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/{exercise_id}/approve</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;notes&quot;: &quot;Exercise form and instructions are comprehensive and accurate&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approval_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;approved&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approved_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T11:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approved_by&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;admin-user-id&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="reject-exercise">
<h3>Reject Exercise<a class="headerlink" href="#reject-exercise" title="Link to this heading"></a></h3>
<p>Reject an exercise submission.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/{exercise_id}/reject</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;notes&quot;: &quot;Form cues need more detail for safety&quot;</span>
<span class="err">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;is_approved&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;approval_status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;rejected&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="version-management">
<h2>Version Management<a class="headerlink" href="#version-management" title="Link to this heading"></a></h2>
<section id="get-exercise-versions">
<h3>Get Exercise Versions<a class="headerlink" href="#get-exercise-versions" title="Link to this heading"></a></h3>
<p>Get all versions of an exercise.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/exercises/uuid/{exercise_uuid}/versions</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;exercise_uuid&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;987fcdeb-51a2-43d1-9c4e-123456789abc&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total_versions&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;versions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;is_current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;456e7890-e89b-12d3-a456-************&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;is_current_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T11:00:00Z&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-specific-version">
<h3>Get Specific Version<a class="headerlink" href="#get-specific-version" title="Link to this heading"></a></h3>
<p>Get a specific version of an exercise.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">GET /api/v1/exercises/uuid/{exercise_uuid}/version/{version}</span>
</pre></div>
</div>
</section>
<section id="set-current-version">
<h3>Set Current Version<a class="headerlink" href="#set-current-version" title="Link to this heading"></a></h3>
<p>Set a specific version as the current version.</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">POST /api/v1/exercises/uuid/{exercise_uuid}/set-current-version/{version}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Current version updated successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="modules.html" class="btn btn-neutral float-left" title="API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="exercise_media.html" class="btn btn-neutral float-right" title="Exercise Media API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Forge Protocol Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>