# Nix Development Environment

Forge Protocol uses <PERSON> for reproducible development environments, ensuring all developers have identical tooling and dependencies.

## Quick Start

### Option 1: <PERSON> (Recommended)
```bash
# Enter development environment
nix develop

# Or with direnv (automatic)
direnv allow
```

### Option 2: Legacy Nix Shell
```bash
nix-shell
```

## What's Included

The Nix environment provides:

### Core Development Tools
- **Python 3.10** with pip, virtualenv, setuptools
- **PostgreSQL 15** for database development
- **Redis** for caching and sessions
- **Docker & Docker Compose** for containerization

### System Dependencies
- **libffi, openssl, zlib** - Required for Python cryptography packages
- **gcc, make** - Build tools for native extensions
- **pkg-config** - Package configuration

### Development Utilities
- **git** - Version control
- **curl, jq** - API testing and JSON processing
- **tree, htop** - File system and process monitoring
- **tmux, vim** - Terminal multiplexing and editing
- **pgcli, redis-cli** - Database clients

## Environment Variables

The Nix shell automatically sets:

```bash
# Python environment
PYTHONPATH="$PWD:$PYTHONPATH"
PYTHONDONTWRITEBYTECODE="1"
PYTHONUNBUFFERED="1"

# Development mode
ENVIRONMENT="development"
DEBUG="true"

# Database defaults
PGHOST="localhost"
PGPORT="5432"
PGUSER="rp_user"
PGPASSWORD="rp_password"
PGDATABASE="rp_training"
```

## Development Workflow

1. **Enter environment:**
   ```bash
   nix develop  # or just cd into directory with direnv
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements/dev.txt
   ```

3. **Start services:**
   ```bash
   docker compose -f docker/docker-compose.yml up -d
   ```

4. **Run migrations:**
   ```bash
   python -m alembic upgrade head
   ```

5. **Start development server:**
   ```bash
   python -m uvicorn app.main:app --reload
   ```

6. **Run tests:**
   ```bash
   python -m pytest
   ```

## Direnv Integration

For automatic environment loading:

1. **Install direnv:**
   ```bash
   # On NixOS
   nix-env -iA nixpkgs.direnv
   
   # On other systems
   curl -sfL https://direnv.net/install.sh | bash
   ```

2. **Add to shell:**
   ```bash
   # For bash
   echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
   
   # For zsh
   echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
   ```

3. **Allow in project:**
   ```bash
   direnv allow
   ```

Now the environment loads automatically when you `cd` into the project!

## Benefits

### Reproducibility
- Identical development environment across all machines
- Pinned package versions prevent "works on my machine" issues
- Declarative configuration in version control

### Isolation
- No global Python package pollution
- Clean separation from system packages
- Easy to reset/rebuild environment

### Mobile-First Optimizations
- Pre-configured for mobile API development
- Optimized Python packages for performance
- Database tools for mobile-optimized queries

## Troubleshooting

### Common Issues

**"command not found" errors:**
```bash
# Ensure you're in the Nix shell
nix develop
```

**Python package installation fails:**
```bash
# Check system dependencies are available
echo $LD_LIBRARY_PATH
# Should include libffi, openssl, zlib paths
```

**Database connection issues:**
```bash
# Check environment variables
env | grep PG
# Start database container
docker compose -f docker/docker-compose.yml up -d db
```

### Rebuilding Environment

If you need to rebuild the environment:

```bash
# Clear Nix cache
nix-collect-garbage

# Rebuild flake
nix develop --rebuild
```

## CI/CD Integration

The Nix environment can be used in CI/CD:

```yaml
# GitHub Actions example
- uses: cachix/install-nix-action@v22
- run: nix develop --command python -m pytest
```

This ensures CI runs in the exact same environment as development.
