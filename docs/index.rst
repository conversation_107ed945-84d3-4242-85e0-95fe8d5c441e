RP Training API Documentation
==============================

Welcome to the comprehensive documentation for the **RP Training API** - a modern, scalable REST API for resistance training program management and periodization.

.. image:: https://img.shields.io/badge/version-0.1.0-blue.svg
   :target: https://github.com/forkrul/forge-protocol
   :alt: Version

.. image:: https://img.shields.io/badge/python-3.11+-blue.svg
   :target: https://python.org
   :alt: Python Version

.. image:: https://img.shields.io/badge/fastapi-0.115+-green.svg
   :target: https://fastapi.tiangolo.com
   :alt: FastAPI

.. image:: https://img.shields.io/badge/coverage-88%25-brightgreen.svg
   :target: #testing
   :alt: Test Coverage

Overview
--------

The RP Training API is a comprehensive solution for managing resistance training programs, user profiles, and workout periodization. Built with modern Python technologies and following clean architecture principles, it provides a robust foundation for fitness applications.

**Key Features:**

🔐 **Secure Authentication**
   - JWT-based authentication and authorization
   - Password hashing with bcrypt
   - Session management and token refresh

👤 **User Management**
   - User registration and profile management
   - Training experience tracking
   - Personalized recommendations

🏥 **Health Monitoring**
   - Comprehensive health checks
   - System performance monitoring
   - Database health validation

🧪 **Enterprise Testing**
   - 88% test coverage with 400+ tests
   - Unit, integration, and BDD testing
   - Comprehensive error handling

🏗️ **Clean Architecture**
   - Domain-driven design
   - Dependency injection
   - SOLID principles

Quick Start
-----------

Get started with the RP Training API in minutes:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

   # Set up development environment
   nix-shell

   # Run the API
   python -m uvicorn app.main:app --reload

   # Access the API documentation
   open http://localhost:8000/docs

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   user_guide/getting_started
   user_guide/authentication
   user_guide/user_management
   user_guide/api_usage
   user_guide/examples

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/endpoints
   api/schemas
   api/authentication
   api/errors
   api/examples

.. toctree::
   :maxdepth: 2
   :caption: Developer Guide

   developer/architecture
   developer/setup
   developer/testing
   developer/contributing
   developer/deployment

.. toctree::
   :maxdepth: 2
   :caption: Testing Documentation

   testing/overview
   testing/unit_tests
   testing/integration_tests
   testing/bdd_tests
   testing/coverage

.. toctree::
   :maxdepth: 2
   :caption: Architecture & Design

   architecture/overview
   architecture/components
   architecture/domain_model
   architecture/clean_architecture
   architecture/database_design
   architecture/security

.. toctree::
   :maxdepth: 2
   :caption: Training Science

   training_science/overview
   training_science/periodization
   training_science/mesocycles
   training_science/training_load
   training_science/recovery_adaptation

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   modules/app
   modules/domain
   modules/application
   modules/infrastructure
   modules/presentation

.. toctree::
   :maxdepth: 1
   :caption: Additional Resources

   faq
   changelog
   roadmap
   glossary

API Features
------------

Authentication & Authorization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The API provides secure authentication using JWT tokens:

- **Registration**: Create new user accounts with validation
- **Login**: Authenticate users and receive access tokens
- **Profile Management**: Update user information and preferences
- **Token Refresh**: Maintain sessions with refresh tokens

User Management
~~~~~~~~~~~~~~~

Comprehensive user profile management:

- **Profile Creation**: Set up user profiles with training experience
- **Profile Updates**: Modify user information and preferences
- **Training History**: Track user progress and achievements
- **Personalization**: Customize experience based on user data

Health Monitoring
~~~~~~~~~~~~~~~~~

Built-in health monitoring and observability:

- **Health Checks**: Basic and detailed system health endpoints
- **Performance Metrics**: Monitor response times and throughput
- **Database Health**: Validate database connectivity and performance
- **System Monitoring**: Track memory, CPU, and disk usage

Technology Stack
----------------

**Backend Framework**
   - FastAPI 0.115+ for high-performance API development
   - Python 3.11+ with modern async/await support
   - Pydantic for data validation and serialization

**Database & ORM**
   - SQLAlchemy 2.0+ for database operations
   - Async database support with aiosqlite
   - Database migrations with Alembic

**Authentication & Security**
   - JWT tokens for stateless authentication
   - bcrypt for secure password hashing
   - CORS and security headers configuration

**Testing & Quality**
   - pytest for unit and integration testing
   - Behave for behavior-driven development
   - 88% test coverage with comprehensive scenarios

**Development Tools**
   - Nix for reproducible development environments
   - Black and isort for code formatting
   - mypy for static type checking

Architecture Highlights
-----------------------

The RP Training API follows **Clean Architecture** principles with clear layer separation and dependency inversion:

.. mermaid::

   graph TB
       subgraph "Clean Architecture Layers"
           subgraph "Presentation Layer"
               API[FastAPI Endpoints]
               SCHEMAS[Pydantic Schemas]
               DI[Dependency Injection]
           end

           subgraph "Application Layer"
               UC[Use Cases]
               DTO[Data Transfer Objects]
               INTERFACES[Service Interfaces]
           end

           subgraph "Domain Layer"
               ENTITIES[Domain Entities]
               SERVICES[Domain Services]
               REPO_INT[Repository Interfaces]
           end

           subgraph "Infrastructure Layer"
               DATABASE[Database/SQLAlchemy]
               AUTH[Authentication/JWT]
               EXTERNAL[External Services]
           end
       end

       %% Dependencies flow inward
       API --> UC
       SCHEMAS --> DTO
       DI --> INTERFACES

       UC --> ENTITIES
       DTO --> ENTITIES
       INTERFACES --> REPO_INT

       ENTITIES --> SERVICES
       SERVICES --> REPO_INT

       %% Infrastructure implements interfaces
       DATABASE -.-> REPO_INT
       AUTH -.-> INTERFACES
       EXTERNAL -.-> INTERFACES

       %% Styling
       classDef presentation fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
       classDef application fill:#e74c3c,stroke:#c0392b,stroke-width:2px,color:#fff
       classDef domain fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
       classDef infrastructure fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff

       class API,SCHEMAS,DI presentation
       class UC,DTO,INTERFACES application
       class ENTITIES,SERVICES,REPO_INT domain
       class DATABASE,AUTH,EXTERNAL infrastructure

API Workflow Overview
---------------------

Here's how a typical API request flows through the system:

.. mermaid::

   sequenceDiagram
       participant Client
       participant FastAPI
       participant UseCase
       participant Domain
       participant Repository
       participant Database

       Client->>FastAPI: HTTP Request
       FastAPI->>FastAPI: Validate Schema
       FastAPI->>UseCase: Execute Business Logic
       UseCase->>Domain: Apply Business Rules
       Domain-->>UseCase: Domain Result
       UseCase->>Repository: Data Operation
       Repository->>Database: SQL Query
       Database-->>Repository: Query Result
       Repository-->>UseCase: Domain Entity
       UseCase-->>FastAPI: Response Data
       FastAPI->>FastAPI: Format Response
       FastAPI-->>Client: HTTP Response

Getting Help
------------

- **Documentation**: This comprehensive guide covers all aspects of the API
- **API Explorer**: Interactive documentation at ``/docs`` endpoint
- **GitHub Issues**: Report bugs and request features
- **Community**: Join discussions and get support

Contributing
------------

We welcome contributions! Please see our :doc:`developer/contributing` guide for details on:

- Setting up the development environment
- Running tests and quality checks
- Submitting pull requests
- Code style and conventions

License
-------

This project is licensed under the MIT License. See the LICENSE file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
