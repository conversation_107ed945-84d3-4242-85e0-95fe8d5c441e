.. Forge Protocol documentation master file

🏋️‍♂️ Forge Protocol Documentation
=====================================

*Evidence-based hypertrophy training platform implementing Renaissance Periodisation scientific principles*

.. image:: https://img.shields.io/badge/Python-3.11+-blue.svg
   :target: https://python.org
   :alt: Python Version

.. image:: https://img.shields.io/badge/FastAPI-0.104+-green.svg
   :target: https://fastapi.tiangolo.com
   :alt: FastAPI Version

.. image:: https://img.shields.io/badge/PostgreSQL-15+-blue.svg
   :target: https://postgresql.org
   :alt: PostgreSQL Version

.. image:: https://img.shields.io/badge/License-MIT-yellow.svg
   :target: https://github.com/forkrul/forge-protocol/blob/main/LICENSE
   :alt: License

Welcome to Forge Protocol
--------------------------

**Forge Protocol** is a comprehensive hypertrophy-focused training platform that implements evidence-based periodisation principles from the renowned "Scientific Principles of Hypertrophy Training" methodology. Built with Clean Architecture principles, this platform provides intelligent, adaptive training programmes that optimise muscle growth through precise volume management and progressive overload.

🎯 Key Objectives
~~~~~~~~~~~~~~~~~

* **Scientific Accuracy**: Implement RP's core algorithms for MEV estimation, set progression, and stimulus-to-fatigue ratio calculations
* **Intelligent Programming**: Automated mesocycle planning with volume progression and deload scheduling  
* **Comprehensive Database**: 100+ exercises with proper form guidance and video demonstrations
* **Data-Driven Insights**: Advanced analytics for continuous programme optimisation
* **Enterprise-Grade**: Scalable, maintainable platform following modern software engineering practices

👥 Target Users
~~~~~~~~~~~~~~~

* **Intermediate to Advanced Trainees** seeking evidence-based hypertrophy programming
* **Personal Trainers & Coaches** requiring scientifically-backed programming tools
* **Fitness Enthusiasts** interested in optimising training through data-driven approaches

📚 Documentation Structure
~~~~~~~~~~~~~~~~~~~~~~~~~~

This documentation is organised into three main sections:

**🔬 Protocols & Science**
   Deep dive into the scientific principles, algorithms, and methodologies that power Forge Protocol.

**⚙️ Getting Started (Admin)**
   Complete setup guide for administrators, including installation, configuration, and deployment.

**📖 User Guide**
   Comprehensive guide for end users, covering API usage, authentication, and integration examples.

✨ Key Features
~~~~~~~~~~~~~~~

🧠 **Scientific Training Intelligence**
   * MEV Stimulus Estimation - Intelligent volume recommendations based on post-workout feedback
   * Automated Set Progression - Weekly volume adjustments using soreness and performance metrics
   * Stimulus-to-Fatigue Ratio - Exercise effectiveness analysis for optimal exercise selection
   * Mesocycle Planning - Complete periodisation with automated deload scheduling

💪 **Comprehensive Exercise Database**
   * 100+ Exercises with detailed form instructions and video demonstrations
   * Versioning System - Complete version control with rollback capabilities
   * Approval Workflow - Multi-stage review process for exercise quality assurance
   * Media Management - Support for videos, images, GIFs, and audio content
   * Muscle Group Targeting - Primary and secondary muscle group classifications
   * Equipment Flexibility - Exercise variations for different gym setups
   * Movement Pattern Analysis - Push, pull, squat, hinge, and isolation categorisation
   * Advanced Search - Filter by muscle groups, equipment, difficulty, and approval status
   * Soft Delete - Exercises are never permanently lost, allowing for recovery

📊 **Advanced Analytics & Progress Tracking**
   * Volume Progression Tracking - Visual representation of training load over time
   * Performance Trend Analysis - Identify strengths and areas for improvement
   * Exercise Effectiveness Ranking - Data-driven exercise selection recommendations
   * Personalised Insights - Tailored recommendations based on individual response patterns

🔐 **Enterprise-Grade Security**
   * JWT Authentication with refresh token rotation
   * bcrypt Password Hashing with configurable rounds
   * Input Validation using Pydantic schemas
   * SQL Injection Protection through parameterised queries
   * CORS & Rate Limiting for API security

🏗️ **Clean Architecture**
   * Domain Layer: Business logic, entities, and domain services
   * Application Layer: Use cases and application-specific business rules
   * Infrastructure Layer: Database, external services, and framework implementations
   * Presentation Layer: API endpoints, schemas, and HTTP concerns

🚀 Quick Start
~~~~~~~~~~~~~~

Get started with Forge Protocol in minutes using Docker:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/forge-protocol.git
   cd forge-protocol

   # Start all services
   cd docker
   docker-compose up -d

   # Run database migrations
   docker-compose exec app alembic upgrade head

   # Access the application
   open http://localhost:8000/docs

For detailed setup instructions, see the :doc:`admin/installation` guide.

📋 Table of Contents
~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2
   :caption: 🔬 Protocols & Science

   protocols/index
   protocols/rp-principles
   protocols/algorithms
   protocols/mesocycle-planning
   protocols/exercise-science
   protocols/references

.. toctree::
   :maxdepth: 2
   :caption: ⚙️ Getting Started (Admin)

   admin/index
   admin/installation
   admin/configuration
   admin/database
   admin/deployment
   admin/testing
   admin/monitoring
   admin/troubleshooting

.. toctree::
   :maxdepth: 2
   :caption: 📖 User Guide

   user/index
   user/authentication
   user/exercise_guide
   user/api-reference
   user/examples
   user/integration
   user/sdk

.. toctree::
   :maxdepth: 2
   :caption: 🏗️ Architecture & Development

   architecture/index
   architecture/clean-architecture
   architecture/domain-model
   architecture/exercise_system
   architecture/api-design
   architecture/database-schema
   architecture/testing-strategy

.. toctree::
   :maxdepth: 1
   :caption: 📚 API Reference

   api/modules
   api/exercises
   api/exercise_media

.. toctree::
   :maxdepth: 1
   :caption: 📝 Additional Resources

   changelog
   contributing
   license
   glossary

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

.. note::
   This documentation is for Forge Protocol v1.0. For the latest updates and changes, 
   see the :doc:`changelog` or visit our `GitHub repository <https://github.com/forkrul/forge-protocol>`_.
