Contributing to Forge Protocol
==============================

Thank you for your interest in contributing to Forge Protocol! This document provides guidelines and information for contributors to help maintain code quality and project consistency.

🎯 Project Vision
-----------------

We're building an evidence-based hypertrophy training platform that implements Renaissance Periodisation principles with scientific rigour and clean architecture. Every contribution should align with these core values:

**Scientific Accuracy**
   All training algorithms and recommendations must be based on peer-reviewed research and validated methodologies.

**Code Quality**
   We maintain high standards for code quality, testing, and documentation to ensure long-term maintainability.

**User Experience**
   Features should be intuitive, reliable, and provide clear value to users seeking evidence-based training guidance.

**Open Collaboration**
   We welcome contributions from developers, exercise scientists, and fitness professionals worldwide.

🚀 Getting Started
------------------

Development Setup
~~~~~~~~~~~~~~~~~

1. **Fork and Clone**

   .. code-block:: bash

      # Fork the repository on GitHub, then clone your fork
      git clone https://github.com/YOUR_USERNAME/forge-protocol.git
      cd forge-protocol

2. **Set Up Development Environment**

   .. code-block:: bash

      # Create virtual environment
      python3.11 -m venv venv
      source venv/bin/activate  # Linux/macOS
      # venv\Scripts\activate   # Windows

      # Install development dependencies
      pip install -r requirements/dev.txt

3. **Start Development Services**

   .. code-block:: bash

      # Start database and supporting services
      cd docker
      docker-compose up -d db redis pgadmin

      # Run database migrations
      cd ..
      alembic upgrade head

4. **Run Tests**

   .. code-block:: bash

      # Run the full test suite
      pytest

      # Run with coverage
      pytest --cov=app --cov-report=html

5. **Start Development Server**

   .. code-block:: bash

      # Start with hot reload
      uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

Development Workflow
~~~~~~~~~~~~~~~~~~~

1. **Create Feature Branch**

   .. code-block:: bash

      # Create and switch to feature branch
      git checkout -b feature/your-feature-name

      # Use descriptive branch names:
      # feature/exercise-database
      # bugfix/auth-token-refresh
      # docs/api-documentation

2. **Make Changes**
   - Follow the coding standards outlined below
   - Write tests for new functionality
   - Update documentation as needed
   - Ensure all tests pass

3. **Commit Changes**

   .. code-block:: bash

      # Stage your changes
      git add .

      # Commit with descriptive message
      git commit -m "feat: add exercise database with search functionality"

4. **Push and Create Pull Request**

   .. code-block:: bash

      # Push to your fork
      git push origin feature/your-feature-name

      # Create pull request on GitHub

📝 Coding Standards
-------------------

Code Style
~~~~~~~~~~

We use automated tools to maintain consistent code style:

**Python Code Formatting**

.. code-block:: bash

   # Format code with Black
   black .

   # Sort imports with isort
   isort .

   # Run all formatting tools
   make format

**Type Hints**
All functions and classes must include complete type annotations:

.. code-block:: python

   from typing import Optional, List
   from uuid import UUID

   def get_user_by_id(user_id: UUID) -> Optional[User]:
       """Retrieve user by ID."""
       return user_repository.get_by_id(user_id)

   async def create_workout(
       user_id: UUID,
       exercises: List[ExerciseSelection]
   ) -> Workout:
       """Create new workout session."""
       # Implementation here

**Docstrings**
Use Google-style docstrings for all public functions and classes:

.. code-block:: python

   def calculate_mev(
       user_profile: UserProfile,
       muscle_group: str
   ) -> float:
       """Calculate Minimum Effective Volume for user and muscle group.
       
       Args:
           user_profile: User's training history and characteristics
           muscle_group: Target muscle group for MEV calculation
           
       Returns:
           Estimated MEV in sets per week
           
       Raises:
           ValueError: If muscle group is not supported
           
       Example:
           >>> profile = UserProfile(training_years=3, age=25)
           >>> mev = calculate_mev(profile, "chest")
           >>> print(f"MEV for chest: {mev} sets/week")
       """

Architecture Guidelines
~~~~~~~~~~~~~~~~~~~~~~

**Clean Architecture Principles**
- Respect layer boundaries and dependency rules
- Domain layer should not depend on infrastructure
- Use dependency injection for external dependencies
- Keep business logic in domain services

**Repository Pattern**

.. code-block:: python

   # Abstract repository interface (domain layer)
   class UserRepository(ABC):
       @abstractmethod
       async def create(self, user: User) -> User:
           """Create new user."""
           
       @abstractmethod
       async def get_by_id(self, user_id: UUID) -> Optional[User]:
           """Get user by ID."""

   # Concrete implementation (infrastructure layer)
   class SQLUserRepository(UserRepository):
       async def create(self, user: User) -> User:
           # Database implementation
           pass

**Error Handling**

.. code-block:: python

   # Domain exceptions
   class UserNotFoundError(DomainException):
       """Raised when user cannot be found."""
       
   class InvalidCredentialsError(DomainException):
       """Raised when authentication fails."""

   # Use specific exceptions
   async def authenticate_user(email: str, password: str) -> User:
       user = await user_repository.get_by_email(email)
       if not user:
           raise UserNotFoundError(f"User with email {email} not found")
       
       if not verify_password(password, user.hashed_password):
           raise InvalidCredentialsError("Invalid password")
       
       return user

🧪 Testing Guidelines
---------------------

Test Categories
~~~~~~~~~~~~~~~

**Unit Tests**
Test individual components in isolation:

.. code-block:: python

   # tests/unit/test_mev_calculator.py
   import pytest
   from app.domain.services.mev_calculator import MEVCalculator

   class TestMEVCalculator:
       def test_calculate_mev_beginner(self):
           """Test MEV calculation for beginner user."""
           calculator = MEVCalculator()
           profile = UserProfile(training_years=0.5, age=25)
           
           mev = calculator.calculate_mev(profile, "chest")
           
           assert 6 <= mev <= 8  # Expected range for beginners

**Integration Tests**
Test component interactions:

.. code-block:: python

   # tests/integration/test_auth_endpoints.py
   import pytest
   from httpx import AsyncClient

   @pytest.mark.asyncio
   async def test_user_registration_flow(client: AsyncClient):
       """Test complete user registration flow."""
       # Register user
       response = await client.post("/api/v1/auth/register", json={
           "email": "<EMAIL>",
           "password": "SecurePassword123!",
           "first_name": "Test",
           "last_name": "User"
       })
       
       assert response.status_code == 201
       user_data = response.json()
       assert user_data["email"] == "<EMAIL>"

**Behavioral Tests**
Test user scenarios with Gherkin:

.. code-block:: gherkin

   # tests/behavioral/features/user_registration.feature
   Feature: User Registration
     As a new user
     I want to register for an account
     So that I can access the training platform

     Scenario: Successful registration with valid data
       Given I am on the registration page
       When I enter valid registration details
       And I submit the registration form
       Then I should receive a success confirmation
       And my account should be created in the system

Test Requirements
~~~~~~~~~~~~~~~~

**Coverage Requirements**
- Minimum 90% overall test coverage
- 100% coverage for critical paths (authentication, algorithms)
- All public methods must have tests
- Edge cases and error conditions must be tested

**Test Naming**
Use descriptive test names that explain the scenario:

.. code-block:: python

   def test_mev_calculation_increases_with_training_experience():
       """MEV should increase for users with more training experience."""
       
   def test_authentication_fails_with_invalid_password():
       """Authentication should fail when password is incorrect."""
       
   def test_workout_creation_requires_authenticated_user():
       """Workout creation should require valid authentication."""

**Test Data**
Use factories for test data creation:

.. code-block:: python

   # tests/factories.py
   import factory
   from app.domain.entities.user import User

   class UserFactory(factory.Factory):
       class Meta:
           model = User
       
       email = factory.Sequence(lambda n: f"user{n}@example.com")
       first_name = "Test"
       last_name = "User"
       training_experience_years = 2
       is_active = True

   # Usage in tests
   def test_user_profile_update():
       user = UserFactory()
       # Test implementation

📚 Documentation Standards
--------------------------

Documentation Types
~~~~~~~~~~~~~~~~~~~

**API Documentation**
- All endpoints must have OpenAPI documentation
- Include request/response examples
- Document error responses and status codes
- Provide usage examples in multiple languages

**Code Documentation**
- Comprehensive docstrings for all public APIs
- Inline comments for complex algorithms
- Architecture decision records (ADRs) for major decisions
- README files for each major component

**User Documentation**
- Getting started guides
- API reference with examples
- Integration guides for common use cases
- Troubleshooting and FAQ sections

Documentation Guidelines
~~~~~~~~~~~~~~~~~~~~~~~

**Writing Style**
- Use clear, concise language
- Write for your audience (developers, users, admins)
- Include practical examples and code snippets
- Use British English spelling consistently

**Structure**
- Start with overview and key concepts
- Provide step-by-step instructions
- Include troubleshooting sections
- Add cross-references to related topics

**Code Examples**
- Test all code examples before publishing
- Include complete, runnable examples
- Show both success and error cases
- Provide examples in multiple programming languages

🔬 Scientific Contributions
---------------------------

Algorithm Development
~~~~~~~~~~~~~~~~~~~~

**Research Requirements**
- All algorithms must be based on peer-reviewed research
- Include citations and references for scientific claims
- Document assumptions and limitations
- Provide validation methodology

**Implementation Standards**
- Include mathematical formulations in documentation
- Implement comprehensive unit tests for algorithms
- Validate against known test cases
- Document parameter tuning and sensitivity analysis

**Review Process**
- Scientific review by qualified exercise physiologists
- Code review by senior developers
- Validation testing with real-world data
- Performance benchmarking and optimization

Research Contributions
~~~~~~~~~~~~~~~~~~~~~

**Literature Reviews**
- Systematic review of relevant research
- Critical analysis of methodology and findings
- Identification of research gaps and opportunities
- Regular updates as new research emerges

**Validation Studies**
- Design and conduct validation studies
- Statistical analysis of algorithm performance
- Comparison with existing methods
- Publication of findings in peer-reviewed journals

🐛 Bug Reports and Issues
-------------------------

Reporting Bugs
~~~~~~~~~~~~~~

When reporting bugs, please include:

**Environment Information**
- Operating system and version
- Python version and virtual environment
- Docker version (if applicable)
- Browser and version (for web interface issues)

**Reproduction Steps**
1. Clear steps to reproduce the issue
2. Expected behavior vs actual behavior
3. Screenshots or error messages
4. Minimal code example (if applicable)

**Additional Context**
- When did the issue first occur?
- Does it happen consistently?
- Any recent changes to your setup?
- Relevant log files or error traces

Feature Requests
~~~~~~~~~~~~~~~

For feature requests, please provide:

**Use Case Description**
- What problem does this solve?
- Who would benefit from this feature?
- How does it align with project goals?

**Proposed Solution**
- Detailed description of the feature
- User interface mockups (if applicable)
- Technical implementation considerations
- Alternative solutions considered

**Acceptance Criteria**
- Clear definition of "done"
- Success metrics and validation criteria
- Performance and security requirements
- Documentation and testing requirements

🎉 Recognition
--------------

Contributors
~~~~~~~~~~~~

We recognize and appreciate all forms of contribution:

**Code Contributors**
- Feature development and bug fixes
- Performance optimizations
- Security improvements
- Test coverage enhancements

**Documentation Contributors**
- API documentation improvements
- User guide enhancements
- Translation and localization
- Video tutorials and examples

**Scientific Contributors**
- Algorithm development and validation
- Research reviews and analysis
- Expert consultation and review
- Educational content creation

**Community Contributors**
- Issue reporting and triage
- Community support and mentoring
- Event organization and speaking
- Feedback and user experience testing

Attribution
~~~~~~~~~~~

All contributors are recognized in:
- Project README and documentation
- Release notes and changelogs
- Annual contributor reports
- Conference presentations and papers

📞 Getting Help
---------------

Communication Channels
~~~~~~~~~~~~~~~~~~~~~~

**GitHub Issues**
- Bug reports and feature requests
- Technical discussions and questions
- Project planning and roadmap discussions

**Development Chat**
- Real-time development discussions
- Quick questions and clarifications
- Pair programming coordination

**Community Forum**
- User support and questions
- Best practices sharing
- Integration examples and tutorials

**Email Contact**
- Security vulnerability reports: <EMAIL>
- Partnership inquiries: <EMAIL>
- General questions: <EMAIL>

Office Hours
~~~~~~~~~~~~

**Weekly Developer Office Hours**
- Tuesdays 2:00-3:00 PM UTC
- Open discussion and Q&A
- Code review and architecture discussions

**Monthly Community Calls**
- First Friday of each month, 3:00-4:00 PM UTC
- Project updates and roadmap discussions
- Community showcase and feedback

Thank you for contributing to Forge Protocol! Together, we're building the future of evidence-based training technology. 🏋️‍♂️
