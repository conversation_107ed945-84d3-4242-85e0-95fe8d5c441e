Development Setup
=================

This guide will help you set up a complete development environment for the RP Training API. We provide multiple setup options to accommodate different development preferences and environments.

Prerequisites
-------------

Before setting up the development environment, ensure you have the following installed:

**Required**:
- **Git**: Version control system
- **Python 3.11+**: Programming language runtime
- **Internet Connection**: For downloading dependencies

**Recommended**:
- **Nix Package Manager**: For reproducible development environments
- **VS Code**: Recommended IDE with excellent Python support
- **Docker**: For containerized development (optional)

Quick Start (Nix - Recommended)
--------------------------------

The fastest way to get started is using Nix, which provides a completely reproducible development environment.

1. **Install Nix** (if not already installed):

   .. code-block:: bash

      # On Linux/macOS
      curl -L https://nixos.org/nix/install | sh

      # Follow the installation instructions and restart your shell

2. **Clone the Repository**:

   .. code-block:: bash

      git clone https://github.com/forkrul/forge-protocol.git
      cd forge-protocol

3. **Enter Development Environment**:

   .. code-block:: bash

      nix-shell

   This command will:
   - Install Python 3.11 and all required dependencies
   - Set up the development environment
   - Configure environment variables
   - Display helpful information about available commands

4. **Verify Installation**:

   .. code-block:: bash

      # Check Python version
      python --version  # Should show Python 3.11.x

      # Check FastAPI installation
      python -c "import fastapi; print(f'FastAPI {fastapi.__version__}')"

      # Run tests to verify everything works
      python -m pytest tests/unit/ -v

5. **Start the Development Server**:

   .. code-block:: bash

      python -m uvicorn app.main:app --reload

   The API will be available at:
   - **API**: http://localhost:8000
   - **Interactive Docs**: http://localhost:8000/docs
   - **ReDoc**: http://localhost:8000/redoc

Alternative Setup (pip/venv)
----------------------------

If you prefer not to use Nix, you can set up the environment using standard Python tools.

1. **Clone the Repository**:

   .. code-block:: bash

      git clone https://github.com/forkrul/forge-protocol.git
      cd forge-protocol

2. **Create Virtual Environment**:

   .. code-block:: bash

      # Create virtual environment
      python -m venv venv

      # Activate virtual environment
      # On Linux/macOS:
      source venv/bin/activate
      # On Windows:
      venv\Scripts\activate

3. **Install Dependencies**:

   .. code-block:: bash

      # Upgrade pip
      pip install --upgrade pip

      # Install project dependencies
      pip install -r requirements.txt

      # Install development dependencies
      pip install -r requirements-dev.txt

4. **Set Up Environment Variables**:

   .. code-block:: bash

      # Copy environment template
      cp .env.example .env

      # Edit .env file with your configuration
      # Most defaults work for local development

5. **Initialize Database**:

   .. code-block:: bash

      # Run database migrations
      python -m alembic upgrade head

6. **Verify Installation**:

   .. code-block:: bash

      # Run tests
      python -m pytest tests/unit/ -v

      # Start development server
      python -m uvicorn app.main:app --reload

Development Environment Configuration
-------------------------------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

The application uses environment variables for configuration. Copy ``.env.example`` to ``.env`` and modify as needed:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=sqlite:///./app.db

   # JWT Configuration
   SECRET_KEY=your-secret-key-here
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7

   # Application Configuration
   DEBUG=true
   TESTING=false
   LOG_LEVEL=INFO

   # CORS Configuration
   ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

Database Setup
~~~~~~~~~~~~~~

The application uses SQLite for development and PostgreSQL for production.

**SQLite (Development)**:

.. code-block:: bash

   # Database file will be created automatically
   # Location: ./app.db (configurable via DATABASE_URL)

   # Run migrations
   python -m alembic upgrade head

   # Reset database (if needed)
   rm app.db
   python -m alembic upgrade head

**PostgreSQL (Production)**:

.. code-block:: bash

   # Install PostgreSQL
   # Update DATABASE_URL in .env:
   DATABASE_URL=postgresql://user:password@localhost/rptraining

   # Run migrations
   python -m alembic upgrade head

IDE Configuration
-----------------

Visual Studio Code (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Install VS Code Extensions**:

   .. code-block:: json

      {
        "recommendations": [
          "ms-python.python",
          "ms-python.black-formatter",
          "ms-python.isort",
          "ms-python.mypy-type-checker",
          "ms-python.pylint",
          "ms-toolsai.jupyter",
          "redhat.vscode-yaml",
          "ms-vscode.vscode-json"
        ]
      }

2. **Configure Python Interpreter**:

   - Open Command Palette (Ctrl+Shift+P)
   - Type "Python: Select Interpreter"
   - Choose the Python interpreter from your virtual environment

3. **Configure Settings** (``.vscode/settings.json``):

   .. code-block:: json

      {
        "python.defaultInterpreterPath": "./venv/bin/python",
        "python.formatting.provider": "black",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": true,
        "python.linting.mypyEnabled": true,
        "python.testing.pytestEnabled": true,
        "python.testing.pytestArgs": ["tests"],
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true
        }
      }

4. **Configure Launch Configuration** (``.vscode/launch.json``):

   .. code-block:: json

      {
        "version": "0.2.0",
        "configurations": [
          {
            "name": "FastAPI Development Server",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
          },
          {
            "name": "Run Tests",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["tests/", "-v"],
            "console": "integratedTerminal"
          }
        ]
      }

PyCharm Configuration
~~~~~~~~~~~~~~~~~~~~~

1. **Open Project**: File → Open → Select project directory
2. **Configure Interpreter**: Settings → Project → Python Interpreter → Add → Existing environment
3. **Configure Run Configuration**:
   - Run → Edit Configurations
   - Add new Python configuration
   - Script path: Select uvicorn module
   - Parameters: ``app.main:app --reload``

Development Workflow
--------------------

Daily Development
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Start development environment
   nix-shell  # or activate venv

   # 2. Pull latest changes
   git pull origin main

   # 3. Run migrations (if any)
   python -m alembic upgrade head

   # 4. Start development server
   python -m uvicorn app.main:app --reload

   # 5. Run tests during development
   python -m pytest tests/unit/ -x --ff

Code Quality Checks
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Format code
   black .
   isort .

   # Type checking
   mypy app/

   # Linting
   flake8 .

   # Run all quality checks
   python scripts/quality_check.py

Testing During Development
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Quick unit tests
   python -m pytest tests/unit/ -x --ff

   # Integration tests
   python -m pytest tests/integration/ -v

   # BDD tests
   python run_bdd_tests.py --tags @smoke

   # Full test suite with coverage
   python -m pytest tests/ --cov=app --cov-report=term-missing

Git Workflow
~~~~~~~~~~~~

.. code-block:: bash

   # Create feature branch
   git checkout -b feature/new-feature

   # Make changes and commit
   git add .
   git commit -m "feat: add new feature"

   # Run tests before pushing
   python -m pytest tests/
   python run_bdd_tests.py

   # Push changes
   git push origin feature/new-feature

   # Create pull request on GitHub

Available Commands
------------------

The development environment provides several helpful commands:

**Testing Commands**:

.. code-block:: bash

   # Run all tests
   python scripts/run_tests.py

   # Run specific test categories
   python -m pytest tests/unit/                    # Unit tests
   python -m pytest tests/integration/             # Integration tests
   python run_bdd_tests.py                         # BDD tests

   # Run tests with coverage
   python -m pytest --cov=app --cov-report=html

**Code Quality Commands**:

.. code-block:: bash

   # Format code
   black .                                          # Code formatting
   isort .                                          # Import sorting

   # Check code quality
   flake8 .                                         # Linting
   mypy app/                                        # Type checking

   # Run all quality checks
   python scripts/quality_check.py

**Database Commands**:

.. code-block:: bash

   # Database migrations
   python -m alembic upgrade head                   # Apply migrations
   python -m alembic downgrade -1                  # Rollback one migration
   python -m alembic revision --autogenerate -m "description"  # Create migration

**Development Server Commands**:

.. code-block:: bash

   # Start development server
   python -m uvicorn app.main:app --reload         # With auto-reload
   python -m uvicorn app.main:app --reload --port 8080  # Custom port

   # Start with debugging
   python -m uvicorn app.main:app --reload --log-level debug

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Issue**: ``ModuleNotFoundError: No module named 'app'``

**Solution**:

.. code-block:: bash

   # Ensure you're in the project root directory
   pwd  # Should show /path/to/forge-protocol

   # Ensure Python path is correct
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"

**Issue**: Database connection errors

**Solution**:

.. code-block:: bash

   # Check database file exists
   ls -la app.db

   # Recreate database
   rm app.db
   python -m alembic upgrade head

**Issue**: Import errors in tests

**Solution**:

.. code-block:: bash

   # Install in development mode
   pip install -e .

**Issue**: Port already in use

**Solution**:

.. code-block:: bash

   # Find process using port 8000
   lsof -i :8000

   # Kill the process
   kill -9 <PID>

   # Or use different port
   python -m uvicorn app.main:app --reload --port 8080

Getting Help
~~~~~~~~~~~~

**Documentation**:
- API Documentation: http://localhost:8000/docs
- This documentation: ``docs/`` directory
- README.md: Project overview and quick start

**Community**:
- GitHub Issues: Report bugs and request features
- GitHub Discussions: Ask questions and share ideas

**Development Resources**:
- FastAPI Documentation: https://fastapi.tiangolo.com/
- SQLAlchemy Documentation: https://docs.sqlalchemy.org/
- pytest Documentation: https://docs.pytest.org/

Next Steps
----------

After setting up your development environment:

1. **Explore the Codebase**: Start with ``app/main.py`` and follow the imports
2. **Run the Tests**: Understand the testing strategy and patterns
3. **Read the Documentation**: Familiarize yourself with the architecture
4. **Make a Small Change**: Try adding a simple feature or fixing a bug
5. **Contribute**: Submit your first pull request

**Useful Starting Points**:
- :doc:`../architecture/overview` - Understand the system architecture
- :doc:`../testing/overview` - Learn about the testing strategy
- :doc:`contributing` - Guidelines for contributing to the project
- :doc:`../api/endpoints` - API reference documentation
