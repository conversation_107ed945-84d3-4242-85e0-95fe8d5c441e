#!/usr/bin/env python3
"""
BDD Test Runner for RP Training API

This script provides a convenient way to run Behave BDD tests
with various options and configurations.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def setup_environment():
    """Set up the test environment."""
    # Add the project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    # Set environment variables for testing
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    
    # Create reports directory if it doesn't exist
    reports_dir = project_root / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    return project_root


def run_behave_tests(args):
    """Run Behave tests with specified arguments."""
    project_root = setup_environment()
    
    # Build behave command
    cmd = ["python", "-m", "behave"]
    
    # Add format options
    if args.format:
        cmd.extend(["--format", args.format])
    
    # Add tag filters
    if args.tags:
        cmd.extend(["--tags", args.tags])
    
    # Add specific features
    if args.features:
        for feature in args.features:
            cmd.append(f"features/{feature}")
    else:
        cmd.append("features")
    
    # Add dry run option
    if args.dry_run:
        cmd.append("--dry-run")
    
    # Add stop on first failure
    if args.stop:
        cmd.append("--stop")
    
    # Add verbose output
    if args.verbose:
        cmd.extend(["--verbose"])
    
    # Add no capture option for debugging
    if args.no_capture:
        cmd.append("--no-capture")
    
    # Add junit output
    if args.junit:
        cmd.extend(["--junit", "--junit-directory", "reports/junit"])
    
    # Add custom user data
    if args.userdata:
        for data in args.userdata:
            cmd.extend(["-D", data])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run the command
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTest execution interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(
        description="Run BDD tests for RP Training API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run all tests
  python run_bdd_tests.py
  
  # Run only authentication tests
  python run_bdd_tests.py --tags @authentication
  
  # Run smoke tests with JUnit output
  python run_bdd_tests.py --tags @smoke --junit
  
  # Run specific feature
  python run_bdd_tests.py --features user_authentication.feature
  
  # Run tests with verbose output
  python run_bdd_tests.py --verbose --no-capture
  
  # Dry run to check syntax
  python run_bdd_tests.py --dry-run
  
  # Run performance tests
  python run_bdd_tests.py --tags @performance
  
  # Run security tests
  python run_bdd_tests.py --tags @security
  
  # Run critical tests only
  python run_bdd_tests.py --tags @critical
  
  # Exclude work-in-progress tests
  python run_bdd_tests.py --tags "not @wip"
  
  # Run authentication and profile tests
  python run_bdd_tests.py --tags "@authentication or @profile"
        """
    )
    
    parser.add_argument(
        "--tags", "-t",
        help="Tag expression to filter scenarios (e.g., '@smoke', '@auth and @api')"
    )
    
    parser.add_argument(
        "--features", "-f",
        nargs="+",
        help="Specific feature files to run (e.g., user_authentication.feature)"
    )
    
    parser.add_argument(
        "--format",
        choices=["pretty", "plain", "json", "junit", "progress"],
        default="pretty",
        help="Output format for test results"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run to check syntax without executing tests"
    )
    
    parser.add_argument(
        "--stop",
        action="store_true",
        help="Stop on first failure"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-capture",
        action="store_true",
        help="Don't capture stdout/stderr (useful for debugging)"
    )
    
    parser.add_argument(
        "--junit",
        action="store_true",
        help="Generate JUnit XML reports"
    )
    
    parser.add_argument(
        "--userdata", "-D",
        action="append",
        help="Define user data (e.g., -D api_url=http://localhost:8000)"
    )
    
    # Predefined test suites
    parser.add_argument(
        "--smoke",
        action="store_true",
        help="Run smoke tests (equivalent to --tags @smoke)"
    )
    
    parser.add_argument(
        "--regression",
        action="store_true",
        help="Run regression tests (equivalent to --tags @regression)"
    )
    
    parser.add_argument(
        "--performance",
        action="store_true",
        help="Run performance tests (equivalent to --tags @performance)"
    )
    
    parser.add_argument(
        "--security",
        action="store_true",
        help="Run security tests (equivalent to --tags @security)"
    )
    
    args = parser.parse_args()
    
    # Handle predefined test suites
    if args.smoke:
        args.tags = "@smoke"
    elif args.regression:
        args.tags = "@regression"
    elif args.performance:
        args.tags = "@performance"
    elif args.security:
        args.tags = "@security"
    
    # Run the tests
    exit_code = run_behave_tests(args)
    
    # Print summary
    if exit_code == 0:
        print("\n" + "=" * 60)
        print("🎉 All BDD tests passed successfully!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ Some BDD tests failed or encountered errors")
        print("=" * 60)
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
