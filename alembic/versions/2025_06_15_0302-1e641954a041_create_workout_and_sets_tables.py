"""create_workout_and_sets_tables

Revision ID: 1e641954a041
Revises: 43b684c685fe
Create Date: 2025-06-15 03:02:29.128429

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1e641954a041"
down_revision: Union[str, None] = "43b684c685fe"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("email", sa.String(length=255), nullable=False),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column("first_name", sa.String(length=100), nullable=True),
        sa.Column("last_name", sa.String(length=100), nullable=True),
        sa.Column("training_experience_years", sa.Integer(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_verified", sa.Boolean(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_users_active",
        "users",
        ["is_active"],
        unique=False,
        postgresql_where="deleted_at IS NULL",
    )
    op.create_index(
        "idx_users_email_active", "users", ["email", "is_active"], unique=False
    )
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_table(
        "exercises",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("exercise_uuid", sa.UUID(), nullable=False),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("is_current_version", sa.Boolean(), nullable=False),
        sa.Column("parent_version_id", sa.UUID(), nullable=True),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "primary_muscle_group",
            sa.Enum(
                "chest",
                "back",
                "shoulders",
                "biceps",
                "triceps",
                "quads",
                "hamstrings",
                "glutes",
                "calves",
                "abs",
                "forearms",
                "traps",
                "lats",
                "rear_delts",
                "side_delts",
                name="muscle_group_enum",
            ),
            nullable=False,
        ),
        sa.Column("secondary_muscle_groups", sa.JSON(), nullable=True),
        sa.Column(
            "movement_pattern",
            sa.Enum(
                "push",
                "pull",
                "squat",
                "hinge",
                "carry",
                "isolation",
                "rotation",
                "anti_extension",
                "anti_flexion",
                "anti_rotation",
                name="movement_pattern_enum",
            ),
            nullable=False,
        ),
        sa.Column("equipment_required", sa.JSON(), nullable=True),
        sa.Column(
            "difficulty_level",
            sa.Enum("beginner", "intermediate", "advanced", name="difficulty_enum"),
            nullable=False,
        ),
        sa.Column("video_url", sa.String(length=500), nullable=True),
        sa.Column("thumbnail_url", sa.String(length=500), nullable=True),
        sa.Column("form_cues", sa.JSON(), nullable=True),
        sa.Column("setup_instructions", sa.Text(), nullable=True),
        sa.Column("execution_steps", sa.JSON(), nullable=True),
        sa.Column("common_mistakes", sa.JSON(), nullable=True),
        sa.Column("safety_notes", sa.Text(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_approved", sa.Boolean(), nullable=False),
        sa.Column(
            "approval_status",
            sa.Enum(
                "pending",
                "approved",
                "rejected",
                "needs_review",
                name="approval_status_enum",
            ),
            nullable=False,
        ),
        sa.Column("created_by", sa.UUID(), nullable=True),
        sa.Column("updated_by", sa.UUID(), nullable=True),
        sa.Column("approved_by", sa.UUID(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("approved_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("version_notes", sa.Text(), nullable=True),
        sa.Column(
            "change_reason",
            sa.Enum(
                "initial_creation",
                "content_update",
                "correction",
                "media_update",
                "approval_change",
                "user_request",
                name="change_reason_enum",
            ),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["approved_by"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["created_by"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_version_id"],
            ["exercises.id"],
        ),
        sa.ForeignKeyConstraint(
            ["updated_by"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "exercise_uuid", "is_current_version", name="uq_exercise_current_version"
        ),
        sa.UniqueConstraint(
            "exercise_uuid", "version", name="uq_exercise_version_number"
        ),
    )
    op.create_index(
        "idx_exercises_active_not_deleted", "exercises", ["is_active"], unique=False
    )
    op.create_index(
        "idx_exercises_approval",
        "exercises",
        ["approval_status", "is_active"],
        unique=False,
    )
    op.create_index(
        "idx_exercises_muscle_active",
        "exercises",
        ["primary_muscle_group", "is_active"],
        unique=False,
    )
    op.create_index(
        "idx_exercises_pattern_difficulty",
        "exercises",
        ["movement_pattern", "difficulty_level"],
        unique=False,
    )
    op.create_index("idx_exercises_search", "exercises", ["name"], unique=False)
    op.create_index(
        "idx_exercises_uuid_current",
        "exercises",
        ["exercise_uuid", "is_current_version"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercises_approval_status"),
        "exercises",
        ["approval_status"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercises_created_at"), "exercises", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_exercises_deleted_at"), "exercises", ["deleted_at"], unique=False
    )
    op.create_index(
        op.f("ix_exercises_difficulty_level"),
        "exercises",
        ["difficulty_level"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercises_exercise_uuid"), "exercises", ["exercise_uuid"], unique=False
    )
    op.create_index(
        op.f("ix_exercises_is_active"), "exercises", ["is_active"], unique=False
    )
    op.create_index(
        op.f("ix_exercises_is_approved"), "exercises", ["is_approved"], unique=False
    )
    op.create_index(
        op.f("ix_exercises_is_current_version"),
        "exercises",
        ["is_current_version"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercises_movement_pattern"),
        "exercises",
        ["movement_pattern"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercises_primary_muscle_group"),
        "exercises",
        ["primary_muscle_group"],
        unique=False,
    )
    op.create_table(
        "refresh_tokens",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("token_hash", sa.String(length=255), nullable=False),
        sa.Column("expires_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("is_revoked", sa.Boolean(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_refresh_tokens_expires", "refresh_tokens", ["expires_at"], unique=False
    )
    op.create_index(
        "idx_refresh_tokens_user_active",
        "refresh_tokens",
        ["user_id", "is_revoked"],
        unique=False,
    )
    op.create_index(
        op.f("ix_refresh_tokens_expires_at"),
        "refresh_tokens",
        ["expires_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_refresh_tokens_token_hash"),
        "refresh_tokens",
        ["token_hash"],
        unique=False,
    )
    op.create_index(
        op.f("ix_refresh_tokens_user_id"), "refresh_tokens", ["user_id"], unique=False
    )
    op.create_table(
        "workouts",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=True),
        sa.Column("workout_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "total_volume_load", sa.Numeric(precision=10, scale=2), nullable=True
        ),
        sa.Column("average_rpe", sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column("total_sets", sa.Integer(), nullable=False),
        sa.Column("total_exercises", sa.Integer(), nullable=False),
        sa.Column("duration_minutes", sa.Integer(), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("is_template", sa.Boolean(), nullable=False),
        sa.Column("template_name", sa.String(length=255), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_workouts_created_at"), "workouts", ["created_at"], unique=False
    )
    op.create_index(
        op.f("ix_workouts_is_template"), "workouts", ["is_template"], unique=False
    )
    op.create_index(op.f("ix_workouts_user_id"), "workouts", ["user_id"], unique=False)
    op.create_index(
        op.f("ix_workouts_workout_date"), "workouts", ["workout_date"], unique=False
    )
    op.create_table(
        "exercise_audit_log",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("exercise_id", sa.UUID(), nullable=False),
        sa.Column("exercise_uuid", sa.UUID(), nullable=False),
        sa.Column(
            "action",
            sa.Enum(
                "created",
                "updated",
                "deleted",
                "restored",
                "approved",
                "rejected",
                "version_created",
                name="audit_action_enum",
            ),
            nullable=False,
        ),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("ip_address", sa.String(length=45), nullable=True),
        sa.Column("user_agent", sa.Text(), nullable=True),
        sa.Column("field_changes", sa.JSON(), nullable=True),
        sa.Column("old_values", sa.JSON(), nullable=True),
        sa.Column("new_values", sa.JSON(), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["exercise_id"], ["exercises.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_audit_log_exercise_action",
        "exercise_audit_log",
        ["exercise_id", "action"],
        unique=False,
    )
    op.create_index(
        "idx_audit_log_user_date",
        "exercise_audit_log",
        ["user_id", "created_at"],
        unique=False,
    )
    op.create_index(
        "idx_audit_log_uuid_date",
        "exercise_audit_log",
        ["exercise_uuid", "created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_audit_log_action"),
        "exercise_audit_log",
        ["action"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_audit_log_created_at"),
        "exercise_audit_log",
        ["created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_audit_log_exercise_id"),
        "exercise_audit_log",
        ["exercise_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_audit_log_exercise_uuid"),
        "exercise_audit_log",
        ["exercise_uuid"],
        unique=False,
    )
    op.create_table(
        "exercise_media",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("exercise_id", sa.UUID(), nullable=False),
        sa.Column(
            "media_type",
            sa.Enum("video", "image", "gif", "audio", name="media_type_enum"),
            nullable=False,
        ),
        sa.Column("url", sa.String(length=500), nullable=False),
        sa.Column("thumbnail_url", sa.String(length=500), nullable=True),
        sa.Column("title", sa.String(length=255), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("file_size_bytes", sa.Integer(), nullable=True),
        sa.Column("duration_seconds", sa.Integer(), nullable=True),
        sa.Column("width_pixels", sa.Integer(), nullable=True),
        sa.Column("height_pixels", sa.Integer(), nullable=True),
        sa.Column("mime_type", sa.String(length=100), nullable=True),
        sa.Column("sort_order", sa.Integer(), nullable=False),
        sa.Column("is_primary", sa.Boolean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["exercise_id"], ["exercises.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_exercise_media_exercise_type",
        "exercise_media",
        ["exercise_id", "media_type"],
        unique=False,
    )
    op.create_index(
        "idx_exercise_media_primary",
        "exercise_media",
        ["exercise_id", "is_primary"],
        unique=False,
    )
    op.create_index(
        "idx_exercise_media_sort",
        "exercise_media",
        ["exercise_id", "sort_order"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_media_exercise_id"),
        "exercise_media",
        ["exercise_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_media_is_primary"),
        "exercise_media",
        ["is_primary"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_media_media_type"),
        "exercise_media",
        ["media_type"],
        unique=False,
    )
    op.create_table(
        "exercise_sets",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("workout_id", sa.UUID(), nullable=False),
        sa.Column("exercise_id", sa.UUID(), nullable=False),
        sa.Column("set_number", sa.Integer(), nullable=False),
        sa.Column("weight_kg", sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column("reps_completed", sa.Integer(), nullable=True),
        sa.Column("reps_target", sa.Integer(), nullable=True),
        sa.Column("rir_target", sa.Integer(), nullable=True),
        sa.Column("rir_actual", sa.Integer(), nullable=True),
        sa.Column("rpe", sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column("rest_seconds", sa.Integer(), nullable=True),
        sa.Column("tempo", sa.String(length=20), nullable=True),
        sa.Column("range_of_motion", sa.String(length=50), nullable=True),
        sa.Column("is_warmup", sa.Boolean(), nullable=False),
        sa.Column("is_dropset", sa.Boolean(), nullable=False),
        sa.Column("is_failure", sa.Boolean(), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["exercise_id"],
            ["exercises.id"],
        ),
        sa.ForeignKeyConstraint(["workout_id"], ["workouts.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_exercise_sets_created_at"),
        "exercise_sets",
        ["created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_sets_exercise_id"),
        "exercise_sets",
        ["exercise_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_exercise_sets_workout_id"),
        "exercise_sets",
        ["workout_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_exercise_sets_workout_id"), table_name="exercise_sets")
    op.drop_index(op.f("ix_exercise_sets_exercise_id"), table_name="exercise_sets")
    op.drop_index(op.f("ix_exercise_sets_created_at"), table_name="exercise_sets")
    op.drop_table("exercise_sets")
    op.drop_index(op.f("ix_exercise_media_media_type"), table_name="exercise_media")
    op.drop_index(op.f("ix_exercise_media_is_primary"), table_name="exercise_media")
    op.drop_index(op.f("ix_exercise_media_exercise_id"), table_name="exercise_media")
    op.drop_index("idx_exercise_media_sort", table_name="exercise_media")
    op.drop_index("idx_exercise_media_primary", table_name="exercise_media")
    op.drop_index("idx_exercise_media_exercise_type", table_name="exercise_media")
    op.drop_table("exercise_media")
    op.drop_index(
        op.f("ix_exercise_audit_log_exercise_uuid"), table_name="exercise_audit_log"
    )
    op.drop_index(
        op.f("ix_exercise_audit_log_exercise_id"), table_name="exercise_audit_log"
    )
    op.drop_index(
        op.f("ix_exercise_audit_log_created_at"), table_name="exercise_audit_log"
    )
    op.drop_index(op.f("ix_exercise_audit_log_action"), table_name="exercise_audit_log")
    op.drop_index("idx_audit_log_uuid_date", table_name="exercise_audit_log")
    op.drop_index("idx_audit_log_user_date", table_name="exercise_audit_log")
    op.drop_index("idx_audit_log_exercise_action", table_name="exercise_audit_log")
    op.drop_table("exercise_audit_log")
    op.drop_index(op.f("ix_workouts_workout_date"), table_name="workouts")
    op.drop_index(op.f("ix_workouts_user_id"), table_name="workouts")
    op.drop_index(op.f("ix_workouts_is_template"), table_name="workouts")
    op.drop_index(op.f("ix_workouts_created_at"), table_name="workouts")
    op.drop_table("workouts")
    op.drop_index(op.f("ix_refresh_tokens_user_id"), table_name="refresh_tokens")
    op.drop_index(op.f("ix_refresh_tokens_token_hash"), table_name="refresh_tokens")
    op.drop_index(op.f("ix_refresh_tokens_expires_at"), table_name="refresh_tokens")
    op.drop_index("idx_refresh_tokens_user_active", table_name="refresh_tokens")
    op.drop_index("idx_refresh_tokens_expires", table_name="refresh_tokens")
    op.drop_table("refresh_tokens")
    op.drop_index(op.f("ix_exercises_primary_muscle_group"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_movement_pattern"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_is_current_version"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_is_approved"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_is_active"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_exercise_uuid"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_difficulty_level"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_deleted_at"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_created_at"), table_name="exercises")
    op.drop_index(op.f("ix_exercises_approval_status"), table_name="exercises")
    op.drop_index("idx_exercises_uuid_current", table_name="exercises")
    op.drop_index("idx_exercises_search", table_name="exercises")
    op.drop_index("idx_exercises_pattern_difficulty", table_name="exercises")
    op.drop_index("idx_exercises_muscle_active", table_name="exercises")
    op.drop_index("idx_exercises_approval", table_name="exercises")
    op.drop_index("idx_exercises_active_not_deleted", table_name="exercises")
    op.drop_table("exercises")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_index("idx_users_email_active", table_name="users")
    op.drop_index(
        "idx_users_active", table_name="users", postgresql_where="deleted_at IS NULL"
    )
    op.drop_table("users")
    # ### end Alembic commands ###
