"""create exercise database with versioning and soft delete

Revision ID: 43b684c685fe
Revises: 7d7825210c3f
Create Date: 2025-06-14 13:12:06.530155

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '43b684c685fe'
down_revision: Union[str, None] = '7d7825210c3f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create enums
    muscle_group_enum = sa.Enum(
        'chest', 'back', 'shoulders', 'biceps', 'triceps',
        'quads', 'hamstrings', 'glutes', 'calves', 'abs',
        'forearms', 'traps', 'lats', 'rear_delts', 'side_delts',
        name='muscle_group_enum'
    )
    movement_pattern_enum = sa.Enum(
        'push', 'pull', 'squat', 'hinge', 'carry', 'isolation',
        'rotation', 'anti_extension', 'anti_flexion', 'anti_rotation',
        name='movement_pattern_enum'
    )
    difficulty_enum = sa.Enum('beginner', 'intermediate', 'advanced', name='difficulty_enum')
    approval_status_enum = sa.Enum('pending', 'approved', 'rejected', 'needs_review', name='approval_status_enum')
    change_reason_enum = sa.Enum(
        'initial_creation', 'content_update', 'correction',
        'media_update', 'approval_change', 'user_request',
        name='change_reason_enum'
    )
    media_type_enum = sa.Enum('video', 'image', 'gif', 'audio', name='media_type_enum')
    audit_action_enum = sa.Enum(
        'created', 'updated', 'deleted', 'restored',
        'approved', 'rejected', 'version_created',
        name='audit_action_enum'
    )

    # Create exercises table
    op.create_table(
        'exercises',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('exercise_uuid', sa.String(36), nullable=False, index=True),
        sa.Column('version', sa.Integer, nullable=False, default=1),
        sa.Column('is_current_version', sa.Boolean, default=True, index=True),
        sa.Column('parent_version_id', sa.String(36), sa.ForeignKey('exercises.id'), nullable=True),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('primary_muscle_group', muscle_group_enum, nullable=False, index=True),
        sa.Column('secondary_muscle_groups', sa.JSON),
        sa.Column('movement_pattern', movement_pattern_enum, nullable=False, index=True),
        sa.Column('equipment_required', sa.JSON),
        sa.Column('difficulty_level', difficulty_enum, nullable=False, index=True),
        sa.Column('video_url', sa.String(500)),
        sa.Column('thumbnail_url', sa.String(500)),
        sa.Column('form_cues', sa.JSON),
        sa.Column('setup_instructions', sa.Text),
        sa.Column('execution_steps', sa.JSON),
        sa.Column('common_mistakes', sa.JSON),
        sa.Column('safety_notes', sa.Text),
        sa.Column('is_active', sa.Boolean, default=True, index=True),
        sa.Column('is_approved', sa.Boolean, default=False, index=True),
        sa.Column('approval_status', approval_status_enum, default='pending', index=True),
        sa.Column('created_by', sa.String(36), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('updated_by', sa.String(36), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('approved_by', sa.String(36), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), index=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, index=True),
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('version_notes', sa.Text),
        sa.Column('change_reason', change_reason_enum, default='initial_creation'),
    )

    # Create indexes for exercises table
    op.create_index('idx_exercises_uuid_current', 'exercises', ['exercise_uuid', 'is_current_version'])
    op.create_index('idx_exercises_muscle_active', 'exercises', ['primary_muscle_group', 'is_active'])
    op.create_index('idx_exercises_pattern_difficulty', 'exercises', ['movement_pattern', 'difficulty_level'])
    op.create_index('idx_exercises_approval', 'exercises', ['approval_status', 'is_active'])
    op.create_index('idx_exercises_search', 'exercises', ['name'])
    op.create_index('idx_exercises_active_not_deleted', 'exercises', ['is_active'])

    # Create unique constraints
    op.create_unique_constraint('uq_exercise_current_version', 'exercises', ['exercise_uuid', 'is_current_version'])
    op.create_unique_constraint('uq_exercise_version_number', 'exercises', ['exercise_uuid', 'version'])

    # Create exercise_media table
    op.create_table(
        'exercise_media',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('exercise_id', sa.String(36), sa.ForeignKey('exercises.id', ondelete='CASCADE'), nullable=False, index=True),
        sa.Column('media_type', media_type_enum, nullable=False, index=True),
        sa.Column('url', sa.String(500), nullable=False),
        sa.Column('thumbnail_url', sa.String(500)),
        sa.Column('title', sa.String(255)),
        sa.Column('description', sa.Text),
        sa.Column('file_size_bytes', sa.Integer),
        sa.Column('duration_seconds', sa.Integer),
        sa.Column('width_pixels', sa.Integer),
        sa.Column('height_pixels', sa.Integer),
        sa.Column('mime_type', sa.String(100)),
        sa.Column('sort_order', sa.Integer, default=0),
        sa.Column('is_primary', sa.Boolean, default=False, index=True),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
    )

    # Create indexes for exercise_media table
    op.create_index('idx_exercise_media_exercise_type', 'exercise_media', ['exercise_id', 'media_type'])
    op.create_index('idx_exercise_media_primary', 'exercise_media', ['exercise_id', 'is_primary'])
    op.create_index('idx_exercise_media_sort', 'exercise_media', ['exercise_id', 'sort_order'])

    # Create exercise_audit_log table
    op.create_table(
        'exercise_audit_log',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('exercise_id', sa.String(36), sa.ForeignKey('exercises.id', ondelete='CASCADE'), nullable=False, index=True),
        sa.Column('exercise_uuid', sa.String(36), nullable=False, index=True),
        sa.Column('action', audit_action_enum, nullable=False, index=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('ip_address', sa.String(45)),
        sa.Column('user_agent', sa.Text),
        sa.Column('field_changes', sa.JSON),
        sa.Column('old_values', sa.JSON),
        sa.Column('new_values', sa.JSON),
        sa.Column('notes', sa.Text),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), index=True),
    )

    # Create indexes for audit log table
    op.create_index('idx_audit_log_exercise_action', 'exercise_audit_log', ['exercise_id', 'action'])
    op.create_index('idx_audit_log_uuid_date', 'exercise_audit_log', ['exercise_uuid', 'created_at'])
    op.create_index('idx_audit_log_user_date', 'exercise_audit_log', ['user_id', 'created_at'])


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop tables in reverse order
    op.drop_table('exercise_audit_log')
    op.drop_table('exercise_media')
    op.drop_table('exercises')

    # Drop enums
    sa.Enum(name='audit_action_enum').drop(op.get_bind())
    sa.Enum(name='media_type_enum').drop(op.get_bind())
    sa.Enum(name='change_reason_enum').drop(op.get_bind())
    sa.Enum(name='approval_status_enum').drop(op.get_bind())
    sa.Enum(name='difficulty_enum').drop(op.get_bind())
    sa.Enum(name='movement_pattern_enum').drop(op.get_bind())
    sa.Enum(name='muscle_group_enum').drop(op.get_bind())
