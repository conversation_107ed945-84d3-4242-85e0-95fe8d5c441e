# Core dependencies for the RP Training API
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.4.0
pydantic[email]>=2.4.0

# Database
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Configuration
python-dotenv>=1.0.0

# HTTP Client
httpx>=0.25.0

# Caching
redis>=5.0.0

# Logging
structlog>=23.2.0
