# Testing-specific dependencies
-r base.txt

# Core testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.3.0  # Parallel test execution

# HTTP testing
httpx>=0.25.0

# BDD testing
behave>=1.2.6

# Database testing
pytest-postgresql>=5.0.0

# Factories for test data
factory-boy>=3.3.0

# Time mocking
freezegun>=1.2.0

# Security testing
bandit>=1.7.5

# Performance testing
locust>=2.17.0

# Container testing
docker>=6.1.0

# Load testing
aiohttp>=3.8.0

# Test reporting
pytest-html>=3.2.0
pytest-json-report>=1.5.0

# Chaos testing
chaos-engineering>=0.1.0
