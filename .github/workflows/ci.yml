name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: rp_password
          POSTGRES_USER: rp_user
          POSTGRES_DB: rp_training_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements/*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/test.txt
    
    - name: Set up environment variables
      run: |
        echo "SECRET_KEY=test-secret-key-for-ci-minimum-32-characters-long" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql+asyncpg://rp_user:rp_password@localhost:5432/rp_training_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        echo "TESTING=true" >> $GITHUB_ENV
    
    - name: Run code quality checks
      run: |
        # Format checking
        black --check .
        
        # Import sorting
        isort --check-only .
        
        # Linting
        flake8 .
        
        # Type checking
        mypy .
    
    - name: Test project structure
      run: |
        python scripts/test_structure.py
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=app --cov-report=xml --cov-report=term-missing
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ -v
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run security checks
      run: |
        # Check for security issues in code
        bandit -r app/ -f json -o bandit-report.json || true
        
        # Check for known security vulnerabilities in dependencies
        safety check --json --output safety-report.json || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      run: |
        docker build -f docker/Dockerfile.dev -t rp-training-api:test .
    
    - name: Test Docker image
      run: |
        # Start the container
        docker run -d --name test-container \
          -e SECRET_KEY=test-secret-key-for-ci-minimum-32-characters-long \
          -e DATABASE_URL=sqlite+aiosqlite:///:memory: \
          -p 8000:8000 \
          rp-training-api:test
        
        # Wait for container to start
        sleep 10
        
        # Test health endpoint
        curl -f http://localhost:8000/api/v1/health || exit 1
        
        # Clean up
        docker stop test-container
        docker rm test-container
