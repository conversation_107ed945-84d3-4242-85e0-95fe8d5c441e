## 📋 Pull Request Summary

**Brief description of changes**

## 🔗 Related Issues

Fixes #(issue number)
Closes #(issue number)
Relates to #(issue number)

## 🎯 Type of Change

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test improvements

## 🧪 Testing

**Test Coverage**

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Behavioural tests added/updated (if applicable)
- [ ] All tests pass locally
- [ ] Coverage meets minimum requirements (90%)

**Manual Testing**

- [ ] Tested locally with Docker environment
- [ ] API endpoints tested with Swagger UI
- [ ] Database migrations tested
- [ ] Error scenarios tested

## 🏗️ Architecture Compliance

- [ ] Follows Clean Architecture principles
- [ ] Respects layer boundaries (Domain → Application → Infrastructure → Presentation)
- [ ] Uses dependency injection appropriately
- [ ] Implements repository pattern for data access
- [ ] Domain logic isolated from technical concerns

## 📝 Code Quality

- [ ] Code follows PEP 8 standards
- [ ] All functions have type hints
- [ ] Docstrings added for public functions/classes
- [ ] British English spelling used throughout
- [ ] No linting errors (`flake8`, `mypy`, `black`, `isort`)
- [ ] Pre-commit hooks pass

## 🔐 Security Considerations

- [ ] Input validation implemented
- [ ] SQL injection protection maintained
- [ ] Authentication/authorisation requirements met
- [ ] Sensitive data handling reviewed
- [ ] No secrets committed to repository

## 📊 Performance Impact

- [ ] No significant performance degradation
- [ ] Database queries optimised
- [ ] API response times within targets (<200ms)
- [ ] Memory usage considerations addressed

## 📚 Documentation

- [ ] README updated (if applicable)
- [ ] API documentation updated
- [ ] Docstrings added/updated
- [ ] CHANGELOG.md updated
- [ ] Architecture decisions documented (if applicable)

## 🏋️‍♂️ RP Scientific Accuracy

- [ ] Algorithms align with RP methodology
- [ ] Calculations validated against reference materials
- [ ] Training principles correctly implemented
- [ ] Scientific accuracy maintained

## 📱 Mobile-First Considerations

- [ ] API responses optimised for mobile
- [ ] Payload sizes minimised
- [ ] Offline-friendly design considered
- [ ] Progressive loading supported (if applicable)

## 🔄 Deployment Considerations

- [ ] Database migrations included (if applicable)
- [ ] Environment variables documented
- [ ] Docker configuration updated (if needed)
- [ ] Backwards compatibility maintained

## 📸 Screenshots/Evidence

**Before/After (if applicable)**

**Test Results**

```
Paste test output or coverage report here
```

## 📋 Reviewer Checklist

**For Reviewers:**

- [ ] Code review completed
- [ ] Architecture compliance verified
- [ ] Test coverage adequate
- [ ] Documentation sufficient
- [ ] Security considerations addressed
- [ ] Performance impact acceptable

## 🎉 Additional Notes

Any additional information that reviewers should know about this PR.
