[behave]
# Behave configuration for RP Training API BDD tests

# Test discovery
paths = features

# Output format
format = pretty
outfiles = reports/behave-report.txt
junit = true
junit_directory = reports/junit

# Logging
logging_level = INFO
logging_format = %(levelname)-8s %(name)-10s %(message)s
logging_datefmt = %Y-%m-%d %H:%M:%S

# Tags
tags = ~@wip,~@skip

# Step definitions
steps_dir = features/steps

# Dry run for syntax checking
# dry_run = false

# Stop on first failure
# stop = true

# Show skipped steps
show_skipped = true

# Show multiline text
show_multiline = true

# Color output
color = true

# Summary
summary = true

# Default language
lang = en

# User data
userdata_defines = 
    test_environment=local
    api_base_url=http://testserver
    timeout=30

# Include/exclude scenarios based on tags
# Example tag expressions:
# @smoke - Run only smoke tests
# @regression - Run regression tests
# @api and @auth - Run tests tagged with both api and auth
# @slow or @performance - Run tests tagged with either slow or performance
# not @wip - Exclude work-in-progress tests

# Performance testing tags
# @performance - Performance and load testing scenarios
# @security - Security testing scenarios
# @integration - Integration testing scenarios
# @unit - Unit-level BDD scenarios

# Feature-specific tags
# @authentication - Authentication and login scenarios
# @profile - User profile management scenarios
# @health - Health check and monitoring scenarios
# @error_handling - Error handling and edge case scenarios

# Priority tags
# @critical - Critical functionality that must work
# @high - High priority features
# @medium - Medium priority features
# @low - Low priority features

# Environment tags
# @local - Tests that run in local environment
# @staging - Tests for staging environment
# @production - Tests safe for production environment

# Test type tags
# @smoke - Quick smoke tests
# @regression - Full regression test suite
# @acceptance - User acceptance tests
# @exploratory - Exploratory testing scenarios
