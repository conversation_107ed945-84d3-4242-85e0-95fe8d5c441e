[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=85",
    "-v",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests for individual components",
    "integration: Integration tests for component interactions", 
    "auth: Authentication and authorization tests",
    "database: Database-related tests",
    "api: API endpoint tests",
    "security: Security-focused tests",
    "performance: Performance and load tests",
    "slow: Tests that take longer to run",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
