[pytest]
minversion = 7.0
addopts =
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=85
    -v
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    auth: Authentication and authorization tests
    database: Database-related tests
    api: API endpoint tests
    security: Security-focused tests
    performance: Performance and load tests
    slow: Tests that take longer to run
    e2e: End-to-end workflow tests
    error_handling: Error handling and exception tests
    health: Health check and monitoring tests
    cross_layer: Cross-layer integration tests
    real_world: Real-world scenario tests
    service: Service layer integration tests
    coverage_boost: Tests specifically designed to boost coverage
    real: Real service tests without mocking
    domain: Domain layer tests
    parametrize: Parametrized tests
    infrastructure: Infrastructure layer tests
asyncio_mode = auto
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
