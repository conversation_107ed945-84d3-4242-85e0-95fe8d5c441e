version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: rp_training_db
    environment:
      POSTGRES_DB: rp_training
      POSTGRES_USER: rp_user
      POSTGRES_PASSWORD: rp_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rp_user -d rp_training"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - rp_network

  # Test Database
  test_db:
    image: postgres:15-alpine
    container_name: rp_training_test_db
    environment:
      POSTGRES_DB: rp_training_test
      POSTGRES_USER: rp_user
      POSTGRES_PASSWORD: rp_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5433:5432"
    volumes:
      - test_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rp_user -d rp_training_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - rp_network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: rp_training_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - rp_network

  # FastAPI Application (Development)
  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: rp_training_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://rp_user:rp_password@db:5432/rp_training
      - TEST_DATABASE_URL=postgresql+asyncpg://rp_user:rp_password@test_db:5432/rp_training_test
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production-minimum-32-chars
      - DEBUG=true
    volumes:
      - ../app:/app/app
      - ../tests:/app/tests
      - ../alembic:/app/alembic
      - ../alembic.ini:/app/alembic.ini
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - rp_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: rp_training_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - rp_network

volumes:
  postgres_data:
  test_postgres_data:
  redis_data:
  pgadmin_data:

networks:
  rp_network:
    driver: bridge
