-- Initialize RP Training Database
-- This script runs when the PostgreSQL container starts for the first time

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types (enums) that will be used by Alembic migrations
-- These are created here to ensure they exist before migrations run

-- Training goal enumeration
DO $$ BEGIN
    CREATE TYPE training_goal_enum AS ENUM (
        'hypertrophy', 
        'strength', 
        'endurance', 
        'general_fitness'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Mesocycle status enumeration  
DO $$ BEGIN
    CREATE TYPE mesocycle_status_enum AS ENUM (
        'planning', 
        'active', 
        'completed', 
        'paused'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Muscle group enumeration
DO $$ BEGIN
    CREATE TYPE muscle_group_enum AS ENUM (
        'chest', 
        'back', 
        'shoulders', 
        'biceps', 
        'triceps', 
        'quads', 
        'hamstrings', 
        'glutes', 
        'calves', 
        'abs'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Movement pattern enumeration
DO $$ BEGIN
    CREATE TYPE movement_pattern_enum AS ENUM (
        'push', 
        'pull', 
        'squat', 
        'hinge', 
        'carry', 
        'isolation'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Equipment enumeration
DO $$ BEGIN
    CREATE TYPE equipment_enum AS ENUM (
        'barbell', 
        'dumbbell', 
        'cable', 
        'machine', 
        'bodyweight', 
        'resistance_band'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Difficulty enumeration
DO $$ BEGIN
    CREATE TYPE difficulty_enum AS ENUM (
        'beginner', 
        'intermediate', 
        'advanced'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- MEV recommendation enumeration
DO $$ BEGIN
    CREATE TYPE mev_recommendation_enum AS ENUM (
        'increase_2_4_sets', 
        'progress_normally', 
        'drop_volume', 
        'recovery_session'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Grant permissions to the application user
GRANT ALL PRIVILEGES ON DATABASE rp_training TO rp_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO rp_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO rp_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO rp_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO rp_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO rp_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO rp_user;
