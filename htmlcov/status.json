{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "0e1d264fa18662a5830f067f89cd81e9", "files": {"z_8fff35c1a8c58736_exercise_dto_py": {"hash": "4a217f170497d4454f53e509c308d8b2", "index": {"url": "z_8fff35c1a8c58736_exercise_dto_py.html", "file": "app/application/dto/exercise_dto.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8fff35c1a8c58736_user_dto_py": {"hash": "ad2c6879eec256331df193c1367c99a4", "index": {"url": "z_8fff35c1a8c58736_user_dto_py.html", "file": "app/application/dto/user_dto.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_095011898a1367ac_exceptions_py": {"hash": "ccea2c7f729f832dd5c9ebe2a71f1e9a", "index": {"url": "z_095011898a1367ac_exceptions_py.html", "file": "app/application/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c63f50be4a9a76d_exercise_service_py": {"hash": "f32de611e2c45ad410758a29ff2f80a0", "index": {"url": "z_6c63f50be4a9a76d_exercise_service_py.html", "file": "app/application/services/exercise_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c63f50be4a9a76d_workout_service_py": {"hash": "7a1abd5e734ed1f5d461f989f11a3f48", "index": {"url": "z_6c63f50be4a9a76d_workout_service_py.html", "file": "app/application/services/workout_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_authenticate_user_py": {"hash": "0516f47fc51ab6840c7b8a729286b88b", "index": {"url": "z_330f5ee58403c77c_authenticate_user_py.html", "file": "app/application/use_cases/authenticate_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_exercise_media_use_cases_py": {"hash": "4c1dae434e8e9d1166c3b4eba9f2e98e", "index": {"url": "z_330f5ee58403c77c_exercise_media_use_cases_py.html", "file": "app/application/use_cases/exercise_media_use_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_exercise_use_cases_py": {"hash": "c92ae55d35a0e048989515a522d81bc6", "index": {"url": "z_330f5ee58403c77c_exercise_use_cases_py.html", "file": "app/application/use_cases/exercise_use_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_get_user_profile_py": {"hash": "a06659bbb788921fe6de509aed22ed76", "index": {"url": "z_330f5ee58403c77c_get_user_profile_py.html", "file": "app/application/use_cases/get_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_register_user_py": {"hash": "13998063ccd13dd2dd4a87a864a927ff", "index": {"url": "z_330f5ee58403c77c_register_user_py.html", "file": "app/application/use_cases/register_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_update_user_profile_py": {"hash": "a5712e8c1c0816e9d6474622aa14aa2f", "index": {"url": "z_330f5ee58403c77c_update_user_profile_py.html", "file": "app/application/use_cases/update_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_base_py": {"hash": "0414a3214a6d92aa66a1ece2198a2184", "index": {"url": "z_3efee37c27925ae3_base_py.html", "file": "app/domain/entities/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 3, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_exercise_py": {"hash": "454acc533cd559717a5e991aa8575ec2", "index": {"url": "z_3efee37c27925ae3_exercise_py.html", "file": "app/domain/entities/exercise.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 224, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_user_py": {"hash": "10452a36357897df7ebfa454bbc8c438", "index": {"url": "z_3efee37c27925ae3_user_py.html", "file": "app/domain/entities/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_workout_py": {"hash": "adaa6534cf8f65d76aefe1d1e0d1773e", "index": {"url": "z_3efee37c27925ae3_workout_py.html", "file": "app/domain/entities/workout.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_91f70f5812085d2d_auth_exceptions_py": {"hash": "cd35d33c56c0d237647e35164f55a0eb", "index": {"url": "z_91f70f5812085d2d_auth_exceptions_py.html", "file": "app/domain/exceptions/auth_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_exercise_repository_py": {"hash": "6fc2b885fa366f06f8d9bba11c0afc9b", "index": {"url": "z_a6abcadb6030ddcc_exercise_repository_py.html", "file": "app/domain/repositories/exercise_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 304, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_user_repository_py": {"hash": "bf038efb102acf2b31e24b5eef1dbd6f", "index": {"url": "z_a6abcadb6030ddcc_user_repository_py.html", "file": "app/domain/repositories/user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 66, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_workout_repository_py": {"hash": "33ceba224a00576718275d4b9fc91d27", "index": {"url": "z_a6abcadb6030ddcc_workout_repository_py.html", "file": "app/domain/repositories/workout_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 304, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_525f7ea2345a0e41_auth_service_py": {"hash": "c79fc23cc667b5744d1939fe9075f230", "index": {"url": "z_525f7ea2345a0e41_auth_service_py.html", "file": "app/domain/services/auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 10, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_jwt_handler_py": {"hash": "19a9451f98fe98adddfe2fffdf4eb695", "index": {"url": "z_53e7b745ea159b35_jwt_handler_py.html", "file": "app/infrastructure/auth/jwt_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_password_handler_py": {"hash": "5288e602e1fcc6e557a42a4bfb0af2aa", "index": {"url": "z_53e7b745ea159b35_password_handler_py.html", "file": "app/infrastructure/auth/password_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_90bdfa917c6a6771_connection_py": {"hash": "97b6ad1b5fd751f902014a1116b77174", "index": {"url": "z_90bdfa917c6a6771_connection_py.html", "file": "app/infrastructure/database/connection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_exercise_model_py": {"hash": "eeed68946e2697af57e530276b3d7086", "index": {"url": "z_abf0792f91b2e734_exercise_model_py.html", "file": "app/infrastructure/database/models/exercise_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 11, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_user_model_py": {"hash": "17e62951ffdb930559a62ed3fc348e37", "index": {"url": "z_abf0792f91b2e734_user_model_py.html", "file": "app/infrastructure/database/models/user_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 6, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_workout_model_py": {"hash": "def5e613427952fdcf2bcea630cda816", "index": {"url": "z_abf0792f91b2e734_workout_model_py.html", "file": "app/infrastructure/database/models/workout_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 6, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_exercise_repository_impl_py": {"hash": "0db817ee123d83413f8d1c8f60d113e0", "index": {"url": "z_2fdff8974586b090_exercise_repository_impl_py.html", "file": "app/infrastructure/database/repositories/exercise_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 181, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_user_repository_impl_py": {"hash": "96a45bb8643e797f4c9bbd8638ed601c", "index": {"url": "z_2fdff8974586b090_user_repository_impl_py.html", "file": "app/infrastructure/database/repositories/user_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_workout_repository_impl_py": {"hash": "786557cc4e74e66ecd79b434924961fb", "index": {"url": "z_2fdff8974586b090_workout_repository_impl_py.html", "file": "app/infrastructure/database/repositories/workout_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 224, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "c0d62f8609f80c48b04f4b2d3a23f5fe", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d36cccc441caff8a_dependencies_py": {"hash": "23aa00f4361f2119190add0a37936aa9", "index": {"url": "z_d36cccc441caff8a_dependencies_py.html", "file": "app/presentation/api/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_auth_py": {"hash": "f505e7b9ccb172a2a2da445252c5f791", "index": {"url": "z_02460f4fdd8331f5_auth_py.html", "file": "app/presentation/api/v1/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_health_py": {"hash": "30fbba9e62aca4ce5b9dd59a80201cda", "index": {"url": "z_02460f4fdd8331f5_health_py.html", "file": "app/presentation/api/v1/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6afd4035ffcb4024_exercises_py": {"hash": "400844a345a3c450347e9c902ef62ac9", "index": {"url": "z_6afd4035ffcb4024_exercises_py.html", "file": "app/presentation/api/v1/routes/exercises.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_auth_schema_py": {"hash": "35ee4dcbd966b77d93e8fd4ad93f2dc3", "index": {"url": "z_7e13110740283a02_auth_schema_py.html", "file": "app/presentation/schemas/auth_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_exercise_schemas_py": {"hash": "2cfe107fb610f811cecdb6b4db107141", "index": {"url": "z_7e13110740283a02_exercise_schemas_py.html", "file": "app/presentation/schemas/exercise_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_user_schema_py": {"hash": "584be5a9127fcdab93bb910babaa2a03", "index": {"url": "z_7e13110740283a02_user_schema_py.html", "file": "app/presentation/schemas/user_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}