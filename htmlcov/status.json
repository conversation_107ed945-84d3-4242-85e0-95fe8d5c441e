{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "95de4edc9f63044dce8572074e339202", "files": {"z_8fff35c1a8c58736_user_dto_py": {"hash": "f886675a0d50509c7ba97b8f8e76dc5a", "index": {"url": "z_8fff35c1a8c58736_user_dto_py.html", "file": "app/application/dto/user_dto.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_authenticate_user_py": {"hash": "2f11afae070c70a01ec20b8c2fb8fad6", "index": {"url": "z_330f5ee58403c77c_authenticate_user_py.html", "file": "app/application/use_cases/authenticate_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_get_user_profile_py": {"hash": "b45c41e5a1ab196ac6edd8c2960fddb7", "index": {"url": "z_330f5ee58403c77c_get_user_profile_py.html", "file": "app/application/use_cases/get_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_register_user_py": {"hash": "7012ddc1343038c77b665bfa240d4131", "index": {"url": "z_330f5ee58403c77c_register_user_py.html", "file": "app/application/use_cases/register_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_update_user_profile_py": {"hash": "9dfa3a02aef9ca8a7eab0cb71c5904b0", "index": {"url": "z_330f5ee58403c77c_update_user_profile_py.html", "file": "app/application/use_cases/update_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_base_py": {"hash": "a14a8e2e1db47a88249b84549874f519", "index": {"url": "z_3efee37c27925ae3_base_py.html", "file": "app/domain/entities/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 3, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_user_py": {"hash": "28333e8e879caf28dedd880ae8253559", "index": {"url": "z_3efee37c27925ae3_user_py.html", "file": "app/domain/entities/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_91f70f5812085d2d_auth_exceptions_py": {"hash": "5e7e24c6e0ac0110e19a41b5c9170202", "index": {"url": "z_91f70f5812085d2d_auth_exceptions_py.html", "file": "app/domain/exceptions/auth_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_user_repository_py": {"hash": "b8aa3e7f090c66d03657c46c3a6544c0", "index": {"url": "z_a6abcadb6030ddcc_user_repository_py.html", "file": "app/domain/repositories/user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 78, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_525f7ea2345a0e41_auth_service_py": {"hash": "984284f08aecd3d0684fda93873deab6", "index": {"url": "z_525f7ea2345a0e41_auth_service_py.html", "file": "app/domain/services/auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 10, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_jwt_handler_py": {"hash": "f6546c8011f1913934fee1ad70e37a71", "index": {"url": "z_53e7b745ea159b35_jwt_handler_py.html", "file": "app/infrastructure/auth/jwt_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_password_handler_py": {"hash": "ee3f042b2a7bb38aa84238a9091ff32d", "index": {"url": "z_53e7b745ea159b35_password_handler_py.html", "file": "app/infrastructure/auth/password_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_90bdfa917c6a6771_connection_py": {"hash": "d4b753fb090dc40985ea811e567ddcb0", "index": {"url": "z_90bdfa917c6a6771_connection_py.html", "file": "app/infrastructure/database/connection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_user_model_py": {"hash": "6f2bf44430e9b537843f62c57cd34ffb", "index": {"url": "z_abf0792f91b2e734_user_model_py.html", "file": "app/infrastructure/database/models/user_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 6, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_user_repository_impl_py": {"hash": "a9bafc8bac8b8df2db709ffaffde0734", "index": {"url": "z_2fdff8974586b090_user_repository_impl_py.html", "file": "app/infrastructure/database/repositories/user_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "2121e02ba89e78492c20cda0ac697340", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d36cccc441caff8a_dependencies_py": {"hash": "43da500cdb00028dbc507dcf3ddbbb16", "index": {"url": "z_d36cccc441caff8a_dependencies_py.html", "file": "app/presentation/api/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_auth_py": {"hash": "7147b10ba4b61d2368c1ee79ad1d5d81", "index": {"url": "z_02460f4fdd8331f5_auth_py.html", "file": "app/presentation/api/v1/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_health_py": {"hash": "73355810294a660c76f96e4156b32d71", "index": {"url": "z_02460f4fdd8331f5_health_py.html", "file": "app/presentation/api/v1/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_auth_schema_py": {"hash": "2b109d03a3b5c94989ab8ec4fb50558c", "index": {"url": "z_7e13110740283a02_auth_schema_py.html", "file": "app/presentation/schemas/auth_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_user_schema_py": {"hash": "003a4cabde198233f67b3ba389f3cbf0", "index": {"url": "z_7e13110740283a02_user_schema_py.html", "file": "app/presentation/schemas/user_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}