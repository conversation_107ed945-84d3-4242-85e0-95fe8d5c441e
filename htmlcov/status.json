{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "95de4edc9f63044dce8572074e339202", "files": {"z_8fff35c1a8c58736_user_dto_py": {"hash": "3005e5558fde288478291c07137604f2", "index": {"url": "z_8fff35c1a8c58736_user_dto_py.html", "file": "app/application/dto/user_dto.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_095011898a1367ac_exceptions_py": {"hash": "fd709f766be7cb3f4889c080c7a6cbc6", "index": {"url": "z_095011898a1367ac_exceptions_py.html", "file": "app/application/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c63f50be4a9a76d_exercise_service_py": {"hash": "5ef359e0c6a4e9d45539170914e94f34", "index": {"url": "z_6c63f50be4a9a76d_exercise_service_py.html", "file": "app/application/services/exercise_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c63f50be4a9a76d_workout_service_py": {"hash": "7a1abd5e734ed1f5d461f989f11a3f48", "index": {"url": "z_6c63f50be4a9a76d_workout_service_py.html", "file": "app/application/services/workout_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 51, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_authenticate_user_py": {"hash": "2f9c52bd5bbefd6529d00b6569b8e660", "index": {"url": "z_330f5ee58403c77c_authenticate_user_py.html", "file": "app/application/use_cases/authenticate_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_exercise_media_use_cases_py": {"hash": "10746f9b8b54b3763382fc1f8d92ec3a", "index": {"url": "z_330f5ee58403c77c_exercise_media_use_cases_py.html", "file": "app/application/use_cases/exercise_media_use_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 90, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_exercise_use_cases_py": {"hash": "d12b8920f653f5acacc7b7a2aa6d790f", "index": {"url": "z_330f5ee58403c77c_exercise_use_cases_py.html", "file": "app/application/use_cases/exercise_use_cases.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_get_user_profile_py": {"hash": "88bb2a4b55c5f7aee65d478f4889a80f", "index": {"url": "z_330f5ee58403c77c_get_user_profile_py.html", "file": "app/application/use_cases/get_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_register_user_py": {"hash": "686be641ca0e4135227208280842c92d", "index": {"url": "z_330f5ee58403c77c_register_user_py.html", "file": "app/application/use_cases/register_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 10, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_330f5ee58403c77c_update_user_profile_py": {"hash": "a5e7ee64e571fb5ae2baf5b503eb1846", "index": {"url": "z_330f5ee58403c77c_update_user_profile_py.html", "file": "app/application/use_cases/update_user_profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_base_py": {"hash": "a403ad90a4a5212c7b8eeef84e681b53", "index": {"url": "z_3efee37c27925ae3_base_py.html", "file": "app/domain/entities/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 3, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_exercise_py": {"hash": "e676938db73e0476afa389bd73199c59", "index": {"url": "z_3efee37c27925ae3_exercise_py.html", "file": "app/domain/entities/exercise.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_user_py": {"hash": "d830c04547c215b959ecf9cf74be826a", "index": {"url": "z_3efee37c27925ae3_user_py.html", "file": "app/domain/entities/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3efee37c27925ae3_workout_py": {"hash": "adaa6534cf8f65d76aefe1d1e0d1773e", "index": {"url": "z_3efee37c27925ae3_workout_py.html", "file": "app/domain/entities/workout.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_91f70f5812085d2d_auth_exceptions_py": {"hash": "cbbef7a4b24335a91031e97f7a448a2c", "index": {"url": "z_91f70f5812085d2d_auth_exceptions_py.html", "file": "app/domain/exceptions/auth_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_exercise_repository_py": {"hash": "d9b1dfa44de7c2e9a833b364fe2bb4f1", "index": {"url": "z_a6abcadb6030ddcc_exercise_repository_py.html", "file": "app/domain/repositories/exercise_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 368, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_user_repository_py": {"hash": "969162882949262423220fd8b8962346", "index": {"url": "z_a6abcadb6030ddcc_user_repository_py.html", "file": "app/domain/repositories/user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 78, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a6abcadb6030ddcc_workout_repository_py": {"hash": "33ceba224a00576718275d4b9fc91d27", "index": {"url": "z_a6abcadb6030ddcc_workout_repository_py.html", "file": "app/domain/repositories/workout_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 304, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_525f7ea2345a0e41_auth_service_py": {"hash": "28522275d1a0270fbda08bc1065c32b5", "index": {"url": "z_525f7ea2345a0e41_auth_service_py.html", "file": "app/domain/services/auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 10, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_jwt_handler_py": {"hash": "6c1ee07145b5510a23ff131947fd7c0d", "index": {"url": "z_53e7b745ea159b35_jwt_handler_py.html", "file": "app/infrastructure/auth/jwt_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_53e7b745ea159b35_password_handler_py": {"hash": "9f54ac7555cfaafe9fcf30116f147944", "index": {"url": "z_53e7b745ea159b35_password_handler_py.html", "file": "app/infrastructure/auth/password_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_90bdfa917c6a6771_connection_py": {"hash": "b0ea9c3aba662deafa2526ab3c86dc78", "index": {"url": "z_90bdfa917c6a6771_connection_py.html", "file": "app/infrastructure/database/connection.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_exercise_model_py": {"hash": "7b7b2252fd937d1d25f2512262e0345e", "index": {"url": "z_abf0792f91b2e734_exercise_model_py.html", "file": "app/infrastructure/database/models/exercise_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 9, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_user_model_py": {"hash": "136ab9de2f09ddb08675dc66c44ca588", "index": {"url": "z_abf0792f91b2e734_user_model_py.html", "file": "app/infrastructure/database/models/user_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 6, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_abf0792f91b2e734_workout_model_py": {"hash": "51a1b1592f8af0f748cf7efd85285997", "index": {"url": "z_abf0792f91b2e734_workout_model_py.html", "file": "app/infrastructure/database/models/workout_model.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_exercise_repository_impl_py": {"hash": "bf4676e1488d1612b90a9ed95e926998", "index": {"url": "z_2fdff8974586b090_exercise_repository_impl_py.html", "file": "app/infrastructure/database/repositories/exercise_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 0, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_user_repository_impl_py": {"hash": "74327f4c099c9de19d710b2e5813d6fe", "index": {"url": "z_2fdff8974586b090_user_repository_impl_py.html", "file": "app/infrastructure/database/repositories/user_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2fdff8974586b090_workout_repository_impl_py": {"hash": "786557cc4e74e66ecd79b434924961fb", "index": {"url": "z_2fdff8974586b090_workout_repository_impl_py.html", "file": "app/infrastructure/database/repositories/workout_repository_impl.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 224, "n_excluded": 0, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "a0af053344268a93955d9e28f6d51a98", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d36cccc441caff8a_dependencies_py": {"hash": "42b57356144ab7cc8a75a41c34cb1dcf", "index": {"url": "z_d36cccc441caff8a_dependencies_py.html", "file": "app/presentation/api/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_auth_py": {"hash": "8da5a43ba23f04f1e8251d5a15675225", "index": {"url": "z_02460f4fdd8331f5_auth_py.html", "file": "app/presentation/api/v1/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_02460f4fdd8331f5_health_py": {"hash": "0bf72c0539c56a1fe968ad8227511a59", "index": {"url": "z_02460f4fdd8331f5_health_py.html", "file": "app/presentation/api/v1/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6afd4035ffcb4024_exercises_py": {"hash": "43c3454280fc6d6be60067a8f2199ded", "index": {"url": "z_6afd4035ffcb4024_exercises_py.html", "file": "app/presentation/api/v1/routes/exercises.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_auth_schema_py": {"hash": "b03e80b730a2d76709f410e1493aac31", "index": {"url": "z_7e13110740283a02_auth_schema_py.html", "file": "app/presentation/schemas/auth_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_exercise_schemas_py": {"hash": "92c3fe53343bab459cf8393f697d1517", "index": {"url": "z_7e13110740283a02_exercise_schemas_py.html", "file": "app/presentation/schemas/exercise_schemas.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7e13110740283a02_user_schema_py": {"hash": "081dc16827f3319f7ce9c45663386bcd", "index": {"url": "z_7e13110740283a02_user_schema_py.html", "file": "app/presentation/schemas/user_schema.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}