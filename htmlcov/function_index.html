<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">54%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-15 10:27 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t31">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t31"><data value='from_entity'>UserDTO.from_entity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t11">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t11"><data value='init__'>ApplicationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t21">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t21"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t30">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t30"><data value='init__'>BusinessRuleViolationError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t39">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t39"><data value='init__'>NotFoundError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t49">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t49"><data value='init__'>ConflictError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t58">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t58"><data value='init__'>UnauthorizedError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t66">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t66"><data value='init__'>ForbiddenError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t74">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t74"><data value='init__'>ExternalServiceError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t84">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t84"><data value='init__'>RateLimitError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t93">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t93"><data value='init__'>DataIntegrityError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t102">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t102"><data value='init__'>ConcurrencyError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t110">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t110"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t30">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t30"><data value='init__'>ExerciseService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t42">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t42"><data value='create_exercise'>ExerciseService.create_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t63">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t63"><data value='get_exercise'>ExerciseService.get_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t78">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t78"><data value='update_exercise'>ExerciseService.update_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t102">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t102"><data value='delete_exercise'>ExerciseService.delete_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t119">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t119"><data value='search_exercises'>ExerciseService.search_exercises</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t138">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t138"><data value='approve_exercise'>ExerciseService.approve_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t161">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t161"><data value='reject_exercise'>ExerciseService.reject_exercise</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t184">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t184"><data value='get_exercise_by_name'>ExerciseService.get_exercise_by_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t196">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t196"><data value='get_exercises_by_muscle_group'>ExerciseService.get_exercises_by_muscle_group</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t217">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t217"><data value='get_exercises_by_equipment'>ExerciseService.get_exercises_by_equipment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t238">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t238"><data value='get_random_exercises'>ExerciseService.get_random_exercises</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t33">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t33"><data value='init__'>WorkoutService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t43">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t43"><data value='create_workout'>WorkoutService.create_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t71">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t71"><data value='get_workout'>WorkoutService.get_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t90">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t90"><data value='update_workout'>WorkoutService.update_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t115">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t115"><data value='delete_workout'>WorkoutService.delete_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t134">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t134"><data value='start_workout'>WorkoutService.start_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t154">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t154"><data value='complete_workout'>WorkoutService.complete_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t174">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t174"><data value='get_user_workouts'>WorkoutService.get_user_workouts</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t193">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t193"><data value='get_active_workout'>WorkoutService.get_active_workout</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t205">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t205"><data value='search_workouts'>WorkoutService.search_workouts</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t227">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t227"><data value='add_exercise_set'>WorkoutService.add_exercise_set</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t251">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t251"><data value='update_exercise_set'>WorkoutService.update_exercise_set</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t275">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t275"><data value='delete_exercise_set'>WorkoutService.delete_exercise_set</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t294">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t294"><data value='get_workout_sets'>WorkoutService.get_workout_sets</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t307">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t307"><data value='get_workout_templates'>WorkoutService.get_workout_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t319">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t319"><data value='create_template_from_workout'>WorkoutService.create_template_from_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t345">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t345"><data value='create_workout_from_template'>WorkoutService.create_workout_from_template</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t374">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t374"><data value='get_workout_statistics'>WorkoutService.get_workout_statistics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t19">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t19"><data value='generate_access_token'>TokenGenerator.generate_access_token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t23">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t23"><data value='generate_refresh_token'>TokenGenerator.generate_refresh_token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t38">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t38"><data value='init__'>AuthenticateUserResponse.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t54">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t54"><data value='init__'>AuthenticateUserUseCase.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t61">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t61"><data value='execute'>AuthenticateUserUseCase.execute</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t25">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t25"><data value='init__'>ExerciseMediaUseCases.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t29">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t29"><data value='add_media'>ExerciseMediaUseCases.add_media</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t126">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t126"><data value='get_exercise_media'>ExerciseMediaUseCases.get_exercise_media</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t146">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t146"><data value='get_media_by_type'>ExerciseMediaUseCases.get_media_by_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t164">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t164"><data value='get_primary_media'>ExerciseMediaUseCases.get_primary_media</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t187">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t187"><data value='update_media'>ExerciseMediaUseCases.update_media</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t260">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t260"><data value='delete_media'>ExerciseMediaUseCases.delete_media</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t278">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t278"><data value='reorder_media'>ExerciseMediaUseCases.reorder_media</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t316">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t316"><data value='validate_media_data'>ExerciseMediaUseCases._validate_media_data</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t350">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t350"><data value='get_media_by_id'>ExerciseMediaUseCases._get_media_by_id</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t38">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t38"><data value='init__'>ExerciseUseCases.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t42">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t42"><data value='create_exercise'>ExerciseUseCases.create_exercise</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t101">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t101"><data value='get_exercise'>ExerciseUseCases.get_exercise</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t120">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t120"><data value='get_exercise_by_uuid'>ExerciseUseCases.get_exercise_by_uuid</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t149">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t149"><data value='search_exercises'>ExerciseUseCases.search_exercises</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t177">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t177"><data value='update_exercise'>ExerciseUseCases.update_exercise</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t225">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t225"><data value='delete_exercise'>ExerciseUseCases.delete_exercise</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t248">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t248"><data value='restore_exercise'>ExerciseUseCases.restore_exercise</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t276">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t276"><data value='approve_exercise'>ExerciseUseCases.approve_exercise</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t307">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t307"><data value='reject_exercise'>ExerciseUseCases.reject_exercise</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t335">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t335"><data value='get_exercise_versions'>ExerciseUseCases.get_exercise_versions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t351">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t351"><data value='set_current_version'>ExerciseUseCases.set_current_version</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t384">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t384"><data value='apply_updates'>ExerciseUseCases._apply_updates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t17">app/application/use_cases/get_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t17"><data value='init__'>GetUserProfileUseCase.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t21">app/application/use_cases/get_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t21"><data value='execute'>GetUserProfileUseCase.execute</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html">app/application/use_cases/get_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t17">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t17"><data value='generate_access_token'>TokenGenerator.generate_access_token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t21">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t21"><data value='generate_refresh_token'>TokenGenerator.generate_refresh_token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t29">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t29"><data value='init__'>RegisterUserResponse.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t45">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t45"><data value='init__'>RegisterUserUseCase.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t52">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t52"><data value='execute'>RegisterUserUseCase.execute</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t17">app/application/use_cases/update_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t17"><data value='init__'>UpdateUserProfileUseCase.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t21">app/application/use_cases/update_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t21"><data value='execute'>UpdateUserProfileUseCase.execute</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html">app/application/use_cases/update_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t17">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t17"><data value='init__'>BaseEntity.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t30">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t30"><data value='mark_as_updated'>BaseEntity.mark_as_updated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t34">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t34"><data value='soft_delete'>BaseEntity.soft_delete</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t39">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t39"><data value='is_deleted'>BaseEntity.is_deleted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t43">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t43"><data value='restore'>BaseEntity.restore</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t48">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t48"><data value='eq__'>BaseEntity.__eq__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t54">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t54"><data value='hash__'>BaseEntity.__hash__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t58">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t58"><data value='repr__'>BaseEntity.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t204">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t204"><data value='validate_secondary_muscle_groups'>Exercise.validate_secondary_muscle_groups</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t213">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t213"><data value='validate_equipment_required'>Exercise.validate_equipment_required</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t221">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t221"><data value='validate_instruction_lists'>Exercise.validate_instruction_lists</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t227">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t227"><data value='is_deleted'>Exercise.is_deleted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t231">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t231"><data value='is_published'>Exercise.is_published</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t240">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t240"><data value='get_all_muscle_groups'>Exercise.get_all_muscle_groups</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t247">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t247"><data value='get_primary_media'>Exercise.get_primary_media</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t255">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t255"><data value='get_media_by_type'>Exercise.get_media_by_type</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>201</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="201 201">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t26">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t26"><data value='init__'>User.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t56">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t56"><data value='create'>User.create</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t79">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t79"><data value='update_profile'>User.update_profile</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t97">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t97"><data value='activate'>User.activate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t102">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t102"><data value='deactivate'>User.deactivate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t107">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t107"><data value='verify_email'>User.verify_email</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t112">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t112"><data value='get_full_name'>User.get_full_name</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t122">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t122"><data value='can_authenticate'>User.can_authenticate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t127">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t127"><data value='validate_email'>User._validate_email</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t136">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t136"><data value='validate_password'>User._validate_password</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t160">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t160"><data value='str__'>User.__str__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t73">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t73"><data value='status'>Workout.status</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t85">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t85"><data value='is_active'>Workout.is_active</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t89">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t89"><data value='start_workout'>Workout.start_workout</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t95">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t95"><data value='complete_workout'>Workout.complete_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t102">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t102"><data value='calculate_statistics'>Workout._calculate_statistics</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t177">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t177"><data value='validate_tempo'>ExerciseSet.validate_tempo</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t195">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t195"><data value='volume_load'>ExerciseSet.volume_load</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>115</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="115 115">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t30">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t30"><data value='create'>ExerciseRepository.create</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t46">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t46"><data value='get_by_id'>ExerciseRepository.get_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t59">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t59"><data value='get_by_uuid'>ExerciseRepository.get_by_uuid</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t79">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t79"><data value='get_versions'>ExerciseRepository.get_versions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t92">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t92"><data value='search'>ExerciseRepository.search</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t112">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t112"><data value='count'>ExerciseRepository.count</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t125">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t125"><data value='update'>ExerciseRepository.update</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t142">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t142"><data value='soft_delete'>ExerciseRepository.soft_delete</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t159">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t159"><data value='restore'>ExerciseRepository.restore</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t176">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t176"><data value='approve'>ExerciseRepository.approve</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t199">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t199"><data value='reject'>ExerciseRepository.reject</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t222">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t222"><data value='create_version'>ExerciseRepository.create_version</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t245">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t245"><data value='get_current_version'>ExerciseRepository.get_current_version</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t258">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t258"><data value='set_current_version'>ExerciseRepository.set_current_version</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t282">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t282"><data value='add_media'>ExerciseRepository.add_media</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t296">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t296"><data value='get_media'>ExerciseRepository.get_media</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t309">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t309"><data value='update_media'>ExerciseRepository.update_media</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t323">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t323"><data value='delete_media'>ExerciseRepository.delete_media</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t337">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t337"><data value='log_audit'>ExerciseRepository.log_audit</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t371">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t371"><data value='get_audit_log'>ExerciseRepository.get_audit_log</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t391">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t391"><data value='exists'>ExerciseRepository.exists</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t404">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t404"><data value='exists_by_name'>ExerciseRepository.exists_by_name</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>95</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t19">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t19"><data value='create'>UserRepository.create</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t35">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t35"><data value='get_by_id'>UserRepository.get_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t48">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t48"><data value='get_by_email'>UserRepository.get_by_email</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t61">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t61"><data value='update'>UserRepository.update</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t77">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t77"><data value='delete'>UserRepository.delete</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t90">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t90"><data value='exists_by_email'>UserRepository.exists_by_email</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t33">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t33"><data value='create_workout'>WorkoutRepository.create_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t50">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t50"><data value='get_workout_by_id'>WorkoutRepository.get_workout_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t63">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t63"><data value='update_workout'>WorkoutRepository.update_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t80">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t80"><data value='delete_workout'>WorkoutRepository.delete_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t93">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t93"><data value='search_workouts'>WorkoutRepository.search_workouts</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t113">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t113"><data value='count_workouts'>WorkoutRepository.count_workouts</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t126">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t126"><data value='get_user_workouts'>WorkoutRepository.get_user_workouts</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t146">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t146"><data value='get_active_workout'>WorkoutRepository.get_active_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t159">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t159"><data value='start_workout'>WorkoutRepository.start_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t172">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t172"><data value='complete_workout'>WorkoutRepository.complete_workout</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t186">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t186"><data value='create_exercise_set'>WorkoutRepository.create_exercise_set</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t207">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t207"><data value='get_exercise_set_by_id'>WorkoutRepository.get_exercise_set_by_id</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t220">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t220"><data value='update_exercise_set'>WorkoutRepository.update_exercise_set</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t238">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t238"><data value='delete_exercise_set'>WorkoutRepository.delete_exercise_set</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t251">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t251"><data value='get_workout_sets'>WorkoutRepository.get_workout_sets</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t264">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t264"><data value='get_exercise_sets_by_exercise'>WorkoutRepository.get_exercise_sets_by_exercise</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t283">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t283"><data value='get_workout_templates'>WorkoutRepository.get_workout_templates</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t296">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t296"><data value='copy_workout_as_template'>WorkoutRepository.copy_workout_as_template</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t314">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t314"><data value='create_workout_from_template'>WorkoutRepository.create_workout_from_template</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t335">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t335"><data value='exists'>WorkoutRepository.exists</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t348">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t348"><data value='get_workout_statistics'>WorkoutRepository.get_workout_statistics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>73</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t24">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t24"><data value='hash_password'>PasswordHandler.hash_password</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t28">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t28"><data value='verify_password'>PasswordHandler.verify_password</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t36">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t36"><data value='init__'>AuthService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t45">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t45"><data value='register_user'>AuthService.register_user</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t97">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t97"><data value='authenticate_user'>AuthService.authenticate_user</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t138">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t138"><data value='get_user_by_id'>AuthService.get_user_by_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t157">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t157"><data value='update_user_profile'>AuthService.update_user_profile</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t189">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t189"><data value='verify_password'>AuthService._verify_password</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t20">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t20"><data value='init__'>JWTHandler.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t27">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t27"><data value='generate_access_token'>JWTHandler.generate_access_token</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t50">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t50"><data value='generate_refresh_token'>JWTHandler.generate_refresh_token</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t71">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t71"><data value='decode_token'>JWTHandler.decode_token</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t94">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t94"><data value='get_user_id_from_token'>JWTHandler.get_user_id_from_token</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t119">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t119"><data value='get_token_type'>JWTHandler.get_token_type</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t141">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t141"><data value='is_token_expired'>JWTHandler.is_token_expired</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t159">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t159"><data value='get_token_expiry'>JWTHandler.get_token_expiry</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t13">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t13"><data value='init__'>PasswordHandler.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t21">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t21"><data value='hash_password'>PasswordHandler.hash_password</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t33">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t33"><data value='verify_password'>PasswordHandler.verify_password</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t46">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t46"><data value='needs_update'>PasswordHandler.needs_update</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t31">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t31"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t36">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t36"><data value='initialize'>DatabaseManager.initialize</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t74">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t74"><data value='close'>DatabaseManager.close</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t81">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t81"><data value='get_session'>DatabaseManager.get_session</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t105">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t105"><data value='engine'>DatabaseManager.engine</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t116">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t116"><data value='get_database_session'>get_database_session</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t182">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t182"><data value='repr__'>ExerciseModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t248">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t248"><data value='repr__'>ExerciseMediaModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t308">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t308"><data value='repr__'>ExerciseAuditLogModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>81</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="81 81">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t66">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t66"><data value='repr__'>UserModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t112">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t112"><data value='repr__'>RefreshTokenModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t81">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t81"><data value='repr__'>WorkoutModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t148">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t148"><data value='repr__'>ExerciseSetModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>50</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t34">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t34"><data value='init__'>ExerciseRepositoryImpl.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t38">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t38"><data value='create'>ExerciseRepositoryImpl.create</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t97">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t97"><data value='get_by_id'>ExerciseRepositoryImpl.get_by_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t109">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t109"><data value='get_by_uuid'>ExerciseRepositoryImpl.get_by_uuid</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t132">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t132"><data value='get_versions'>ExerciseRepositoryImpl.get_versions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t145">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t145"><data value='search'>ExerciseRepositoryImpl.search</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t168">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t168"><data value='count'>ExerciseRepositoryImpl.count</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t178">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t178"><data value='apply_search_filters'>ExerciseRepositoryImpl._apply_search_filters</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t222">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t222"><data value='update'>ExerciseRepositoryImpl.update</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t274">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t274"><data value='soft_delete'>ExerciseRepositoryImpl.soft_delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t302">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t302"><data value='approve'>ExerciseRepositoryImpl.approve</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t337">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t337"><data value='reject'>ExerciseRepositoryImpl.reject</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t370">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t370"><data value='create_version'>ExerciseRepositoryImpl.create_version</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t428">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t428"><data value='get_current_version'>ExerciseRepositoryImpl.get_current_version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t432">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t432"><data value='set_current_version'>ExerciseRepositoryImpl.set_current_version</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t465">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t465"><data value='add_media'>ExerciseRepositoryImpl.add_media</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t492">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t492"><data value='get_media'>ExerciseRepositoryImpl.get_media</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t505">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t505"><data value='update_media'>ExerciseRepositoryImpl.update_media</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t536">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t536"><data value='delete_media'>ExerciseRepositoryImpl.delete_media</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t548">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t548"><data value='log_audit'>ExerciseRepositoryImpl.log_audit</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t582">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t582"><data value='get_audit_log'>ExerciseRepositoryImpl.get_audit_log</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t601">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t601"><data value='exists'>ExerciseRepositoryImpl.exists</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t607">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t607"><data value='exists_by_name'>ExerciseRepositoryImpl.exists_by_name</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t627">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t627"><data value='model_to_entity'>ExerciseRepositoryImpl._model_to_entity</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t676">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t676"><data value='media_model_to_entity'>ExerciseRepositoryImpl._media_model_to_entity</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t700">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t700"><data value='audit_model_to_entity'>ExerciseRepositoryImpl._audit_model_to_entity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t717">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t717"><data value='model_to_dict'>ExerciseRepositoryImpl._model_to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t735">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t735"><data value='entity_to_dict'>ExerciseRepositoryImpl._entity_to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t755">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t755"><data value='restore'>ExerciseRepositoryImpl.restore</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t26">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t26"><data value='init__'>UserRepositoryImpl.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t30">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t30"><data value='create'>UserRepositoryImpl.create</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t74">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t74"><data value='get_by_id'>UserRepositoryImpl.get_by_id</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t94">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t94"><data value='get_by_email'>UserRepositoryImpl.get_by_email</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t114">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t114"><data value='update'>UserRepositoryImpl.update</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t151">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t151"><data value='delete'>UserRepositoryImpl.delete</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t170">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t170"><data value='exists_by_email'>UserRepositoryImpl.exists_by_email</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t186">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t186"><data value='model_to_entity'>UserRepositoryImpl._model_to_entity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t37">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t37"><data value='init__'>WorkoutRepositoryImpl.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t47">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t47"><data value='create_workout'>WorkoutRepositoryImpl.create_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t66">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t66"><data value='get_workout_by_id'>WorkoutRepositoryImpl.get_workout_by_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t83">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t83"><data value='update_workout'>WorkoutRepositoryImpl.update_workout</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t117">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t117"><data value='delete_workout'>WorkoutRepositoryImpl.delete_workout</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t133">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t133"><data value='search_workouts'>WorkoutRepositoryImpl.search_workouts</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t189">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t189"><data value='count_workouts'>WorkoutRepositoryImpl.count_workouts</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t212">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t212"><data value='get_user_workouts'>WorkoutRepositoryImpl.get_user_workouts</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t222">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t222"><data value='get_active_workout'>WorkoutRepositoryImpl.get_active_workout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t242">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t242"><data value='start_workout'>WorkoutRepositoryImpl.start_workout</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t266">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t266"><data value='complete_workout'>WorkoutRepositoryImpl.complete_workout</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t300">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t300"><data value='create_exercise_set'>WorkoutRepositoryImpl.create_exercise_set</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t333">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t333"><data value='get_exercise_set_by_id'>WorkoutRepositoryImpl.get_exercise_set_by_id</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t341">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t341"><data value='update_exercise_set'>WorkoutRepositoryImpl.update_exercise_set</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t390">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t390"><data value='delete_exercise_set'>WorkoutRepositoryImpl.delete_exercise_set</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t402">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t402"><data value='get_workout_sets'>WorkoutRepositoryImpl.get_workout_sets</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t415">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t415"><data value='get_exercise_sets_by_exercise'>WorkoutRepositoryImpl.get_exercise_sets_by_exercise</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t438">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t438"><data value='get_workout_templates'>WorkoutRepositoryImpl.get_workout_templates</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t443">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t443"><data value='copy_workout_as_template'>WorkoutRepositoryImpl.copy_workout_as_template</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t482">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t482"><data value='create_workout_from_template'>WorkoutRepositoryImpl.create_workout_from_template</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t522">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t522"><data value='exists'>WorkoutRepositoryImpl.exists</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t533">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t533"><data value='get_workout_statistics'>WorkoutRepositoryImpl.get_workout_statistics</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t563">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t563"><data value='update_workout_statistics'>WorkoutRepositoryImpl._update_workout_statistics</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t598">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t598"><data value='set_model_to_entity'>WorkoutRepositoryImpl._set_model_to_entity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t622">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t622"><data value='model_to_entity'>WorkoutRepositoryImpl._model_to_entity</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t22">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t22"><data value='lifespan'>lifespan</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t45">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t45"><data value='create_application'>create_application</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t97">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t97"><data value='root'>root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t46">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t46"><data value='get_user_repository'>get_user_repository</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t53">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t53"><data value='get_auth_service'>get_auth_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t60">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t60"><data value='get_register_user_use_case'>get_register_user_use_case</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t67">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t67"><data value='get_authenticate_user_use_case'>get_authenticate_user_use_case</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t74">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t74"><data value='get_user_profile_use_case'>get_user_profile_use_case</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t81">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t81"><data value='get_update_user_profile_use_case'>get_update_user_profile_use_case</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t88">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t88"><data value='get_current_user_id'>get_current_user_id</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t133">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t133"><data value='get_current_user'>get_current_user</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t166">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t166"><data value='get_exercise_repository'>get_exercise_repository</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t173">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t173"><data value='get_workout_repository'>get_workout_repository</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t181">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t181"><data value='get_exercise_service'>get_exercise_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t188">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html#t188"><data value='get_workout_service'>get_workout_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t51">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t51"><data value='register'>register</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t103">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t103"><data value='login'>login</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t149">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t149"><data value='get_profile'>get_profile</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t182">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html#t182"><data value='update_profile'>update_profile</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t22">app/presentation/api/v1/health.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t22"><data value='health_check'>health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t39">app/presentation/api/v1/health.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t39"><data value='database_health_check'>database_health_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t80">app/presentation/api/v1/health.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html#t80"><data value='detailed_health_check'>detailed_health_check</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html">app/presentation/api/v1/health.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t48">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t48"><data value='get_exercise_service'>get_exercise_service</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t61">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t61"><data value='create_exercise'>create_exercise</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t110">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t110"><data value='search_exercises'>search_exercises</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t169">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t169"><data value='get_exercise'>get_exercise</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t191">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t191"><data value='update_exercise'>update_exercise</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t250">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t250"><data value='delete_exercise'>delete_exercise</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t272">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t272"><data value='restore_exercise'>restore_exercise</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t301">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t301"><data value='approve_exercise'>approve_exercise</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t332">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t332"><data value='reject_exercise'>reject_exercise</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t365">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t365"><data value='add_exercise_media'>add_exercise_media</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t412">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t412"><data value='get_exercise_media'>get_exercise_media</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t439">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t439"><data value='update_exercise_media'>update_exercise_media</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t474">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t474"><data value='delete_exercise_media'>delete_exercise_media</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t495">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html#t495"><data value='get_exercise_statistics'>get_exercise_statistics</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t75">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t75"><data value='validate_secondary_muscle_groups'>ExerciseBaseSchema.validate_secondary_muscle_groups</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t84">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t84"><data value='validate_equipment_required'>ExerciseBaseSchema.validate_equipment_required</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t150">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t150"><data value='validate_secondary_muscle_groups'>ExerciseUpdateRequest.validate_secondary_muscle_groups</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t211">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t211"><data value='calculate_has_more'>ExerciseListResponse.calculate_has_more</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>171</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="171 171">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t50">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t50"><data value='validate_names'>UserCreateRequest.validate_names</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t59">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t59"><data value='validate_password'>UserCreateRequest.validate_password</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t101">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t101"><data value='validate_names'>UserUpdateRequest.validate_names</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2276</td>
                <td>1041</td>
                <td>804</td>
                <td class="right" data-ratio="1235 2276">54%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-15 10:27 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
