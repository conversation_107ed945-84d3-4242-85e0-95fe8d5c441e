<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">54%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-15 11:19 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t19">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t19"><data value='ExerciseMediaDTO'>ExerciseMediaDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t40">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t40"><data value='Config'>ExerciseMediaDTO.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t46">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t46"><data value='ExerciseDTO'>ExerciseDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t103">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t103"><data value='Config'>ExerciseDTO.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t110">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t110"><data value='ExerciseCreateDTO'>ExerciseCreateDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t130">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html#t130"><data value='ExerciseUpdateDTO'>ExerciseUpdateDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html">app/application/dto/exercise_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_exercise_dto_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t15">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t15"><data value='UserDTO'>UserDTO</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t43">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t43"><data value='Config'>UserDTO.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t47">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t47"><data value='CreateUserDTO'>CreateUserDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t56">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t56"><data value='Config'>CreateUserDTO.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t60">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t60"><data value='UpdateUserDTO'>UpdateUserDTO</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t67">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html#t67"><data value='Config'>UpdateUserDTO.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html">app/application/dto/user_dto.py</a></td>
                <td class="name left"><a href="z_8fff35c1a8c58736_user_dto_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t7">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t7"><data value='ApplicationError'>ApplicationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t17">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t17"><data value='ValidationError'>ValidationError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t26">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t26"><data value='BusinessRuleViolationError'>BusinessRuleViolationError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t35">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t35"><data value='NotFoundError'>NotFoundError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t47">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t47"><data value='ConflictError'>ConflictError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t56">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t56"><data value='UnauthorizedError'>UnauthorizedError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t64">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t64"><data value='ForbiddenError'>ForbiddenError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t72">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t72"><data value='ExternalServiceError'>ExternalServiceError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t82">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t82"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t91">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t91"><data value='DataIntegrityError'>DataIntegrityError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t100">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t100"><data value='ConcurrencyError'>ConcurrencyError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t108">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html#t108"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html">app/application/exceptions.py</a></td>
                <td class="name left"><a href="z_095011898a1367ac_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t19">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html#t19"><data value='ExerciseService'>ExerciseService</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html">app/application/services/exercise_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_exercise_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t25">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html#t25"><data value='WorkoutService'>WorkoutService</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html">app/application/services/workout_service.py</a></td>
                <td class="name left"><a href="z_6c63f50be4a9a76d_workout_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t15">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t15"><data value='TokenGenerator'>TokenGenerator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t27">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t27"><data value='AuthenticateUserRequest'>AuthenticateUserRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t34">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t34"><data value='AuthenticateUserResponse'>AuthenticateUserResponse</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t50">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html#t50"><data value='AuthenticateUserUseCase'>AuthenticateUserUseCase</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html">app/application/use_cases/authenticate_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_authenticate_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t15">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html#t15"><data value='ExerciseMediaUseCases'>ExerciseMediaUseCases</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html">app/application/use_cases/exercise_media_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_media_use_cases_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t26">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html#t26"><data value='ExerciseUseCases'>ExerciseUseCases</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html">app/application/use_cases/exercise_use_cases.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_exercise_use_cases_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t13">app/application/use_cases/get_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html#t13"><data value='GetUserProfileUseCase'>GetUserProfileUseCase</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html">app/application/use_cases/get_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_get_user_profile_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t13">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t13"><data value='TokenGenerator'>TokenGenerator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t25">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t25"><data value='RegisterUserResponse'>RegisterUserResponse</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t41">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html#t41"><data value='RegisterUserUseCase'>RegisterUserUseCase</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html">app/application/use_cases/register_user.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_register_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t13">app/application/use_cases/update_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html#t13"><data value='UpdateUserProfileUseCase'>UpdateUserProfileUseCase</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html">app/application/use_cases/update_user_profile.py</a></td>
                <td class="name left"><a href="z_330f5ee58403c77c_update_user_profile_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t12">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html#t12"><data value='BaseEntity'>BaseEntity</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html">app/domain/entities/base.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t13">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t13"><data value='MuscleGroup'>MuscleGroup</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t33">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t33"><data value='MovementPattern'>MovementPattern</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t48">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t48"><data value='Equipment'>Equipment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t64">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t64"><data value='DifficultyLevel'>DifficultyLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t72">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t72"><data value='ApprovalStatus'>ApprovalStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t81">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t81"><data value='ChangeReason'>ChangeReason</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t92">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t92"><data value='MediaType'>MediaType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t101">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t101"><data value='AuditAction'>AuditAction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t113">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t113"><data value='ExerciseMedia'>ExerciseMedia</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t134">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t134"><data value='Config'>ExerciseMedia.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t140">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t140"><data value='Exercise'>Exercise</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t261">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t261"><data value='Config'>Exercise.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t268">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t268"><data value='ExerciseAuditLog'>ExerciseAuditLog</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t284">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t284"><data value='Config'>ExerciseAuditLog.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t290">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t290"><data value='ExerciseSearchFilters'>ExerciseSearchFilters</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t309">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t309"><data value='ExerciseCreateRequest'>ExerciseCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t329">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html#t329"><data value='ExerciseUpdateRequest'>ExerciseUpdateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html">app/domain/entities/exercise.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_exercise_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>200</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="200 200">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t15">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html#t15"><data value='User'>User</data></a></td>
                <td>47</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 47">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html">app/domain/entities/user.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t17">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t17"><data value='WorkoutStatus'>WorkoutStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t26">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t26"><data value='RangeOfMotion'>RangeOfMotion</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t35">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t35"><data value='Workout'>Workout</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t133">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t133"><data value='ExerciseSet'>ExerciseSet</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t202">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t202"><data value='WorkoutCreateRequest'>WorkoutCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t212">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t212"><data value='WorkoutUpdateRequest'>WorkoutUpdateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t222">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t222"><data value='ExerciseSetCreateRequest'>ExerciseSetCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t242">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t242"><data value='ExerciseSetUpdateRequest'>ExerciseSetUpdateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t260">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html#t260"><data value='WorkoutSearchFilters'>WorkoutSearchFilters</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html">app/domain/entities/workout.py</a></td>
                <td class="name left"><a href="z_3efee37c27925ae3_workout_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>115</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="115 115">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t8">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t8"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t12">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t12"><data value='InvalidCredentialsError'>InvalidCredentialsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t16">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t16"><data value='InvalidEmailError'>InvalidEmailError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t20">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t20"><data value='WeakPasswordError'>WeakPasswordError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t24">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t24"><data value='UserNotFoundError'>UserNotFoundError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t28">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t28"><data value='UserAlreadyExistsError'>UserAlreadyExistsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t32">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t32"><data value='DuplicateEmailError'>DuplicateEmailError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t36">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t36"><data value='InactiveUserError'>InactiveUserError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t40">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t40"><data value='TokenExpiredError'>TokenExpiredError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t44">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html#t44"><data value='InvalidTokenError'>InvalidTokenError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html">app/domain/exceptions/auth_exceptions.py</a></td>
                <td class="name left"><a href="z_91f70f5812085d2d_auth_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t19">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html#t19"><data value='ExerciseRepository'>ExerciseRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>229</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html">app/domain/repositories/exercise_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_exercise_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>75</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t13">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html#t13"><data value='UserRepository'>UserRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>54</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html">app/domain/repositories/user_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_user_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t23">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html#t23"><data value='WorkoutRepository'>WorkoutRepository</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>231</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html">app/domain/repositories/workout_repository.py</a></td>
                <td class="name left"><a href="z_a6abcadb6030ddcc_workout_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>73</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t20">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t20"><data value='PasswordHandler'>PasswordHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t32">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html#t32"><data value='AuthService'>AuthService</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html">app/domain/services/auth_service.py</a></td>
                <td class="name left"><a href="z_525f7ea2345a0e41_auth_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t16">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html#t16"><data value='JWTHandler'>JWTHandler</data></a></td>
                <td>45</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="4 45">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html">app/infrastructure/auth/jwt_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_jwt_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t9">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html#t9"><data value='PasswordHandler'>PasswordHandler</data></a></td>
                <td>4</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="1 4">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html">app/infrastructure/auth/password_handler.py</a></td>
                <td class="name left"><a href="z_53e7b745ea159b35_password_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t21">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t21"><data value='Base'>Base</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t25">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html#t25"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>27</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="2 27">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html">app/infrastructure/database/connection.py</a></td>
                <td class="name left"><a href="z_90bdfa917c6a6771_connection_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="15 17">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t30">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t30"><data value='ExerciseModel'>ExerciseModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t213">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t213"><data value='ExerciseMediaModel'>ExerciseMediaModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t279">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html#t279"><data value='ExerciseAuditLogModel'>ExerciseAuditLogModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html">app/infrastructure/database/models/exercise_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_exercise_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="81 81">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t17">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t17"><data value='UserModel'>UserModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t69">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html#t69"><data value='RefreshTokenModel'>RefreshTokenModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html">app/infrastructure/database/models/user_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_user_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t29">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t29"><data value='WorkoutModel'>WorkoutModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t86">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html#t86"><data value='ExerciseSetModel'>ExerciseSetModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html">app/infrastructure/database/models/workout_model.py</a></td>
                <td class="name left"><a href="z_abf0792f91b2e734_workout_model_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t29">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html#t29"><data value='ExerciseRepositoryImpl'>ExerciseRepositoryImpl</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html">app/infrastructure/database/repositories/exercise_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_exercise_repository_impl_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t21">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html#t21"><data value='UserRepositoryImpl'>UserRepositoryImpl</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html">app/infrastructure/database/repositories/user_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_user_repository_impl_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t29">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html#t29"><data value='WorkoutRepositoryImpl'>WorkoutRepositoryImpl</data></a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html">app/infrastructure/database/repositories/workout_repository_impl.py</a></td>
                <td class="name left"><a href="z_2fdff8974586b090_workout_repository_impl_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="22 30">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html">app/presentation/api/dependencies.py</a></td>
                <td class="name left"><a href="z_d36cccc441caff8a_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="29 55">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html">app/presentation/api/v1/auth.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="19 44">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html">app/presentation/api/v1/health.py</a></td>
                <td class="name left"><a href="z_02460f4fdd8331f5_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="14 32">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html">app/presentation/api/v1/routes/exercises.py</a></td>
                <td class="name left"><a href="z_6afd4035ffcb4024_exercises_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>128</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="36 128">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t11">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t11"><data value='LoginRequest'>LoginRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t17">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t17"><data value='Config'>LoginRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t23">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t23"><data value='LoginResponse'>LoginResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t31">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t31"><data value='Config'>LoginResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t52">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t52"><data value='RefreshTokenRequest'>RefreshTokenRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t57">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t57"><data value='Config'>RefreshTokenRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t63">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t63"><data value='RefreshTokenResponse'>RefreshTokenResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t70">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html#t70"><data value='Config'>RefreshTokenResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html">app/presentation/schemas/auth_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_auth_schema_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t24">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t24"><data value='ExerciseMediaSchema'>ExerciseMediaSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t45">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t45"><data value='Config'>ExerciseMediaSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t51">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t51"><data value='ExerciseBaseSchema'>ExerciseBaseSchema</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t109">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t109"><data value='ExerciseCreateRequest'>ExerciseCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t114">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t114"><data value='Config'>ExerciseCreateRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t147">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t147"><data value='ExerciseUpdateRequest'>ExerciseUpdateRequest</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t180">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t180"><data value='ExerciseSchema'>ExerciseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t216">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t216"><data value='Config'>ExerciseSchema.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t222">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t222"><data value='ExerciseListResponse'>ExerciseListResponse</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t239">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t239"><data value='ExerciseVersionsResponse'>ExerciseVersionsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t249">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t249"><data value='ExerciseMediaCreateRequest'>ExerciseMediaCreateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t268">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t268"><data value='ExerciseMediaUpdateRequest'>ExerciseMediaUpdateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t278">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t278"><data value='ExerciseMediaReorderRequest'>ExerciseMediaReorderRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t287">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t287"><data value='ExerciseApprovalRequest'>ExerciseApprovalRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t293">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t293"><data value='ExerciseRejectionRequest'>ExerciseRejectionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t300">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t300"><data value='ExerciseStatisticsResponse'>ExerciseStatisticsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t325">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t325"><data value='BulkApprovalRequest'>BulkApprovalRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t332">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t332"><data value='BulkDeleteRequest'>BulkDeleteRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t338">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html#t338"><data value='BulkOperationResponse'>BulkOperationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html">app/presentation/schemas/exercise_schemas.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_exercise_schemas_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>170</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="170 170">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t12">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t12"><data value='UserResponse'>UserResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t25">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t25"><data value='Config'>UserResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t30">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t30"><data value='UserCreateRequest'>UserCreateRequest</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t71">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t71"><data value='Config'>UserCreateRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t83">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t83"><data value='UserUpdateRequest'>UserUpdateRequest</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t103">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html#t103"><data value='Config'>UserUpdateRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html">app/presentation/schemas/user_schema.py</a></td>
                <td class="name left"><a href="z_7e13110740283a02_user_schema_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2341</td>
                <td>1084</td>
                <td>730</td>
                <td class="right" data-ratio="1257 2341">54%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-15 11:19 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
