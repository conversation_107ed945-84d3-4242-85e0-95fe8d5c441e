Feature: User Authentication # features/user_authentication.feature:1
  As a fitness enthusiast
  I want to register and authenticate with the RP Training API
  So that I can access personalized training features
  Background:   # features/user_authentication.feature:6

  @authentication @registration
  Scenario: Successful user registration                     # features/user_authentication.feature:11
    Given the RP Training API is running                     # features/steps/authentication_steps.py:32
    And the database is clean                                # features/steps/authentication_steps.py:44
    Given I am a new user                                    # features/steps/authentication_steps.py:55
    When I register with valid credentials                   # features/steps/authentication_steps.py:63
      | email                | password       | first_name | last_name | training_experience |
      | <EMAIL> | SecurePass123! | John       | Doe       | 2                   |
      Traceback (most recent call last):
        File "/nix/store/dmbql7m71b349bxkhmkix8bvxd61mh5z-python3.11-behave-1.2.7.dev5/lib/python3.11/site-packages/behave/model.py", line 1812, in run
          match.run(runner.context)
        File "/nix/store/dmbql7m71b349bxkhmkix8bvxd61mh5z-python3.11-behave-1.2.7.dev5/lib/python3.11/site-packages/behave/matchers.py", line 103, in run
          self.func(context, *args, **kwargs)
        File "features/steps/authentication_steps.py", line 90, in step_register_valid_credentials
          user=UserDTO(
               ^^^^^^^^
        File "/nix/store/zp0aiisb3gcp63hj58kpniizqjawj1mc-python3.11-pydantic-2.9.2/lib/python3.11/site-packages/pydantic/main.py", line 212, in __init__
          validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      pydantic_core._pydantic_core.ValidationError: 1 validation error for UserDTO
      id
        Input should be a valid UUID, invalid character: expected an optional prefix of `urn:uuid:` followed by [0-9a-fA-F-], found `t` at 1 [type=uuid_parsing, input_value='test-user-id', input_type=str]
          For further information visit https://errors.pydantic.dev/2.9/v/uuid_parsing
      
      Captured logging:
      INFO     httpx      HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"

    Then I should receive a successful registration response # None
    And I should get an authentication token                 # None
    And my user profile should be created                    # None
    And I should be able to access protected endpoints       # None

