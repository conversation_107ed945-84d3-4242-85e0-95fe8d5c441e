<testsuite name="user_authentication.User Authentication" tests="19" errors="0" failures="6" skipped="19" time="0.0" timestamp="2025-06-15T11:22:22.232203" hostname="ganymede"><testcase classname="user_authentication.User Authentication" name="Successful user registration" status="untested" time="0"><failure type="undefined" message="Undefined Step: I should be able to access protected endpoints" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration
  Scenario: Successful user registration
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with valid credentials ... untested in 0.000s
      | email                | password       | first_name | last_name | training_experience |
      | <EMAIL> | SecurePass123! | John       | Doe       | 2                   |
    Then I should receive a successful registration response ... untested in 0.000s
    And I should get an authentication token ... untested in 0.000s
    And my user profile should be created ... untested in 0.000s
    And I should be able to access protected endpoints ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with existing email" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration
  Scenario: Registration with existing email
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given a user already exists with email "<EMAIL>" ... untested in 0.000s
    When I try to register with the same email ... untested in 0.000s
      | email                | password    | first_name | last_name | training_experience |
      | <EMAIL> | NewPass123! | Jane       | Smith     | 3                   |
    Then I should receive a conflict error ... untested in 0.000s
    And the response should indicate the email is already taken ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with invalid data -- @1.1 " status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data -- @1.1 
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with invalid data ... untested in 0.000s
      | email         | password       | first_name | last_name | training_experience |
      | invalid-email | SecurePass123! | John       | Doe       | 2                   |
    Then I should receive a validation error ... untested in 0.000s
    And the response should indicate the specific validation issue ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with invalid data -- @1.2 " status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data -- @1.2 
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with invalid data ... untested in 0.000s
      | email            | password | first_name | last_name | training_experience |
      | <EMAIL> | weak     | John       | Doe       | 2                   |
    Then I should receive a validation error ... untested in 0.000s
    And the response should indicate the specific validation issue ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with invalid data -- @1.3 " status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data -- @1.3 
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with invalid data ... untested in 0.000s
      | email            | password       | first_name | last_name | training_experience |
      | <EMAIL> | SecurePass123! |            | Doe       | 2                   |
    Then I should receive a validation error ... untested in 0.000s
    And the response should indicate the specific validation issue ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with invalid data -- @1.4 " status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data -- @1.4 
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with invalid data ... untested in 0.000s
      | email            | password       | first_name | last_name | training_experience |
      | <EMAIL> | SecurePass123! | John       |           | 2                   |
    Then I should receive a validation error ... untested in 0.000s
    And the response should indicate the specific validation issue ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Registration with invalid data -- @1.5 " status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data -- @1.5 
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a new user ... untested in 0.000s
    When I register with invalid data ... untested in 0.000s
      | email            | password       | first_name | last_name | training_experience |
      | <EMAIL> | SecurePass123! | John       | Doe       | -1                  |
    Then I should receive a validation error ... untested in 0.000s
    And the response should indicate the specific validation issue ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Successful user login" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @login
  Scenario: Successful user login
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a registered user with credentials ... untested in 0.000s
      | email             | password       |
      | <EMAIL> | SecurePass123! |
    When I login with correct credentials ... untested in 0.000s
    Then I should receive a successful login response ... untested in 0.000s
    And I should get an authentication token ... untested in 0.000s
    And the token should be valid for API access ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Login with incorrect password" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @login
  Scenario: Login with incorrect password
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a registered user with email "<EMAIL>" ... untested in 0.000s
    When I login with incorrect password "WrongPassword123!" ... untested in 0.000s
    Then I should receive an authentication error ... untested in 0.000s
    And the response should indicate invalid credentials ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Login with non-existent user" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @login
  Scenario: Login with non-existent user
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given no user exists with email "<EMAIL>" ... untested in 0.000s
    When I try to login with email "<EMAIL>" ... untested in 0.000s
    Then I should receive an authentication error ... untested in 0.000s
    And the response should indicate invalid credentials ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Login with inactive user account" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @login @inactive
  Scenario: Login with inactive user account
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a registered but inactive user ... untested in 0.000s
    When I try to login with my credentials ... untested in 0.000s
    Then I should receive an authentication error ... untested in 0.000s
    And the response should indicate the account is inactive ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Access protected endpoint with valid token" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @token
  Scenario: Access protected endpoint with valid token
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a logged-in user ... untested in 0.000s
    When I access a protected endpoint with my token ... untested in 0.000s
    Then I should receive a successful response ... untested in 0.000s
    And I should get my user profile data ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Access protected endpoint with invalid token" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @token
  Scenario: Access protected endpoint with invalid token
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I have an invalid authentication token ... untested in 0.000s
    When I try to access a protected endpoint ... untested in 0.000s
    Then I should receive an unauthorized error ... untested in 0.000s
    And I should be prompted to authenticate ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Access protected endpoint with expired token" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @authentication @token
  Scenario: Access protected endpoint with expired token
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I have an expired authentication token ... untested in 0.000s
    When I try to access a protected endpoint ... untested in 0.000s
    Then I should receive an unauthorized error ... untested in 0.000s
    And the response should indicate the token has expired ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Token refresh workflow" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am a logged-in user with a refresh token" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @token @refresh
  Scenario: Token refresh workflow
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a logged-in user with a refresh token ... undefined in 0.000s
    When my access token expires ... undefined in 0.000s
    And I use my refresh token to get a new access token ... undefined in 0.000s
    Then I should receive a new valid access token ... undefined in 0.000s
    And I should be able to access protected endpoints again ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Multiple failed login attempts" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am a registered user" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @security
  Scenario: Multiple failed login attempts
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a registered user ... undefined in 0.000s
    When I make multiple failed login attempts ... undefined in 0.000s
    Then the system should handle the attempts gracefully ... undefined in 0.000s
    And I should still be able to login with correct credentials ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Password strength validation" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am registering a new account" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @security
  Scenario: Password strength validation
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am registering a new account ... undefined in 0.000s
    When I try different password strengths ... undefined in 0.000s
      | password        | should_succeed |
      | weak            | false          |
      | NoNumbers!      | false          |
      | nonumbers123    | false          |
      | NOLOWERCASE123! | false          |
      | SecurePass123!  | true           |
    Then the system should enforce password strength requirements ... undefined in 0.000s
    And only strong passwords should be accepted ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="Concurrent user registrations" status="untested" time="0"><failure type="undefined" message="Undefined Step: multiple users are registering simultaneously" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @concurrent
  Scenario: Concurrent user registrations
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given multiple users are registering simultaneously ... undefined in 0.000s
    When they all submit registration requests at the same time ... undefined in 0.000s
    Then all valid registrations should succeed ... undefined in 0.000s
    And each user should get a unique account ... undefined in 0.000s
    And no data corruption should occur ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_authentication.User Authentication" name="User session management" status="untested" time="0"><failure type="undefined" message="Undefined Step: I perform various authenticated actions" /><system-out>
<![CDATA[
@scenario.begin

  @authentication @session
  Scenario: User session management
    Given the RP Training API is running ... untested in 0.000s
    And the database is clean ... untested in 0.000s
    Given I am a logged-in user ... untested in 0.000s
    When I perform various authenticated actions ... undefined in 0.000s
    Then my session should remain active ... undefined in 0.000s
    And I should not need to re-authenticate for each request ... undefined in 0.000s
    And my session should eventually expire after inactivity ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>