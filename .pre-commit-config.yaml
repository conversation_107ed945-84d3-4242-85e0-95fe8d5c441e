# Pre-commit configuration for Forge Protocol
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Pre-commit hooks for general file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        exclude: ^docs/
      - id: end-of-file-fixer
        exclude: ^docs/
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
        args: ["--maxkb=1000"]
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable

  # Ruff for linting and formatting
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.4
    hooks:
      # Run the linter
      - id: ruff
        args: [--fix]
        types_or: [python, pyi, jupyter]
      # Run the formatter
      - id: ruff-format
        types_or: [python, pyi, jupyter]

  # MyPy for type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.13.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-python-jose
          - types-passlib
          - sqlalchemy[mypy]
          - pydantic
        args: [--strict, --ignore-missing-imports]
        exclude: ^(alembic/|tests/|docs/)

  # Security checks with bandit
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^tests/

  # Check for common security issues
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ["--baseline", ".secrets.baseline"]
        exclude: ^(\.secrets\.baseline|test.*|.*\.lock)$

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # SQL formatting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 3.2.5
    hooks:
      - id: sqlfluff-lint
        additional_dependencies: ["dbt-postgres", "sqlfluff-templater-dbt"]
        args: [--dialect, postgres]
        files: \.sql$
        exclude: ^alembic/versions/

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        types_or: [yaml, markdown]
        exclude: ^docs/

  # Check Python docstrings
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        additional_dependencies: [tomli]
        args: [--convention=google]
        exclude: ^(tests/|alembic/|docs/)

  # Check for Python import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile, black, --check-only, --diff]

  # Check for unused Python imports
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args:
          - --in-place
          - --remove-all-unused-imports
          - --remove-unused-variables
          - --remove-duplicate-keys
          - --ignore-init-module-imports
        exclude: ^(alembic/|__init__\.py)

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ""
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit autoupdate"
  autoupdate_schedule: weekly
  skip: []
  submodules: false
