# BDD Tests for RP Training API

This directory contains comprehensive Behavior-Driven Development (BDD) tests for the RP Training API using the Gherkin language and Behave framework.

## 📁 Directory Structure

```
features/
├── README.md                           # This file
├── environment.py                      # Behave environment setup and utilities
├── steps/                             # Step definitions
│   ├── authentication_steps.py        # Authentication scenario steps
│   ├── profile_steps.py              # Profile management steps
│   ├── health_monitoring_steps.py    # Health check steps
│   └── error_performance_steps.py    # Error handling and performance steps
├── user_authentication.feature        # User authentication scenarios
├── user_profile_management.feature    # Profile management scenarios
├── api_health_monitoring.feature      # Health and monitoring scenarios
├── error_handling_edge_cases.feature  # Error handling scenarios
└── performance_security.feature       # Performance and security scenarios
```

## 🎯 Test Coverage

### **User Authentication** (`user_authentication.feature`)
- ✅ User registration with valid/invalid data
- ✅ User login with correct/incorrect credentials
- ✅ Token-based authentication and authorization
- ✅ Session management and token refresh
- ✅ Security validations and attack prevention
- ✅ Concurrent user operations

### **User Profile Management** (`user_profile_management.feature`)
- ✅ Profile viewing and data retrieval
- ✅ Profile updates with validation
- ✅ Partial profile updates
- ✅ Training experience progression
- ✅ Data consistency and audit trails
- ✅ Privacy and authorization controls

### **API Health and Monitoring** (`api_health_monitoring.feature`)
- ✅ Basic and detailed health checks
- ✅ Database health monitoring
- ✅ System performance metrics
- ✅ Component status reporting
- ✅ Graceful degradation scenarios
- ✅ Real-time monitoring capabilities

### **Error Handling and Edge Cases** (`error_handling_edge_cases.feature`)
- ✅ Input validation and sanitization
- ✅ Malformed request handling
- ✅ Boundary value testing
- ✅ Unicode and internationalization
- ✅ Security attack prevention
- ✅ Graceful error recovery

### **Performance and Security** (`performance_security.feature`)
- ✅ Response time requirements
- ✅ Throughput and load testing
- ✅ Security vulnerability assessment
- ✅ Rate limiting and DDoS protection
- ✅ Encryption and data protection
- ✅ Compliance and audit requirements

## 🏷️ Test Tags

### **Feature Tags**
- `@authentication` - Authentication and login scenarios
- `@profile` - User profile management scenarios
- `@health` - Health check and monitoring scenarios
- `@error_handling` - Error handling and edge cases
- `@performance` - Performance testing scenarios
- `@security` - Security testing scenarios

### **Test Type Tags**
- `@smoke` - Quick smoke tests for basic functionality
- `@regression` - Comprehensive regression test suite
- `@integration` - Integration testing scenarios
- `@acceptance` - User acceptance tests

### **Priority Tags**
- `@critical` - Critical functionality that must work
- `@high` - High priority features
- `@medium` - Medium priority features
- `@low` - Low priority features

### **Environment Tags**
- `@local` - Tests for local development environment
- `@staging` - Tests for staging environment
- `@production` - Tests safe for production environment

### **Specialized Tags**
- `@validation` - Input validation scenarios
- `@concurrent` - Concurrent operation tests
- `@rate_limiting` - Rate limiting tests
- `@boundary` - Boundary value tests
- `@unicode` - Unicode and internationalization tests

## 🚀 Running BDD Tests

### **Prerequisites**
```bash
# Install Behave (if not already installed)
pip install behave

# Ensure you're in the project root directory
cd /path/to/rp-training-api
```

### **Basic Test Execution**

```bash
# Run all BDD tests
python run_bdd_tests.py

# Run with Behave directly
python -m behave features/
```

### **Tag-Based Test Execution**

```bash
# Run smoke tests
python run_bdd_tests.py --smoke

# Run authentication tests
python run_bdd_tests.py --tags @authentication

# Run critical tests only
python run_bdd_tests.py --tags @critical

# Run performance and security tests
python run_bdd_tests.py --tags "@performance or @security"

# Exclude work-in-progress tests
python run_bdd_tests.py --tags "not @wip"

# Run authentication AND profile tests
python run_bdd_tests.py --tags "@authentication and @profile"
```

### **Feature-Specific Test Execution**

```bash
# Run specific feature file
python run_bdd_tests.py --features user_authentication.feature

# Run multiple feature files
python run_bdd_tests.py --features user_authentication.feature api_health_monitoring.feature
```

### **Advanced Test Options**

```bash
# Dry run to check syntax
python run_bdd_tests.py --dry-run

# Verbose output with debugging
python run_bdd_tests.py --verbose --no-capture

# Generate JUnit reports
python run_bdd_tests.py --junit

# Stop on first failure
python run_bdd_tests.py --stop

# Custom output format
python run_bdd_tests.py --format json
```

### **Predefined Test Suites**

```bash
# Smoke test suite
python run_bdd_tests.py --smoke

# Regression test suite
python run_bdd_tests.py --regression

# Performance test suite
python run_bdd_tests.py --performance

# Security test suite
python run_bdd_tests.py --security
```

## 📊 Test Reports

### **Output Formats**
- **Pretty** (default): Human-readable console output
- **JSON**: Machine-readable JSON format
- **JUnit**: XML format for CI/CD integration
- **Plain**: Simple text output
- **Progress**: Progress bar format

### **Report Locations**
- Console output: Real-time during test execution
- Text reports: `reports/behave-report.txt`
- JUnit XML: `reports/junit/`
- JSON reports: Custom location with `--format json`

## 🔧 Configuration

### **Behave Configuration** (`behave.ini`)
```ini
[behave]
format = pretty
tags = ~@wip,~@skip
show_skipped = true
color = true
summary = true
```

### **Environment Configuration** (`features/environment.py`)
- Test context management
- Database setup and cleanup
- Mock configuration
- Utility functions

## 🧪 Writing New BDD Tests

### **1. Create Feature File**
```gherkin
Feature: New Feature
  As a user
  I want to perform some action
  So that I can achieve some goal

  @new_feature @smoke
  Scenario: Basic functionality
    Given I have some precondition
    When I perform some action
    Then I should see some result
```

### **2. Implement Step Definitions**
```python
@given('I have some precondition')
def step_precondition(context):
    # Setup code
    pass

@when('I perform some action')
def step_action(context):
    # Action code
    pass

@then('I should see some result')
def step_verification(context):
    # Verification code
    pass
```

### **3. Add Appropriate Tags**
- Feature tags: `@authentication`, `@profile`, etc.
- Priority tags: `@critical`, `@high`, etc.
- Test type tags: `@smoke`, `@regression`, etc.

## 🎯 Best Practices

### **Scenario Writing**
1. **Clear and Descriptive**: Scenarios should be self-explanatory
2. **Independent**: Each scenario should be able to run independently
3. **Focused**: One scenario should test one specific behavior
4. **Realistic**: Use realistic data and user workflows

### **Step Definitions**
1. **Reusable**: Write steps that can be reused across scenarios
2. **Atomic**: Each step should perform one clear action
3. **Robust**: Handle edge cases and error conditions
4. **Clean**: Use proper setup and teardown

### **Data Management**
1. **Test Data**: Use factories for consistent test data
2. **Isolation**: Ensure tests don't interfere with each other
3. **Cleanup**: Clean up test data after scenarios
4. **Mocking**: Use mocks for external dependencies

## 🔍 Debugging BDD Tests

### **Common Issues**
1. **Step Not Found**: Check step definition imports and patterns
2. **Context Errors**: Verify context setup in environment.py
3. **Mock Issues**: Ensure mocks are properly configured
4. **Data Issues**: Check test data factories and setup

### **Debugging Commands**
```bash
# Run with verbose output
python run_bdd_tests.py --verbose --no-capture

# Run single scenario with debugging
python -m behave features/user_authentication.feature:10 --no-capture

# Dry run to check syntax
python run_bdd_tests.py --dry-run
```

## 📈 Continuous Integration

### **CI/CD Integration**
```yaml
# Example GitHub Actions workflow
- name: Run BDD Tests
  run: |
    python run_bdd_tests.py --junit --tags "not @slow"
    
- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: bdd-test-results
    path: reports/junit/
```

### **Quality Gates**
- All `@critical` tests must pass
- Smoke tests must pass for deployment
- Performance tests must meet SLA requirements
- Security tests must show no vulnerabilities

## 🎉 Benefits of BDD Testing

1. **Living Documentation**: Tests serve as up-to-date documentation
2. **Stakeholder Communication**: Non-technical stakeholders can understand tests
3. **Requirement Validation**: Ensures features meet business requirements
4. **Regression Prevention**: Catches regressions in user workflows
5. **Quality Assurance**: Validates end-to-end user experiences

---

**Happy Testing! 🧪✨**

For questions or contributions, please refer to the main project documentation or create an issue in the repository.
