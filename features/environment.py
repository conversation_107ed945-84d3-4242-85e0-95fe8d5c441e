"""
Behave environment configuration for RP Training API BDD tests.

This module sets up the testing environment, manages test data,
and provides common utilities for BDD scenarios.
"""

import asyncio
import os
import sys
from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, patch

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.main import app
from app.infrastructure.database.connection import db_manager
from tests.factories import UserFactory


class BDDTestContext:
    """Context manager for BDD test scenarios."""
    
    def __init__(self):
        self.client: Optional[AsyncClient] = None
        self.test_client: Optional[TestClient] = None
        self.response: Optional[Any] = None
        self.user_data: Dict[str, Any] = {}
        self.auth_token: Optional[str] = None
        self.created_users: list = []
        self.test_data: Dict[str, Any] = {}
        self.mock_patches: list = []
    
    async def setup_async_client(self):
        """Set up async HTTP client for testing."""
        self.client = AsyncClient(app=app, base_url="http://testserver")
    
    async def cleanup_async_client(self):
        """Clean up async HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None
    
    def setup_test_client(self):
        """Set up synchronous test client."""
        self.test_client = TestClient(app)
    
    def cleanup_test_client(self):
        """Clean up test client."""
        if self.test_client:
            self.test_client.close()
            self.test_client = None
    
    def create_test_user_data(self, **overrides) -> Dict[str, Any]:
        """Create test user data with optional overrides."""
        user_data = UserFactory.create_user_data()
        user_data.update(overrides)
        self.user_data = user_data
        return user_data
    
    def add_mock_patch(self, patch_obj):
        """Add a mock patch to be cleaned up later."""
        self.mock_patches.append(patch_obj)
    
    def cleanup_mocks(self):
        """Clean up all mock patches."""
        for mock_patch in self.mock_patches:
            if hasattr(mock_patch, 'stop'):
                mock_patch.stop()
        self.mock_patches.clear()
    
    def reset(self):
        """Reset context for new scenario."""
        self.response = None
        self.user_data = {}
        self.auth_token = None
        self.test_data = {}
        self.cleanup_mocks()


def before_all(context):
    """Set up before all scenarios."""
    context.bdd_context = BDDTestContext()
    context.event_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(context.event_loop)


def before_scenario(context, scenario):
    """Set up before each scenario."""
    context.bdd_context.reset()
    context.bdd_context.setup_test_client()
    
    # Set up async client if needed
    if hasattr(scenario, 'tags') and 'async' in scenario.tags:
        context.event_loop.run_until_complete(
            context.bdd_context.setup_async_client()
        )


def after_scenario(context, scenario):
    """Clean up after each scenario."""
    context.bdd_context.cleanup_test_client()
    
    # Clean up async client if it was set up
    if context.bdd_context.client:
        context.event_loop.run_until_complete(
            context.bdd_context.cleanup_async_client()
        )
    
    context.bdd_context.cleanup_mocks()


def after_all(context):
    """Clean up after all scenarios."""
    if hasattr(context, 'event_loop'):
        context.event_loop.close()


# Common test utilities for BDD scenarios
class BDDTestUtils:
    """Utility functions for BDD tests."""
    
    @staticmethod
    def assert_response_status(response, expected_status: int):
        """Assert response has expected status code."""
        assert response.status_code == expected_status, (
            f"Expected status {expected_status}, got {response.status_code}. "
            f"Response: {response.text}"
        )
    
    @staticmethod
    def assert_response_contains(response, expected_content: str):
        """Assert response contains expected content."""
        response_text = response.text.lower()
        expected_content = expected_content.lower()
        assert expected_content in response_text, (
            f"Expected '{expected_content}' in response. "
            f"Got: {response.text}"
        )
    
    @staticmethod
    def assert_json_field(response, field_path: str, expected_value: Any):
        """Assert JSON response field has expected value."""
        try:
            data = response.json()
            field_parts = field_path.split('.')
            current = data
            
            for part in field_parts:
                current = current[part]
            
            assert current == expected_value, (
                f"Expected {field_path}={expected_value}, got {current}"
            )
        except (KeyError, TypeError) as e:
            raise AssertionError(f"Field {field_path} not found in response: {e}")
    
    @staticmethod
    def assert_json_field_exists(response, field_path: str):
        """Assert JSON response field exists."""
        try:
            data = response.json()
            field_parts = field_path.split('.')
            current = data
            
            for part in field_parts:
                current = current[part]
                
        except (KeyError, TypeError) as e:
            raise AssertionError(f"Field {field_path} not found in response: {e}")
    
    @staticmethod
    def extract_auth_token(response) -> str:
        """Extract authentication token from response."""
        try:
            data = response.json()
            return data.get('access_token', '')
        except Exception:
            return ''
    
    @staticmethod
    def create_auth_headers(token: str) -> Dict[str, str]:
        """Create authorization headers with token."""
        return {"Authorization": f"Bearer {token}"}


# Make utilities available to step definitions
def get_bdd_utils():
    """Get BDD test utilities."""
    return BDDTestUtils
