Feature: Performance and Security
  As a system administrator and security officer
  I want to ensure the RP Training API meets performance requirements and security standards
  So that users have a fast, secure, and reliable experience

  Background:
    Given the RP Training API is running
    And performance monitoring is enabled
    And security measures are active

  @performance @response_time
  Scenario: API response time requirements
    When I measure response times for critical endpoints:
      | endpoint                | max_response_time | load_level |
      | /api/v1/health         | 500ms             | normal     |
      | /api/v1/auth/register  | 2s                | normal     |
      | /api/v1/auth/login     | 1s                | normal     |
      | /api/v1/auth/me        | 500ms             | normal     |
    Then all endpoints should meet response time requirements
    And performance should be consistent across multiple requests
    And no endpoint should exceed the maximum allowed time

  @performance @throughput
  Scenario: API throughput and concurrent users
    Given the system can handle concurrent users
    When I simulate concurrent user load:
      | concurrent_users | requests_per_second | duration | success_rate |
      | 10              | 50                  | 30s      | 99%          |
      | 50              | 200                 | 60s      | 95%          |
      | 100             | 400                 | 30s      | 90%          |
    Then the system should maintain the required success rate
    And response times should remain within acceptable limits
    And no requests should be dropped or corrupted

  @performance @load_testing
  Scenario: Load testing under stress conditions
    Given the system is under stress testing
    When I apply increasing load levels
    Then the system should handle load gracefully
    And performance should degrade predictably
    And the system should recover when load decreases
    And no data should be lost or corrupted

  @performance @memory_usage
  Scenario: Memory usage optimization
    When I monitor memory usage during operations
    Then memory consumption should remain within limits
    And memory leaks should not occur
    And garbage collection should be efficient
    And memory usage should scale predictably with load

  @performance @database_performance
  Scenario: Database query performance
    When I execute database operations
    Then query response times should be optimized
    And database connections should be managed efficiently
    And connection pooling should work effectively
    And database locks should be minimized

  @security @authentication
  Scenario: Authentication security measures
    When I test authentication mechanisms
    Then passwords should be properly hashed
    And JWT tokens should be securely generated
    And token expiration should be enforced
    And authentication should resist brute force attacks
    And session management should be secure

  @security @authorization
  Scenario: Authorization and access control
    Given users have different permission levels
    When I test access control mechanisms
    Then users should only access authorized resources
    And privilege escalation should be prevented
    And resource isolation should be maintained
    And authorization decisions should be logged

  @security @input_validation
  Scenario: Input validation and sanitization
    When I submit various types of malicious input:
      | input_type           | payload                              | expected_behavior    |
      | sql_injection        | ' OR '1'='1                         | blocked_and_logged   |
      | xss_payload          | <script>alert('xss')</script>      | sanitized            |
      | command_injection    | ; cat /etc/passwd                   | blocked_and_logged   |
      | path_traversal       | ../../../etc/passwd                 | blocked_and_logged   |
      | buffer_overflow      | very_long_string_exceeding_limits   | handled_gracefully   |
      | format_string        | %s%s%s%s%s%s%s%s%s%s                | sanitized            |
    Then all malicious input should be properly handled
    And security incidents should be logged
    And the system should remain stable

  @security @data_protection
  Scenario: Data protection and privacy
    When I handle sensitive user data
    Then personal information should be encrypted at rest
    And data transmission should use HTTPS
    And sensitive data should not appear in logs
    And data retention policies should be enforced
    And user consent should be respected

  @security @rate_limiting
  Scenario: Rate limiting and DDoS protection
    Given rate limiting is configured
    When I test rate limiting mechanisms:
      | endpoint              | rate_limit        | time_window | expected_behavior |
      | /api/v1/auth/login   | 5 attempts       | 1 minute    | temporary_block   |
      | /api/v1/auth/register| 3 registrations  | 1 hour      | temporary_block   |
      | /api/v1/health       | 100 requests     | 1 minute    | throttling        |
    Then rate limits should be enforced correctly
    And legitimate users should not be affected
    And the system should recover after rate limit periods

  @security @encryption
  Scenario: Encryption and cryptographic security
    When I verify encryption implementations
    Then all passwords should use strong hashing algorithms
    And cryptographic keys should be properly managed
    And encryption should use current best practices
    And random number generation should be cryptographically secure
    And sensitive data should never be stored in plaintext

  @security @headers
  Scenario: Security headers and HTTPS
    When I check HTTP security headers
    Then security headers should be properly configured:
      | header                    | expected_value                |
      | X-Content-Type-Options    | nosniff                      |
      | X-Frame-Options           | DENY                         |
      | X-XSS-Protection          | 1; mode=block                |
      | Strict-Transport-Security | max-age=31536000             |
      | Content-Security-Policy   | appropriate_policy           |
    And HTTPS should be enforced
    And insecure protocols should be disabled

  @security @vulnerability_scanning
  Scenario: Vulnerability assessment
    When I perform security vulnerability scans
    Then no critical vulnerabilities should be present
    And known security issues should be patched
    And dependency vulnerabilities should be addressed
    And security configurations should be hardened

  @performance @caching
  Scenario: Caching and optimization
    When I test caching mechanisms
    Then frequently accessed data should be cached
    And cache invalidation should work correctly
    And cache hit rates should be optimized
    And caching should improve response times
    And cache should not serve stale data

  @performance @scalability
  Scenario: Horizontal and vertical scalability
    Given the system needs to scale
    When I test scalability mechanisms
    Then the system should scale horizontally
    And load should be distributed evenly
    And scaling should not affect data consistency
    And performance should improve with additional resources

  @security @audit_logging
  Scenario: Security audit logging
    When security-relevant events occur
    Then all events should be logged with sufficient detail
    And logs should include timestamps and user context
    And log integrity should be protected
    And logs should be available for security analysis
    And sensitive information should not be logged

  @security @compliance
  Scenario: Security compliance requirements
    When I verify compliance with security standards
    Then the system should meet OWASP guidelines
    And data protection regulations should be followed
    And security policies should be implemented
    And compliance documentation should be maintained
    And regular security assessments should be conducted

  @performance @monitoring
  Scenario: Performance monitoring and alerting
    When I monitor system performance
    Then key performance metrics should be tracked
    And performance alerts should be configured
    And monitoring should not impact system performance
    And historical performance data should be available
    And performance trends should be analyzable

  @security @incident_response
  Scenario: Security incident response
    Given a security incident occurs
    When the incident response plan is activated
    Then the incident should be contained quickly
    And affected systems should be isolated
    And incident details should be documented
    And recovery procedures should be followed
    And lessons learned should be incorporated

  @performance @resource_optimization
  Scenario: Resource utilization optimization
    When I analyze resource utilization
    Then CPU usage should be optimized
    And memory allocation should be efficient
    And network bandwidth should be used effectively
    And storage I/O should be minimized
    And resource contention should be avoided

  @security @penetration_testing
  Scenario: Penetration testing scenarios
    When I conduct penetration testing
    Then authentication bypasses should be prevented
    And privilege escalation should be blocked
    And data exfiltration should be detected
    And system compromise should be prevented
    And security controls should be effective

  @performance @stress_recovery
  Scenario: Stress testing and recovery
    Given the system is under extreme stress
    When stress conditions are applied and then removed
    Then the system should handle stress gracefully
    And recovery should be automatic and complete
    And no permanent damage should occur
    And performance should return to normal levels
    And system stability should be maintained

  @security @zero_trust
  Scenario: Zero trust security model
    When I implement zero trust principles
    Then every request should be authenticated
    And authorization should be verified for each resource
    And network traffic should be encrypted
    And access should be logged and monitored
    And trust should never be assumed
