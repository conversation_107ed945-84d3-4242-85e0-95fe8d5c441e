Feature: User Authentication
  As a fitness enthusiast
  I want to register and authenticate with the RP Training API
  So that I can access personalized training features

  Background:
    Given the RP Training API is running
    And the database is clean

  @authentication @registration
  Scenario: Successful user registration
    Given I am a new user
    When I register with valid credentials:
      | email                    | password      | first_name | last_name | training_experience |
      | <EMAIL>     | SecurePass123!| John       | Doe       | 2                   |
    Then I should receive a successful registration response
    And I should get an authentication token
    And my user profile should be created
    And I should be able to access protected endpoints

  @authentication @registration
  Scenario: Registration with existing email
    Given a user already exists with email "<EMAIL>"
    When I try to register with the same email:
      | email                | password      | first_name | last_name | training_experience |
      | <EMAIL> | NewPass123!   | Jane       | Smith     | 3                   |
    Then I should receive a conflict error
    And the response should indicate the email is already taken

  @authentication @registration @validation
  Scenario Outline: Registration with invalid data
    Given I am a new user
    When I register with invalid data:
      | email   | password   | first_name   | last_name   | training_experience   |
      | <email> | <password> | <first_name> | <last_name> | <training_experience> |
    Then I should receive a validation error
    And the response should indicate the specific validation issue

    Examples:
      | email              | password    | first_name | last_name | training_experience | validation_issue        |
      | invalid-email      | SecurePass123! | John    | Doe       | 2                   | Invalid email format    |
      | <EMAIL>   | weak        | John       | Doe       | 2                   | Password too weak       |
      | <EMAIL>   | SecurePass123! |         | Doe       | 2                   | First name required     |
      | <EMAIL>   | SecurePass123! | John    |           | 2                   | Last name required      |
      | <EMAIL>   | SecurePass123! | John    | Doe       | -1                  | Invalid experience      |

  @authentication @login
  Scenario: Successful user login
    Given I am a registered user with credentials:
      | email                | password      |
      | <EMAIL>    | SecurePass123!|
    When I login with correct credentials
    Then I should receive a successful login response
    And I should get an authentication token
    And the token should be valid for API access

  @authentication @login
  Scenario: Login with incorrect password
    Given I am a registered user with email "<EMAIL>"
    When I login with incorrect password "WrongPassword123!"
    Then I should receive an authentication error
    And the response should indicate invalid credentials

  @authentication @login
  Scenario: Login with non-existent user
    Given no user exists with email "<EMAIL>"
    When I try to login with email "<EMAIL>"
    Then I should receive an authentication error
    And the response should indicate invalid credentials

  @authentication @login @inactive
  Scenario: Login with inactive user account
    Given I am a registered but inactive user
    When I try to login with my credentials
    Then I should receive an authentication error
    And the response should indicate the account is inactive

  @authentication @token
  Scenario: Access protected endpoint with valid token
    Given I am a logged-in user
    When I access a protected endpoint with my token
    Then I should receive a successful response
    And I should get my user profile data

  @authentication @token
  Scenario: Access protected endpoint with invalid token
    Given I have an invalid authentication token
    When I try to access a protected endpoint
    Then I should receive an unauthorized error
    And I should be prompted to authenticate

  @authentication @token
  Scenario: Access protected endpoint with expired token
    Given I have an expired authentication token
    When I try to access a protected endpoint
    Then I should receive an unauthorized error
    And the response should indicate the token has expired

  @authentication @token @refresh
  Scenario: Token refresh workflow
    Given I am a logged-in user with a refresh token
    When my access token expires
    And I use my refresh token to get a new access token
    Then I should receive a new valid access token
    And I should be able to access protected endpoints again

  @authentication @security
  Scenario: Multiple failed login attempts
    Given I am a registered user
    When I make multiple failed login attempts
    Then the system should handle the attempts gracefully
    And I should still be able to login with correct credentials

  @authentication @security
  Scenario: Password strength validation
    Given I am registering a new account
    When I try different password strengths:
      | password        | should_succeed |
      | weak            | false          |
      | NoNumbers!      | false          |
      | nonumbers123    | false          |
      | NOLOWERCASE123! | false          |
      | SecurePass123!  | true           |
    Then the system should enforce password strength requirements
    And only strong passwords should be accepted

  @authentication @concurrent
  Scenario: Concurrent user registrations
    Given multiple users are registering simultaneously
    When they all submit registration requests at the same time
    Then all valid registrations should succeed
    And each user should get a unique account
    And no data corruption should occur

  @authentication @session
  Scenario: User session management
    Given I am a logged-in user
    When I perform various authenticated actions
    Then my session should remain active
    And I should not need to re-authenticate for each request
    And my session should eventually expire after inactivity
