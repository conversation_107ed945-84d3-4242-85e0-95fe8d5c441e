Feature: Error <PERSON>ling and <PERSON>s
  As a developer using the RP Training API
  I want the system to handle errors gracefully and provide meaningful feedback
  So that I can build robust applications and troubleshoot issues effectively

  Background:
    Given the RP Training API is running
    And error handling is properly configured

  @error_handling @validation
  Scenario: Invalid JSON request body
    When I send a request with invalid JSON:
      """
      {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "first_name": "<PERSON>",
        "invalid_json": 
      }
      """
    Then I should receive a JSON parsing error
    And the response should indicate the specific JSON syntax issue
    And the error should include helpful debugging information

  @error_handling @validation
  Scenario: Missing required fields in registration
    When I attempt to register without required fields:
      | field_missing | expected_error                    |
      | email         | Email is required                 |
      | password      | Password is required              |
      | first_name    | First name is required            |
      | last_name     | Last name is required             |
    Then I should receive field validation errors
    And the response should specify which fields are missing
    And the error format should be consistent

  @error_handling @validation
  Scenario: Invalid field formats and constraints
    When I submit registration data with invalid formats:
      | field                   | invalid_value                    | expected_error_type    |
      | email                   | not-an-email                     | Invalid email format   |
      | email                   | @missing-local.com               | Invalid email format   |
      | email                   | missing-domain@                  | Invalid email format   |
      | password                | weak                             | Password too weak      |
      | password                | 1234567                          | Password too weak      |
      | training_experience_years | -5                              | Invalid experience     |
      | training_experience_years | 999                             | Experience too high    |
      | training_experience_years | not_a_number                    | Invalid number format  |
    Then I should receive appropriate validation errors
    And each error should provide clear guidance for correction

  @error_handling @authentication
  Scenario: Authentication with malformed tokens
    When I access protected endpoints with malformed tokens:
      | token_type        | token_value                      | expected_error         |
      | missing_bearer    | invalid_token_123                | Invalid token format   |
      | empty_token       |                                  | Missing token          |
      | malformed_jwt     | not.a.valid.jwt.token           | Invalid token format   |
      | expired_token     | expired_jwt_token_12345          | Token expired          |
      | invalid_signature | tampered_jwt_token_67890         | Invalid token          |
    Then I should receive appropriate authentication errors
    And the system should not expose sensitive token details

  @error_handling @rate_limiting
  Scenario: Rate limiting and throttling
    Given rate limiting is enabled
    When I make excessive requests in a short time period
    Then I should receive rate limiting errors
    And the response should include retry-after information
    And the system should recover after the rate limit period

  @error_handling @database
  Scenario: Database connection failures
    Given the database becomes unavailable
    When I attempt to perform database operations
    Then I should receive appropriate service unavailable errors
    And the system should not expose database connection details
    And the API should remain responsive for non-database operations

  @error_handling @concurrent
  Scenario: Concurrent modification conflicts
    Given two users are updating the same resource simultaneously
    When both submit updates at the same time
    Then one update should succeed
    And the other should receive a conflict error
    And the conflict should be resolved gracefully
    And data integrity should be maintained

  @edge_cases @boundary_values
  Scenario: Boundary value testing for numeric fields
    When I test boundary values for training experience:
      | value | should_succeed | reason                           |
      | 0     | true           | Minimum valid experience         |
      | 1     | true           | Valid low experience             |
      | 25    | true           | Valid high experience            |
      | 50    | true           | Maximum realistic experience     |
      | -1    | false          | Below minimum                    |
      | 51    | false          | Above maximum                    |
      | 999   | false          | Unrealistic value                |
    Then the system should enforce appropriate boundaries
    And provide clear feedback for invalid values

  @edge_cases @string_lengths
  Scenario: String length validation and limits
    When I test string length limits:
      | field      | test_case                        | should_succeed |
      | first_name | A                                | true           |
      | first_name | John                             | true           |
      | first_name | VeryLongFirstNameThatExceedsLimit| false          |
      | first_name |                                  | false          |
      | last_name  | B                                | true           |
      | last_name  | Smith                            | true           |
      | last_name  | VeryLongLastNameThatExceedsLimit | false          |
      | email      | <EMAIL>                           | true           |
      | email      | <EMAIL> | depends |
    Then the system should enforce string length constraints
    And provide appropriate validation messages

  @edge_cases @special_characters
  Scenario: Special character handling
    When I submit data with special characters:
      | field      | value                           | expected_result |
      | first_name | José                            | accepted        |
      | first_name | Mary-Jane                       | accepted        |
      | first_name | O'Connor                        | accepted        |
      | first_name | <script>alert('xss')</script>   | sanitized       |
      | last_name  | van der Berg                    | accepted        |
      | last_name  | Smith-Jones                     | accepted        |
      | email      | <EMAIL>            | accepted        |
      | email      | <EMAIL>        | accepted        |
    Then the system should handle special characters appropriately
    And prevent security vulnerabilities

  @edge_cases @unicode
  Scenario: Unicode and internationalization support
    When I submit data with unicode characters:
      | field      | value                    | language    | should_succeed |
      | first_name | 田中                     | Japanese    | true           |
      | first_name | José                     | Spanish     | true           |
      | first_name | François                 | French      | true           |
      | first_name | Müller                   | German      | true           |
      | last_name  | Петров                   | Russian     | true           |
      | email      | test@münchen.de          | German      | true           |
    Then the system should support international characters
    And handle unicode encoding correctly

  @error_handling @network
  Scenario: Network timeout and connection issues
    Given network conditions are simulated
    When requests experience timeouts or connection issues
    Then the system should handle timeouts gracefully
    And provide appropriate error responses
    And not leave resources in inconsistent states

  @error_handling @memory
  Scenario: Memory and resource exhaustion
    Given the system is under memory pressure
    When processing large requests or high load
    Then the system should handle resource constraints gracefully
    And provide appropriate error responses
    And recover when resources become available

  @edge_cases @null_values
  Scenario: Null and undefined value handling
    When I submit requests with null or undefined values:
      | field                   | value     | expected_behavior       |
      | first_name              | null      | validation_error        |
      | last_name               | null      | validation_error        |
      | training_experience_years | null    | validation_error        |
      | optional_field          | null      | accepted_as_empty       |
    Then the system should handle null values appropriately
    And distinguish between required and optional fields

  @edge_cases @empty_values
  Scenario: Empty string and whitespace handling
    When I submit data with empty or whitespace-only values:
      | field      | value    | expected_result    |
      | first_name | ""       | validation_error   |
      | first_name | "   "    | validation_error   |
      | first_name | "\t\n"   | validation_error   |
      | last_name  | ""       | validation_error   |
      | email      | ""       | validation_error   |
      | email      | "  "     | validation_error   |
    Then the system should reject empty and whitespace-only values
    And provide clear validation messages

  @error_handling @security
  Scenario: Security attack prevention
    When I attempt various security attacks:
      | attack_type        | payload                                    | expected_response      |
      | sql_injection      | '; DROP TABLE users; --                   | sanitized_safely       |
      | xss_script         | <script>alert('xss')</script>            | sanitized_safely       |
      | path_traversal     | ../../etc/passwd                          | blocked                |
      | command_injection  | ; rm -rf /                                | sanitized_safely       |
      | header_injection   | \r\nSet-Cookie: malicious=true            | blocked                |
    Then the system should prevent security attacks
    And log security incidents appropriately
    And not expose sensitive system information

  @error_handling @data_integrity
  Scenario: Data integrity and consistency checks
    When data integrity violations occur
    Then the system should detect inconsistencies
    And prevent data corruption
    And provide rollback mechanisms
    And maintain referential integrity

  @edge_cases @timezone
  Scenario: Timezone and datetime handling
    When I work with timestamps and dates
    Then all timestamps should be in UTC
    And timezone information should be preserved
    And date parsing should handle various formats
    And edge cases like leap years should be handled correctly

  @error_handling @graceful_degradation
  Scenario: Graceful degradation during partial failures
    Given some system components are failing
    When users continue to use the API
    Then core functionality should remain available
    And users should receive appropriate service levels
    And the system should not cascade failures
    And recovery should be automatic when possible

  @error_handling @logging
  Scenario: Error logging and monitoring
    When errors occur in the system
    Then all errors should be properly logged
    And log entries should include sufficient context
    And sensitive information should not be logged
    And logs should be structured for analysis
    And monitoring alerts should be triggered appropriately
