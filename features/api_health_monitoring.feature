Feature: API Health and Monitoring
  As a system administrator
  I want to monitor the health and performance of the RP Training API
  So that I can ensure system reliability and optimal user experience

  Background:
    Given the RP Training API is running
    And the monitoring endpoints are accessible

  @health @basic
  Scenario: Basic health check
    When I check the basic health endpoint
    Then the API should report as healthy
    And the response should include service information
    And the response should include a timestamp
    And the response should include the API version

  @health @detailed
  Scenario: Detailed health check with all systems healthy
    Given all system components are healthy
    When I check the detailed health endpoint
    Then the API should report overall status as healthy
    And the database check should pass
    And the memory usage should be within acceptable limits
    And the disk usage should be within acceptable limits
    And all component checks should be marked as healthy

  @health @detailed @degraded
  Scenario: Detailed health check with degraded performance
    Given the database is experiencing high load
    And memory usage is at 85%
    When I check the detailed health endpoint
    Then the API should report overall status as degraded
    And the database check should show performance issues
    And the memory check should show high usage warning
    And the response should include specific performance metrics

  @health @detailed @unhealthy
  Scenario: Detailed health check with system failures
    Given the database connection is failing
    And memory usage is at 95%
    And disk usage is at 98%
    When I check the detailed health endpoint
    Then the API should report overall status as unhealthy
    And the database check should fail
    And the memory check should show critical usage
    And the disk check should show critical usage
    And the response should include error details

  @health @database
  Scenario: Database-specific health check
    Given the database is running normally
    When I check the database health endpoint
    Then the database should report as healthy
    And the connection status should be confirmed
    And the response time should be measured
    And the response should include database metrics

  @health @database @failure
  Scenario: Database health check during outage
    Given the database is unavailable
    When I check the database health endpoint
    Then the database should report as unhealthy
    And the connection status should show failure
    And the response should include error information
    And the API should remain responsive despite database issues

  @health @performance
  Scenario: Health check response time requirements
    When I make multiple health check requests
    Then each request should complete within 2 seconds
    And the average response time should be under 500ms
    And the API should handle concurrent health checks efficiently

  @health @security
  Scenario: Health endpoint security validation
    When I check the health endpoints
    Then no sensitive information should be exposed
    And database credentials should not be visible
    And internal system details should be appropriately filtered
    And the response should be safe for public consumption

  @monitoring @uptime
  Scenario: API uptime monitoring
    Given the API has been running for some time
    When I check the system uptime
    Then the uptime should be accurately reported
    And the start time should be included
    And the system should show stable operation

  @monitoring @metrics
  Scenario: System metrics collection
    When I request system metrics
    Then CPU usage should be reported
    And memory usage should be reported
    And disk usage should be reported
    And network statistics should be available
    And all metrics should have timestamps

  @monitoring @load @performance
  Scenario: Health checks under load
    Given the system is under high load
    When multiple clients check health simultaneously
    Then all health checks should complete successfully
    And response times should remain acceptable
    And the system should not become unstable
    And health status should accurately reflect system state

  @monitoring @alerts
  Scenario: Health status alerting thresholds
    When system metrics cross alerting thresholds:
      | metric | threshold | expected_status |
      | memory | 80%       | healthy         |
      | memory | 90%       | degraded        |
      | memory | 95%       | unhealthy       |
      | disk   | 85%       | healthy         |
      | disk   | 95%       | degraded        |
      | disk   | 98%       | unhealthy       |
    Then the health status should reflect the appropriate alert level
    And the response should include threshold information

  @monitoring @recovery
  Scenario: System recovery monitoring
    Given the system was previously unhealthy
    When system components recover
    Then the health status should update to reflect recovery
    And the recovery time should be tracked
    And the system should return to normal operation

  @monitoring @dependencies
  Scenario: External dependency health monitoring
    Given the API depends on external services
    When I check dependency health
    Then each dependency status should be reported
    And connection timeouts should be handled gracefully
    And dependency failures should not crash the health check
    And the overall status should reflect dependency health

  @monitoring @historical
  Scenario: Health check history tracking
    Given health checks have been performed over time
    When I request health history
    Then historical health data should be available
    And trends should be identifiable
    And the data should help with capacity planning

  @monitoring @compliance
  Scenario: Health monitoring compliance requirements
    When I verify monitoring compliance
    Then health endpoints should meet SLA requirements
    And monitoring data should be properly logged
    And audit trails should be maintained
    And compliance reports should be available

  @health @endpoints @comprehensive
  Scenario: Comprehensive endpoint health validation
    When I test all health-related endpoints:
      | endpoint                    | expected_status | response_time_limit |
      | /api/v1/health             | 200             | 2s                  |
      | /api/v1/health/detailed    | 200             | 5s                  |
      | /api/v1/health/database    | 200             | 3s                  |
    Then all endpoints should respond within time limits
    And all responses should follow the health schema
    And all endpoints should be accessible without authentication

  @health @graceful_degradation
  Scenario: Graceful degradation during partial failures
    Given some system components are failing
    When the API continues to operate
    Then critical functionality should remain available
    And health checks should accurately report component status
    And users should receive appropriate service level
    And the system should not cascade failures

  @monitoring @real_time
  Scenario: Real-time health monitoring
    Given real-time monitoring is enabled
    When system conditions change
    Then health status should update immediately
    And monitoring dashboards should reflect current state
    And alerts should be triggered appropriately
    And stakeholders should be notified of status changes

  @health @maintenance
  Scenario: Health monitoring during maintenance
    Given the system is undergoing maintenance
    When health checks are performed
    Then maintenance mode should be clearly indicated
    And expected downtime should be communicated
    And health status should reflect maintenance state
    And recovery progress should be trackable

  @monitoring @scalability
  Scenario: Health monitoring scalability
    Given the system is scaling up or down
    When monitoring multiple instances
    Then each instance health should be tracked
    And aggregate health should be calculated
    And load balancing should consider health status
    And scaling decisions should use health metrics

  @health @integration
  Scenario: Health check integration with external monitoring
    Given external monitoring tools are configured
    When health data is exported
    Then the data should be in standard formats
    And integration should be seamless
    And external tools should receive accurate information
    And monitoring workflows should be automated
