Feature: User Profile Management
  As a registered user of the RP Training API
  I want to manage my profile information
  So that I can keep my training data up-to-date and personalized

  Background:
    Given the RP Training API is running
    And I am a logged-in user with profile:
      | email                | first_name | last_name | training_experience |
      | <EMAIL>     | John       | Doe       | 2                   |

  @profile @view
  Scenario: View my current profile
    When I request my profile information
    Then I should see my current profile details
    And the profile should contain my email "<EMAIL>"
    And the profile should contain my name "<PERSON>"
    And the profile should contain my training experience "2 years"

  @profile @update
  Scenario: Update profile with valid information
    When I update my profile with:
      | first_name | last_name | training_experience |
      | Jane       | Smith     | 5                   |
    Then my profile should be updated successfully
    And I should see the updated information in my profile
    And my email should remain unchanged

  @profile @update @validation
  Scenario Outline: Update profile with invalid data
    When I try to update my profile with invalid data:
      | first_name   | last_name   | training_experience   |
      | <first_name> | <last_name> | <training_experience> |
    Then I should receive a validation error
    And my profile should remain unchanged

    Examples:
      | first_name | last_name | training_experience | validation_issue           |
      |            | <PERSON>     | 3                   | First name cannot be empty |
      | <PERSON>       |           | 3                   | Last name cannot be empty  |
      | Jane       | Smith     | -1                  | Invalid training experience|
      | Jane       | Smith     | 100                 | Training experience too high|

  @profile @update @partial
  Scenario: Partial profile update
    When I update only my first name to "Michael"
    Then my first name should be updated to "Michael"
    And my last name should remain "Doe"
    And my training experience should remain "2"

  @profile @update @training_experience
  Scenario: Update training experience progression
    Given my current training experience is 2 years
    When I update my training experience to 3 years
    Then my training experience should be updated to 3 years
    And I should be able to access features for intermediate users

  @profile @update @concurrent
  Scenario: Concurrent profile updates
    Given multiple users are updating their profiles simultaneously
    When they all submit profile update requests
    Then each user's profile should be updated correctly
    And no data corruption should occur
    And each user should see only their own updated data

  @profile @history @audit
  Scenario: Profile update history tracking
    Given I have made previous profile updates
    When I update my profile information
    Then the system should track the update timestamp
    And I should be able to see when my profile was last modified

  @profile @validation @email
  Scenario: Email field protection
    When I try to update my email address
    Then the system should not allow email changes through profile update
    And I should be directed to use the email change process
    And my current email should remain unchanged

  @profile @privacy @data
  Scenario: Profile data privacy
    When I view my profile
    Then sensitive information should not be exposed
    And my password should not be visible
    And only appropriate profile fields should be returned

  @profile @delete @deactivation
  Scenario: Profile deactivation
    When I request to deactivate my account
    Then my profile should be marked as inactive
    And I should not be able to login with my credentials
    And my data should be preserved for potential reactivation

  @profile @validation @business_rules
  Scenario: Training experience business rules
    Given I am updating my training experience
    When I set my experience to different values:
      | experience | should_succeed | reason                    |
      | 0          | true           | Beginner level            |
      | 1          | true           | Valid experience          |
      | 10         | true           | Advanced level            |
      | 25         | true           | Expert level              |
      | 50         | false          | Unrealistic experience    |
      | -1         | false          | Negative experience       |
    Then the system should enforce training experience rules
    And only realistic experience values should be accepted

  @profile @integration @training_data
  Scenario: Profile integration with training data
    Given I have training data associated with my profile
    When I update my training experience level
    Then my training recommendations should be updated
    And my workout difficulty should be adjusted accordingly
    And my progress tracking should reflect the new experience level

  @profile @performance @load
  Scenario: Profile operations under load
    Given the system is under high load
    When I perform profile operations
    Then my profile updates should complete within acceptable time
    And the system should remain responsive
    And my data should be consistent

  @profile @security @authorization
  Scenario: Profile access authorization
    Given I am logged in as user A
    When I try to access user B's profile
    Then I should receive an authorization error
    And I should only be able to access my own profile data
    And user B's data should remain private

  @profile @api @endpoints
  Scenario: Profile API endpoint validation
    When I interact with profile endpoints
    Then GET /api/v1/auth/me should return my profile
    And PUT /api/v1/auth/me should update my profile
    And all endpoints should require authentication
    And all responses should follow the API specification

  @profile @data_consistency
  Scenario: Profile data consistency across sessions
    Given I update my profile in one session
    When I login from a different session
    Then I should see the updated profile information
    And the data should be consistent across all sessions
    And no data should be lost or corrupted

  @profile @rollback @error_handling
  Scenario: Profile update error handling and rollback
    Given I am updating my profile
    When a system error occurs during the update
    Then the update should be rolled back
    And my profile should remain in its previous state
    And I should receive an appropriate error message
    And I should be able to retry the update

  @profile @validation @comprehensive
  Scenario: Comprehensive profile validation
    When I submit a complete profile update with:
      | field               | value                    | expected_result |
      | first_name          | John                     | valid           |
      | last_name           | Doe-Smith               | valid           |
      | training_experience | 5                        | valid           |
      | first_name          | J                        | valid           |
      | first_name          | VeryLongFirstNameThatExceedsReasonableLength | invalid |
      | last_name           | VeryLongLastNameThatExceedsReasonableLength  | invalid |
      | training_experience | 999                      | invalid         |
    Then the system should validate each field appropriately
    And provide specific feedback for invalid fields
    And accept all valid field values
