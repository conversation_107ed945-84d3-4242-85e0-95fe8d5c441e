"""
Step definitions for API health and monitoring BDD scenarios.

This module implements the Gherkin step definitions for health checks,
system monitoring, and operational scenarios in the RP Training API.
"""

import time
from typing import Dict, Any
from unittest.mock import patch, Mock

from behave import given, when, then, step
from fastapi import status

from features.environment import get_bdd_utils


def get_context(context):
    """Get the BDD test context."""
    return context.bdd_context


def get_utils():
    """Get BDD test utilities."""
    return get_bdd_utils()


# Background and setup steps
@given('the monitoring endpoints are accessible')
def step_monitoring_endpoints_accessible(context):
    """Ensure monitoring endpoints are accessible."""
    bdd_context = get_context(context)
    
    # Test basic health endpoint accessibility
    response = bdd_context.test_client.get("/api/v1/health")
    assert response.status_code == 200, f"Health endpoint not accessible: {response.text}"
    
    context.monitoring_accessible = True


# Basic health check steps
@when('I check the basic health endpoint')
def step_check_basic_health_endpoint(context):
    """Check the basic health endpoint."""
    bdd_context = get_context(context)
    
    response = bdd_context.test_client.get("/api/v1/health")
    bdd_context.response = response


@then('the API should report as healthy')
def step_api_reports_healthy(context):
    """Verify API reports as healthy."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "healthy")


@then('the response should include service information')
def step_response_includes_service_info(context):
    """Verify response includes service information."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "service")


@then('the response should include a timestamp')
def step_response_includes_timestamp(context):
    """Verify response includes timestamp."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "timestamp")


@then('the response should include the API version')
def step_response_includes_version(context):
    """Verify response includes API version."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "version")


# Detailed health check steps
@given('all system components are healthy')
def step_all_components_healthy(context):
    """Set up all system components as healthy."""
    context.system_state = {
        "database": "healthy",
        "memory_usage": 50.0,
        "disk_usage": 60.0
    }


@when('I check the detailed health endpoint')
def step_check_detailed_health_endpoint(context):
    """Check the detailed health endpoint."""
    bdd_context = get_context(context)
    
    # Mock system components based on context state
    system_state = getattr(context, 'system_state', {})
    
    with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
         patch('psutil.virtual_memory') as mock_memory, \
         patch('psutil.disk_usage') as mock_disk:
        
        # Configure mocks based on system state
        if system_state.get("database") == "healthy":
            mock_db.health_check.return_value = True
        elif system_state.get("database") == "failing":
            mock_db.health_check.return_value = False
        else:
            mock_db.health_check.return_value = True
        
        mock_memory.return_value = Mock(percent=system_state.get("memory_usage", 50.0))
        mock_disk.return_value = Mock(percent=system_state.get("disk_usage", 60.0))
        
        response = bdd_context.test_client.get("/api/v1/health/detailed")
        bdd_context.response = response


@then('the API should report overall status as healthy')
def step_overall_status_healthy(context):
    """Verify overall status is healthy."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "healthy")


@then('the database check should pass')
def step_database_check_passes(context):
    """Verify database check passes."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "checks.database.status", "healthy")


@then('the memory usage should be within acceptable limits')
def step_memory_usage_acceptable(context):
    """Verify memory usage is within acceptable limits."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "checks.memory.status", "healthy")


@then('the disk usage should be within acceptable limits')
def step_disk_usage_acceptable(context):
    """Verify disk usage is within acceptable limits."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "checks.disk.status", "healthy")


@then('all component checks should be marked as healthy')
def step_all_components_marked_healthy(context):
    """Verify all component checks are marked as healthy."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    checks = response_data.get("checks", {})
    
    for component, check_data in checks.items():
        assert check_data.get("status") == "healthy", (
            f"Component {component} is not healthy: {check_data}"
        )


# Degraded performance scenarios
@given('the database is experiencing high load')
def step_database_high_load(context):
    """Set up database experiencing high load."""
    if not hasattr(context, 'system_state'):
        context.system_state = {}
    context.system_state["database"] = "degraded"


@given('memory usage is at {percentage:d}%')
def step_memory_usage_percentage(context, percentage):
    """Set memory usage to specific percentage."""
    if not hasattr(context, 'system_state'):
        context.system_state = {}
    context.system_state["memory_usage"] = float(percentage)


@then('the API should report overall status as degraded')
def step_overall_status_degraded(context):
    """Verify overall status is degraded."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "degraded")


@then('the database check should show performance issues')
def step_database_shows_performance_issues(context):
    """Verify database check shows performance issues."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    db_check = response_data.get("checks", {}).get("database", {})
    
    # Database might be healthy but with performance warnings
    assert db_check.get("status") in ["healthy", "degraded"], (
        f"Unexpected database status: {db_check}"
    )


@then('the memory check should show high usage warning')
def step_memory_shows_high_usage(context):
    """Verify memory check shows high usage warning."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    memory_check = response_data.get("checks", {}).get("memory", {})
    
    # High memory usage should be reflected in status or details
    memory_usage = memory_check.get("details", {}).get("usage_percent", 0)
    assert memory_usage >= 80, f"Expected high memory usage, got {memory_usage}%"


@then('the response should include specific performance metrics')
def step_response_includes_performance_metrics(context):
    """Verify response includes performance metrics."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "checks")
    
    response_data = bdd_context.response.json()
    checks = response_data.get("checks", {})
    
    # Verify each check has details with metrics
    for component, check_data in checks.items():
        assert "details" in check_data, f"No details for {component}"


# Unhealthy system scenarios
@given('the database connection is failing')
def step_database_connection_failing(context):
    """Set up database connection as failing."""
    if not hasattr(context, 'system_state'):
        context.system_state = {}
    context.system_state["database"] = "failing"


@given('disk usage is at {percentage:d}%')
def step_disk_usage_percentage(context, percentage):
    """Set disk usage to specific percentage."""
    if not hasattr(context, 'system_state'):
        context.system_state = {}
    context.system_state["disk_usage"] = float(percentage)


@then('the API should report overall status as unhealthy')
def step_overall_status_unhealthy(context):
    """Verify overall status is unhealthy."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "unhealthy")


@then('the database check should fail')
def step_database_check_fails(context):
    """Verify database check fails."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "checks.database.status", "unhealthy")


@then('the memory check should show critical usage')
def step_memory_shows_critical_usage(context):
    """Verify memory check shows critical usage."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    memory_check = response_data.get("checks", {}).get("memory", {})
    
    assert memory_check.get("status") == "unhealthy", (
        f"Expected unhealthy memory status, got {memory_check.get('status')}"
    )


@then('the disk check should show critical usage')
def step_disk_shows_critical_usage(context):
    """Verify disk check shows critical usage."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    disk_check = response_data.get("checks", {}).get("disk", {})
    
    assert disk_check.get("status") == "unhealthy", (
        f"Expected unhealthy disk status, got {disk_check.get('status')}"
    )


@then('the response should include error details')
def step_response_includes_error_details(context):
    """Verify response includes error details."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    checks = response_data.get("checks", {})
    
    # At least one check should have error details
    has_error_details = False
    for component, check_data in checks.items():
        if check_data.get("status") == "unhealthy":
            details = check_data.get("details", {})
            if "error" in details or "connected" in details:
                has_error_details = True
                break
    
    assert has_error_details, "No error details found in unhealthy checks"


# Database-specific health checks
@given('the database is running normally')
def step_database_running_normally(context):
    """Set up database as running normally."""
    context.database_state = "healthy"


@when('I check the database health endpoint')
def step_check_database_health_endpoint(context):
    """Check the database health endpoint."""
    bdd_context = get_context(context)
    
    database_state = getattr(context, 'database_state', 'healthy')
    
    with patch('app.infrastructure.database.connection.db_manager') as mock_db:
        if database_state == "healthy":
            mock_db.health_check.return_value = True
        else:
            mock_db.health_check.return_value = False
        
        response = bdd_context.test_client.get("/api/v1/health/database")
        bdd_context.response = response


@then('the database should report as healthy')
def step_database_reports_healthy(context):
    """Verify database reports as healthy."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "healthy")


@then('the connection status should be confirmed')
def step_connection_status_confirmed(context):
    """Verify connection status is confirmed."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "database.connected", True)


@then('the response time should be measured')
def step_response_time_measured(context):
    """Verify response time is measured."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "database.response_time_ms")


@then('the response should include database metrics')
def step_response_includes_database_metrics(context):
    """Verify response includes database metrics."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "database")


@given('the database is unavailable')
def step_database_unavailable(context):
    """Set up database as unavailable."""
    context.database_state = "unavailable"


@then('the database should report as unhealthy')
def step_database_reports_unhealthy(context):
    """Verify database reports as unhealthy."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field(bdd_context.response, "status", "unhealthy")


@then('the connection status should show failure')
def step_connection_status_shows_failure(context):
    """Verify connection status shows failure."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "database.connected", False)


@then('the response should include error information')
def step_response_includes_error_information(context):
    """Verify response includes error information."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    database_info = response_data.get("database", {})
    
    # Should have error information when database is unavailable
    assert "error" in database_info or database_info.get("connected") is False, (
        "No error information found for unavailable database"
    )


@then('the API should remain responsive despite database issues')
def step_api_remains_responsive(context):
    """Verify API remains responsive despite database issues."""
    bdd_context = get_context(context)
    
    # The fact that we got a response means the API is responsive
    assert bdd_context.response.status_code == 200, (
        "API should remain responsive even with database issues"
    )
