"""
Step definitions for user authentication BDD scenarios.

This module implements the <PERSON><PERSON>kin step definitions for user registration,
login, and authentication workflows in the RP Training API.
"""

import json
import time
from typing import Dict, Any
from unittest.mock import patch, AsyncMock

from behave import given, when, then, step
from fastapi import status

from features.environment import get_bdd_utils
from tests.factories import UserFactory


# Utility function to get BDD context
def get_context(context):
    """Get the BDD test context."""
    return context.bdd_context


def get_utils():
    """Get BDD test utilities."""
    return get_bdd_utils()


# Background steps
@given('the RP Training API is running')
def step_api_running(context):
    """Ensure the API is running and accessible."""
    bdd_context = get_context(context)
    
    # Test basic connectivity
    response = bdd_context.test_client.get("/api/v1/health")
    assert response.status_code == 200, f"API not accessible: {response.text}"
    
    context.api_running = True


@given('the database is clean')
def step_database_clean(context):
    """Ensure database is in clean state for testing."""
    # In a real scenario, this would clean the test database
    # For now, we'll mock the database operations
    bdd_context = get_context(context)
    bdd_context.created_users.clear()
    context.database_clean = True


# Registration steps
@given('I am a new user')
def step_new_user(context):
    """Set up context for a new user registration."""
    bdd_context = get_context(context)
    bdd_context.user_data = UserFactory.create_user_data()
    context.user_type = "new"


@when('I register with valid credentials')
def step_register_valid_credentials(context):
    """Register with valid user credentials from table."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    # Extract data from table
    row = context.table[0]
    user_data = {
        "email": row["email"],
        "password": row["password"],
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"])
    }
    
    bdd_context.user_data = user_data
    
    # Mock successful registration
    with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        # Mock successful response
        from app.application.use_cases.register_user import RegisterUserResponse
        from app.application.dto.user_dto import UserDTO
        
        mock_response = RegisterUserResponse(
            user=UserDTO(
                id="test-user-id",
                email=user_data["email"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                training_experience_years=user_data["training_experience_years"],
                is_active=True,
                is_verified=False,
                created_at="2023-01-01T00:00:00",
                updated_at="2023-01-01T00:00:00"
            ),
            access_token="mock_access_token_12345",
            refresh_token="mock_refresh_token_67890",
            token_type="bearer"
        )
        
        mock_use_case.execute.return_value = mock_response
        mock_dep.return_value = mock_use_case
        
        # Make registration request
        response = bdd_context.test_client.post("/api/v1/auth/register", json=user_data)
        bdd_context.response = response
        bdd_context.created_users.append(user_data["email"])


@given('a user already exists with email "{email}"')
def step_user_exists(context, email):
    """Set up context where a user already exists."""
    bdd_context = get_context(context)
    bdd_context.created_users.append(email)
    context.existing_email = email


@when('I try to register with the same email')
def step_register_existing_email(context):
    """Try to register with an email that already exists."""
    bdd_context = get_context(context)
    
    # Extract data from table
    row = context.table[0]
    user_data = {
        "email": row["email"],
        "password": row["password"],
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"])
    }
    
    # Mock user already exists error
    with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        from app.domain.exceptions.auth_exceptions import UserAlreadyExistsError
        mock_use_case.execute.side_effect = UserAlreadyExistsError("User already exists")
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.post("/api/v1/auth/register", json=user_data)
        bdd_context.response = response


@when('I register with invalid data')
def step_register_invalid_data(context):
    """Register with invalid data from table."""
    bdd_context = get_context(context)
    
    # Extract data from table
    row = context.table[0]
    user_data = {
        "email": row["email"],
        "password": row["password"],
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"]) if row["training_experience"].isdigit() or (row["training_experience"].startswith('-') and row["training_experience"][1:].isdigit()) else 0
    }
    
    # Mock validation error based on the invalid data
    with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        from app.domain.exceptions.auth_exceptions import InvalidEmailError, WeakPasswordError
        
        if "@" not in row["email"]:
            mock_use_case.execute.side_effect = InvalidEmailError("Invalid email format")
        elif len(row["password"]) < 8:
            mock_use_case.execute.side_effect = WeakPasswordError("Password too weak")
        elif not row["first_name"]:
            mock_use_case.execute.side_effect = ValueError("First name required")
        elif not row["last_name"]:
            mock_use_case.execute.side_effect = ValueError("Last name required")
        elif int(row["training_experience"]) < 0:
            mock_use_case.execute.side_effect = ValueError("Invalid experience")
        
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.post("/api/v1/auth/register", json=user_data)
        bdd_context.response = response


# Login steps
@given('I am a registered user with credentials')
def step_registered_user_credentials(context):
    """Set up a registered user with specific credentials."""
    bdd_context = get_context(context)
    
    row = context.table[0]
    bdd_context.user_data = {
        "email": row["email"],
        "password": row["password"]
    }
    bdd_context.created_users.append(row["email"])


@given('I am a registered user with email "{email}"')
def step_registered_user_email(context, email):
    """Set up a registered user with specific email."""
    bdd_context = get_context(context)
    bdd_context.user_data = {"email": email, "password": "SecurePass123!"}
    bdd_context.created_users.append(email)


@when('I login with correct credentials')
def step_login_correct_credentials(context):
    """Login with correct credentials."""
    bdd_context = get_context(context)
    
    login_data = {
        "email": bdd_context.user_data["email"],
        "password": bdd_context.user_data["password"]
    }
    
    # Mock successful login
    with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        from app.application.use_cases.authenticate_user import AuthenticateUserResponse
        from app.application.dto.user_dto import UserDTO
        
        mock_response = AuthenticateUserResponse(
            user=UserDTO(
                id="test-user-id",
                email=login_data["email"],
                first_name="Test",
                last_name="User",
                training_experience_years=2,
                is_active=True,
                is_verified=False,
                created_at="2023-01-01T00:00:00",
                updated_at="2023-01-01T00:00:00"
            ),
            access_token="mock_access_token_login",
            refresh_token="mock_refresh_token_login",
            token_type="bearer"
        )
        
        mock_use_case.execute.return_value = mock_response
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.post("/api/v1/auth/login", json=login_data)
        bdd_context.response = response
        
        if response.status_code == 200:
            bdd_context.auth_token = "mock_access_token_login"


@when('I login with incorrect password "{password}"')
def step_login_incorrect_password(context, password):
    """Login with incorrect password."""
    bdd_context = get_context(context)
    
    login_data = {
        "email": bdd_context.user_data["email"],
        "password": password
    }
    
    # Mock authentication error
    with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        from app.domain.exceptions.auth_exceptions import InvalidCredentialsError
        mock_use_case.execute.side_effect = InvalidCredentialsError("Invalid credentials")
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.post("/api/v1/auth/login", json=login_data)
        bdd_context.response = response


@given('no user exists with email "{email}"')
def step_no_user_exists(context, email):
    """Ensure no user exists with the given email."""
    bdd_context = get_context(context)
    if email in bdd_context.created_users:
        bdd_context.created_users.remove(email)


@when('I try to login with email "{email}"')
def step_login_nonexistent_user(context, email):
    """Try to login with non-existent user email."""
    bdd_context = get_context(context)
    
    login_data = {
        "email": email,
        "password": "AnyPassword123!"
    }
    
    # Mock authentication error for non-existent user
    with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()
        
        from app.domain.exceptions.auth_exceptions import InvalidCredentialsError
        mock_use_case.execute.side_effect = InvalidCredentialsError("Invalid credentials")
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.post("/api/v1/auth/login", json=login_data)
        bdd_context.response = response


# Response validation steps
@then('I should receive a successful registration response')
def step_successful_registration_response(context):
    """Verify successful registration response."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_201_CREATED)


@then('I should get an authentication token')
def step_get_auth_token(context):
    """Verify authentication token is received."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "access_token")
    utils.assert_json_field_exists(bdd_context.response, "refresh_token")
    
    # Extract token for later use
    bdd_context.auth_token = utils.extract_auth_token(bdd_context.response)


@then('my user profile should be created')
def step_user_profile_created(context):
    """Verify user profile is created."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "user")
    utils.assert_json_field_exists(bdd_context.response, "user.email")
    utils.assert_json_field_exists(bdd_context.response, "user.first_name")
    utils.assert_json_field_exists(bdd_context.response, "user.last_name")


@then('I should receive a conflict error')
def step_conflict_error(context):
    """Verify conflict error response."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_409_CONFLICT)


@then('I should receive a validation error')
def step_validation_error(context):
    """Verify validation error response."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_422_UNPROCESSABLE_ENTITY)


@then('I should receive a successful login response')
def step_successful_login_response(context):
    """Verify successful login response."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)


@then('I should receive an authentication error')
def step_authentication_error(context):
    """Verify authentication error response."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_status(bdd_context.response, status.HTTP_401_UNAUTHORIZED)


@then('the response should indicate the email is already taken')
def step_email_already_taken(context):
    """Verify response indicates email is already taken."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_contains(bdd_context.response, "already exists")


@then('the response should indicate the specific validation issue')
def step_specific_validation_issue(context):
    """Verify response indicates specific validation issue."""
    bdd_context = get_context(context)
    utils = get_utils()

    # The response should contain some validation error message
    assert bdd_context.response.status_code in [400, 422], (
        f"Expected validation error status, got {bdd_context.response.status_code}"
    )


@then('the token should be valid for API access')
def step_token_valid_api_access(context):
    """Verify token is valid for API access."""
    bdd_context = get_context(context)

    assert bdd_context.auth_token is not None, "No auth token received"
    assert len(bdd_context.auth_token) > 0, "Empty auth token received"


@then('the response should indicate invalid credentials')
def step_invalid_credentials_response(context):
    """Verify response indicates invalid credentials."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_contains(bdd_context.response, "credentials")


@given('I am a registered but inactive user')
def step_inactive_user(context):
    """Set up an inactive user."""
    bdd_context = get_context(context)
    bdd_context.user_data = UserFactory.create_user_data()
    bdd_context.user_data["is_active"] = False
    bdd_context.created_users.append(bdd_context.user_data["email"])


@when('I try to login with my credentials')
def step_login_with_credentials(context):
    """Login with user's credentials."""
    bdd_context = get_context(context)

    login_data = {
        "email": bdd_context.user_data["email"],
        "password": bdd_context.user_data["password"]
    }

    # Mock inactive user error
    with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
        mock_use_case = AsyncMock()

        from app.domain.exceptions.auth_exceptions import InactiveUserError
        mock_use_case.execute.side_effect = InactiveUserError("User is inactive")
        mock_dep.return_value = mock_use_case

        response = bdd_context.test_client.post("/api/v1/auth/login", json=login_data)
        bdd_context.response = response


@then('the response should indicate the account is inactive')
def step_account_inactive_response(context):
    """Verify response indicates account is inactive."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_contains(bdd_context.response, "inactive")


@given('I am a logged-in user')
def step_logged_in_user(context):
    """Set up a logged-in user with valid token."""
    bdd_context = get_context(context)
    bdd_context.user_data = UserFactory.create_user_data()
    bdd_context.auth_token = "valid_mock_token_12345"
    bdd_context.created_users.append(bdd_context.user_data["email"])


@when('I access a protected endpoint with my token')
def step_access_protected_endpoint(context):
    """Access a protected endpoint with authentication token."""
    bdd_context = get_context(context)

    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}

    # Mock successful profile access
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_user_profile_use_case') as mock_dep:

        mock_current_user.return_value = "test-user-id"
        mock_use_case = AsyncMock()

        from app.application.dto.user_dto import UserDTO
        mock_user_dto = UserDTO(
            id="test-user-id",
            email=bdd_context.user_data["email"],
            first_name=bdd_context.user_data["first_name"],
            last_name=bdd_context.user_data["last_name"],
            training_experience_years=bdd_context.user_data["training_experience_years"],
            is_active=True,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00"
        )

        mock_use_case.execute.return_value = mock_user_dto
        mock_dep.return_value = mock_use_case

        response = bdd_context.test_client.get("/api/v1/auth/me", headers=headers)
        bdd_context.response = response


@then('I should receive a successful response')
def step_successful_response(context):
    """Verify successful response."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)


@then('I should get my user profile data')
def step_get_user_profile_data(context):
    """Verify user profile data is returned."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_json_field_exists(bdd_context.response, "email")
    utils.assert_json_field_exists(bdd_context.response, "first_name")
    utils.assert_json_field_exists(bdd_context.response, "last_name")


@given('I have an invalid authentication token')
def step_invalid_auth_token(context):
    """Set up invalid authentication token."""
    bdd_context = get_context(context)
    bdd_context.auth_token = "invalid_token_12345"


@when('I try to access a protected endpoint')
def step_try_access_protected_endpoint(context):
    """Try to access protected endpoint."""
    bdd_context = get_context(context)

    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}

    # Mock authentication failure
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user:
        from fastapi import HTTPException
        mock_current_user.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

        response = bdd_context.test_client.get("/api/v1/auth/me", headers=headers)
        bdd_context.response = response


@then('I should receive an unauthorized error')
def step_unauthorized_error(context):
    """Verify unauthorized error response."""
    bdd_context = get_context(context)
    utils = get_utils()

    utils.assert_response_status(bdd_context.response, status.HTTP_401_UNAUTHORIZED)


@then('I should be prompted to authenticate')
def step_prompted_to_authenticate(context):
    """Verify user is prompted to authenticate."""
    bdd_context = get_context(context)

    # Check that the response indicates authentication is required
    assert bdd_context.response.status_code == status.HTTP_401_UNAUTHORIZED


@given('I have an expired authentication token')
def step_expired_auth_token(context):
    """Set up expired authentication token."""
    bdd_context = get_context(context)
    bdd_context.auth_token = "expired_token_12345"


@then('the response should indicate the token has expired')
def step_token_expired_response(context):
    """Verify response indicates token has expired."""
    bdd_context = get_context(context)

    # The response should be unauthorized (expired tokens are treated as invalid)
    assert bdd_context.response.status_code == status.HTTP_401_UNAUTHORIZED
