"""
Step definitions for user profile management BDD scenarios.

This module implements the <PERSON><PERSON>kin step definitions for user profile
viewing, updating, and management workflows in the RP Training API.
"""

from typing import Dict, Any
from unittest.mock import patch, AsyncMock

from behave import given, when, then, step
from fastapi import status

from features.environment import get_bdd_utils
from tests.factories import UserFactory


def get_context(context):
    """Get the BDD test context."""
    return context.bdd_context


def get_utils():
    """Get BDD test utilities."""
    return get_bdd_utils()


# Background and setup steps
@given('I am a logged-in user with profile')
def step_logged_in_user_with_profile(context):
    """Set up a logged-in user with specific profile data."""
    bdd_context = get_context(context)
    
    # Extract profile data from table
    row = context.table[0]
    bdd_context.user_data = {
        "id": "test-user-id",
        "email": row["email"],
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"]),
        "is_active": True,
        "is_verified": False
    }
    
    bdd_context.auth_token = "valid_profile_token_12345"
    bdd_context.created_users.append(row["email"])


# Profile viewing steps
@when('I request my profile information')
def step_request_profile_information(context):
    """Request current user profile information."""
    bdd_context = get_context(context)
    
    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}
    
    # Mock successful profile retrieval
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_user_profile_use_case') as mock_dep:
        
        mock_current_user.return_value = bdd_context.user_data["id"]
        mock_use_case = AsyncMock()
        
        from app.application.dto.user_dto import UserDTO
        mock_user_dto = UserDTO(
            id=bdd_context.user_data["id"],
            email=bdd_context.user_data["email"],
            first_name=bdd_context.user_data["first_name"],
            last_name=bdd_context.user_data["last_name"],
            training_experience_years=bdd_context.user_data["training_experience_years"],
            is_active=bdd_context.user_data["is_active"],
            is_verified=bdd_context.user_data["is_verified"],
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00"
        )
        
        mock_use_case.execute.return_value = mock_user_dto
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.get("/api/v1/auth/me", headers=headers)
        bdd_context.response = response


@then('I should see my current profile details')
def step_see_current_profile_details(context):
    """Verify current profile details are returned."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)
    utils.assert_json_field_exists(bdd_context.response, "email")
    utils.assert_json_field_exists(bdd_context.response, "first_name")
    utils.assert_json_field_exists(bdd_context.response, "last_name")
    utils.assert_json_field_exists(bdd_context.response, "training_experience_years")


@then('the profile should contain my email "{email}"')
def step_profile_contains_email(context, email):
    """Verify profile contains specific email."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "email", email)


@then('the profile should contain my name "{full_name}"')
def step_profile_contains_name(context, full_name):
    """Verify profile contains specific full name."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    actual_full_name = f"{response_data['first_name']} {response_data['last_name']}"
    
    assert actual_full_name == full_name, (
        f"Expected full name '{full_name}', got '{actual_full_name}'"
    )


@then('the profile should contain my training experience "{experience}"')
def step_profile_contains_experience(context, experience):
    """Verify profile contains specific training experience."""
    bdd_context = get_context(context)
    
    response_data = bdd_context.response.json()
    experience_years = int(experience.split()[0])  # Extract number from "2 years"
    
    assert response_data["training_experience_years"] == experience_years, (
        f"Expected {experience_years} years experience, "
        f"got {response_data['training_experience_years']}"
    )


# Profile update steps
@when('I update my profile with')
def step_update_profile_with_data(context):
    """Update profile with data from table."""
    bdd_context = get_context(context)
    
    # Extract update data from table
    row = context.table[0]
    update_data = {
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"])
    }
    
    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}
    
    # Mock successful profile update
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_update_user_profile_use_case') as mock_dep:
        
        mock_current_user.return_value = bdd_context.user_data["id"]
        mock_use_case = AsyncMock()
        
        # Update the user data with new values
        updated_user_data = bdd_context.user_data.copy()
        updated_user_data.update(update_data)
        
        from app.application.dto.user_dto import UserDTO
        mock_user_dto = UserDTO(
            id=updated_user_data["id"],
            email=updated_user_data["email"],
            first_name=updated_user_data["first_name"],
            last_name=updated_user_data["last_name"],
            training_experience_years=updated_user_data["training_experience_years"],
            is_active=updated_user_data["is_active"],
            is_verified=updated_user_data["is_verified"],
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T12:00:00"  # Updated timestamp
        )
        
        mock_use_case.execute.return_value = mock_user_dto
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.put("/api/v1/auth/me", json=update_data, headers=headers)
        bdd_context.response = response
        
        # Update context data if successful
        if response.status_code == 200:
            bdd_context.user_data.update(update_data)


@then('my profile should be updated successfully')
def step_profile_updated_successfully(context):
    """Verify profile update was successful."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_response_status(bdd_context.response, status.HTTP_200_OK)


@then('I should see the updated information in my profile')
def step_see_updated_information(context):
    """Verify updated information is reflected in profile."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field_exists(bdd_context.response, "first_name")
    utils.assert_json_field_exists(bdd_context.response, "last_name")
    utils.assert_json_field_exists(bdd_context.response, "training_experience_years")


@then('my email should remain unchanged')
def step_email_unchanged(context):
    """Verify email remains unchanged after profile update."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    original_email = bdd_context.user_data["email"]
    utils.assert_json_field(bdd_context.response, "email", original_email)


@when('I try to update my profile with invalid data')
def step_update_profile_invalid_data(context):
    """Try to update profile with invalid data."""
    bdd_context = get_context(context)
    
    # Extract invalid data from table
    row = context.table[0]
    update_data = {}
    
    if row["first_name"]:
        update_data["first_name"] = row["first_name"]
    if row["last_name"]:
        update_data["last_name"] = row["last_name"]
    if row["training_experience"]:
        try:
            update_data["training_experience_years"] = int(row["training_experience"])
        except ValueError:
            update_data["training_experience_years"] = row["training_experience"]
    
    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}
    
    # Mock validation error
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_update_user_profile_use_case') as mock_dep:
        
        mock_current_user.return_value = bdd_context.user_data["id"]
        mock_use_case = AsyncMock()
        
        # Determine appropriate error based on invalid data
        if not row["first_name"]:
            mock_use_case.execute.side_effect = ValueError("First name cannot be empty")
        elif not row["last_name"]:
            mock_use_case.execute.side_effect = ValueError("Last name cannot be empty")
        elif int(row["training_experience"]) < 0:
            mock_use_case.execute.side_effect = ValueError("Invalid training experience")
        elif int(row["training_experience"]) > 50:
            mock_use_case.execute.side_effect = ValueError("Training experience too high")
        
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.put("/api/v1/auth/me", json=update_data, headers=headers)
        bdd_context.response = response


@then('my profile should remain unchanged')
def step_profile_unchanged(context):
    """Verify profile remains unchanged after failed update."""
    bdd_context = get_context(context)
    
    # The response should indicate an error, meaning profile wasn't updated
    assert bdd_context.response.status_code != 200, (
        "Profile update should have failed but returned success"
    )


@when('I update only my first name to "{first_name}"')
def step_update_only_first_name(context, first_name):
    """Update only the first name field."""
    bdd_context = get_context(context)
    
    update_data = {"first_name": first_name}
    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}
    
    # Mock successful partial update
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_update_user_profile_use_case') as mock_dep:
        
        mock_current_user.return_value = bdd_context.user_data["id"]
        mock_use_case = AsyncMock()
        
        # Update only first name, keep other fields
        updated_user_data = bdd_context.user_data.copy()
        updated_user_data["first_name"] = first_name
        
        from app.application.dto.user_dto import UserDTO
        mock_user_dto = UserDTO(
            id=updated_user_data["id"],
            email=updated_user_data["email"],
            first_name=updated_user_data["first_name"],
            last_name=updated_user_data["last_name"],
            training_experience_years=updated_user_data["training_experience_years"],
            is_active=updated_user_data["is_active"],
            is_verified=updated_user_data["is_verified"],
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T12:00:00"
        )
        
        mock_use_case.execute.return_value = mock_user_dto
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.put("/api/v1/auth/me", json=update_data, headers=headers)
        bdd_context.response = response
        
        # Update context if successful
        if response.status_code == 200:
            bdd_context.user_data["first_name"] = first_name


@then('my first name should be updated to "{first_name}"')
def step_first_name_updated(context, first_name):
    """Verify first name was updated."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "first_name", first_name)


@then('my last name should remain "{last_name}"')
def step_last_name_unchanged(context, last_name):
    """Verify last name remained unchanged."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "last_name", last_name)


@then('my training experience should remain "{experience}"')
def step_training_experience_unchanged(context, experience):
    """Verify training experience remained unchanged."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    experience_years = int(experience)
    utils.assert_json_field(bdd_context.response, "training_experience_years", experience_years)


@given('my current training experience is {experience:d} years')
def step_current_training_experience(context, experience):
    """Set current training experience."""
    bdd_context = get_context(context)
    bdd_context.user_data["training_experience_years"] = experience


@when('I update my training experience to {experience:d} years')
def step_update_training_experience(context, experience):
    """Update training experience to specific value."""
    bdd_context = get_context(context)
    
    update_data = {"training_experience_years": experience}
    headers = {"Authorization": f"Bearer {bdd_context.auth_token}"}
    
    # Mock successful experience update
    with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
         patch('app.presentation.api.v1.auth.get_update_user_profile_use_case') as mock_dep:
        
        mock_current_user.return_value = bdd_context.user_data["id"]
        mock_use_case = AsyncMock()
        
        updated_user_data = bdd_context.user_data.copy()
        updated_user_data["training_experience_years"] = experience
        
        from app.application.dto.user_dto import UserDTO
        mock_user_dto = UserDTO(
            id=updated_user_data["id"],
            email=updated_user_data["email"],
            first_name=updated_user_data["first_name"],
            last_name=updated_user_data["last_name"],
            training_experience_years=updated_user_data["training_experience_years"],
            is_active=updated_user_data["is_active"],
            is_verified=updated_user_data["is_verified"],
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T12:00:00"
        )
        
        mock_use_case.execute.return_value = mock_user_dto
        mock_dep.return_value = mock_use_case
        
        response = bdd_context.test_client.put("/api/v1/auth/me", json=update_data, headers=headers)
        bdd_context.response = response
        
        if response.status_code == 200:
            bdd_context.user_data["training_experience_years"] = experience


@then('my training experience should be updated to {experience:d} years')
def step_training_experience_updated(context, experience):
    """Verify training experience was updated."""
    bdd_context = get_context(context)
    utils = get_utils()
    
    utils.assert_json_field(bdd_context.response, "training_experience_years", experience)
