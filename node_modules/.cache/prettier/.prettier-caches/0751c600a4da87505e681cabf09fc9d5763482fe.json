{"0dea0a815c0c8a4b747de4239ab8ba58e044ae00": {"files": {"docker/docker-compose.yml": ["5B2xVmkpqBXvy/RJUTsDtvDPKdw=", true], ".github/ISSUE_TEMPLATE/bug_report.md": ["ZkHN6FASCZCx2qjj9neSHwPe9es=", true], "CHANGELOG.md": ["ZdlA5LAWM7mICdGXFx2yMRt0Spc=", true], "README.md": ["jkZSP1eGIOKbUEekupbeBwML+Cg=", true], ".pre-commit-config.yaml": ["VWdIK7XrTEXW+YmiShbmOvCx1Ok=", true], ".github/pull_request_template.md": ["q3MqbYcsC+2G9O7qSgytTUYosbI=", true], "project_seed.md": ["PJQuzjMd/QkCQevXIVsD1B36tNY=", true], ".github/ISSUE_TEMPLATE/feature_request.md": ["ZRdJ28VvFCYOE7LK+46ZxofuyJc=", true], "CONTRIBUTING.md": ["zYrfCVKZLcSwm1Q8WwdpE1CSP34=", true]}, "modified": 1749978099601}}