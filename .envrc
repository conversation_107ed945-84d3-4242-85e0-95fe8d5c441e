# Direnv configuration for Forge Protocol
# Automatically loads Nix development environment when entering directory

# Use Nix flake for development environment
use flake

# Load environment variables from .env file
dotenv_if_exists .env

# Add local bin to PATH for Python packages
PATH_add ~/.local/bin

# Set Python path
export PYTHONPATH="$PWD:$PYTHONPATH"

# Development environment
export ENVIRONMENT="development"
export DEBUG="true"

# Python optimization
export PYTHONDONTWRITEBYTECODE="1"
export PYTHONUNBUFFERED="1"

# Database defaults for development
export PGHOST="localhost"
export PGPORT="5432"
export PGUSER="rp_user"
export PGPASSWORD="rp_password"
export PGDATABASE="rp_training"

echo "🔥 Forge Protocol environment loaded via direnv + Nix"
