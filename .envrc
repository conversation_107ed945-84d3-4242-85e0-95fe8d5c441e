# Automatically load Nix development environment
use flake

# Set up Python path
export PYTHONPATH="$PWD:$PYTHONPATH"

# Development environment variables
export TESTING=true
export SECRET_KEY="dev-secret-key-for-direnv-environment"
export DATABASE_URL="sqlite+aiosqlite:///./test.db"
export REDIS_URL="redis://localhost:6379/0"
export DEBUG=true

# Create necessary directories
mkdir -p logs htmlcov .pytest_cache

echo "🏋️  RP Training API environment loaded via direnv"
