# RP Training API Dependencies for Nix Environment
# This file documents the Python packages we need in our Nix environment

# Core Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
asyncpg>=0.29.0
psycopg2-binary>=2.9.0

# Authentication & Security
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6
bcrypt>=4.0.0

# HTTP Client
httpx>=0.25.0

# Configuration
pydantic>=2.4.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
coverage>=7.3.0

# BDD Testing
behave>=1.2.6

# Code Quality
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0
bandit>=1.7.5

# Development Tools
pre-commit>=3.5.0

# Performance Monitoring
psutil>=5.9.0

# Additional Utilities
aiofiles>=23.2.0
requests>=2.31.0
