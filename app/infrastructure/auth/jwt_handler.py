"""
JWT token handling utilities.

Provides JWT token generation, validation, and refresh token management.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from uuid import UUID

from jose import JWTError, jwt

from ...config import settings
from ...domain.exceptions.auth_exceptions import (
    TokenExpiredError,
    InvalidTokenError
)


class JWTHandler:
    """Handles JWT token operations."""
    
    def __init__(self) -> None:
        """Initialize JWT handler with configuration."""
        self._secret_key = settings.secret_key
        self._algorithm = settings.algorithm
        self._access_token_expire_minutes = settings.access_token_expire_minutes
        self._refresh_token_expire_days = settings.refresh_token_expire_days
    
    async def generate_access_token(self, user_id: str) -> str:
        """
        Generate an access token for a user.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            JWT access token string
        """
        expire = datetime.utcnow() + timedelta(minutes=self._access_token_expire_minutes)
        
        payload = {
            "sub": user_id,
            "type": "access",
            "exp": expire,
            "iat": datetime.utcnow(),
        }
        
        return jwt.encode(payload, self._secret_key, algorithm=self._algorithm)
    
    async def generate_refresh_token(self, user_id: str) -> str:
        """
        Generate a refresh token for a user.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            JWT refresh token string
        """
        expire = datetime.utcnow() + timedelta(days=self._refresh_token_expire_days)
        
        payload = {
            "sub": user_id,
            "type": "refresh",
            "exp": expire,
            "iat": datetime.utcnow(),
        }
        
        return jwt.encode(payload, self._secret_key, algorithm=self._algorithm)
    
    def decode_token(self, token: str) -> Dict[str, Any]:
        """
        Decode and validate a JWT token.
        
        Args:
            token: JWT token string to decode
            
        Returns:
            Token payload dictionary
            
        Raises:
            InvalidTokenError: If token is malformed or invalid
            TokenExpiredError: If token has expired
        """
        try:
            payload = jwt.decode(
                token,
                self._secret_key,
                algorithms=[self._algorithm]
            )
            return payload
            
        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("Token has expired")
        except JWTError as e:
            raise InvalidTokenError(f"Invalid token: {str(e)}")
    
    def get_user_id_from_token(self, token: str) -> UUID:
        """
        Extract user ID from a JWT token.
        
        Args:
            token: JWT token string
            
        Returns:
            User's UUID
            
        Raises:
            InvalidTokenError: If token is invalid or doesn't contain user ID
            TokenExpiredError: If token has expired
        """
        payload = self.decode_token(token)
        
        user_id_str = payload.get("sub")
        if not user_id_str:
            raise InvalidTokenError("Token does not contain user ID")
        
        try:
            return UUID(user_id_str)
        except ValueError:
            raise InvalidTokenError("Invalid user ID format in token")
    
    def get_token_type(self, token: str) -> str:
        """
        Get the type of a JWT token (access or refresh).
        
        Args:
            token: JWT token string
            
        Returns:
            Token type string ("access" or "refresh")
            
        Raises:
            InvalidTokenError: If token is invalid or doesn't contain type
            TokenExpiredError: If token has expired
        """
        payload = self.decode_token(token)
        
        token_type = payload.get("type")
        if not token_type:
            raise InvalidTokenError("Token does not contain type")
        
        return token_type
    
    def is_token_expired(self, token: str) -> bool:
        """
        Check if a token is expired without raising an exception.
        
        Args:
            token: JWT token string
            
        Returns:
            True if token is expired, False otherwise
        """
        try:
            self.decode_token(token)
            return False
        except TokenExpiredError:
            return True
        except InvalidTokenError:
            return True
    
    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """
        Get the expiry time of a token.
        
        Args:
            token: JWT token string
            
        Returns:
            Token expiry datetime or None if invalid
        """
        try:
            payload = self.decode_token(token)
            exp_timestamp = payload.get("exp")
            if exp_timestamp:
                return datetime.fromtimestamp(exp_timestamp)
            return None
        except (TokenExpiredError, InvalidTokenError):
            return None


# Global JWT handler instance
jwt_handler = JWTHandler()
