"""
Password handling utilities.

Provides secure password hashing and verification using bcrypt.
"""

from passlib.context import CryptContext


class PasswordHandler:
    """Handles password hashing and verification."""

    def __init__(self) -> None:
        """Initialize password handler with bcrypt context."""
        self._pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__rounds=12,  # Secure rounds for bcrypt
        )

    def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt.

        Args:
            password: Plain text password to hash

        Returns:
            Hashed password string
        """
        return self._pwd_context.hash(password)

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash.

        Args:
            password: Plain text password to verify
            hashed_password: Hashed password to verify against

        Returns:
            True if password matches, False otherwise
        """
        return self._pwd_context.verify(password, hashed_password)

    def needs_update(self, hashed_password: str) -> bool:
        """
        Check if a hashed password needs to be updated.

        This is useful for migrating to stronger hashing parameters.

        Args:
            hashed_password: Hashed password to check

        Returns:
            True if password needs rehashing, False otherwise
        """
        return self._pwd_context.needs_update(hashed_password)


# Global password handler instance
password_handler = PasswordHandler()
