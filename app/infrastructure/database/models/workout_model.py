"""
Workout database models.

SQLAlchemy models for workout management including workouts, exercise sets,
and workout templates with comprehensive tracking capabilities.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    Boolean,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    Numeric,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..connection import Base


class WorkoutModel(Base):
    """
    Workout database model.
    
    Represents a workout session with metadata, timing, and statistics.
    """
    
    __tablename__ = "workouts"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )
    
    # User relationship
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False, index=True
    )
    
    # Workout metadata
    name: Mapped[Optional[str]] = mapped_column(String(255))
    workout_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, index=True)
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Workout statistics
    total_volume_load: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    average_rpe: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 1))
    total_sets: Mapped[int] = mapped_column(Integer, default=0)
    total_exercises: Mapped[int] = mapped_column(Integer, default=0)
    duration_minutes: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Notes and template info
    notes: Mapped[Optional[str]] = mapped_column(Text)
    is_template: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    template_name: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    exercise_sets: Mapped[List["ExerciseSetModel"]] = relationship(
        "ExerciseSetModel", back_populates="workout", cascade="all, delete-orphan"
    )


class ExerciseSetModel(Base):
    """
    Exercise set database model.
    
    Represents a single set within a workout with detailed tracking
    including RIR, RPE, tempo, and range of motion.
    """
    
    __tablename__ = "exercise_sets"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )
    
    # Foreign keys
    workout_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("workouts.id", ondelete="CASCADE"),
        nullable=False, index=True
    )
    exercise_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("exercises.id"),
        nullable=False, index=True
    )
    
    # Set metadata
    set_number: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Performance data
    weight_kg: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    reps_completed: Mapped[Optional[int]] = mapped_column(Integer)
    reps_target: Mapped[Optional[int]] = mapped_column(Integer)
    
    # RIR and RPE tracking
    rir_target: Mapped[Optional[int]] = mapped_column(Integer)  # 0-10
    rir_actual: Mapped[Optional[int]] = mapped_column(Integer)  # 0-10
    rpe: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 1))  # 1.0-10.0
    
    # Advanced tracking
    rest_seconds: Mapped[Optional[int]] = mapped_column(Integer)
    tempo: Mapped[Optional[str]] = mapped_column(String(20))  # e.g., "3-1-2-1"
    range_of_motion: Mapped[Optional[str]] = mapped_column(String(50))  # e.g., "full", "partial"
    
    # Set characteristics
    is_warmup: Mapped[bool] = mapped_column(Boolean, default=False)
    is_dropset: Mapped[bool] = mapped_column(Boolean, default=False)
    is_failure: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Notes
    notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    
    # Relationships
    workout: Mapped["WorkoutModel"] = relationship("WorkoutModel", back_populates="exercise_sets")
