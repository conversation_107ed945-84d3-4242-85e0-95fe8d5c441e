"""Exercise database models with versioning and soft-delete support.

SQLAlchemy models for exercise-related database tables implementing
comprehensive versioning, soft-delete, and audit trail functionality.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    JSON,
    Boolean,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from ..connection import Base


class ExerciseModel(Base):
    """Exercise database model with versioning and soft-delete support.

    This model implements a versioning system where each exercise can have
    multiple versions, allowing for historical tracking and rollback capabilities.
    """

    __tablename__ = "exercises"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Versioning fields
    exercise_uuid: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=False, index=True
    )
    version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    is_current_version: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    parent_version_id: Mapped[UUID | None] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("exercises.id"), nullable=True
    )

    # Core exercise data
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str | None] = mapped_column(Text)

    # Muscle targeting
    primary_muscle_group: Mapped[str] = mapped_column(
        Enum(
            "chest",
            "back",
            "shoulders",
            "biceps",
            "triceps",
            "quads",
            "hamstrings",
            "glutes",
            "calves",
            "abs",
            "forearms",
            "traps",
            "lats",
            "rear_delts",
            "side_delts",
            name="muscle_group_enum",
        ),
        nullable=False,
        index=True,
    )
    secondary_muscle_groups: Mapped[list[str] | None] = mapped_column(
        JSON, nullable=True
    )

    # Movement classification
    movement_pattern: Mapped[str] = mapped_column(
        Enum(
            "push",
            "pull",
            "squat",
            "hinge",
            "carry",
            "isolation",
            "rotation",
            "anti_extension",
            "anti_flexion",
            "anti_rotation",
            name="movement_pattern_enum",
        ),
        nullable=False,
        index=True,
    )

    # Equipment and difficulty
    equipment_required: Mapped[list[str] | None] = mapped_column(JSON, nullable=True)
    difficulty_level: Mapped[str] = mapped_column(
        Enum("beginner", "intermediate", "advanced", name="difficulty_enum"),
        nullable=False,
        index=True,
    )

    # Media and instructions
    video_url: Mapped[str | None] = mapped_column(String(500))
    thumbnail_url: Mapped[str | None] = mapped_column(String(500))
    form_cues: Mapped[list[str] | None] = mapped_column(JSON)
    setup_instructions: Mapped[str | None] = mapped_column(Text)
    execution_steps: Mapped[list[str] | None] = mapped_column(JSON)
    common_mistakes: Mapped[list[str] | None] = mapped_column(JSON)
    safety_notes: Mapped[str | None] = mapped_column(Text)

    # Status and metadata
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    is_approved: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    approval_status: Mapped[str] = mapped_column(
        Enum(
            "pending",
            "approved",
            "rejected",
            "needs_review",
            name="approval_status_enum",
        ),
        default="pending",
        index=True,
    )

    # Audit fields
    created_by: Mapped[UUID | None] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )
    updated_by: Mapped[UUID | None] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )
    approved_by: Mapped[UUID | None] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    deleted_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    approved_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # Version control metadata
    version_notes: Mapped[str | None] = mapped_column(Text)
    change_reason: Mapped[str | None] = mapped_column(
        Enum(
            "initial_creation",
            "content_update",
            "correction",
            "media_update",
            "approval_change",
            "user_request",
            name="change_reason_enum",
        ),
        default="initial_creation",
    )

    # Relationships
    parent_version: Mapped[Optional["ExerciseModel"]] = relationship(
        "ExerciseModel", remote_side=[id], back_populates="child_versions"
    )
    child_versions: Mapped[list["ExerciseModel"]] = relationship(
        "ExerciseModel", back_populates="parent_version"
    )
    media: Mapped[list["ExerciseMediaModel"]] = relationship(
        "ExerciseMediaModel", back_populates="exercise", cascade="all, delete-orphan"
    )

    # Indexes and constraints
    __table_args__ = (
        # Ensure only one current version per exercise_uuid
        UniqueConstraint(
            "exercise_uuid", "is_current_version", name="uq_exercise_current_version"
        ),
        # Ensure unique version numbers per exercise_uuid
        UniqueConstraint("exercise_uuid", "version", name="uq_exercise_version_number"),
        # Performance indexes
        Index("idx_exercises_uuid_current", "exercise_uuid", "is_current_version"),
        Index("idx_exercises_muscle_active", "primary_muscle_group", "is_active"),
        Index(
            "idx_exercises_pattern_difficulty", "movement_pattern", "difficulty_level"
        ),
        Index("idx_exercises_approval", "approval_status", "is_active"),
        Index("idx_exercises_search", "name"),
        Index("idx_exercises_active_not_deleted", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of exercise model."""
        return (
            f"<ExerciseModel(id={self.id}, name={self.name}, version={self.version})>"
        )


class ExerciseMediaModel(Base):
    """Exercise media database model for videos, images, and other assets."""

    __tablename__ = "exercise_media"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Foreign key to exercise
    exercise_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("exercises.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Media metadata
    media_type: Mapped[str] = mapped_column(
        Enum("video", "image", "gif", "audio", name="media_type_enum"),
        nullable=False,
        index=True,
    )
    url: Mapped[str] = mapped_column(String(500), nullable=False)
    thumbnail_url: Mapped[str | None] = mapped_column(String(500))
    title: Mapped[str | None] = mapped_column(String(255))
    description: Mapped[str | None] = mapped_column(Text)

    # File metadata
    file_size_bytes: Mapped[int | None] = mapped_column(Integer)
    duration_seconds: Mapped[int | None] = mapped_column(Integer)  # for videos/audio
    width_pixels: Mapped[int | None] = mapped_column(Integer)
    height_pixels: Mapped[int | None] = mapped_column(Integer)
    mime_type: Mapped[str | None] = mapped_column(String(100))

    # Organization
    sort_order: Mapped[int] = mapped_column(Integer, default=0)
    is_primary: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationships
    exercise: Mapped["ExerciseModel"] = relationship(
        "ExerciseModel", back_populates="media"
    )

    # Indexes
    __table_args__ = (
        Index("idx_exercise_media_exercise_type", "exercise_id", "media_type"),
        Index("idx_exercise_media_primary", "exercise_id", "is_primary"),
        Index("idx_exercise_media_sort", "exercise_id", "sort_order"),
    )

    def __repr__(self) -> str:
        """String representation of exercise media model."""
        return f"<ExerciseMediaModel(id={self.id}, exercise_id={self.exercise_id}, type={self.media_type})>"


class ExerciseAuditLogModel(Base):
    """Audit log for tracking all changes to exercises."""

    __tablename__ = "exercise_audit_log"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Reference to exercise
    exercise_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("exercises.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    exercise_uuid: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=False, index=True
    )

    # Audit metadata
    action: Mapped[str] = mapped_column(
        Enum(
            "created",
            "updated",
            "deleted",
            "restored",
            "approved",
            "rejected",
            "version_created",
            name="audit_action_enum",
        ),
        nullable=False,
        index=True,
    )
    user_id: Mapped[UUID | None] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )
    ip_address: Mapped[str | None] = mapped_column(String(45))  # IPv6 support
    user_agent: Mapped[str | None] = mapped_column(Text)

    # Change details
    field_changes: Mapped[dict | None] = mapped_column(JSON)  # JSON of changes
    old_values: Mapped[dict | None] = mapped_column(JSON)  # JSON of old values
    new_values: Mapped[dict | None] = mapped_column(JSON)  # JSON of new values
    notes: Mapped[str | None] = mapped_column(Text)

    # Timestamp
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )

    # Indexes
    __table_args__ = (
        Index("idx_audit_log_exercise_action", "exercise_id", "action"),
        Index("idx_audit_log_uuid_date", "exercise_uuid", "created_at"),
        Index("idx_audit_log_user_date", "user_id", "created_at"),
    )

    def __repr__(self) -> str:
        """String representation of audit log model."""
        return f"<ExerciseAuditLogModel(id={self.id}, exercise_id={self.exercise_id}, action={self.action})>"
