"""
User database models.

SQLAlchemy models for user-related database tables.
"""

from datetime import datetime
from typing import Optional
from uuid import UUI<PERSON>, uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Index, Integer, String
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from ..connection import Base


class UserModel(Base):
    """User database model."""

    __tablename__ = "users"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Authentication fields
    email: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=False, index=True
    )
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)

    # Profile fields
    first_name: Mapped[Optional[str]] = mapped_column(String(100))
    last_name: Mapped[Optional[str]] = mapped_column(String(100))
    training_experience_years: Mapped[Optional[int]] = mapped_column(Integer)

    # Status fields
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # Relationships
    refresh_tokens: Mapped[list["RefreshTokenModel"]] = relationship(
        "RefreshTokenModel", back_populates="user", cascade="all, delete-orphan"
    )

    # Indexes
    __table_args__ = (
        Index("idx_users_active", "is_active", postgresql_where="deleted_at IS NULL"),
        Index("idx_users_email_active", "email", "is_active"),
    )

    def __repr__(self) -> str:
        """String representation of user model."""
        return f"<UserModel(id={self.id}, email={self.email})>"


class RefreshTokenModel(Base):
    """Refresh token database model."""

    __tablename__ = "refresh_tokens"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Foreign key to user
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Token data
    token_hash: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )
    is_revoked: Mapped[bool] = mapped_column(Boolean, default=False)

    # Metadata
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # Relationships
    user: Mapped["UserModel"] = relationship(
        "UserModel", back_populates="refresh_tokens"
    )

    # Indexes
    __table_args__ = (
        Index("idx_refresh_tokens_expires", "expires_at"),
        Index("idx_refresh_tokens_user_active", "user_id", "is_revoked"),
    )

    def __repr__(self) -> str:
        """String representation of refresh token model."""
        return f"<RefreshTokenModel(id={self.id}, user_id={self.user_id})>"
