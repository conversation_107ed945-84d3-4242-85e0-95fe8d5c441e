"""
Workout repository implementation.

SQLAlchemy implementation of the workout repository interface providing
data access operations for workouts and exercise sets.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ....domain.entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutUpdateRequest,
)
from ....domain.repositories.workout_repository import WorkoutRepository
from ..models.workout_model import ExerciseSetModel, WorkoutModel


class WorkoutRepositoryImpl(WorkoutRepository):
    """
    SQLAlchemy implementation of workout repository.
    
    Provides data access operations for workouts and exercise sets
    with comprehensive tracking and statistics.
    """
    
    def __init__(self, session: AsyncSession):
        """
        Initialize repository with database session.
        
        Args:
            session: SQLAlchemy async session
        """
        self.session = session
    
    # Workout operations
    async def create_workout(self, workout_request: WorkoutCreateRequest, user_id: UUID) -> Workout:
        """Create a new workout."""
        workout_model = WorkoutModel(
            id=uuid4(),
            user_id=user_id,
            name=workout_request.name,
            workout_date=workout_request.workout_date,
            notes=workout_request.notes,
            is_template=workout_request.is_template,
            template_name=workout_request.template_name,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        self.session.add(workout_model)
        await self.session.flush()
        
        return self._model_to_entity(workout_model)
    
    async def get_workout_by_id(self, workout_id: UUID) -> Optional[Workout]:
        """Get workout by ID."""
        stmt = (
            select(WorkoutModel)
            .options(selectinload(WorkoutModel.exercise_sets))
            .where(
                and_(
                    WorkoutModel.id == workout_id,
                    WorkoutModel.deleted_at.is_(None)
                )
            )
        )
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()
        
        return self._model_to_entity(model) if model else None
    
    async def update_workout(self, workout_id: UUID, workout_request: WorkoutUpdateRequest) -> Optional[Workout]:
        """Update an existing workout."""
        # Build update values
        update_values = {"updated_at": datetime.utcnow()}
        
        if workout_request.name is not None:
            update_values["name"] = workout_request.name
        if workout_request.workout_date is not None:
            update_values["workout_date"] = workout_request.workout_date
        if workout_request.notes is not None:
            update_values["notes"] = workout_request.notes
        if workout_request.is_template is not None:
            update_values["is_template"] = workout_request.is_template
        if workout_request.template_name is not None:
            update_values["template_name"] = workout_request.template_name
        
        stmt = (
            update(WorkoutModel)
            .where(
                and_(
                    WorkoutModel.id == workout_id,
                    WorkoutModel.deleted_at.is_(None)
                )
            )
            .values(**update_values)
        )
        
        result = await self.session.execute(stmt)
        
        if result.rowcount == 0:
            return None
        
        return await self.get_workout_by_id(workout_id)
    
    async def delete_workout(self, workout_id: UUID) -> bool:
        """Soft delete a workout."""
        stmt = (
            update(WorkoutModel)
            .where(
                and_(
                    WorkoutModel.id == workout_id,
                    WorkoutModel.deleted_at.is_(None)
                )
            )
            .values(deleted_at=datetime.utcnow())
        )
        
        result = await self.session.execute(stmt)
        return result.rowcount > 0
    
    async def search_workouts(
        self, 
        filters: WorkoutSearchFilters,
        limit: int = 50,
        offset: int = 0
    ) -> List[Workout]:
        """Search workouts with filters."""
        stmt = (
            select(WorkoutModel)
            .options(selectinload(WorkoutModel.exercise_sets))
            .where(WorkoutModel.deleted_at.is_(None))
        )
        
        # Apply filters
        if filters.user_id:
            stmt = stmt.where(WorkoutModel.user_id == filters.user_id)
        
        if filters.is_template is not None:
            stmt = stmt.where(WorkoutModel.is_template == filters.is_template)
        
        if filters.date_from:
            stmt = stmt.where(WorkoutModel.workout_date >= filters.date_from)
        
        if filters.date_to:
            stmt = stmt.where(WorkoutModel.workout_date <= filters.date_to)
        
        if filters.name_contains:
            stmt = stmt.where(WorkoutModel.name.ilike(f"%{filters.name_contains}%"))
        
        # Apply status filter
        if filters.status:
            if filters.status.value == "planned":
                stmt = stmt.where(
                    and_(
                        WorkoutModel.started_at.is_(None),
                        WorkoutModel.completed_at.is_(None)
                    )
                )
            elif filters.status.value == "in_progress":
                stmt = stmt.where(
                    and_(
                        WorkoutModel.started_at.is_not(None),
                        WorkoutModel.completed_at.is_(None)
                    )
                )
            elif filters.status.value == "completed":
                stmt = stmt.where(WorkoutModel.completed_at.is_not(None))
        
        # Apply ordering and pagination
        stmt = stmt.order_by(desc(WorkoutModel.workout_date)).limit(limit).offset(offset)
        
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._model_to_entity(model) for model in models]
    
    async def count_workouts(self, filters: WorkoutSearchFilters) -> int:
        """Count workouts matching filters."""
        stmt = select(func.count(WorkoutModel.id)).where(WorkoutModel.deleted_at.is_(None))
        
        # Apply same filters as search
        if filters.user_id:
            stmt = stmt.where(WorkoutModel.user_id == filters.user_id)
        
        if filters.is_template is not None:
            stmt = stmt.where(WorkoutModel.is_template == filters.is_template)
        
        if filters.date_from:
            stmt = stmt.where(WorkoutModel.workout_date >= filters.date_from)
        
        if filters.date_to:
            stmt = stmt.where(WorkoutModel.workout_date <= filters.date_to)
        
        if filters.name_contains:
            stmt = stmt.where(WorkoutModel.name.ilike(f"%{filters.name_contains}%"))
        
        result = await self.session.execute(stmt)
        return result.scalar() or 0
    
    async def get_user_workouts(
        self, 
        user_id: UUID,
        limit: int = 50,
        offset: int = 0
    ) -> List[Workout]:
        """Get workouts for a specific user."""
        filters = WorkoutSearchFilters(user_id=user_id, is_template=False)
        return await self.search_workouts(filters, limit, offset)
    
    async def get_active_workout(self, user_id: UUID) -> Optional[Workout]:
        """Get user's currently active workout."""
        stmt = (
            select(WorkoutModel)
            .options(selectinload(WorkoutModel.exercise_sets))
            .where(
                and_(
                    WorkoutModel.user_id == user_id,
                    WorkoutModel.started_at.is_not(None),
                    WorkoutModel.completed_at.is_(None),
                    WorkoutModel.deleted_at.is_(None)
                )
            )
        )
        
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()
        
        return self._model_to_entity(model) if model else None
    
    async def start_workout(self, workout_id: UUID) -> Optional[Workout]:
        """Start a workout session."""
        stmt = (
            update(WorkoutModel)
            .where(
                and_(
                    WorkoutModel.id == workout_id,
                    WorkoutModel.started_at.is_(None),
                    WorkoutModel.deleted_at.is_(None)
                )
            )
            .values(
                started_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        )
        
        result = await self.session.execute(stmt)
        
        if result.rowcount == 0:
            return None
        
        return await self.get_workout_by_id(workout_id)
    
    async def complete_workout(self, workout_id: UUID) -> Optional[Workout]:
        """Complete a workout session."""
        stmt = (
            update(WorkoutModel)
            .where(
                and_(
                    WorkoutModel.id == workout_id,
                    WorkoutModel.started_at.is_not(None),
                    WorkoutModel.completed_at.is_(None),
                    WorkoutModel.deleted_at.is_(None)
                )
            )
            .values(
                completed_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        )
        
        result = await self.session.execute(stmt)
        
        if result.rowcount == 0:
            return None
        
        # Get the workout and calculate statistics
        workout = await self.get_workout_by_id(workout_id)
        if workout:
            # Update statistics
            await self._update_workout_statistics(workout_id)
            # Reload with updated statistics
            workout = await self.get_workout_by_id(workout_id)
        
        return workout

    # Exercise set operations
    async def create_exercise_set(
        self,
        workout_id: UUID,
        set_request: ExerciseSetCreateRequest
    ) -> ExerciseSet:
        """Create a new exercise set."""
        set_model = ExerciseSetModel(
            id=uuid4(),
            workout_id=workout_id,
            exercise_id=set_request.exercise_id,
            set_number=set_request.set_number,
            weight_kg=set_request.weight_kg,
            reps_completed=set_request.reps_completed,
            reps_target=set_request.reps_target,
            rir_target=set_request.rir_target,
            rir_actual=set_request.rir_actual,
            rpe=set_request.rpe,
            rest_seconds=set_request.rest_seconds,
            tempo=set_request.tempo,
            range_of_motion=set_request.range_of_motion,
            is_warmup=set_request.is_warmup,
            is_dropset=set_request.is_dropset,
            is_failure=set_request.is_failure,
            notes=set_request.notes,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        self.session.add(set_model)
        await self.session.flush()

        return self._set_model_to_entity(set_model)

    async def get_exercise_set_by_id(self, set_id: UUID) -> Optional[ExerciseSet]:
        """Get exercise set by ID."""
        stmt = select(ExerciseSetModel).where(ExerciseSetModel.id == set_id)
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()

        return self._set_model_to_entity(model) if model else None

    async def update_exercise_set(
        self,
        set_id: UUID,
        set_request: ExerciseSetUpdateRequest
    ) -> Optional[ExerciseSet]:
        """Update an existing exercise set."""
        # Build update values
        update_values = {"updated_at": datetime.utcnow()}

        if set_request.weight_kg is not None:
            update_values["weight_kg"] = set_request.weight_kg
        if set_request.reps_completed is not None:
            update_values["reps_completed"] = set_request.reps_completed
        if set_request.reps_target is not None:
            update_values["reps_target"] = set_request.reps_target
        if set_request.rir_target is not None:
            update_values["rir_target"] = set_request.rir_target
        if set_request.rir_actual is not None:
            update_values["rir_actual"] = set_request.rir_actual
        if set_request.rpe is not None:
            update_values["rpe"] = set_request.rpe
        if set_request.rest_seconds is not None:
            update_values["rest_seconds"] = set_request.rest_seconds
        if set_request.tempo is not None:
            update_values["tempo"] = set_request.tempo
        if set_request.range_of_motion is not None:
            update_values["range_of_motion"] = set_request.range_of_motion
        if set_request.is_warmup is not None:
            update_values["is_warmup"] = set_request.is_warmup
        if set_request.is_dropset is not None:
            update_values["is_dropset"] = set_request.is_dropset
        if set_request.is_failure is not None:
            update_values["is_failure"] = set_request.is_failure
        if set_request.notes is not None:
            update_values["notes"] = set_request.notes

        stmt = (
            update(ExerciseSetModel)
            .where(ExerciseSetModel.id == set_id)
            .values(**update_values)
        )

        result = await self.session.execute(stmt)

        if result.rowcount == 0:
            return None

        return await self.get_exercise_set_by_id(set_id)

    async def delete_exercise_set(self, set_id: UUID) -> bool:
        """Delete an exercise set."""
        stmt = select(ExerciseSetModel).where(ExerciseSetModel.id == set_id)
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()

        if not model:
            return False

        await self.session.delete(model)
        return True

    async def get_workout_sets(self, workout_id: UUID) -> List[ExerciseSet]:
        """Get all exercise sets for a workout."""
        stmt = (
            select(ExerciseSetModel)
            .where(ExerciseSetModel.workout_id == workout_id)
            .order_by(ExerciseSetModel.set_number)
        )

        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._set_model_to_entity(model) for model in models]

    async def get_exercise_sets_by_exercise(
        self,
        workout_id: UUID,
        exercise_id: UUID
    ) -> List[ExerciseSet]:
        """Get exercise sets for a specific exercise in a workout."""
        stmt = (
            select(ExerciseSetModel)
            .where(
                and_(
                    ExerciseSetModel.workout_id == workout_id,
                    ExerciseSetModel.exercise_id == exercise_id
                )
            )
            .order_by(ExerciseSetModel.set_number)
        )

        result = await self.session.execute(stmt)
        models = result.scalars().all()

        return [self._set_model_to_entity(model) for model in models]

    # Template operations
    async def get_workout_templates(self, user_id: UUID) -> List[Workout]:
        """Get workout templates for a user."""
        filters = WorkoutSearchFilters(user_id=user_id, is_template=True)
        return await self.search_workouts(filters)

    async def copy_workout_as_template(
        self,
        workout_id: UUID,
        template_name: str
    ) -> Optional[Workout]:
        """Copy a workout as a template."""
        # Get the original workout
        original = await self.get_workout_by_id(workout_id)
        if not original:
            return None

        # Create template workout
        template_request = WorkoutCreateRequest(
            name=template_name,
            workout_date=datetime.utcnow(),
            notes=original.notes,
            is_template=True,
            template_name=template_name,
        )

        template = await self.create_workout(template_request, original.user_id)

        # Copy exercise sets
        for original_set in original.exercise_sets:
            set_request = ExerciseSetCreateRequest(
                exercise_id=original_set.exercise_id,
                set_number=original_set.set_number,
                weight_kg=original_set.weight_kg,
                reps_target=original_set.reps_target,
                rir_target=original_set.rir_target,
                tempo=original_set.tempo,
                range_of_motion=original_set.range_of_motion,
                is_warmup=original_set.is_warmup,
                notes=original_set.notes,
            )
            await self.create_exercise_set(template.id, set_request)

        return await self.get_workout_by_id(template.id)

    async def create_workout_from_template(
        self,
        template_id: UUID,
        user_id: UUID,
        workout_date: Optional[str] = None
    ) -> Optional[Workout]:
        """Create a new workout from a template."""
        # Get the template
        template = await self.get_workout_by_id(template_id)
        if not template or not template.is_template:
            return None

        # Create new workout
        workout_request = WorkoutCreateRequest(
            name=template.name,
            workout_date=datetime.fromisoformat(workout_date) if workout_date else datetime.utcnow(),
            notes=template.notes,
            is_template=False,
        )

        workout = await self.create_workout(workout_request, user_id)

        # Copy exercise sets from template
        for template_set in template.exercise_sets:
            set_request = ExerciseSetCreateRequest(
                exercise_id=template_set.exercise_id,
                set_number=template_set.set_number,
                weight_kg=template_set.weight_kg,
                reps_target=template_set.reps_target,
                rir_target=template_set.rir_target,
                tempo=template_set.tempo,
                range_of_motion=template_set.range_of_motion,
                is_warmup=template_set.is_warmup,
                notes=template_set.notes,
            )
            await self.create_exercise_set(workout.id, set_request)

        return await self.get_workout_by_id(workout.id)

    # Utility methods
    async def exists(self, workout_id: UUID) -> bool:
        """Check if workout exists."""
        stmt = select(func.count(WorkoutModel.id)).where(
            and_(
                WorkoutModel.id == workout_id,
                WorkoutModel.deleted_at.is_(None)
            )
        )
        result = await self.session.execute(stmt)
        return (result.scalar() or 0) > 0

    async def get_workout_statistics(self, workout_id: UUID) -> dict:
        """Get comprehensive statistics for a workout."""
        workout = await self.get_workout_by_id(workout_id)
        if not workout:
            return {}

        stats = {
            "total_sets": workout.total_sets,
            "total_exercises": workout.total_exercises,
            "total_volume_load": float(workout.total_volume_load) if workout.total_volume_load else 0,
            "average_rpe": float(workout.average_rpe) if workout.average_rpe else None,
            "duration_minutes": workout.duration_minutes,
            "status": workout.status.value,
        }

        # Calculate additional statistics from sets
        if workout.exercise_sets:
            weights = [float(s.weight_kg) for s in workout.exercise_sets if s.weight_kg]
            reps = [s.reps_completed for s in workout.exercise_sets if s.reps_completed]

            if weights:
                stats["max_weight"] = max(weights)
                stats["average_weight"] = sum(weights) / len(weights)

            if reps:
                stats["total_reps"] = sum(reps)
                stats["average_reps"] = sum(reps) / len(reps)

        return stats

    async def _update_workout_statistics(self, workout_id: UUID) -> None:
        """Update workout statistics based on exercise sets."""
        # Get all sets for the workout
        sets = await self.get_workout_sets(workout_id)

        if not sets:
            return

        # Calculate statistics
        total_volume = sum(
            float(s.weight_kg * s.reps_completed)
            for s in sets
            if s.weight_kg and s.reps_completed
        )

        rpe_values = [float(s.rpe) for s in sets if s.rpe]
        average_rpe = sum(rpe_values) / len(rpe_values) if rpe_values else None

        unique_exercises = set(s.exercise_id for s in sets)

        # Update the workout
        stmt = (
            update(WorkoutModel)
            .where(WorkoutModel.id == workout_id)
            .values(
                total_volume_load=total_volume if total_volume > 0 else None,
                average_rpe=average_rpe,
                total_sets=len(sets),
                total_exercises=len(unique_exercises),
                updated_at=datetime.utcnow()
            )
        )

        await self.session.execute(stmt)

    def _set_model_to_entity(self, model: ExerciseSetModel) -> ExerciseSet:
        """Convert exercise set database model to domain entity."""
        return ExerciseSet(
            id=model.id,
            workout_id=model.workout_id,
            exercise_id=model.exercise_id,
            set_number=model.set_number,
            weight_kg=model.weight_kg,
            reps_completed=model.reps_completed,
            reps_target=model.reps_target,
            rir_target=model.rir_target,
            rir_actual=model.rir_actual,
            rpe=model.rpe,
            rest_seconds=model.rest_seconds,
            tempo=model.tempo,
            range_of_motion=model.range_of_motion,
            is_warmup=model.is_warmup,
            is_dropset=model.is_dropset,
            is_failure=model.is_failure,
            notes=model.notes,
            created_at=model.created_at,
            updated_at=model.updated_at,
        )
    
    def _model_to_entity(self, model: WorkoutModel) -> Workout:
        """Convert database model to domain entity."""
        exercise_sets = []
        if hasattr(model, 'exercise_sets') and model.exercise_sets:
            exercise_sets = [self._set_model_to_entity(set_model) for set_model in model.exercise_sets]
        
        return Workout(
            id=model.id,
            user_id=model.user_id,
            name=model.name,
            workout_date=model.workout_date,
            started_at=model.started_at,
            completed_at=model.completed_at,
            total_volume_load=model.total_volume_load,
            average_rpe=model.average_rpe,
            total_sets=model.total_sets,
            total_exercises=model.total_exercises,
            duration_minutes=model.duration_minutes,
            notes=model.notes,
            is_template=model.is_template,
            template_name=model.template_name,
            created_at=model.created_at,
            updated_at=model.updated_at,
            deleted_at=model.deleted_at,
            exercise_sets=exercise_sets,
        )
