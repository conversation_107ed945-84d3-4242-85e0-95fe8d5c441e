"""User repository implementation.

Concrete implementation of UserRepository interface using SQLAlchemy.
"""

from datetime import datetime
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ....domain.entities.user import User
from ....domain.exceptions.auth_exceptions import (
    UserAlreadyExistsError,
    UserNotFoundError,
)
from ....domain.repositories.user_repository import UserRepository
from ..models.user_model import UserModel


class UserRepositoryImpl(UserRepository):
    """SQLAlchemy implementation of UserRepository."""

    def __init__(self, session: AsyncSession) -> None:
        """Initialize repository with database session."""
        self._session = session

    async def create(self, user: User) -> User:
        """Create a new user in the database.

        Args:
            user: User entity to create

        Returns:
            Created user entity with generated ID

        Raises:
            UserAlreadyExistsError: If user with email already exists
        """
        # Check if user already exists
        if await self.exists_by_email(user.email):
            raise UserAlreadyExistsError(f"User with email {user.email} already exists")

        # Password should already be hashed by the domain service
        # No additional hashing needed here
        hashed_password = user.hashed_password

        # Create database model
        user_model = UserModel(
            id=user.id,
            email=user.email,
            hashed_password=hashed_password,
            first_name=user.first_name,
            last_name=user.last_name,
            training_experience_years=user.training_experience_years,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            deleted_at=user.deleted_at,
        )

        # Add to session and flush to get generated values
        self._session.add(user_model)
        await self._session.flush()
        await self._session.refresh(user_model)

        # Convert back to domain entity
        return self._model_to_entity(user_model)

    async def get_by_id(self, user_id: UUID) -> User | None:
        """Get user by ID.

        Args:
            user_id: User's unique identifier

        Returns:
            User entity if found, None otherwise
        """
        stmt = select(UserModel).where(
            UserModel.id == user_id, UserModel.deleted_at.is_(None)
        )
        result = await self._session.execute(stmt)
        user_model = result.scalar_one_or_none()

        if user_model:
            return self._model_to_entity(user_model)
        return None

    async def get_by_email(self, email: str) -> User | None:
        """Get user by email address.

        Args:
            email: User's email address

        Returns:
            User entity if found, None otherwise
        """
        stmt = select(UserModel).where(
            UserModel.email == email.lower(), UserModel.deleted_at.is_(None)
        )
        result = await self._session.execute(stmt)
        user_model = result.scalar_one_or_none()

        if user_model:
            return self._model_to_entity(user_model)
        return None

    async def update(self, user: User) -> User:
        """Update existing user.

        Args:
            user: User entity with updated data

        Returns:
            Updated user entity

        Raises:
            UserNotFoundError: If user doesn't exist
        """
        stmt = (
            update(UserModel)
            .where(UserModel.id == user.id, UserModel.deleted_at.is_(None))
            .values(
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                training_experience_years=user.training_experience_years,
                is_active=user.is_active,
                is_verified=user.is_verified,
                updated_at=user.updated_at,
                deleted_at=user.deleted_at,
            )
            .returning(UserModel)
        )

        result = await self._session.execute(stmt)
        user_model = result.scalar_one_or_none()

        if not user_model:
            raise UserNotFoundError(f"User with ID {user.id} not found")

        return self._model_to_entity(user_model)

    async def delete(self, user_id: UUID) -> bool:
        """Soft delete user by ID.

        Args:
            user_id: User's unique identifier

        Returns:
            True if user was deleted, False if not found
        """
        stmt = (
            update(UserModel)
            .where(UserModel.id == user_id, UserModel.deleted_at.is_(None))
            .values(deleted_at=datetime.utcnow(), updated_at=datetime.utcnow())
        )

        result = await self._session.execute(stmt)
        return result.rowcount > 0

    async def exists_by_email(self, email: str) -> bool:
        """Check if user exists by email.

        Args:
            email: Email address to check

        Returns:
            True if user exists, False otherwise
        """
        stmt = select(UserModel.id).where(
            UserModel.email == email.lower(), UserModel.deleted_at.is_(None)
        )
        result = await self._session.execute(stmt)
        return result.scalar_one_or_none() is not None

    def _model_to_entity(self, model: UserModel) -> User:
        """Convert database model to domain entity.

        Args:
            model: UserModel instance

        Returns:
            User domain entity
        """
        return User(
            id=model.id,
            email=model.email,
            hashed_password=model.hashed_password,
            first_name=model.first_name,
            last_name=model.last_name,
            training_experience_years=model.training_experience_years,
            is_active=model.is_active,
            is_verified=model.is_verified,
            created_at=model.created_at,
            updated_at=model.updated_at,
            deleted_at=model.deleted_at,
        )
