"""
Application configuration management.

Handles environment variables, database settings, and application configuration
following the 12-factor app methodology.
"""

import os
from functools import lru_cache
from typing import Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application
    app_name: str = "RP Training API"
    app_version: str = "0.1.0"
    debug: bool = False

    # API Configuration
    api_v1_prefix: str = "/api/v1"

    # Security
    secret_key: str = "dev-secret-key-change-in-production-minimum-32-chars"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 30

    # Database
    database_url: str = "sqlite+aiosqlite:///./test.db"
    database_pool_size: int = 10
    database_max_overflow: int = 20

    # Redis (for caching and sessions)
    redis_url: str = "redis://localhost:6379/0"

    # CORS
    allowed_origins: str = "http://localhost:3000,http://localhost:8080"

    # Logging
    log_level: str = "INFO"

    # Testing
    testing: bool = False
    test_database_url: Optional[str] = None

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Ensure secret key is provided and sufficiently long."""
        if not v:
            raise ValueError("SECRET_KEY must be provided")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Ensure database URL is provided."""
        if not v:
            raise ValueError("DATABASE_URL must be provided")
        return v

    @property
    def cors_origins(self) -> list[str]:
        """Convert comma-separated origins string to list."""
        return [
            origin.strip()
            for origin in self.allowed_origins.split(",")
            if origin.strip()
        ]

    model_config = {"env_file": ".env", "case_sensitive": False}


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


# Global settings instance
settings = get_settings()
