"""Exercise API schemas.

Pydantic models for exercise API request/response serialization
with comprehensive validation and documentation.
"""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, validator

from ...domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    MediaType,
    MovementPattern,
    MuscleGroup,
)


# Base schemas
class ExerciseMediaSchema(BaseModel):
    """Exercise media response schema."""

    id: UUID
    exercise_id: UUID
    media_type: MediaType
    url: str
    thumbnail_url: str | None = None
    title: str | None = None
    description: str | None = None
    file_size_bytes: int | None = None
    duration_seconds: int | None = None
    width_pixels: int | None = None
    height_pixels: int | None = None
    mime_type: str | None = None
    sort_order: int
    is_primary: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class ExerciseBaseSchema(BaseModel):
    """Base exercise schema with common fields."""

    name: str = Field(..., min_length=1, max_length=255, description="Exercise name")
    description: str | None = Field(None, description="Exercise description")
    primary_muscle_group: MuscleGroup = Field(
        ..., description="Primary muscle group targeted"
    )
    secondary_muscle_groups: list[MuscleGroup] | None = Field(
        None, description="Secondary muscle groups targeted"
    )
    movement_pattern: MovementPattern = Field(
        ..., description="Movement pattern classification"
    )
    equipment_required: list[Equipment] | None = Field(
        None, description="Equipment required for exercise"
    )
    difficulty_level: DifficultyLevel = Field(
        ..., description="Exercise difficulty level"
    )
    video_url: str | None = Field(
        None, max_length=500, description="Exercise video URL"
    )
    thumbnail_url: str | None = Field(
        None, max_length=500, description="Exercise thumbnail URL"
    )
    form_cues: list[str] | None = Field(None, description="Form and technique cues")
    setup_instructions: str | None = Field(
        None, description="Exercise setup instructions"
    )
    execution_steps: list[str] | None = Field(
        None, description="Step-by-step execution"
    )
    common_mistakes: list[str] | None = Field(
        None, description="Common mistakes to avoid"
    )
    safety_notes: str | None = Field(None, description="Safety considerations")

    @validator("secondary_muscle_groups")
    def validate_secondary_muscle_groups(cls, v, values):
        """Ensure secondary muscle groups don't include primary."""
        if v and "primary_muscle_group" in values:
            primary = values["primary_muscle_group"]
            if primary in v:
                raise ValueError(
                    "Secondary muscle groups cannot include the primary muscle group"
                )
        return v

    @validator("equipment_required")
    def validate_equipment_required(cls, v):
        """Ensure no duplicate equipment."""
        if v and len(v) != len(set(v)):
            raise ValueError("Equipment list cannot contain duplicates")
        return v


# Request schemas
class ExerciseCreateRequest(ExerciseBaseSchema):
    """Exercise creation request schema."""

    version_notes: str | None = Field(None, description="Notes for this version")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "name": "Barbell Bench Press",
                "description": "Classic compound chest exercise using a barbell",
                "primary_muscle_group": "chest",
                "secondary_muscle_groups": ["triceps", "shoulders"],
                "movement_pattern": "push",
                "equipment_required": ["barbell"],
                "difficulty_level": "intermediate",
                "form_cues": [
                    "Keep shoulder blades retracted",
                    "Maintain arch in lower back",
                    "Control the descent",
                ],
                "setup_instructions": "Lie on bench with eyes under the bar",
                "execution_steps": [
                    "Unrack the bar with straight arms",
                    "Lower bar to chest with control",
                    "Press bar back to starting position",
                ],
                "common_mistakes": [
                    "Bouncing bar off chest",
                    "Flaring elbows too wide",
                    "Not using full range of motion",
                ],
                "safety_notes": "Always use a spotter for heavy weights",
            }
        }


class ExerciseUpdateRequest(BaseModel):
    """Exercise update request schema."""

    name: str | None = Field(None, min_length=1, max_length=255)
    description: str | None = None
    primary_muscle_group: MuscleGroup | None = None
    secondary_muscle_groups: list[MuscleGroup] | None = None
    movement_pattern: MovementPattern | None = None
    equipment_required: list[Equipment] | None = None
    difficulty_level: DifficultyLevel | None = None
    video_url: str | None = Field(None, max_length=500)
    thumbnail_url: str | None = Field(None, max_length=500)
    form_cues: list[str] | None = None
    setup_instructions: str | None = None
    execution_steps: list[str] | None = None
    common_mistakes: list[str] | None = None
    safety_notes: str | None = None
    version_notes: str | None = None
    change_reason: ChangeReason = ChangeReason.CONTENT_UPDATE

    @validator("secondary_muscle_groups")
    def validate_secondary_muscle_groups(cls, v, values):
        """Ensure secondary muscle groups don't include primary."""
        if v and "primary_muscle_group" in values and values["primary_muscle_group"]:
            primary = values["primary_muscle_group"]
            if primary in v:
                raise ValueError(
                    "Secondary muscle groups cannot include the primary muscle group"
                )
        return v


# Response schemas
class ExerciseSchema(BaseModel):
    """Exercise response schema."""

    id: UUID
    exercise_uuid: UUID
    version: int
    is_current_version: bool
    parent_version_id: UUID | None = None
    name: str
    description: str | None = None
    primary_muscle_group: MuscleGroup
    secondary_muscle_groups: list[MuscleGroup] | None = None
    movement_pattern: MovementPattern
    equipment_required: list[Equipment] | None = None
    difficulty_level: DifficultyLevel
    video_url: str | None = None
    thumbnail_url: str | None = None
    form_cues: list[str] | None = None
    setup_instructions: str | None = None
    execution_steps: list[str] | None = None
    common_mistakes: list[str] | None = None
    safety_notes: str | None = None
    is_active: bool
    is_approved: bool
    approval_status: ApprovalStatus
    created_by: UUID | None = None
    updated_by: UUID | None = None
    approved_by: UUID | None = None
    created_at: datetime
    updated_at: datetime
    deleted_at: datetime | None = None
    approved_at: datetime | None = None
    version_notes: str | None = None
    change_reason: ChangeReason
    media: list[ExerciseMediaSchema] | None = None

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class ExerciseListResponse(BaseModel):
    """Exercise list response schema."""

    exercises: list[ExerciseSchema]
    total_count: int
    limit: int
    offset: int
    has_more: bool

    @validator("has_more", always=True)
    def calculate_has_more(cls, v, values):
        """Calculate if there are more results."""
        if "total_count" in values and "limit" in values and "offset" in values:
            return values["offset"] + values["limit"] < values["total_count"]
        return False


class ExerciseVersionsResponse(BaseModel):
    """Exercise versions response schema."""

    exercise_uuid: UUID
    versions: list[ExerciseSchema]
    current_version: int
    total_versions: int


# Media request/response schemas
class ExerciseMediaCreateRequest(BaseModel):
    """Exercise media creation request schema."""

    media_type: MediaType = Field(..., description="Type of media")
    url: str = Field(..., max_length=500, description="Media URL")
    title: str | None = Field(None, max_length=255, description="Media title")
    description: str | None = Field(None, description="Media description")
    thumbnail_url: str | None = Field(None, max_length=500, description="Thumbnail URL")
    file_size_bytes: int | None = Field(None, ge=0, description="File size in bytes")
    duration_seconds: int | None = Field(
        None, ge=0, description="Duration for videos/audio"
    )
    width_pixels: int | None = Field(None, gt=0, description="Width in pixels")
    height_pixels: int | None = Field(None, gt=0, description="Height in pixels")
    mime_type: str | None = Field(None, max_length=100, description="MIME type")
    is_primary: bool = Field(False, description="Whether this is primary media")
    sort_order: int | None = Field(None, ge=0, description="Sort order")


class ExerciseMediaUpdateRequest(BaseModel):
    """Exercise media update request schema."""

    title: str | None = Field(None, max_length=255)
    description: str | None = None
    thumbnail_url: str | None = Field(None, max_length=500)
    is_primary: bool | None = None
    sort_order: int | None = Field(None, ge=0)


class ExerciseMediaReorderRequest(BaseModel):
    """Exercise media reorder request schema."""

    media_order: list[UUID] = Field(
        ..., description="List of media IDs in desired order"
    )


# Approval schemas
class ExerciseApprovalRequest(BaseModel):
    """Exercise approval request schema."""

    notes: str | None = Field(None, description="Approval notes")


class ExerciseRejectionRequest(BaseModel):
    """Exercise rejection request schema."""

    notes: str | None = Field(None, description="Rejection notes")


# Statistics schema
class ExerciseStatisticsResponse(BaseModel):
    """Exercise statistics response schema."""

    exercise_id: UUID
    exercise_uuid: UUID
    current_version: int
    total_versions: int
    is_approved: bool
    approval_status: str
    is_active: bool
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    media_count: int
    media_by_type: dict
    has_primary_video: bool
    has_primary_image: bool
    audit_entries: int
    muscle_groups: dict
    equipment_required: list[str]
    difficulty_level: str
    movement_pattern: str


# Bulk operation schemas
class BulkApprovalRequest(BaseModel):
    """Bulk approval request schema."""

    exercise_ids: list[UUID] = Field(..., min_items=1, max_items=50)
    notes: str | None = None


class BulkDeleteRequest(BaseModel):
    """Bulk delete request schema."""

    exercise_ids: list[UUID] = Field(..., min_items=1, max_items=50)


class BulkOperationResponse(BaseModel):
    """Bulk operation response schema."""

    successful_ids: list[UUID]
    failed_ids: list[UUID]
    total_requested: int
    total_successful: int
    total_failed: int
