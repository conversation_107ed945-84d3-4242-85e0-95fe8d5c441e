"""Pydantic schemas for API requests and responses.

Contains all input/output schemas for the REST API endpoints.
"""

from .auth_schema import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
)
from .exercise_schemas import (
    BulkApprovalRequest,
    BulkDeleteRequest,
    BulkOperationResponse,
    ExerciseApprovalRequest,
    ExerciseCreateRequest,
    ExerciseListResponse,
    ExerciseMediaCreateRequest,
    ExerciseMediaReorderRequest,
    ExerciseMediaSchema,
    ExerciseMediaUpdateRequest,
    ExerciseRejectionRequest,
    ExerciseSchema,
    ExerciseStatisticsResponse,
    ExerciseUpdateRequest,
    ExerciseVersionsResponse,
)
from .user_schema import UserCreateRequest, UserResponse, UserUpdateRequest

__all__ = [
    "BulkApprovalRequest",
    "BulkDeleteRequest",
    "BulkOperationResponse",
    "ExerciseApprovalRequest",
    "ExerciseCreateRequest",
    "ExerciseListResponse",
    "ExerciseMediaCreateRequest",
    "ExerciseMediaReorderRequest",
    "ExerciseMediaSchema",
    "ExerciseMediaUpdateRequest",
    "ExerciseRejectionRequest",
    "ExerciseSchema",
    "ExerciseStatisticsResponse",
    "ExerciseUpdateRequest",
    "ExerciseVersionsResponse",
    "LoginRequest",
    "LoginResponse",
    "RefreshTokenRequest",
    "RefreshTokenResponse",
    "UserCreateRequest",
    "UserResponse",
    "UserUpdateRequest",
]
