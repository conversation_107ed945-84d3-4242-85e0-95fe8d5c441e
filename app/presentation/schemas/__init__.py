"""
Pydantic schemas for API requests and responses.

Contains all input/output schemas for the REST API endpoints.
"""

from .auth_schema import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
)
from .exercise_schemas import (
    BulkApprovalRequest,
    BulkDeleteRequest,
    BulkOperationResponse,
    ExerciseApprovalRequest,
    ExerciseCreateRequest,
    ExerciseListResponse,
    ExerciseMediaCreateRequest,
    ExerciseMediaReorderRequest,
    ExerciseMediaSchema,
    ExerciseMediaUpdateRequest,
    ExerciseRejectionRequest,
    ExerciseSchema,
    ExerciseStatisticsResponse,
    ExerciseUpdateRequest,
    ExerciseVersionsResponse,
)
from .user_schema import UserCreateRequest, UserResponse, UserUpdateRequest

__all__ = [
    "UserResponse",
    "UserCreateRequest",
    "UserUpdateRequest",
    "LoginRequest",
    "LoginResponse",
    "RefreshTokenRequest",
    "RefreshTokenResponse",
    "ExerciseSchema",
    "ExerciseCreateRequest",
    "ExerciseUpdateRequest",
    "ExerciseListResponse",
    "ExerciseVersionsResponse",
    "ExerciseMediaSchema",
    "ExerciseMediaCreateRequest",
    "ExerciseMediaUpdateRequest",
    "ExerciseMediaReorderRequest",
    "ExerciseApprovalRequest",
    "ExerciseRejectionRequest",
    "ExerciseStatisticsResponse",
    "BulkApprovalRequest",
    "BulkDeleteRequest",
    "BulkOperationResponse",
]
