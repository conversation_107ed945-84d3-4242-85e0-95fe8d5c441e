"""
User-related Pydantic schemas.

Request and response schemas for user management endpoints.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator


class UserResponse(BaseModel):
    """User response schema."""
    
    id: UUID
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    is_verified: bool
    training_experience_years: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class UserCreateRequest(BaseModel):
    """User creation request schema."""
    
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(
        ...,
        min_length=8,
        description="User's password (minimum 8 characters)"
    )
    first_name: Optional[str] = Field(
        None,
        max_length=100,
        description="User's first name"
    )
    last_name: Optional[str] = Field(
        None,
        max_length=100,
        description="User's last name"
    )
    training_experience_years: Optional[int] = Field(
        None,
        ge=0,
        le=50,
        description="Years of training experience"
    )
    
    @validator("first_name", "last_name")
    def validate_names(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean name fields."""
        if v is not None:
            v = v.strip()
            if not v:
                return None
        return v
    
    @validator("password")
    def validate_password(cls, v: str) -> str:
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "John",
                "last_name": "Doe",
                "training_experience_years": 2
            }
        }


class UserUpdateRequest(BaseModel):
    """User update request schema."""
    
    first_name: Optional[str] = Field(
        None,
        max_length=100,
        description="User's first name"
    )
    last_name: Optional[str] = Field(
        None,
        max_length=100,
        description="User's last name"
    )
    training_experience_years: Optional[int] = Field(
        None,
        ge=0,
        le=50,
        description="Years of training experience"
    )
    
    @validator("first_name", "last_name")
    def validate_names(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean name fields."""
        if v is not None:
            v = v.strip()
            if not v:
                return None
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "first_name": "John",
                "last_name": "Doe",
                "training_experience_years": 3
            }
        }
