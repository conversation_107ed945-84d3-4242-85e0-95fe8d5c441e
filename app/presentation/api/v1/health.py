"""
Health check endpoints.

Provides health status information for the API and its dependencies
including database connectivity and system status.
"""

from datetime import datetime
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from ....config import settings
from ....infrastructure.database import get_database_session

router = APIRouter()


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.

    Returns:
        Dict containing basic health status information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": settings.app_name,
        "version": settings.app_version,
        "environment": "development" if settings.debug else "production",
    }


@router.get("/health/db")
async def database_health_check(
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """
    Database health check endpoint.

    Args:
        db: Database session dependency

    Returns:
        Dict containing database health status

    Raises:
        HTTPException: If database is not accessible
    """
    try:
        # Execute a simple query to test database connectivity
        result = await db.execute(text("SELECT 1 as health_check"))
        row = result.fetchone()

        if row and row.health_check == 1:
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "database": "connected",
                "message": "Database is accessible",
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database query returned unexpected result",
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection failed: {str(e)}",
        )


@router.get("/health/detailed")
async def detailed_health_check(
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """
    Detailed health check with all system components.

    Args:
        db: Database session dependency

    Returns:
        Dict containing detailed health status of all components
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": "development" if settings.debug else "production",
        },
        "components": {},
    }

    # Check database
    try:
        result = await db.execute(text("SELECT version() as db_version"))
        row = result.fetchone()
        health_status["components"]["database"] = {
            "status": "healthy",
            "version": row.db_version if row else "unknown",
            "message": "Database is accessible",
        }
    except Exception as e:
        health_status["status"] = "degraded"
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "message": "Database connection failed",
        }

    # Add more component checks here as needed
    # (Redis, external APIs, etc.)

    return health_status
