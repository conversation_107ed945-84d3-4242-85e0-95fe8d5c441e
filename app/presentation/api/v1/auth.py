"""
Authentication API endpoints.

Handles user registration, login, token refresh, and profile management.
"""

from typing import Annotated
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status

from ....application.dto.user_dto import CreateUserDTO, UpdateUserDTO
from ....application.use_cases import (
    AuthenticateUserUseCase,
    GetUserProfileUseCase,
    RegisterUserUseCase,
    UpdateUserProfileUseCase,
)
from ....application.use_cases.authenticate_user import AuthenticateUserRequest
from ....domain.exceptions.auth_exceptions import (
    InactiveUserError,
    InvalidCredentialsError,
    InvalidEmailError,
    UserAlreadyExistsError,
    UserNotFoundError,
    WeakPasswordError,
)
from ...schemas.auth_schema import (
    LoginRequest,
    LoginResponse,
)
from ...schemas.user_schema import UserCreateRequest, UserResponse, UserUpdateRequest
from ..dependencies import (
    get_authenticate_user_use_case,
    get_current_user,
    get_register_user_use_case,
    get_update_user_profile_use_case,
    get_user_profile_use_case,
)

router = APIRouter()


@router.post(
    "/register",
    response_model=LoginResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    description="Create a new user account with email and password",
)
async def register(
    request: UserCreateRequest,
    use_case: Annotated[RegisterUserUseCase, Depends(get_register_user_use_case)],
) -> LoginResponse:
    """
    Register a new user.

    Args:
        request: User registration data
        use_case: Register user use case

    Returns:
        User data with authentication tokens

    Raises:
        HTTPException: If registration fails
    """
    try:
        # Convert request to DTO
        create_dto = CreateUserDTO(
            email=request.email,
            password=request.password,
            first_name=request.first_name,
            last_name=request.last_name,
            training_experience_years=request.training_experience_years,
        )

        # Execute use case
        response = await use_case.execute(create_dto)

        # Return API response
        return LoginResponse(
            user=UserResponse.model_validate(response.user),
            access_token=response.access_token,
            refresh_token=response.refresh_token,
            token_type=response.token_type,
        )

    except UserAlreadyExistsError as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except (InvalidEmailError, WeakPasswordError) as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="User login",
    description="Authenticate user with email and password",
)
async def login(
    request: LoginRequest,
    use_case: Annotated[
        AuthenticateUserUseCase, Depends(get_authenticate_user_use_case)
    ],
) -> LoginResponse:
    """
    Authenticate user login.

    Args:
        request: Login credentials
        use_case: Authenticate user use case

    Returns:
        User data with authentication tokens

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Convert request to use case request
        auth_request = AuthenticateUserRequest(
            email=request.email, password=request.password
        )

        # Execute use case
        response = await use_case.execute(auth_request)

        # Return API response
        return LoginResponse(
            user=UserResponse.model_validate(response.user),
            access_token=response.access_token,
            refresh_token=response.refresh_token,
            token_type=response.token_type,
        )

    except (InvalidCredentialsError, InactiveUserError) as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user profile",
    description="Get the profile of the currently authenticated user",
)
async def get_profile(
    current_user_id: Annotated[UUID, Depends(get_current_user)],
    use_case: Annotated[GetUserProfileUseCase, Depends(get_user_profile_use_case)],
) -> UserResponse:
    """
    Get current user profile.

    Args:
        current_user_id: Current authenticated user ID
        use_case: Get user profile use case

    Returns:
        Current user's profile data

    Raises:
        HTTPException: If user not found
    """
    try:
        user_dto = await use_case.execute(current_user_id)
        return UserResponse.model_validate(user_dto)

    except UserNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )


@router.put(
    "/me",
    response_model=UserResponse,
    summary="Update current user profile",
    description="Update the profile of the currently authenticated user",
)
async def update_profile(
    request: UserUpdateRequest,
    current_user_id: Annotated[UUID, Depends(get_current_user)],
    use_case: Annotated[
        UpdateUserProfileUseCase, Depends(get_update_user_profile_use_case)
    ],
) -> UserResponse:
    """
    Update current user profile.

    Args:
        request: Profile update data
        current_user_id: Current authenticated user ID
        use_case: Update user profile use case

    Returns:
        Updated user profile data

    Raises:
        HTTPException: If user not found
    """
    try:
        # Convert request to DTO
        update_dto = UpdateUserDTO(
            first_name=request.first_name,
            last_name=request.last_name,
            training_experience_years=request.training_experience_years,
        )

        # Execute use case
        user_dto = await use_case.execute(current_user_id, update_dto)
        return UserResponse.model_validate(user_dto)

    except UserNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
