"""
Exercise API routes.

FastAPI routes for exercise management including CRUD operations,
versioning, approval workflows, and media management.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status

from .....application.exceptions import (
    BusinessRuleViolationError,
    NotFoundError,
    ValidationError,
)
from .....application.services.exercise_service import ExerciseService
from .....domain.entities.exercise import (
    ExerciseSearchFilters,
    MediaType,
)
from .....infrastructure.database.repositories.exercise_repository_impl import (
    ExerciseRepositoryImpl,
)
from ...dependencies import get_current_user, get_database_session
from ....schemas.exercise_schemas import (
    BulkApprovalRequest,
    BulkDeleteRequest,
    BulkOperationResponse,
    ExerciseApprovalRequest,
    ExerciseCreateRequest,
    ExerciseListResponse,
    ExerciseMediaCreateRequest,
    ExerciseMediaReorderRequest,
    ExerciseMediaSchema,
    ExerciseMediaUpdateRequest,
    ExerciseRejectionRequest,
    ExerciseSchema,
    ExerciseStatisticsResponse,
    ExerciseUpdateRequest,
    ExerciseVersionsResponse,
)

router = APIRouter(prefix="/exercises", tags=["exercises"])


async def get_exercise_service(session=Depends(get_database_session)) -> ExerciseService:
    """Get exercise service dependency."""
    repository = ExerciseRepositoryImpl(session)
    return ExerciseService(repository)


@router.post(
    "/",
    response_model=ExerciseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new exercise",
    description="Create a new exercise with comprehensive metadata and validation."
)
async def create_exercise(
    request: ExerciseCreateRequest,
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Create a new exercise."""
    try:
        # Convert request to domain entity request
        from .....domain.entities.exercise import ExerciseCreateRequest as DomainRequest
        
        domain_request = DomainRequest(
            name=request.name,
            description=request.description,
            primary_muscle_group=request.primary_muscle_group,
            secondary_muscle_groups=request.secondary_muscle_groups,
            movement_pattern=request.movement_pattern,
            equipment_required=request.equipment_required,
            difficulty_level=request.difficulty_level,
            video_url=request.video_url,
            thumbnail_url=request.thumbnail_url,
            form_cues=request.form_cues,
            setup_instructions=request.setup_instructions,
            execution_steps=request.execution_steps,
            common_mistakes=request.common_mistakes,
            safety_notes=request.safety_notes,
            version_notes=request.version_notes,
        )
        
        exercise = await exercise_service.create_exercise(domain_request, current_user.id)
        return ExerciseSchema.from_orm(exercise)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Validation failed", "message": str(e)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={"error": "Business rule violation", "message": str(e)}
        )


@router.get(
    "/",
    response_model=ExerciseListResponse,
    summary="Search exercises",
    description="Search and filter exercises with pagination support."
)
async def search_exercises(
    name: Optional[str] = Query(None, description="Search by exercise name"),
    primary_muscle_group: Optional[str] = Query(None, description="Filter by primary muscle group"),
    movement_pattern: Optional[str] = Query(None, description="Filter by movement pattern"),
    equipment: Optional[List[str]] = Query(None, description="Filter by equipment"),
    difficulty_level: Optional[str] = Query(None, description="Filter by difficulty"),
    is_active: Optional[bool] = Query(True, description="Filter by active status"),
    is_approved: Optional[bool] = Query(None, description="Filter by approval status"),
    current_version_only: bool = Query(True, description="Only return current versions"),
    include_deleted: bool = Query(False, description="Include soft-deleted exercises"),
    limit: int = Query(50, ge=1, le=100, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Results to skip"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseListResponse:
    """Search exercises with filters."""
    try:
        # Convert query parameters to domain filters
        from .....domain.entities.exercise import (
            DifficultyLevel,
            Equipment,
            MovementPattern,
            MuscleGroup,
        )
        
        filters = ExerciseSearchFilters(
            name=name,
            primary_muscle_group=MuscleGroup(primary_muscle_group) if primary_muscle_group else None,
            movement_pattern=MovementPattern(movement_pattern) if movement_pattern else None,
            equipment_required=[Equipment(eq) for eq in equipment] if equipment else None,
            difficulty_level=DifficultyLevel(difficulty_level) if difficulty_level else None,
            is_active=is_active,
            is_approved=is_approved,
            current_version_only=current_version_only,
            include_deleted=include_deleted,
        )
        
        exercises, total_count = await exercise_service.search_exercises(filters, limit, offset)
        
        return ExerciseListResponse(
            exercises=[ExerciseSchema.from_orm(ex) for ex in exercises],
            total_count=total_count,
            limit=limit,
            offset=offset,
            has_more=offset + limit < total_count
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Invalid parameter", "message": str(e)}
        )


@router.get(
    "/{exercise_id}",
    response_model=ExerciseSchema,
    summary="Get exercise by ID",
    description="Retrieve a specific exercise by its ID."
)
async def get_exercise(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Get exercise by ID."""
    try:
        exercise = await exercise_service.get_exercise(exercise_id)
        return ExerciseSchema.from_orm(exercise)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )


@router.put(
    "/{exercise_id}",
    response_model=ExerciseSchema,
    summary="Update exercise",
    description="Update an exercise, optionally creating a new version."
)
async def update_exercise(
    exercise_id: UUID,
    request: ExerciseUpdateRequest,
    create_new_version: bool = Query(True, description="Create new version"),
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Update an exercise."""
    try:
        # Convert request to domain entity request
        from .....domain.entities.exercise import ExerciseUpdateRequest as DomainRequest
        
        domain_request = DomainRequest(
            name=request.name,
            description=request.description,
            primary_muscle_group=request.primary_muscle_group,
            secondary_muscle_groups=request.secondary_muscle_groups,
            movement_pattern=request.movement_pattern,
            equipment_required=request.equipment_required,
            difficulty_level=request.difficulty_level,
            video_url=request.video_url,
            thumbnail_url=request.thumbnail_url,
            form_cues=request.form_cues,
            setup_instructions=request.setup_instructions,
            execution_steps=request.execution_steps,
            common_mistakes=request.common_mistakes,
            safety_notes=request.safety_notes,
            version_notes=request.version_notes,
            change_reason=request.change_reason,
        )
        
        exercise = await exercise_service.update_exercise(
            exercise_id, domain_request, current_user.id, create_new_version
        )
        return ExerciseSchema.from_orm(exercise)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Validation failed", "message": str(e)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={"error": "Business rule violation", "message": str(e)}
        )


@router.delete(
    "/{exercise_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete exercise",
    description="Soft delete an exercise."
)
async def delete_exercise(
    exercise_id: UUID,
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
):
    """Soft delete an exercise."""
    try:
        await exercise_service.delete_exercise(exercise_id, current_user.id)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )


@router.post(
    "/{exercise_id}/restore",
    response_model=ExerciseSchema,
    summary="Restore exercise",
    description="Restore a soft-deleted exercise."
)
async def restore_exercise(
    exercise_id: UUID,
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Restore a soft-deleted exercise."""
    try:
        await exercise_service.restore_exercise(exercise_id, current_user.id)
        exercise = await exercise_service.get_exercise(exercise_id)
        return ExerciseSchema.from_orm(exercise)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Cannot restore exercise", "message": str(e)}
        )


@router.post(
    "/{exercise_id}/approve",
    response_model=ExerciseSchema,
    summary="Approve exercise",
    description="Approve an exercise for publication."
)
async def approve_exercise(
    exercise_id: UUID,
    request: ExerciseApprovalRequest,
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Approve an exercise."""
    try:
        exercise = await exercise_service.approve_exercise(
            exercise_id, current_user.id, request.notes
        )
        return ExerciseSchema.from_orm(exercise)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Cannot approve exercise", "message": str(e)}
        )


@router.post(
    "/{exercise_id}/reject",
    response_model=ExerciseSchema,
    summary="Reject exercise",
    description="Reject an exercise submission."
)
async def reject_exercise(
    exercise_id: UUID,
    request: ExerciseRejectionRequest,
    current_user=Depends(get_current_user),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseSchema:
    """Reject an exercise."""
    try:
        exercise = await exercise_service.reject_exercise(
            exercise_id, current_user.id, request.notes
        )
        return ExerciseSchema.from_orm(exercise)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Cannot reject exercise", "message": str(e)}
        )


# Media management endpoints
@router.post(
    "/{exercise_id}/media",
    response_model=ExerciseMediaSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Add exercise media",
    description="Add media (video, image, etc.) to an exercise."
)
async def add_exercise_media(
    exercise_id: UUID,
    request: ExerciseMediaCreateRequest,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseMediaSchema:
    """Add media to an exercise."""
    try:
        media = await exercise_service.add_exercise_media(
            exercise_id=exercise_id,
            media_type=request.media_type,
            url=request.url,
            title=request.title,
            description=request.description,
            thumbnail_url=request.thumbnail_url,
            file_size_bytes=request.file_size_bytes,
            duration_seconds=request.duration_seconds,
            width_pixels=request.width_pixels,
            height_pixels=request.height_pixels,
            mime_type=request.mime_type,
            is_primary=request.is_primary,
            sort_order=request.sort_order
        )
        return ExerciseMediaSchema.from_orm(media)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"error": "Validation failed", "message": str(e)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={"error": "Business rule violation", "message": str(e)}
        )


@router.get(
    "/{exercise_id}/media",
    response_model=List[ExerciseMediaSchema],
    summary="Get exercise media",
    description="Get all media for an exercise."
)
async def get_exercise_media(
    exercise_id: UUID,
    media_type: Optional[MediaType] = Query(None, description="Filter by media type"),
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> List[ExerciseMediaSchema]:
    """Get media for an exercise."""
    try:
        if media_type:
            media = await exercise_service.get_media_by_type(exercise_id, media_type)
        else:
            media = await exercise_service.get_exercise_media(exercise_id)

        return [ExerciseMediaSchema.from_orm(m) for m in media]

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )


@router.put(
    "/media/{media_id}",
    response_model=ExerciseMediaSchema,
    summary="Update exercise media",
    description="Update exercise media metadata."
)
async def update_exercise_media(
    media_id: UUID,
    request: ExerciseMediaUpdateRequest,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseMediaSchema:
    """Update exercise media."""
    try:
        media = await exercise_service.update_exercise_media(
            media_id=media_id,
            title=request.title,
            description=request.description,
            thumbnail_url=request.thumbnail_url,
            is_primary=request.is_primary,
            sort_order=request.sort_order
        )
        return ExerciseMediaSchema.from_orm(media)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Media not found", "media_id": str(media_id)}
        )
    except BusinessRuleViolationError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={"error": "Business rule violation", "message": str(e)}
        )


@router.delete(
    "/media/{media_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete exercise media",
    description="Delete exercise media."
)
async def delete_exercise_media(
    media_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service)
):
    """Delete exercise media."""
    try:
        await exercise_service.delete_exercise_media(media_id)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Media not found", "media_id": str(media_id)}
        )


@router.get(
    "/{exercise_id}/statistics",
    response_model=ExerciseStatisticsResponse,
    summary="Get exercise statistics",
    description="Get comprehensive statistics for an exercise."
)
async def get_exercise_statistics(
    exercise_id: UUID,
    exercise_service: ExerciseService = Depends(get_exercise_service)
) -> ExerciseStatisticsResponse:
    """Get exercise statistics."""
    try:
        stats = await exercise_service.get_exercise_statistics(exercise_id)
        return ExerciseStatisticsResponse(**stats)

    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"error": "Exercise not found", "exercise_id": str(exercise_id)}
        )
