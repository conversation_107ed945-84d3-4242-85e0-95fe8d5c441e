"""
FastAPI dependencies.

Common dependencies used across API endpoints including authentication,
database sessions, and service injection.
"""

from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from ...application.use_cases import (
    AuthenticateUserUseCase,
    GetUserProfileUseCase,
    RegisterUserUseCase,
    UpdateUserProfileUseCase,
)
from ...domain.exceptions.auth_exceptions import (
    InvalidTokenError,
    TokenExpiredError,
    UserNotFoundError,
)
from ...domain.services.auth_service import AuthService
from ...infrastructure.auth.jwt_handler import jwt_handler
from ...infrastructure.auth.password_handler import password_handler
from ...infrastructure.database import get_database_session
from ...infrastructure.database.repositories.user_repository_impl import (
    UserRepositoryImpl,
)
from ...infrastructure.database.repositories.exercise_repository_impl import (
    ExerciseRepositoryImpl,
)
from ...infrastructure.database.repositories.workout_repository_impl import (
    WorkoutRepositoryImpl,
)
from ...application.services.exercise_service import ExerciseService
from ...application.services.workout_service import WorkoutService

# Security scheme for JWT authentication
security = HTTPBearer()


async def get_user_repository(
    db: Annotated[AsyncSession, Depends(get_database_session)],
) -> UserRepositoryImpl:
    """Get user repository dependency."""
    return UserRepositoryImpl(db)


async def get_auth_service(
    user_repo: Annotated[UserRepositoryImpl, Depends(get_user_repository)],
) -> AuthService:
    """Get authentication service dependency."""
    return AuthService(user_repo, password_handler)


async def get_register_user_use_case(
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> RegisterUserUseCase:
    """Get register user use case dependency."""
    return RegisterUserUseCase(auth_service, jwt_handler)


async def get_authenticate_user_use_case(
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> AuthenticateUserUseCase:
    """Get authenticate user use case dependency."""
    return AuthenticateUserUseCase(auth_service, jwt_handler)


async def get_user_profile_use_case(
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> GetUserProfileUseCase:
    """Get user profile use case dependency."""
    return GetUserProfileUseCase(auth_service)


async def get_update_user_profile_use_case(
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> UpdateUserProfileUseCase:
    """Get update user profile use case dependency."""
    return UpdateUserProfileUseCase(auth_service)


async def get_current_user_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
) -> UUID:
    """
    Get current authenticated user ID from JWT token.

    Args:
        credentials: HTTP authorization credentials

    Returns:
        Current user's UUID

    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        token = credentials.credentials

        # Verify token type is access token
        token_type = jwt_handler.get_token_type(token)
        if token_type != "access":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract user ID from token
        user_id = jwt_handler.get_user_id_from_token(token)
        return user_id

    except TokenExpiredError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except InvalidTokenError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    user_id: Annotated[UUID, Depends(get_current_user_id)],
    get_profile_use_case: Annotated[
        GetUserProfileUseCase, Depends(get_user_profile_use_case)
    ],
) -> UUID:
    """
    Get current authenticated user and verify they exist.

    Args:
        user_id: Current user's ID from token
        get_profile_use_case: Use case for getting user profile

    Returns:
        Current user's UUID

    Raises:
        HTTPException: If user not found or inactive
    """
    try:
        # Verify user exists and is active
        await get_profile_use_case.execute(user_id)
        return user_id

    except UserNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )


# Repository dependencies
async def get_exercise_repository(
    db: Annotated[AsyncSession, Depends(get_database_session)],
) -> ExerciseRepositoryImpl:
    """Get exercise repository dependency."""
    return ExerciseRepositoryImpl(db)


async def get_workout_repository(
    db: Annotated[AsyncSession, Depends(get_database_session)],
) -> WorkoutRepositoryImpl:
    """Get workout repository dependency."""
    return WorkoutRepositoryImpl(db)


# Service dependencies
async def get_exercise_service(
    exercise_repo: Annotated[ExerciseRepositoryImpl, Depends(get_exercise_repository)],
) -> ExerciseService:
    """Get exercise service dependency."""
    return ExerciseService(exercise_repo)


async def get_workout_service(
    workout_repo: Annotated[WorkoutRepositoryImpl, Depends(get_workout_repository)],
) -> WorkoutService:
    """Get workout service dependency."""
    return WorkoutService(workout_repo)
