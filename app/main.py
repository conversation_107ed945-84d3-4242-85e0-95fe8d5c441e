"""FastAPI application entry point.

Creates and configures the FastAPI application with all necessary
middleware, routes, and startup/shutdown event handlers.
"""

from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from .config import settings
from .infrastructure.database import db_manager
from .presentation.api.v1 import auth, health
from .presentation.api.v1.routes import exercises


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    print("🚀 Starting RP Training API...")

    # Initialize database
    db_manager.initialize()
    print("✅ Database initialized")

    yield

    # Shutdown
    print("🛑 Shutting down RP Training API...")

    # Close database connections
    await db_manager.close()
    print("✅ Database connections closed")


def create_application() -> FastAPI:
    """Create and configure FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Evidence-based hypertrophy training platform implementing Renaissance Periodization principles",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )

    # Add security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"],
    )

    # Add CORS middleware (Mobile-first: Allow common development origins)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["*"],
    )

    # Include API routes
    app.include_router(health.router, prefix=settings.api_v1_prefix, tags=["health"])

    app.include_router(
        auth.router, prefix=f"{settings.api_v1_prefix}/auth", tags=["authentication"]
    )

    app.include_router(
        exercises.router, prefix=settings.api_v1_prefix, tags=["exercises"]
    )

    return app


# Create application instance
app = create_application()


@app.get("/")
async def root() -> dict[str, str]:
    """Root endpoint with basic API information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "docs_url": "/docs" if settings.debug else "disabled",
    }
