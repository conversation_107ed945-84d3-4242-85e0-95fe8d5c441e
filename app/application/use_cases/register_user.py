"""
Register user use case.

Handles the business logic for user registration including
validation, domain service coordination, and response formatting.
"""

from typing import Protocol

from ..dto.user_dto import UserDTO, CreateUserDTO
from ...domain.services.auth_service import AuthService


class TokenGenerator(Protocol):
    """Protocol for token generation."""
    
    async def generate_access_token(self, user_id: str) -> str:
        """Generate access token for user."""
        ...
    
    async def generate_refresh_token(self, user_id: str) -> str:
        """Generate refresh token for user."""
        ...


class RegisterUserResponse:
    """Response for user registration."""
    
    def __init__(
        self,
        user: UserDTO,
        access_token: str,
        refresh_token: str,
        token_type: str = "bearer"
    ) -> None:
        self.user = user
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_type = token_type


class RegisterUserUseCase:
    """Use case for registering a new user."""
    
    def __init__(
        self,
        auth_service: AuthService,
        token_generator: TokenGenerator
    ) -> None:
        """Initialize use case with dependencies."""
        self._auth_service = auth_service
        self._token_generator = token_generator
    
    async def execute(self, request: CreateUserDTO) -> RegisterUserResponse:
        """
        Execute user registration use case.
        
        Args:
            request: User creation data
            
        Returns:
            Registration response with user data and tokens
            
        Raises:
            UserAlreadyExistsError: If user already exists
            InvalidEmailError: If email format is invalid
            WeakPasswordError: If password is too weak
        """
        # Register user through domain service
        user = await self._auth_service.register_user(
            email=request.email,
            password=request.password,
            first_name=request.first_name,
            last_name=request.last_name,
            training_experience_years=request.training_experience_years
        )
        
        # Generate authentication tokens
        access_token = await self._token_generator.generate_access_token(str(user.id))
        refresh_token = await self._token_generator.generate_refresh_token(str(user.id))
        
        # Convert to DTO and return response
        user_dto = UserDTO.from_entity(user)
        
        return RegisterUserResponse(
            user=user_dto,
            access_token=access_token,
            refresh_token=refresh_token
        )
