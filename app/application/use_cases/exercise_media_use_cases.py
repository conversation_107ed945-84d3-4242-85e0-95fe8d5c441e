"""Exercise media use cases.

Business logic for managing exercise media including videos, images,
and other multimedia content with validation and organization.
"""

from datetime import datetime
from uuid import UUID, uuid4

from ...domain.entities.exercise import ExerciseMedia, MediaType
from ...domain.repositories.exercise_repository import ExerciseRepository
from ..exceptions import BusinessRuleViolationError, NotFoundError, ValidationError


class ExerciseMediaUseCases:
    """Exercise media use cases implementing business logic.

    Handles media upload, organization, validation, and management
    for exercise multimedia content.
    """

    def __init__(self, exercise_repository: ExerciseRepository):
        """Initialize use cases with repository dependency."""
        self.exercise_repository = exercise_repository

    async def add_media(
        self,
        exercise_id: UUID,
        media_type: MediaType,
        url: str,
        title: str | None = None,
        description: str | None = None,
        thumbnail_url: str | None = None,
        file_size_bytes: int | None = None,
        duration_seconds: int | None = None,
        width_pixels: int | None = None,
        height_pixels: int | None = None,
        mime_type: str | None = None,
        is_primary: bool = False,
        sort_order: int | None = None,
    ) -> ExerciseMedia:
        """Add media to an exercise.

        Args:
            exercise_id: Exercise ID
            media_type: Type of media
            url: Media URL
            title: Optional title
            description: Optional description
            thumbnail_url: Optional thumbnail URL
            file_size_bytes: File size in bytes
            duration_seconds: Duration for videos/audio
            width_pixels: Width for images/videos
            height_pixels: Height for images/videos
            mime_type: MIME type
            is_primary: Whether this is primary media
            sort_order: Sort order (auto-assigned if None)

        Returns:
            Created media entity

        Raises:
            NotFoundError: If exercise not found
            ValidationError: If media data is invalid
            BusinessRuleViolationError: If business rules violated
        """
        # Verify exercise exists
        exercise = await self.exercise_repository.get_by_id(exercise_id)
        if not exercise:
            raise NotFoundError(f"Exercise with ID {exercise_id} not found")

        if exercise.is_deleted():
            raise BusinessRuleViolationError("Cannot add media to deleted exercise")

        # Validate media data
        self._validate_media_data(
            media_type,
            url,
            file_size_bytes,
            duration_seconds,
            width_pixels,
            height_pixels,
            mime_type,
        )

        # Get existing media for sort order and primary validation
        existing_media = await self.exercise_repository.get_media(exercise_id)

        # Handle primary media logic
        if is_primary:
            # Check if there's already a primary media of this type
            existing_primary = [
                m for m in existing_media if m.media_type == media_type and m.is_primary
            ]
            if existing_primary:
                raise BusinessRuleViolationError(
                    f"Exercise already has primary {media_type.value} media"
                )

        # Auto-assign sort order if not provided
        if sort_order is None:
            max_sort_order = max([m.sort_order for m in existing_media], default=-1)
            sort_order = max_sort_order + 1

        # Create media entity
        media = ExerciseMedia(
            id=uuid4(),
            exercise_id=exercise_id,
            media_type=media_type,
            url=url.strip(),
            thumbnail_url=thumbnail_url.strip() if thumbnail_url else None,
            title=title.strip() if title else None,
            description=description.strip() if description else None,
            file_size_bytes=file_size_bytes,
            duration_seconds=duration_seconds,
            width_pixels=width_pixels,
            height_pixels=height_pixels,
            mime_type=mime_type,
            sort_order=sort_order,
            is_primary=is_primary,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        return await self.exercise_repository.add_media(exercise_id, media)

    async def get_exercise_media(self, exercise_id: UUID) -> list[ExerciseMedia]:
        """Get all media for an exercise.

        Args:
            exercise_id: Exercise ID

        Returns:
            List of exercise media

        Raises:
            NotFoundError: If exercise not found
        """
        # Verify exercise exists
        exercise = await self.exercise_repository.get_by_id(exercise_id)
        if not exercise:
            raise NotFoundError(f"Exercise with ID {exercise_id} not found")

        return await self.exercise_repository.get_media(exercise_id)

    async def get_media_by_type(
        self, exercise_id: UUID, media_type: MediaType
    ) -> list[ExerciseMedia]:
        """Get media by type for an exercise.

        Args:
            exercise_id: Exercise ID
            media_type: Media type to filter by

        Returns:
            List of media of specified type
        """
        all_media = await self.get_exercise_media(exercise_id)
        return [m for m in all_media if m.media_type == media_type]

    async def get_primary_media(
        self, exercise_id: UUID, media_type: MediaType | None = None
    ) -> ExerciseMedia | None:
        """Get primary media for an exercise.

        Args:
            exercise_id: Exercise ID
            media_type: Optional media type filter

        Returns:
            Primary media if found
        """
        all_media = await self.get_exercise_media(exercise_id)
        primary_media = [m for m in all_media if m.is_primary]

        if media_type:
            primary_media = [m for m in primary_media if m.media_type == media_type]

        return primary_media[0] if primary_media else None

    async def update_media(
        self,
        media_id: UUID,
        title: str | None = None,
        description: str | None = None,
        thumbnail_url: str | None = None,
        is_primary: bool | None = None,
        sort_order: int | None = None,
    ) -> ExerciseMedia:
        """Update exercise media.

        Args:
            media_id: Media ID to update
            title: New title
            description: New description
            thumbnail_url: New thumbnail URL
            is_primary: Whether this should be primary
            sort_order: New sort order

        Returns:
            Updated media

        Raises:
            NotFoundError: If media not found
            BusinessRuleViolationError: If business rules violated
        """
        # Get current media to validate update
        current_media = await self._get_media_by_id(media_id)

        # Handle primary media logic
        if is_primary is not None and is_primary != current_media.is_primary:
            if is_primary:
                # Check if there's already a primary media of this type
                existing_media = await self.exercise_repository.get_media(
                    current_media.exercise_id
                )
                existing_primary = [
                    m
                    for m in existing_media
                    if m.media_type == current_media.media_type
                    and m.is_primary
                    and m.id != media_id
                ]
                if existing_primary:
                    raise BusinessRuleViolationError(
                        f"Exercise already has primary {current_media.media_type.value} media"
                    )

        # Create updated media
        updated_media = ExerciseMedia(
            id=current_media.id,
            exercise_id=current_media.exercise_id,
            media_type=current_media.media_type,
            url=current_media.url,
            thumbnail_url=(
                thumbnail_url.strip()
                if thumbnail_url is not None
                else current_media.thumbnail_url
            ),
            title=title.strip() if title is not None else current_media.title,
            description=(
                description.strip()
                if description is not None
                else current_media.description
            ),
            file_size_bytes=current_media.file_size_bytes,
            duration_seconds=current_media.duration_seconds,
            width_pixels=current_media.width_pixels,
            height_pixels=current_media.height_pixels,
            mime_type=current_media.mime_type,
            sort_order=sort_order
            if sort_order is not None
            else current_media.sort_order,
            is_primary=is_primary
            if is_primary is not None
            else current_media.is_primary,
            is_active=current_media.is_active,
            created_at=current_media.created_at,
            updated_at=datetime.utcnow(),
        )

        return await self.exercise_repository.update_media(media_id, updated_media)

    async def delete_media(self, media_id: UUID) -> bool:
        """Delete exercise media.

        Args:
            media_id: Media ID to delete

        Returns:
            True if deleted successfully

        Raises:
            NotFoundError: If media not found
        """
        # Verify media exists
        await self._get_media_by_id(media_id)

        return await self.exercise_repository.delete_media(media_id)

    async def reorder_media(
        self, exercise_id: UUID, media_order: list[UUID]
    ) -> list[ExerciseMedia]:
        """Reorder exercise media.

        Args:
            exercise_id: Exercise ID
            media_order: List of media IDs in desired order

        Returns:
            Reordered media list

        Raises:
            NotFoundError: If exercise not found
            ValidationError: If media order is invalid
        """
        # Get existing media
        existing_media = await self.get_exercise_media(exercise_id)
        existing_ids = {m.id for m in existing_media}

        # Validate media order
        if set(media_order) != existing_ids:
            raise ValidationError("Media order must include all existing media IDs")

        # Update sort orders
        updated_media = []
        for i, media_id in enumerate(media_order):
            media = next(m for m in existing_media if m.id == media_id)
            if media.sort_order != i:
                await self.update_media(media_id, sort_order=i)
                media.sort_order = i
            updated_media.append(media)

        return updated_media

    def _validate_media_data(
        self,
        media_type: MediaType,
        url: str,
        file_size_bytes: int | None,
        duration_seconds: int | None,
        width_pixels: int | None,
        height_pixels: int | None,
        mime_type: str | None,
    ) -> None:
        """Validate media data."""
        if not url or not url.strip():
            raise ValidationError("Media URL is required")

        if file_size_bytes is not None and file_size_bytes < 0:
            raise ValidationError("File size cannot be negative")

        if duration_seconds is not None and duration_seconds < 0:
            raise ValidationError("Duration cannot be negative")

        if width_pixels is not None and width_pixels <= 0:
            raise ValidationError("Width must be positive")

        if height_pixels is not None and height_pixels <= 0:
            raise ValidationError("Height must be positive")

        # Type-specific validations
        if (
            media_type in [MediaType.VIDEO, MediaType.AUDIO]
            and duration_seconds is None
        ):
            raise ValidationError(f"{media_type.value} media should have duration")

        if media_type in [MediaType.IMAGE, MediaType.VIDEO, MediaType.GIF]:
            if width_pixels is None or height_pixels is None:
                raise ValidationError(
                    f"{media_type.value} media should have dimensions"
                )

    async def _get_media_by_id(self, media_id: UUID) -> ExerciseMedia:
        """Get media by ID with error handling."""
        # This is a simplified implementation - in a real scenario,
        # you'd want a direct get_media_by_id method in the repository
        all_exercises = await self.exercise_repository.search(
            filters=type(
                "Filters", (), {"current_version_only": True, "include_deleted": False}
            )(),
            limit=1000,
            offset=0,
        )

        for exercise in all_exercises:
            if exercise.media:
                for media in exercise.media:
                    if media.id == media_id:
                        return media

        raise NotFoundError(f"Media with ID {media_id} not found")
