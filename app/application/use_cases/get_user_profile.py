"""
Get user profile use case.

Handles retrieving user profile information with proper
authorization and data formatting.
"""

from uuid import UUID

from ...domain.services.auth_service import AuthService
from ..dto.user_dto import UserDTO


class GetUserProfileUseCase:
    """Use case for getting user profile."""

    def __init__(self, auth_service: AuthService) -> None:
        """Initialize use case with dependencies."""
        self._auth_service = auth_service

    async def execute(self, user_id: UUID) -> UserDTO:
        """
        Execute get user profile use case.

        Args:
            user_id: ID of the user to retrieve

        Returns:
            User profile data

        Raises:
            UserNotFoundError: If user doesn't exist
        """
        # Get user through domain service
        user = await self._auth_service.get_user_by_id(user_id)

        # Convert to DTO and return
        return UserDTO.from_entity(user)
