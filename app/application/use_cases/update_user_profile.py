"""Update user profile use case.

Handles updating user profile information with validation
and proper data formatting.
"""

from uuid import UUID

from ...domain.services.auth_service import AuthService
from ..dto.user_dto import UpdateUserDTO, UserDTO


class UpdateUserProfileUseCase:
    """Use case for updating user profile."""

    def __init__(self, auth_service: AuthService) -> None:
        """Initialize use case with dependencies."""
        self._auth_service = auth_service

    async def execute(self, user_id: UUID, request: UpdateUserDTO) -> UserDTO:
        """Execute update user profile use case.

        Args:
            user_id: ID of the user to update
            request: Updated profile data

        Returns:
            Updated user profile data

        Raises:
            UserNotFoundError: If user doesn't exist
        """
        # Update user through domain service
        user = await self._auth_service.update_user_profile(
            user_id=user_id,
            first_name=request.first_name,
            last_name=request.last_name,
            training_experience_years=request.training_experience_years,
        )

        # Convert to DTO and return
        return UserDTO.from_entity(user)
