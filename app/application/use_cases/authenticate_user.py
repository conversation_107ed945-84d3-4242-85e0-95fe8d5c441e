"""
Authenticate user use case.

Handles user authentication including credential validation,
token generation, and response formatting.
"""

from typing import Protocol
from pydantic import BaseModel, EmailStr

from ..dto.user_dto import UserDTO
from ...domain.services.auth_service import AuthService


class TokenGenerator(Protocol):
    """Protocol for token generation."""
    
    async def generate_access_token(self, user_id: str) -> str:
        """Generate access token for user."""
        ...
    
    async def generate_refresh_token(self, user_id: str) -> str:
        """Generate refresh token for user."""
        ...


class AuthenticateUserRequest(BaseModel):
    """Request for user authentication."""
    
    email: EmailStr
    password: str


class AuthenticateUserResponse:
    """Response for user authentication."""
    
    def __init__(
        self,
        user: UserDTO,
        access_token: str,
        refresh_token: str,
        token_type: str = "bearer"
    ) -> None:
        self.user = user
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_type = token_type


class AuthenticateUserUseCase:
    """Use case for authenticating a user."""
    
    def __init__(
        self,
        auth_service: AuthService,
        token_generator: TokenGenerator
    ) -> None:
        """Initialize use case with dependencies."""
        self._auth_service = auth_service
        self._token_generator = token_generator
    
    async def execute(self, request: AuthenticateUserRequest) -> AuthenticateUserResponse:
        """
        Execute user authentication use case.
        
        Args:
            request: Authentication credentials
            
        Returns:
            Authentication response with user data and tokens
            
        Raises:
            InvalidCredentialsError: If credentials are invalid
            InactiveUserError: If user account is inactive
        """
        # Authenticate user through domain service
        user = await self._auth_service.authenticate_user(
            email=request.email,
            password=request.password
        )
        
        # Generate authentication tokens
        access_token = await self._token_generator.generate_access_token(str(user.id))
        refresh_token = await self._token_generator.generate_refresh_token(str(user.id))
        
        # Convert to DTO and return response
        user_dto = UserDTO.from_entity(user)
        
        return AuthenticateUserResponse(
            user=user_dto,
            access_token=access_token,
            refresh_token=refresh_token
        )
