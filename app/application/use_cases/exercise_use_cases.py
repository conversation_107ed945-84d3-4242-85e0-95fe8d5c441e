"""Exercise use cases.

Business logic for exercise management including creation, updating,
versioning, approval workflows, and search functionality.
"""

from datetime import datetime
from uuid import UUID, uuid4

from ...domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    Exercise,
    ExerciseCreateRequest,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
)
from ...domain.repositories.exercise_repository import ExerciseRepository
from ..exceptions import (
    BusinessRuleViolationError,
    NotFoundError,
    ValidationError,
)


class ExerciseUseCases:
    """Exercise use cases implementing business logic.

    Handles exercise creation, updating, versioning, approval workflows,
    and search operations while enforcing business rules.
    """

    def __init__(self, exercise_repository: ExerciseRepository):
        """Initialize use cases with repository dependency."""
        self.exercise_repository = exercise_repository

    async def create_exercise(
        self, request: ExerciseCreateRequest, created_by: UUID | None = None
    ) -> Exercise:
        """Create a new exercise.

        Args:
            request: Exercise creation request
            created_by: User ID creating the exercise

        Returns:
            Created exercise

        Raises:
            ValidationError: If exercise data is invalid
            BusinessRuleViolationError: If business rules are violated
        """
        # Check if exercise with same name already exists
        if await self.exercise_repository.exists_by_name(request.name):
            raise BusinessRuleViolationError(
                f"Exercise with name '{request.name}' already exists"
            )

        # Create exercise entity
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            parent_version_id=None,
            name=request.name.strip(),
            description=request.description.strip() if request.description else None,
            primary_muscle_group=request.primary_muscle_group,
            secondary_muscle_groups=request.secondary_muscle_groups,
            movement_pattern=request.movement_pattern,
            equipment_required=request.equipment_required,
            difficulty_level=request.difficulty_level,
            video_url=request.video_url,
            thumbnail_url=request.thumbnail_url,
            form_cues=request.form_cues,
            setup_instructions=request.setup_instructions.strip()
            if request.setup_instructions
            else None,
            execution_steps=request.execution_steps,
            common_mistakes=request.common_mistakes,
            safety_notes=request.safety_notes.strip() if request.safety_notes else None,
            is_active=True,
            is_approved=False,
            approval_status=ApprovalStatus.PENDING,
            created_by=created_by,
            updated_by=created_by,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            version_notes=request.version_notes,
            change_reason=ChangeReason.INITIAL_CREATION,
        )

        return await self.exercise_repository.create(exercise)

    async def get_exercise(self, exercise_id: UUID) -> Exercise:
        """Get exercise by ID.

        Args:
            exercise_id: Exercise ID

        Returns:
            Exercise entity

        Raises:
            NotFoundError: If exercise not found
        """
        exercise = await self.exercise_repository.get_by_id(exercise_id)
        if not exercise:
            raise NotFoundError(f"Exercise with ID {exercise_id} not found")

        return exercise

    async def get_exercise_by_uuid(
        self, exercise_uuid: UUID, version: int | None = None
    ) -> Exercise:
        """Get exercise by UUID and optionally version.

        Args:
            exercise_uuid: Exercise UUID
            version: Specific version (None for current)

        Returns:
            Exercise entity

        Raises:
            NotFoundError: If exercise not found
        """
        exercise = await self.exercise_repository.get_by_uuid(
            exercise_uuid, version=version
        )
        if not exercise:
            version_text = f" version {version}" if version else ""
            raise NotFoundError(
                f"Exercise with UUID {exercise_uuid}{version_text} not found"
            )

        return exercise

    async def search_exercises(
        self, filters: ExerciseSearchFilters, limit: int = 50, offset: int = 0
    ) -> tuple[list[Exercise], int]:
        """Search exercises with filters.

        Args:
            filters: Search filters
            limit: Maximum results
            offset: Results to skip

        Returns:
            Tuple of (exercises, total_count)
        """
        # Validate pagination parameters
        if limit <= 0 or limit > 100:
            raise ValidationError("Limit must be between 1 and 100")
        if offset < 0:
            raise ValidationError("Offset must be non-negative")

        exercises = await self.exercise_repository.search(filters, limit, offset)
        total_count = await self.exercise_repository.count(filters)

        return exercises, total_count

    async def update_exercise(
        self,
        exercise_id: UUID,
        request: ExerciseUpdateRequest,
        updated_by: UUID | None = None,
        create_new_version: bool = True,
    ) -> Exercise:
        """Update an exercise.

        Args:
            exercise_id: Exercise ID to update
            request: Update request
            updated_by: User performing update
            create_new_version: Whether to create new version

        Returns:
            Updated exercise

        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If business rules violated
        """
        # Get current exercise
        current_exercise = await self.get_exercise(exercise_id)

        # Check if name change conflicts with existing exercise
        if (
            request.name
            and request.name != current_exercise.name
            and await self.exercise_repository.exists_by_name(
                request.name, exclude_id=exercise_id
            )
        ):
            raise BusinessRuleViolationError(
                f"Exercise with name '{request.name}' already exists"
            )

        # Create updated exercise
        updated_exercise = self._apply_updates(current_exercise, request, updated_by)

        if create_new_version:
            # Create new version
            return await self.exercise_repository.create_version(
                current_exercise.exercise_uuid, updated_exercise, updated_by
            )
        else:
            # Update in place
            return await self.exercise_repository.update(updated_exercise)

    async def delete_exercise(
        self, exercise_id: UUID, deleted_by: UUID | None = None
    ) -> bool:
        """Soft delete an exercise.

        Args:
            exercise_id: Exercise ID to delete
            deleted_by: User performing deletion

        Returns:
            True if deleted successfully

        Raises:
            NotFoundError: If exercise not found
        """
        # Verify exercise exists
        await self.get_exercise(exercise_id)

        return await self.exercise_repository.soft_delete(exercise_id, deleted_by)

    async def restore_exercise(
        self, exercise_id: UUID, restored_by: UUID | None = None
    ) -> bool:
        """Restore a soft-deleted exercise.

        Args:
            exercise_id: Exercise ID to restore
            restored_by: User performing restoration

        Returns:
            True if restored successfully

        Raises:
            NotFoundError: If exercise not found
        """
        # Verify exercise exists (including deleted ones)
        exercise = await self.exercise_repository.get_by_id(exercise_id)
        if not exercise:
            raise NotFoundError(f"Exercise with ID {exercise_id} not found")

        if not exercise.is_deleted():
            raise BusinessRuleViolationError("Exercise is not deleted")

        return await self.exercise_repository.restore(exercise_id, restored_by)

    async def approve_exercise(
        self, exercise_id: UUID, approved_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Approve an exercise.

        Args:
            exercise_id: Exercise ID to approve
            approved_by: User performing approval
            notes: Optional approval notes

        Returns:
            Approved exercise

        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If exercise cannot be approved
        """
        exercise = await self.get_exercise(exercise_id)

        if exercise.is_deleted():
            raise BusinessRuleViolationError("Cannot approve deleted exercise")

        if exercise.approval_status == ApprovalStatus.APPROVED:
            raise BusinessRuleViolationError("Exercise is already approved")

        return await self.exercise_repository.approve(exercise_id, approved_by, notes)

    async def reject_exercise(
        self, exercise_id: UUID, rejected_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Reject an exercise.

        Args:
            exercise_id: Exercise ID to reject
            rejected_by: User performing rejection
            notes: Optional rejection notes

        Returns:
            Rejected exercise

        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If exercise cannot be rejected
        """
        exercise = await self.get_exercise(exercise_id)

        if exercise.is_deleted():
            raise BusinessRuleViolationError("Cannot reject deleted exercise")

        return await self.exercise_repository.reject(exercise_id, rejected_by, notes)

    async def get_exercise_versions(self, exercise_uuid: UUID) -> list[Exercise]:
        """Get all versions of an exercise.

        Args:
            exercise_uuid: Exercise UUID

        Returns:
            List of exercise versions
        """
        versions = await self.exercise_repository.get_versions(exercise_uuid)
        if not versions:
            raise NotFoundError(f"Exercise with UUID {exercise_uuid} not found")

        return versions

    async def set_current_version(
        self, exercise_uuid: UUID, version: int, updated_by: UUID | None = None
    ) -> bool:
        """Set the current version of an exercise.

        Args:
            exercise_uuid: Exercise UUID
            version: Version to set as current
            updated_by: User performing update

        Returns:
            True if successful

        Raises:
            NotFoundError: If exercise or version not found
        """
        # Verify version exists
        exercise = await self.exercise_repository.get_by_uuid(
            exercise_uuid, version=version, current_only=False
        )
        if not exercise:
            raise NotFoundError(
                f"Exercise version {version} with UUID {exercise_uuid} not found"
            )

        return await self.exercise_repository.set_current_version(
            exercise_uuid, version, updated_by
        )

    def _apply_updates(
        self, current: Exercise, request: ExerciseUpdateRequest, updated_by: UUID | None
    ) -> Exercise:
        """Apply update request to current exercise."""
        return Exercise(
            id=current.id,
            exercise_uuid=current.exercise_uuid,
            version=current.version,
            is_current_version=current.is_current_version,
            parent_version_id=current.parent_version_id,
            name=request.name.strip() if request.name else current.name,
            description=(
                request.description.strip()
                if request.description
                else current.description
            ),
            primary_muscle_group=(
                request.primary_muscle_group
                if request.primary_muscle_group
                else current.primary_muscle_group
            ),
            secondary_muscle_groups=(
                request.secondary_muscle_groups
                if request.secondary_muscle_groups is not None
                else current.secondary_muscle_groups
            ),
            movement_pattern=(
                request.movement_pattern
                if request.movement_pattern
                else current.movement_pattern
            ),
            equipment_required=(
                request.equipment_required
                if request.equipment_required is not None
                else current.equipment_required
            ),
            difficulty_level=(
                request.difficulty_level
                if request.difficulty_level
                else current.difficulty_level
            ),
            video_url=request.video_url
            if request.video_url is not None
            else current.video_url,
            thumbnail_url=(
                request.thumbnail_url
                if request.thumbnail_url is not None
                else current.thumbnail_url
            ),
            form_cues=request.form_cues
            if request.form_cues is not None
            else current.form_cues,
            setup_instructions=(
                request.setup_instructions.strip()
                if request.setup_instructions
                else current.setup_instructions
            ),
            execution_steps=(
                request.execution_steps
                if request.execution_steps is not None
                else current.execution_steps
            ),
            common_mistakes=(
                request.common_mistakes
                if request.common_mistakes is not None
                else current.common_mistakes
            ),
            safety_notes=(
                request.safety_notes.strip()
                if request.safety_notes
                else current.safety_notes
            ),
            is_active=current.is_active,
            is_approved=False,  # Updates require re-approval
            approval_status=ApprovalStatus.PENDING,
            created_by=current.created_by,
            updated_by=updated_by,
            approved_by=None,
            created_at=current.created_at,
            updated_at=datetime.utcnow(),
            deleted_at=current.deleted_at,
            approved_at=None,
            version_notes=request.version_notes,
            change_reason=request.change_reason,
            media=current.media,
        )
