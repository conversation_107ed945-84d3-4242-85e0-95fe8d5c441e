"""
Exercise application service.

High-level service for exercise management operations including CRUD,
search, versioning, and approval workflows.
"""

from typing import List, Optional
from uuid import UUID

from ...domain.entities.exercise import (
    Exercise,
    ExerciseCreateRequest,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
)
from ...domain.repositories.exercise_repository import ExerciseRepository
from ..exceptions import BusinessRuleViolationError, NotFoundError, ValidationError
from ..use_cases.exercise_use_cases import ExerciseUseCases


class ExerciseService:
    """
    Exercise application service.
    
    Orchestrates exercise-related use cases and provides a high-level
    interface for exercise management operations.
    """
    
    def __init__(self, exercise_repository: ExerciseRepository):
        """
        Initialize exercise service.

        Args:
            exercise_repository: Exercise repository implementation
        """
        self.exercise_repository = exercise_repository

        # Initialize use cases
        self.exercise_use_cases = ExerciseUseCases(exercise_repository)
    
    async def create_exercise(
        self, 
        request: ExerciseCreateRequest, 
        created_by: Optional[UUID] = None
    ) -> Exercise:
        """
        Create a new exercise.
        
        Args:
            request: Exercise creation request
            created_by: User ID creating the exercise
            
        Returns:
            Created exercise
            
        Raises:
            ValidationError: If exercise data is invalid
            BusinessRuleViolationError: If business rules are violated
        """
        return await self.exercise_use_cases.create_exercise(request, created_by)
    
    async def get_exercise(self, exercise_id: UUID) -> Exercise:
        """
        Get exercise by ID.
        
        Args:
            exercise_id: Exercise ID
            
        Returns:
            Exercise entity
            
        Raises:
            NotFoundError: If exercise not found
        """
        return await self.exercise_use_cases.get_exercise(exercise_id)
    
    async def update_exercise(
        self, 
        exercise_id: UUID, 
        request: ExerciseUpdateRequest,
        updated_by: Optional[UUID] = None
    ) -> Exercise:
        """
        Update an existing exercise.
        
        Args:
            exercise_id: Exercise ID to update
            request: Update request with new values
            updated_by: User ID performing the update
            
        Returns:
            Updated exercise
            
        Raises:
            NotFoundError: If exercise not found
            ValidationError: If update data is invalid
            BusinessRuleViolationError: If business rules are violated
        """
        return await self.exercise_use_cases.update_exercise(exercise_id, request, updated_by)
    
    async def delete_exercise(self, exercise_id: UUID, deleted_by: Optional[UUID] = None) -> bool:
        """
        Soft delete an exercise.
        
        Args:
            exercise_id: Exercise ID to delete
            deleted_by: User ID performing the deletion
            
        Returns:
            True if exercise was deleted
            
        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If exercise cannot be deleted
        """
        return await self.exercise_use_cases.delete_exercise(exercise_id, deleted_by)
    
    async def search_exercises(
        self, 
        filters: ExerciseSearchFilters,
        limit: int = 50,
        offset: int = 0
    ) -> tuple[List[Exercise], int]:
        """
        Search exercises with filters.
        
        Args:
            filters: Search filters
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            Tuple of (exercises, total_count)
        """
        return await self.exercise_use_cases.search_exercises(filters, limit, offset)
    
    async def approve_exercise(
        self, 
        exercise_id: UUID, 
        approved_by: UUID,
        notes: Optional[str] = None
    ) -> Exercise:
        """
        Approve an exercise.
        
        Args:
            exercise_id: Exercise ID to approve
            approved_by: User ID performing the approval
            notes: Optional approval notes
            
        Returns:
            Approved exercise
            
        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If exercise cannot be approved
        """
        return await self.exercise_use_cases.approve_exercise(exercise_id, approved_by, notes)
    
    async def reject_exercise(
        self, 
        exercise_id: UUID, 
        rejected_by: UUID,
        reason: str
    ) -> Exercise:
        """
        Reject an exercise.
        
        Args:
            exercise_id: Exercise ID to reject
            rejected_by: User ID performing the rejection
            reason: Rejection reason
            
        Returns:
            Rejected exercise
            
        Raises:
            NotFoundError: If exercise not found
            BusinessRuleViolationError: If exercise cannot be rejected
        """
        return await self.exercise_use_cases.reject_exercise(exercise_id, rejected_by, reason)
    
    async def get_exercise_by_name(self, name: str) -> Optional[Exercise]:
        """
        Get exercise by name.
        
        Args:
            name: Exercise name
            
        Returns:
            Exercise if found, None otherwise
        """
        return await self.exercise_repository.get_by_name(name)
    
    async def get_exercises_by_muscle_group(
        self, 
        muscle_group: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Exercise]:
        """
        Get exercises by muscle group.
        
        Args:
            muscle_group: Muscle group name
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of exercises
        """
        filters = ExerciseSearchFilters(primary_muscle_group=muscle_group)
        exercises, _ = await self.search_exercises(filters, limit, offset)
        return exercises
    
    async def get_exercises_by_equipment(
        self, 
        equipment: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Exercise]:
        """
        Get exercises by equipment.
        
        Args:
            equipment: Equipment name
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of exercises
        """
        filters = ExerciseSearchFilters(equipment_required=[equipment])
        exercises, _ = await self.search_exercises(filters, limit, offset)
        return exercises
    
    async def get_random_exercises(self, count: int = 10) -> List[Exercise]:
        """
        Get random exercises.
        
        Args:
            count: Number of random exercises to return
            
        Returns:
            List of random exercises
        """
        return await self.exercise_repository.get_random(count)
