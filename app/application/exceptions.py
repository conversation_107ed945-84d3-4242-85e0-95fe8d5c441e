"""
Application layer exceptions.

Custom exceptions for application-specific errors and business rule violations.
"""


class ApplicationError(Exception):
    """Base exception for application layer errors."""
    
    def __init__(self, message: str, error_code: str = None):
        """Initialize application error."""
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__


class ValidationError(ApplicationError):
    """Raised when input validation fails."""
    
    def __init__(self, message: str, field: str = None):
        """Initialize validation error."""
        super().__init__(message, "VALIDATION_ERROR")
        self.field = field


class BusinessRuleViolationError(ApplicationError):
    """Raised when business rules are violated."""
    
    def __init__(self, message: str, rule: str = None):
        """Initialize business rule violation error."""
        super().__init__(message, "BUSINESS_RULE_VIOLATION")
        self.rule = rule


class NotFoundError(ApplicationError):
    """Raised when a requested resource is not found."""
    
    def __init__(self, message: str, resource_type: str = None, resource_id: str = None):
        """Initialize not found error."""
        super().__init__(message, "NOT_FOUND")
        self.resource_type = resource_type
        self.resource_id = resource_id


class ConflictError(ApplicationError):
    """Raised when there's a conflict with the current state."""
    
    def __init__(self, message: str, conflicting_resource: str = None):
        """Initialize conflict error."""
        super().__init__(message, "CONFLICT")
        self.conflicting_resource = conflicting_resource


class UnauthorizedError(ApplicationError):
    """Raised when user is not authorized to perform an action."""
    
    def __init__(self, message: str = "Unauthorized access"):
        """Initialize unauthorized error."""
        super().__init__(message, "UNAUTHORIZED")


class ForbiddenError(ApplicationError):
    """Raised when user is forbidden from performing an action."""
    
    def __init__(self, message: str = "Forbidden access"):
        """Initialize forbidden error."""
        super().__init__(message, "FORBIDDEN")


class ExternalServiceError(ApplicationError):
    """Raised when external service calls fail."""
    
    def __init__(self, message: str, service_name: str = None, status_code: int = None):
        """Initialize external service error."""
        super().__init__(message, "EXTERNAL_SERVICE_ERROR")
        self.service_name = service_name
        self.status_code = status_code


class RateLimitError(ApplicationError):
    """Raised when rate limits are exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None):
        """Initialize rate limit error."""
        super().__init__(message, "RATE_LIMIT_EXCEEDED")
        self.retry_after = retry_after


class DataIntegrityError(ApplicationError):
    """Raised when data integrity constraints are violated."""
    
    def __init__(self, message: str, constraint: str = None):
        """Initialize data integrity error."""
        super().__init__(message, "DATA_INTEGRITY_ERROR")
        self.constraint = constraint


class ConcurrencyError(ApplicationError):
    """Raised when concurrent modification conflicts occur."""
    
    def __init__(self, message: str = "Concurrent modification detected"):
        """Initialize concurrency error."""
        super().__init__(message, "CONCURRENCY_ERROR")


class ConfigurationError(ApplicationError):
    """Raised when configuration is invalid or missing."""
    
    def __init__(self, message: str, config_key: str = None):
        """Initialize configuration error."""
        super().__init__(message, "CONFIGURATION_ERROR")
        self.config_key = config_key
