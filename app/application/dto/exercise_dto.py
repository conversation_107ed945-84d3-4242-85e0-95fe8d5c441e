"""Exercise Data Transfer Objects."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from ...domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    MediaType,
    MovementPattern,
    MuscleGroup,
)


class ExerciseMediaDTO(BaseModel):
    """Exercise media data transfer object."""

    id: UUID
    exercise_id: UUID
    media_type: MediaType
    url: str
    thumbnail_url: str | None = None
    title: str | None = None
    description: str | None = None
    file_size_bytes: int | None = None
    duration_seconds: int | None = None
    width_pixels: int | None = None
    height_pixels: int | None = None
    mime_type: str | None = None
    sort_order: int = 0
    is_primary: bool = False
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class ExerciseDTO(BaseModel):
    """Exercise data transfer object."""

    # Identity and versioning
    id: UUID
    exercise_uuid: UUID
    version: int = 1
    is_current_version: bool = True
    parent_version_id: UUID | None = None

    # Core exercise data
    name: str
    description: str | None = None

    # Muscle targeting
    primary_muscle_group: MuscleGroup
    secondary_muscle_groups: list[MuscleGroup] | None = None

    # Movement classification
    movement_pattern: MovementPattern

    # Equipment and difficulty
    equipment_required: list[Equipment] | None = None
    difficulty_level: DifficultyLevel

    # Media and instructions
    video_url: str | None = None
    thumbnail_url: str | None = None
    form_cues: list[str] | None = None
    setup_instructions: str | None = None
    execution_steps: list[str] | None = None
    common_mistakes: list[str] | None = None
    safety_notes: str | None = None

    # Status and metadata
    is_active: bool = True
    is_approved: bool = False
    approval_status: ApprovalStatus = ApprovalStatus.PENDING

    # Audit fields
    created_by: UUID | None = None
    updated_by: UUID | None = None
    approved_by: UUID | None = None

    # Timestamps
    created_at: datetime
    updated_at: datetime
    deleted_at: datetime | None = None
    approved_at: datetime | None = None

    # Version control metadata
    version_notes: str | None = None
    change_reason: ChangeReason = ChangeReason.INITIAL_CREATION

    # Related entities
    media: list[ExerciseMediaDTO] | None = None

    class Config:
        """Pydantic configuration."""

        from_attributes = True
        use_enum_values = True


class ExerciseCreateDTO(BaseModel):
    """DTO for creating exercises."""

    name: str = Field(..., min_length=1, max_length=255)
    description: str | None = None
    primary_muscle_group: MuscleGroup
    secondary_muscle_groups: list[MuscleGroup] | None = None
    movement_pattern: MovementPattern
    equipment_required: list[Equipment] | None = None
    difficulty_level: DifficultyLevel
    video_url: str | None = Field(None, max_length=500)
    thumbnail_url: str | None = Field(None, max_length=500)
    form_cues: list[str] | None = None
    setup_instructions: str | None = None
    execution_steps: list[str] | None = None
    common_mistakes: list[str] | None = None
    safety_notes: str | None = None
    version_notes: str | None = None


class ExerciseUpdateDTO(BaseModel):
    """DTO for updating exercises."""

    name: str | None = Field(None, min_length=1, max_length=255)
    description: str | None = None
    primary_muscle_group: MuscleGroup | None = None
    secondary_muscle_groups: list[MuscleGroup] | None = None
    movement_pattern: MovementPattern | None = None
    equipment_required: list[Equipment] | None = None
    difficulty_level: DifficultyLevel | None = None
    video_url: str | None = Field(None, max_length=500)
    thumbnail_url: str | None = Field(None, max_length=500)
    form_cues: list[str] | None = None
    setup_instructions: str | None = None
    execution_steps: list[str] | None = None
    common_mistakes: list[str] | None = None
    safety_notes: str | None = None
    version_notes: str | None = None
    change_reason: ChangeReason = ChangeReason.CONTENT_UPDATE
