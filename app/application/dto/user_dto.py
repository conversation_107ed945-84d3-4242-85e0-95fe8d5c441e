"""
User Data Transfer Objects.

DTOs for transferring user data between application layers
without exposing domain entities directly.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, EmailStr

from ...domain.entities.user import User


class UserDTO(BaseModel):
    """User data transfer object for responses."""
    
    id: UUID
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    is_verified: bool
    training_experience_years: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    @classmethod
    def from_entity(cls, user: User) -> "UserDTO":
        """Create DTO from domain entity."""
        return cls(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            training_experience_years=user.training_experience_years,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
    
    class Config:
        from_attributes = True


class CreateUserDTO(BaseModel):
    """DTO for user creation requests."""
    
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    training_experience_years: Optional[int] = None
    
    class Config:
        from_attributes = True


class UpdateUserDTO(BaseModel):
    """DTO for user update requests."""
    
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    training_experience_years: Optional[int] = None
    
    class Config:
        from_attributes = True
