"""Repository interfaces - Abstract data access contracts.

Defines the contracts for data access without specifying implementation details,
following the Repository pattern and Dependency Inversion Principle.
"""

from .exercise_repository import ExerciseRepository
from .user_repository import UserRepository
from .workout_repository import WorkoutRepository

__all__ = ["ExerciseRepository", "UserRepository", "WorkoutRepository"]
