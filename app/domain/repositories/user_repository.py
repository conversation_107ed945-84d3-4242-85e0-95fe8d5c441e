"""
User repository interface.

Defines the contract for user data access operations without
specifying implementation details.
"""

from abc import ABC, abstractmethod
from typing import Optional
from uuid import UUID

from ..entities.user import User


class UserRepository(ABC):
    """Abstract repository interface for user data access."""

    @abstractmethod
    async def create(self, user: User) -> User:
        """
        Create a new user.

        Args:
            user: User entity to create

        Returns:
            Created user entity with generated ID

        Raises:
            UserAlreadyExistsError: If user with email already exists
        """
        pass

    @abstractmethod
    async def get_by_id(self, user_id: UUID) -> Optional[User]:
        """
        Get user by ID.

        Args:
            user_id: User's unique identifier

        Returns:
            User entity if found, None otherwise
        """
        pass

    @abstractmethod
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.

        Args:
            email: User's email address

        Returns:
            User entity if found, None otherwise
        """
        pass

    @abstractmethod
    async def update(self, user: User) -> User:
        """
        Update existing user.

        Args:
            user: User entity with updated data

        Returns:
            Updated user entity

        Raises:
            UserNotFoundError: If user doesn't exist
        """
        pass

    @abstractmethod
    async def delete(self, user_id: UUID) -> bool:
        """
        Soft delete user by ID.

        Args:
            user_id: User's unique identifier

        Returns:
            True if user was deleted, False if not found
        """
        pass

    @abstractmethod
    async def exists_by_email(self, email: str) -> bool:
        """
        Check if user exists by email.

        Args:
            email: Email address to check

        Returns:
            True if user exists, False otherwise
        """
        pass
