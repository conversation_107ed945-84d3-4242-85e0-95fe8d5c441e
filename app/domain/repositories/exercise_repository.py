"""Exercise repository interface.

Defines the contract for exercise data access operations including
versioning, soft-delete, and audit trail functionality.
"""

from abc import ABC, abstractmethod
from uuid import UUID

from ..entities.exercise import (
    AuditAction,
    Exercise,
    ExerciseAuditLog,
    ExerciseMedia,
    ExerciseSearchFilters,
)


class ExerciseRepository(ABC):
    """Abstract repository interface for exercise data access.

    Defines all operations for managing exercises with versioning,
    soft-delete, and audit trail support.
    """

    @abstractmethod
    async def create(self, exercise: Exercise) -> Exercise:
        """Create a new exercise.

        Args:
            exercise: Exercise entity to create

        Returns:
            Created exercise with generated ID and timestamps

        Raises:
            RepositoryError: If creation fails
        """

    @abstractmethod
    async def get_by_id(self, exercise_id: UUID) -> Exercise | None:
        """Get exercise by ID.

        Args:
            exercise_id: Exercise ID

        Returns:
            Exercise if found, None otherwise
        """

    @abstractmethod
    async def get_by_uuid(
        self, exercise_uuid: UUID, version: int | None = None, current_only: bool = True
    ) -> Exercise | None:
        """Get exercise by UUID and optionally version.

        Args:
            exercise_uuid: Exercise UUID
            version: Specific version number (None for current)
            current_only: If True, only return current version

        Returns:
            Exercise if found, None otherwise
        """

    @abstractmethod
    async def get_versions(self, exercise_uuid: UUID) -> list[Exercise]:
        """Get all versions of an exercise.

        Args:
            exercise_uuid: Exercise UUID

        Returns:
            List of all exercise versions, ordered by version number
        """

    @abstractmethod
    async def search(
        self, filters: ExerciseSearchFilters, limit: int = 50, offset: int = 0
    ) -> list[Exercise]:
        """Search exercises with filters.

        Args:
            filters: Search filters
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of matching exercises
        """

    @abstractmethod
    async def count(self, filters: ExerciseSearchFilters) -> int:
        """Count exercises matching filters.

        Args:
            filters: Search filters

        Returns:
            Number of matching exercises
        """

    @abstractmethod
    async def update(self, exercise: Exercise) -> Exercise:
        """Update an exercise (creates new version).

        Args:
            exercise: Updated exercise entity

        Returns:
            Updated exercise with new version

        Raises:
            RepositoryError: If update fails
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def soft_delete(
        self, exercise_id: UUID, deleted_by: UUID | None = None
    ) -> bool:
        """Soft delete an exercise.

        Args:
            exercise_id: Exercise ID to delete
            deleted_by: User ID performing the deletion

        Returns:
            True if deleted successfully

        Raises:
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def restore(self, exercise_id: UUID, restored_by: UUID | None = None) -> bool:
        """Restore a soft-deleted exercise.

        Args:
            exercise_id: Exercise ID to restore
            restored_by: User ID performing the restoration

        Returns:
            True if restored successfully

        Raises:
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def approve(
        self, exercise_id: UUID, approved_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Approve an exercise.

        Args:
            exercise_id: Exercise ID to approve
            approved_by: User ID performing the approval
            notes: Optional approval notes

        Returns:
            Approved exercise

        Raises:
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def reject(
        self, exercise_id: UUID, rejected_by: UUID, notes: str | None = None
    ) -> Exercise:
        """Reject an exercise.

        Args:
            exercise_id: Exercise ID to reject
            rejected_by: User ID performing the rejection
            notes: Optional rejection notes

        Returns:
            Rejected exercise

        Raises:
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def create_version(
        self,
        exercise_uuid: UUID,
        updated_exercise: Exercise,
        created_by: UUID | None = None,
    ) -> Exercise:
        """Create a new version of an existing exercise.

        Args:
            exercise_uuid: UUID of exercise to version
            updated_exercise: Updated exercise data
            created_by: User creating the version

        Returns:
            New exercise version

        Raises:
            NotFoundError: If exercise not found
        """

    @abstractmethod
    async def get_current_version(self, exercise_uuid: UUID) -> Exercise | None:
        """Get the current version of an exercise.

        Args:
            exercise_uuid: Exercise UUID

        Returns:
            Current exercise version if found
        """

    @abstractmethod
    async def set_current_version(
        self, exercise_uuid: UUID, version: int, updated_by: UUID | None = None
    ) -> bool:
        """Set the current version of an exercise.

        Args:
            exercise_uuid: Exercise UUID
            version: Version number to set as current
            updated_by: User performing the update

        Returns:
            True if successful

        Raises:
            NotFoundError: If exercise or version not found
        """

    # Media management methods
    @abstractmethod
    async def add_media(self, exercise_id: UUID, media: ExerciseMedia) -> ExerciseMedia:
        """Add media to an exercise.

        Args:
            exercise_id: Exercise ID
            media: Media to add

        Returns:
            Created media with generated ID
        """

    @abstractmethod
    async def get_media(self, exercise_id: UUID) -> list[ExerciseMedia]:
        """Get all media for an exercise.

        Args:
            exercise_id: Exercise ID

        Returns:
            List of exercise media
        """

    @abstractmethod
    async def update_media(self, media_id: UUID, media: ExerciseMedia) -> ExerciseMedia:
        """Update exercise media.

        Args:
            media_id: Media ID
            media: Updated media data

        Returns:
            Updated media
        """

    @abstractmethod
    async def delete_media(self, media_id: UUID) -> bool:
        """Delete exercise media.

        Args:
            media_id: Media ID to delete

        Returns:
            True if deleted successfully
        """

    # Audit trail methods
    @abstractmethod
    async def log_audit(
        self,
        exercise_id: UUID,
        exercise_uuid: UUID,
        action: AuditAction,
        user_id: UUID | None = None,
        ip_address: str | None = None,
        user_agent: str | None = None,
        field_changes: dict | None = None,
        old_values: dict | None = None,
        new_values: dict | None = None,
        notes: str | None = None,
    ) -> ExerciseAuditLog:
        """Log an audit trail entry.

        Args:
            exercise_id: Exercise ID
            exercise_uuid: Exercise UUID
            action: Audit action
            user_id: User performing the action
            ip_address: IP address of the user
            user_agent: User agent string
            field_changes: Dictionary of changed fields
            old_values: Old values before change
            new_values: New values after change
            notes: Additional notes

        Returns:
            Created audit log entry
        """

    @abstractmethod
    async def get_audit_log(
        self, exercise_uuid: UUID, limit: int = 50, offset: int = 0
    ) -> list[ExerciseAuditLog]:
        """Get audit log for an exercise.

        Args:
            exercise_uuid: Exercise UUID
            limit: Maximum number of entries
            offset: Number of entries to skip

        Returns:
            List of audit log entries
        """

    @abstractmethod
    async def exists(self, exercise_id: UUID) -> bool:
        """Check if exercise exists.

        Args:
            exercise_id: Exercise ID

        Returns:
            True if exercise exists
        """

    @abstractmethod
    async def exists_by_name(self, name: str, exclude_id: UUID | None = None) -> bool:
        """Check if exercise with name exists.

        Args:
            name: Exercise name
            exclude_id: Exercise ID to exclude from check

        Returns:
            True if exercise with name exists
        """
