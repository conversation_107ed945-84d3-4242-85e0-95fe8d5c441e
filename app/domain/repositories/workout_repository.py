"""
Workout repository interface.

Defines the contract for workout and exercise set data access operations
including session management and statistics calculation.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from ..entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutUpdateRequest,
)


class WorkoutRepository(ABC):
    """
    Abstract repository interface for workout data access.
    
    Defines all operations for managing workouts and exercise sets
    with comprehensive tracking and statistics.
    """

    # Workout operations
    @abstractmethod
    async def create_workout(self, workout_request: WorkoutCreateRequest, user_id: UUID) -> Workout:
        """
        Create a new workout.
        
        Args:
            workout_request: Workout creation request
            user_id: User ID creating the workout
            
        Returns:
            Created workout with generated ID and timestamps
            
        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_workout_by_id(self, workout_id: UUID) -> Optional[Workout]:
        """
        Get workout by ID.
        
        Args:
            workout_id: Workout ID
            
        Returns:
            Workout if found, None otherwise
        """
        pass

    @abstractmethod
    async def update_workout(self, workout_id: UUID, workout_request: WorkoutUpdateRequest) -> Optional[Workout]:
        """
        Update an existing workout.
        
        Args:
            workout_id: Workout ID to update
            workout_request: Update request with new values
            
        Returns:
            Updated workout if found, None otherwise
            
        Raises:
            RepositoryError: If update fails
        """
        pass

    @abstractmethod
    async def delete_workout(self, workout_id: UUID) -> bool:
        """
        Soft delete a workout.
        
        Args:
            workout_id: Workout ID to delete
            
        Returns:
            True if workout was deleted, False if not found
        """
        pass

    @abstractmethod
    async def search_workouts(
        self, 
        filters: WorkoutSearchFilters,
        limit: int = 50,
        offset: int = 0
    ) -> List[Workout]:
        """
        Search workouts with filters.
        
        Args:
            filters: Search filters
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of matching workouts
        """
        pass

    @abstractmethod
    async def count_workouts(self, filters: WorkoutSearchFilters) -> int:
        """
        Count workouts matching filters.
        
        Args:
            filters: Search filters
            
        Returns:
            Number of matching workouts
        """
        pass

    @abstractmethod
    async def get_user_workouts(
        self, 
        user_id: UUID,
        limit: int = 50,
        offset: int = 0
    ) -> List[Workout]:
        """
        Get workouts for a specific user.
        
        Args:
            user_id: User ID
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of user's workouts
        """
        pass

    @abstractmethod
    async def get_active_workout(self, user_id: UUID) -> Optional[Workout]:
        """
        Get user's currently active workout.
        
        Args:
            user_id: User ID
            
        Returns:
            Active workout if found, None otherwise
        """
        pass

    @abstractmethod
    async def start_workout(self, workout_id: UUID) -> Optional[Workout]:
        """
        Start a workout session.
        
        Args:
            workout_id: Workout ID to start
            
        Returns:
            Started workout if found, None otherwise
        """
        pass

    @abstractmethod
    async def complete_workout(self, workout_id: UUID) -> Optional[Workout]:
        """
        Complete a workout session.
        
        Args:
            workout_id: Workout ID to complete
            
        Returns:
            Completed workout if found, None otherwise
        """
        pass

    # Exercise set operations
    @abstractmethod
    async def create_exercise_set(
        self, 
        workout_id: UUID, 
        set_request: ExerciseSetCreateRequest
    ) -> ExerciseSet:
        """
        Create a new exercise set.
        
        Args:
            workout_id: Workout ID to add set to
            set_request: Set creation request
            
        Returns:
            Created exercise set
            
        Raises:
            RepositoryError: If creation fails
        """
        pass

    @abstractmethod
    async def get_exercise_set_by_id(self, set_id: UUID) -> Optional[ExerciseSet]:
        """
        Get exercise set by ID.
        
        Args:
            set_id: Exercise set ID
            
        Returns:
            Exercise set if found, None otherwise
        """
        pass

    @abstractmethod
    async def update_exercise_set(
        self, 
        set_id: UUID, 
        set_request: ExerciseSetUpdateRequest
    ) -> Optional[ExerciseSet]:
        """
        Update an existing exercise set.
        
        Args:
            set_id: Exercise set ID to update
            set_request: Update request with new values
            
        Returns:
            Updated exercise set if found, None otherwise
        """
        pass

    @abstractmethod
    async def delete_exercise_set(self, set_id: UUID) -> bool:
        """
        Delete an exercise set.
        
        Args:
            set_id: Exercise set ID to delete
            
        Returns:
            True if set was deleted, False if not found
        """
        pass

    @abstractmethod
    async def get_workout_sets(self, workout_id: UUID) -> List[ExerciseSet]:
        """
        Get all exercise sets for a workout.
        
        Args:
            workout_id: Workout ID
            
        Returns:
            List of exercise sets
        """
        pass

    @abstractmethod
    async def get_exercise_sets_by_exercise(
        self, 
        workout_id: UUID, 
        exercise_id: UUID
    ) -> List[ExerciseSet]:
        """
        Get exercise sets for a specific exercise in a workout.
        
        Args:
            workout_id: Workout ID
            exercise_id: Exercise ID
            
        Returns:
            List of exercise sets for the exercise
        """
        pass

    # Template operations
    @abstractmethod
    async def get_workout_templates(self, user_id: UUID) -> List[Workout]:
        """
        Get workout templates for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of workout templates
        """
        pass

    @abstractmethod
    async def copy_workout_as_template(
        self, 
        workout_id: UUID, 
        template_name: str
    ) -> Optional[Workout]:
        """
        Copy a workout as a template.
        
        Args:
            workout_id: Workout ID to copy
            template_name: Name for the template
            
        Returns:
            Created template if successful, None otherwise
        """
        pass

    @abstractmethod
    async def create_workout_from_template(
        self, 
        template_id: UUID, 
        user_id: UUID,
        workout_date: Optional[str] = None
    ) -> Optional[Workout]:
        """
        Create a new workout from a template.
        
        Args:
            template_id: Template ID to copy from
            user_id: User ID creating the workout
            workout_date: Date for the new workout
            
        Returns:
            Created workout if successful, None otherwise
        """
        pass

    # Utility methods
    @abstractmethod
    async def exists(self, workout_id: UUID) -> bool:
        """
        Check if workout exists.
        
        Args:
            workout_id: Workout ID
            
        Returns:
            True if workout exists
        """
        pass

    @abstractmethod
    async def get_workout_statistics(self, workout_id: UUID) -> dict:
        """
        Get comprehensive statistics for a workout.
        
        Args:
            workout_id: Workout ID
            
        Returns:
            Dictionary with workout statistics
        """
        pass
