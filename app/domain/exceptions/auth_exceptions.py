"""
Authentication and authorization domain exceptions.

Custom exceptions for authentication-related business rule violations
and error conditions in the domain layer.
"""


class AuthenticationError(Exception):
    """Base exception for authentication errors."""

    pass


class InvalidCredentialsError(AuthenticationError):
    """Raised when user provides invalid login credentials."""

    pass


class InvalidEmailError(AuthenticationError):
    """Raised when email format is invalid."""

    pass


class WeakPasswordError(AuthenticationError):
    """Raised when password doesn't meet security requirements."""

    pass


class UserNotFoundError(AuthenticationError):
    """Raised when user cannot be found."""

    pass


class UserAlreadyExistsError(AuthenticationError):
    """Raised when attempting to create a user that already exists."""

    pass


class InactiveUserError(AuthenticationError):
    """Raised when user account is inactive."""

    pass


class TokenExpiredError(AuthenticationError):
    """Raised when authentication token has expired."""

    pass


class InvalidTokenError(AuthenticationError):
    """Raised when authentication token is invalid or malformed."""

    pass
