"""Authentication and authorization domain exceptions.

Custom exceptions for authentication-related business rule violations
and error conditions in the domain layer.
"""


class AuthenticationError(Exception):
    """Base exception for authentication errors."""


class InvalidCredentialsError(AuthenticationError):
    """Raised when user provides invalid login credentials."""


class InvalidEmailError(AuthenticationError):
    """Raised when email format is invalid."""


class WeakPasswordError(AuthenticationError):
    """Raised when password doesn't meet security requirements."""


class UserNotFoundError(AuthenticationError):
    """Raised when user cannot be found."""


class UserAlreadyExistsError(AuthenticationError):
    """Raised when attempting to create a user that already exists."""


class DuplicateEmailError(AuthenticationError):
    """Raised when attempting to register with an email that already exists."""


class InactiveUserError(AuthenticationError):
    """Raised when user account is inactive."""


class TokenExpiredError(AuthenticationError):
    """Raised when authentication token has expired."""


class InvalidTokenError(AuthenticationError):
    """Raised when authentication token is invalid or malformed."""
