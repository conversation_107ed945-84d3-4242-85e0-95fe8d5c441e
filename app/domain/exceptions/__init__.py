"""
Domain exceptions.

Custom exceptions for domain-specific business rule violations
and error conditions.
"""

from .auth_exceptions import (
    AuthenticationError,
    InactiveUserError,
    InvalidCredentialsError,
    InvalidEmailError,
    InvalidTokenError,
    TokenExpiredError,
    UserAlreadyExistsError,
    UserNotFoundError,
    WeakPasswordError,
)

__all__ = [
    "AuthenticationError",
    "InvalidCredentialsError",
    "InvalidEmailError",
    "WeakPasswordError",
    "UserNotFoundError",
    "UserAlreadyExistsError",
    "InactiveUserError",
    "TokenExpiredError",
    "InvalidTokenError",
]
