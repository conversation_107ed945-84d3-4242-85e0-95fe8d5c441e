"""
Domain exceptions.

Custom exceptions for domain-specific business rule violations
and error conditions.
"""

from .auth_exceptions import (
    AuthenticationError,
    InvalidCredentialsError,
    InvalidEmailError,
    WeakPasswordError,
    UserNotFoundError,
    UserAlreadyExistsError,
    InactiveUserError,
    TokenExpiredError,
    InvalidTokenError
)

__all__ = [
    "AuthenticationError",
    "InvalidCredentialsError", 
    "InvalidEmailError",
    "WeakPasswordError",
    "UserNotFoundError",
    "UserAlreadyExistsError",
    "InactiveUserError",
    "TokenExpiredError",
    "InvalidTokenError"
]
