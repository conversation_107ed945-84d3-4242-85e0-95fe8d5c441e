"""
Authentication domain service.

Contains business logic for authentication operations that don't
naturally belong to a single entity.
"""

from typing import Optional
from uuid import UUID

from ..entities.user import User
from ..repositories.user_repository import UserRepository
from ..exceptions.auth_exceptions import (
    UserAlreadyExistsError,
    UserNotFoundError,
    InvalidCredentialsError,
    InactiveUserError
)


class AuthService:
    """Domain service for authentication business logic."""
    
    def __init__(self, user_repository: UserRepository) -> None:
        """Initialize auth service with user repository."""
        self._user_repository = user_repository
    
    async def register_user(
        self,
        email: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        training_experience_years: Optional[int] = None
    ) -> User:
        """
        Register a new user.
        
        Args:
            email: User's email address
            password: User's password (will be validated)
            first_name: User's first name (optional)
            last_name: User's last name (optional)
            training_experience_years: Years of training experience (optional)
            
        Returns:
            Created user entity
            
        Raises:
            UserAlreadyExistsError: If user with email already exists
            InvalidEmailError: If email format is invalid
            WeakPasswordError: If password doesn't meet requirements
        """
        # Check if user already exists
        if await self._user_repository.exists_by_email(email):
            raise UserAlreadyExistsError(f"User with email {email} already exists")
        
        # Create user entity (this validates email and password)
        user = User.create(
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            training_experience_years=training_experience_years
        )
        
        # Save user to repository
        return await self._user_repository.create(user)
    
    async def authenticate_user(self, email: str, password: str) -> User:
        """
        Authenticate user with email and password.
        
        Args:
            email: User's email address
            password: User's password
            
        Returns:
            Authenticated user entity
            
        Raises:
            InvalidCredentialsError: If credentials are invalid
            InactiveUserError: If user account is inactive
            UserNotFoundError: If user doesn't exist
        """
        # Get user by email
        user = await self._user_repository.get_by_email(email)
        if not user:
            raise InvalidCredentialsError("Invalid email or password")
        
        # Check if user can authenticate
        if not user.can_authenticate():
            if user.is_deleted():
                raise InvalidCredentialsError("Invalid email or password")
            elif not user.is_active:
                raise InactiveUserError("User account is inactive")
        
        # Note: In a real implementation, password verification would be done
        # by the infrastructure layer using proper hashing
        # This is just a placeholder for password verification
        if not self._verify_password(password, user.hashed_password):
            raise InvalidCredentialsError("Invalid email or password")
        
        return user
    
    async def get_user_by_id(self, user_id: UUID) -> User:
        """
        Get user by ID.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            User entity
            
        Raises:
            UserNotFoundError: If user doesn't exist
        """
        user = await self._user_repository.get_by_id(user_id)
        if not user or user.is_deleted():
            raise UserNotFoundError(f"User with ID {user_id} not found")
        
        return user
    
    async def update_user_profile(
        self,
        user_id: UUID,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        training_experience_years: Optional[int] = None
    ) -> User:
        """
        Update user profile.
        
        Args:
            user_id: User's unique identifier
            first_name: Updated first name (optional)
            last_name: Updated last name (optional)
            training_experience_years: Updated training experience (optional)
            
        Returns:
            Updated user entity
            
        Raises:
            UserNotFoundError: If user doesn't exist
        """
        user = await self.get_user_by_id(user_id)
        
        user.update_profile(
            first_name=first_name,
            last_name=last_name,
            training_experience_years=training_experience_years
        )
        
        return await self._user_repository.update(user)
    
    def _verify_password(self, password: str, hashed_password: str) -> bool:
        """
        Verify password against hash.
        
        Note: This is a placeholder. In a real implementation,
        this would be handled by the infrastructure layer.
        """
        return hashed_password == f"hashed_{password}"
