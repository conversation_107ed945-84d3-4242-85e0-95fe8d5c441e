"""
Exercise domain entities.

Core business entities for exercise management with versioning and soft-delete support.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class MuscleGroup(str, Enum):
    """Enumeration of muscle groups."""
    
    CHEST = "chest"
    BACK = "back"
    SHOULDERS = "shoulders"
    BICEPS = "biceps"
    TRICEPS = "triceps"
    QUADS = "quads"
    HAMSTRINGS = "hamstrings"
    GLUTES = "glutes"
    CALVES = "calves"
    ABS = "abs"
    FOREARMS = "forearms"
    TRAPS = "traps"
    LATS = "lats"
    REAR_DELTS = "rear_delts"
    SIDE_DELTS = "side_delts"


class MovementPattern(str, Enum):
    """Enumeration of movement patterns."""
    
    PUSH = "push"
    PULL = "pull"
    SQUAT = "squat"
    HINGE = "hinge"
    CARRY = "carry"
    ISOLATION = "isolation"
    ROTATION = "rotation"
    ANTI_EXTENSION = "anti_extension"
    ANTI_FLEXION = "anti_flexion"
    ANTI_ROTATION = "anti_rotation"


class Equipment(str, Enum):
    """Enumeration of equipment types."""
    
    BARBELL = "barbell"
    DUMBBELL = "dumbbell"
    CABLE = "cable"
    MACHINE = "machine"
    BODYWEIGHT = "bodyweight"
    RESISTANCE_BAND = "resistance_band"
    KETTLEBELL = "kettlebell"
    SUSPENSION_TRAINER = "suspension_trainer"
    MEDICINE_BALL = "medicine_ball"
    PLATE = "plate"
    SMITH_MACHINE = "smith_machine"


class DifficultyLevel(str, Enum):
    """Enumeration of difficulty levels."""
    
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class ApprovalStatus(str, Enum):
    """Enumeration of approval statuses."""
    
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVIEW = "needs_review"


class ChangeReason(str, Enum):
    """Enumeration of change reasons for versioning."""
    
    INITIAL_CREATION = "initial_creation"
    CONTENT_UPDATE = "content_update"
    CORRECTION = "correction"
    MEDIA_UPDATE = "media_update"
    APPROVAL_CHANGE = "approval_change"
    USER_REQUEST = "user_request"


class MediaType(str, Enum):
    """Enumeration of media types."""
    
    VIDEO = "video"
    IMAGE = "image"
    GIF = "gif"
    AUDIO = "audio"


class AuditAction(str, Enum):
    """Enumeration of audit actions."""
    
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    RESTORED = "restored"
    APPROVED = "approved"
    REJECTED = "rejected"
    VERSION_CREATED = "version_created"


class ExerciseMedia(BaseModel):
    """Exercise media entity."""
    
    id: UUID
    exercise_id: UUID
    media_type: MediaType
    url: str = Field(..., max_length=500)
    thumbnail_url: Optional[str] = Field(None, max_length=500)
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    file_size_bytes: Optional[int] = Field(None, ge=0)
    duration_seconds: Optional[int] = Field(None, ge=0)
    width_pixels: Optional[int] = Field(None, ge=0)
    height_pixels: Optional[int] = Field(None, ge=0)
    mime_type: Optional[str] = Field(None, max_length=100)
    sort_order: int = Field(0, ge=0)
    is_primary: bool = False
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class Exercise(BaseModel):
    """
    Exercise domain entity with versioning and soft-delete support.
    
    Represents a single exercise with all its metadata, instructions,
    and versioning information.
    """
    
    # Identity and versioning
    id: UUID
    exercise_uuid: UUID
    version: int = Field(1, ge=1)
    is_current_version: bool = True
    parent_version_id: Optional[UUID] = None
    
    # Core exercise data
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    
    # Muscle targeting
    primary_muscle_group: MuscleGroup
    secondary_muscle_groups: Optional[List[MuscleGroup]] = None
    
    # Movement classification
    movement_pattern: MovementPattern
    
    # Equipment and difficulty
    equipment_required: Optional[List[Equipment]] = None
    difficulty_level: DifficultyLevel
    
    # Media and instructions
    video_url: Optional[str] = Field(None, max_length=500)
    thumbnail_url: Optional[str] = Field(None, max_length=500)
    form_cues: Optional[List[str]] = None
    setup_instructions: Optional[str] = None
    execution_steps: Optional[List[str]] = None
    common_mistakes: Optional[List[str]] = None
    safety_notes: Optional[str] = None
    
    # Status and metadata
    is_active: bool = True
    is_approved: bool = False
    approval_status: ApprovalStatus = ApprovalStatus.PENDING
    
    # Audit fields
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    approved_by: Optional[UUID] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    
    # Version control metadata
    version_notes: Optional[str] = None
    change_reason: ChangeReason = ChangeReason.INITIAL_CREATION
    
    # Related entities
    media: Optional[List[ExerciseMedia]] = None

    @validator('secondary_muscle_groups')
    def validate_secondary_muscle_groups(cls, v, values):
        """Ensure secondary muscle groups don't include primary muscle group."""
        if v and 'primary_muscle_group' in values:
            primary = values['primary_muscle_group']
            if primary in v:
                raise ValueError("Secondary muscle groups cannot include the primary muscle group")
        return v

    @validator('equipment_required')
    def validate_equipment_required(cls, v):
        """Ensure equipment list has no duplicates."""
        if v:
            if len(v) != len(set(v)):
                raise ValueError("Equipment list cannot contain duplicates")
        return v

    @validator('form_cues', 'execution_steps', 'common_mistakes')
    def validate_instruction_lists(cls, v):
        """Ensure instruction lists are not empty if provided."""
        if v is not None and len(v) == 0:
            return None
        return v

    def is_deleted(self) -> bool:
        """Check if the exercise is soft-deleted."""
        return self.deleted_at is not None

    def is_published(self) -> bool:
        """Check if the exercise is published (approved and active)."""
        return (
            self.is_approved 
            and self.is_active 
            and not self.is_deleted()
            and self.approval_status == ApprovalStatus.APPROVED
        )

    def get_all_muscle_groups(self) -> List[MuscleGroup]:
        """Get all muscle groups (primary + secondary)."""
        muscle_groups = [self.primary_muscle_group]
        if self.secondary_muscle_groups:
            muscle_groups.extend(self.secondary_muscle_groups)
        return muscle_groups

    def get_primary_media(self) -> Optional[ExerciseMedia]:
        """Get the primary media item."""
        if not self.media:
            return None
        
        primary_media = [m for m in self.media if m.is_primary and m.is_active]
        return primary_media[0] if primary_media else None

    def get_media_by_type(self, media_type: MediaType) -> List[ExerciseMedia]:
        """Get media items by type."""
        if not self.media:
            return []
        
        return [
            m for m in self.media 
            if m.media_type == media_type and m.is_active
        ]

    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True


class ExerciseAuditLog(BaseModel):
    """Exercise audit log entity."""
    
    id: UUID
    exercise_id: UUID
    exercise_uuid: UUID
    action: AuditAction
    user_id: Optional[UUID] = None
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None
    field_changes: Optional[dict] = None
    old_values: Optional[dict] = None
    new_values: Optional[dict] = None
    notes: Optional[str] = None
    created_at: datetime

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ExerciseSearchFilters(BaseModel):
    """Filters for exercise search."""
    
    name: Optional[str] = None
    primary_muscle_group: Optional[MuscleGroup] = None
    secondary_muscle_groups: Optional[List[MuscleGroup]] = None
    movement_pattern: Optional[MovementPattern] = None
    equipment_required: Optional[List[Equipment]] = None
    difficulty_level: Optional[DifficultyLevel] = None
    is_active: Optional[bool] = True
    is_approved: Optional[bool] = None
    approval_status: Optional[ApprovalStatus] = None
    created_by: Optional[UUID] = None
    
    # Version filtering
    current_version_only: bool = True
    include_deleted: bool = False


class ExerciseCreateRequest(BaseModel):
    """Request model for creating a new exercise."""
    
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    primary_muscle_group: MuscleGroup
    secondary_muscle_groups: Optional[List[MuscleGroup]] = None
    movement_pattern: MovementPattern
    equipment_required: Optional[List[Equipment]] = None
    difficulty_level: DifficultyLevel
    video_url: Optional[str] = Field(None, max_length=500)
    thumbnail_url: Optional[str] = Field(None, max_length=500)
    form_cues: Optional[List[str]] = None
    setup_instructions: Optional[str] = None
    execution_steps: Optional[List[str]] = None
    common_mistakes: Optional[List[str]] = None
    safety_notes: Optional[str] = None
    version_notes: Optional[str] = None


class ExerciseUpdateRequest(BaseModel):
    """Request model for updating an exercise."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    primary_muscle_group: Optional[MuscleGroup] = None
    secondary_muscle_groups: Optional[List[MuscleGroup]] = None
    movement_pattern: Optional[MovementPattern] = None
    equipment_required: Optional[List[Equipment]] = None
    difficulty_level: Optional[DifficultyLevel] = None
    video_url: Optional[str] = Field(None, max_length=500)
    thumbnail_url: Optional[str] = Field(None, max_length=500)
    form_cues: Optional[List[str]] = None
    setup_instructions: Optional[str] = None
    execution_steps: Optional[List[str]] = None
    common_mistakes: Optional[List[str]] = None
    safety_notes: Optional[str] = None
    version_notes: Optional[str] = None
    change_reason: ChangeReason = ChangeReason.CONTENT_UPDATE
