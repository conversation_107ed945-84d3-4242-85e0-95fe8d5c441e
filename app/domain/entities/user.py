"""User domain entity.

Represents a user in the RP Training system with business rules
for user management, authentication, and profile data.
"""

import re
from datetime import datetime
from uuid import UUID

from ..exceptions.auth_exceptions import InvalidEmailError, WeakPasswordError
from .base import BaseEntity


class User(BaseEntity):
    """User domain entity with business rules."""

    # Email validation regex
    EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

    # Password requirements
    MIN_PASSWORD_LENGTH = 8

    def __init__(
        self,
        email: str,
        hashed_password: str,
        first_name: str | None = None,
        last_name: str | None = None,
        is_active: bool = True,
        is_verified: bool = False,
        training_experience_years: int | None = None,
        id: UUID | None = None,
        created_at: datetime | None = None,
        updated_at: datetime | None = None,
        deleted_at: datetime | None = None,
    ) -> None:
        """Initialize user entity with validation."""
        super().__init__(id, created_at, updated_at, deleted_at)

        # Validate and set email
        self._validate_email(email)
        self.email = email.lower().strip()

        # Set other attributes
        self.hashed_password = hashed_password
        self.first_name = first_name.strip() if first_name else None
        self.last_name = last_name.strip() if last_name else None
        self.is_active = is_active
        self.is_verified = is_verified
        self.training_experience_years = training_experience_years

    @classmethod
    def create(
        cls,
        email: str,
        hashed_password: str,
        first_name: str | None = None,
        last_name: str | None = None,
        training_experience_years: int | None = None,
    ) -> "User":
        """Create a new user with pre-hashed password.

        Note: Password should be hashed by the infrastructure layer
        before calling this method. This maintains clean architecture
        by keeping infrastructure concerns out of the domain.
        """
        return cls(
            email=email,
            hashed_password=hashed_password,
            first_name=first_name,
            last_name=last_name,
            training_experience_years=training_experience_years,
        )

    def update_profile(
        self,
        first_name: str | None = None,
        last_name: str | None = None,
        training_experience_years: int | None = None,
    ) -> None:
        """Update user profile information."""
        if first_name is not None:
            self.first_name = first_name.strip() if first_name else None

        if last_name is not None:
            self.last_name = last_name.strip() if last_name else None

        if training_experience_years is not None:
            self.training_experience_years = training_experience_years

        self.mark_as_updated()

    def activate(self) -> None:
        """Activate user account."""
        self.is_active = True
        self.mark_as_updated()

    def deactivate(self) -> None:
        """Deactivate user account."""
        self.is_active = False
        self.mark_as_updated()

    def verify_email(self) -> None:
        """Mark user email as verified."""
        self.is_verified = True
        self.mark_as_updated()

    def get_full_name(self) -> str | None:
        """Get user's full name if available."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        return None

    def can_authenticate(self) -> bool:
        """Check if user can authenticate (active and not deleted)."""
        return self.is_active and not self.is_deleted()

    @classmethod
    def _validate_email(cls, email: str) -> None:
        """Validate email format."""
        if not email or not email.strip():
            raise InvalidEmailError("Email cannot be empty")

        if not cls.EMAIL_REGEX.match(email.strip()):
            raise InvalidEmailError("Invalid email format")

    @classmethod
    def _validate_password(cls, password: str) -> None:
        """Validate password strength."""
        if not password:
            raise WeakPasswordError("Password cannot be empty")

        if len(password) < cls.MIN_PASSWORD_LENGTH:
            raise WeakPasswordError(
                f"Password must be at least {cls.MIN_PASSWORD_LENGTH} characters long"
            )

        # Additional password strength checks
        if not any(c.isupper() for c in password):
            raise WeakPasswordError(
                "Password must contain at least one uppercase letter"
            )

        if not any(c.islower() for c in password):
            raise WeakPasswordError(
                "Password must contain at least one lowercase letter"
            )

        if not any(c.isdigit() for c in password):
            raise WeakPasswordError("Password must contain at least one digit")

    def __str__(self) -> str:
        """String representation of user."""
        name = self.get_full_name()
        return f"User({self.email}" + (f", {name}" if name else "") + ")"
