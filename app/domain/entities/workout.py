"""
Workout domain entities.

Core business entities for workout management including workouts, exercise sets,
and workout templates with comprehensive tracking capabilities.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class WorkoutStatus(str, Enum):
    """Enumeration of workout statuses."""
    
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class RangeOfMotion(str, Enum):
    """Enumeration of range of motion options."""
    
    FULL = "full"
    PARTIAL = "partial"
    LENGTHENED = "lengthened"
    SHORTENED = "shortened"


class Workout(BaseModel):
    """
    Workout domain entity.
    
    Represents a workout session with metadata, timing, and statistics.
    """
    
    # Identity
    id: UUID
    user_id: UUID
    
    # Workout metadata
    name: Optional[str] = None
    workout_date: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Workout statistics
    total_volume_load: Optional[Decimal] = None
    average_rpe: Optional[Decimal] = None
    total_sets: int = 0
    total_exercises: int = 0
    duration_minutes: Optional[int] = None
    
    # Notes and template info
    notes: Optional[str] = None
    is_template: bool = False
    template_name: Optional[str] = None
    
    # Audit fields
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    # Relationships
    exercise_sets: List["ExerciseSet"] = Field(default_factory=list)
    
    @property
    def status(self) -> WorkoutStatus:
        """Get workout status based on timestamps."""
        if self.deleted_at:
            return WorkoutStatus.CANCELLED
        elif self.completed_at:
            return WorkoutStatus.COMPLETED
        elif self.started_at:
            return WorkoutStatus.IN_PROGRESS
        else:
            return WorkoutStatus.PLANNED
    
    @property
    def is_active(self) -> bool:
        """Check if workout is currently active."""
        return self.status == WorkoutStatus.IN_PROGRESS
    
    def start_workout(self) -> None:
        """Start the workout session."""
        if self.status != WorkoutStatus.PLANNED:
            raise ValueError("Can only start planned workouts")
        self.started_at = datetime.utcnow()
    
    def complete_workout(self) -> None:
        """Complete the workout session."""
        if self.status != WorkoutStatus.IN_PROGRESS:
            raise ValueError("Can only complete in-progress workouts")
        self.completed_at = datetime.utcnow()
        self._calculate_statistics()
    
    def _calculate_statistics(self) -> None:
        """Calculate workout statistics from exercise sets."""
        if not self.exercise_sets:
            return
        
        # Calculate total volume load
        total_volume = Decimal(0)
        rpe_values = []
        
        for exercise_set in self.exercise_sets:
            if exercise_set.weight_kg and exercise_set.reps_completed:
                volume = exercise_set.weight_kg * exercise_set.reps_completed
                total_volume += volume
            
            if exercise_set.rpe:
                rpe_values.append(exercise_set.rpe)
        
        self.total_volume_load = total_volume if total_volume > 0 else None
        self.average_rpe = sum(rpe_values) / len(rpe_values) if rpe_values else None
        self.total_sets = len(self.exercise_sets)
        
        # Calculate unique exercises
        unique_exercises = set(s.exercise_id for s in self.exercise_sets)
        self.total_exercises = len(unique_exercises)
        
        # Calculate duration
        if self.started_at and self.completed_at:
            duration = self.completed_at - self.started_at
            self.duration_minutes = int(duration.total_seconds() / 60)


class ExerciseSet(BaseModel):
    """
    Exercise set domain entity.
    
    Represents a single set within a workout with detailed tracking
    including RIR, RPE, tempo, and range of motion.
    """
    
    # Identity
    id: UUID
    workout_id: UUID
    exercise_id: UUID
    
    # Set metadata
    set_number: int = Field(..., ge=1)
    
    # Performance data
    weight_kg: Optional[Decimal] = Field(None, ge=0)
    reps_completed: Optional[int] = Field(None, ge=0)
    reps_target: Optional[int] = Field(None, ge=0)
    
    # RIR and RPE tracking
    rir_target: Optional[int] = Field(None, ge=0, le=10)
    rir_actual: Optional[int] = Field(None, ge=0, le=10)
    rpe: Optional[Decimal] = Field(None, ge=1.0, le=10.0)
    
    # Advanced tracking
    rest_seconds: Optional[int] = Field(None, ge=0)
    tempo: Optional[str] = Field(None, max_length=20)  # e.g., "3-1-2-1"
    range_of_motion: Optional[RangeOfMotion] = None
    
    # Set characteristics
    is_warmup: bool = False
    is_dropset: bool = False
    is_failure: bool = False
    
    # Notes
    notes: Optional[str] = None
    
    # Audit fields
    created_at: datetime
    updated_at: datetime
    
    @validator('tempo')
    def validate_tempo(cls, v):
        """Validate tempo format (e.g., '3-1-2-1')."""
        if v is None:
            return v
        
        parts = v.split('-')
        if len(parts) != 4:
            raise ValueError('Tempo must be in format "eccentric-pause-concentric-pause" (e.g., "3-1-2-1")')
        
        try:
            for part in parts:
                int(part)
        except ValueError:
            raise ValueError('Tempo parts must be integers')
        
        return v
    
    @property
    def volume_load(self) -> Optional[Decimal]:
        """Calculate volume load for this set."""
        if self.weight_kg and self.reps_completed:
            return self.weight_kg * self.reps_completed
        return None


class WorkoutCreateRequest(BaseModel):
    """Request model for creating a new workout."""
    
    name: Optional[str] = Field(None, max_length=255)
    workout_date: datetime
    notes: Optional[str] = None
    is_template: bool = False
    template_name: Optional[str] = Field(None, max_length=255)


class WorkoutUpdateRequest(BaseModel):
    """Request model for updating a workout."""
    
    name: Optional[str] = Field(None, max_length=255)
    workout_date: Optional[datetime] = None
    notes: Optional[str] = None
    is_template: Optional[bool] = None
    template_name: Optional[str] = Field(None, max_length=255)


class ExerciseSetCreateRequest(BaseModel):
    """Request model for creating a new exercise set."""
    
    exercise_id: UUID
    set_number: int = Field(..., ge=1)
    weight_kg: Optional[Decimal] = Field(None, ge=0)
    reps_completed: Optional[int] = Field(None, ge=0)
    reps_target: Optional[int] = Field(None, ge=0)
    rir_target: Optional[int] = Field(None, ge=0, le=10)
    rir_actual: Optional[int] = Field(None, ge=0, le=10)
    rpe: Optional[Decimal] = Field(None, ge=1.0, le=10.0)
    rest_seconds: Optional[int] = Field(None, ge=0)
    tempo: Optional[str] = Field(None, max_length=20)
    range_of_motion: Optional[RangeOfMotion] = None
    is_warmup: bool = False
    is_dropset: bool = False
    is_failure: bool = False
    notes: Optional[str] = None


class ExerciseSetUpdateRequest(BaseModel):
    """Request model for updating an exercise set."""
    
    weight_kg: Optional[Decimal] = Field(None, ge=0)
    reps_completed: Optional[int] = Field(None, ge=0)
    reps_target: Optional[int] = Field(None, ge=0)
    rir_target: Optional[int] = Field(None, ge=0, le=10)
    rir_actual: Optional[int] = Field(None, ge=0, le=10)
    rpe: Optional[Decimal] = Field(None, ge=1.0, le=10.0)
    rest_seconds: Optional[int] = Field(None, ge=0)
    tempo: Optional[str] = Field(None, max_length=20)
    range_of_motion: Optional[RangeOfMotion] = None
    is_warmup: Optional[bool] = None
    is_dropset: Optional[bool] = None
    is_failure: Optional[bool] = None
    notes: Optional[str] = None


class WorkoutSearchFilters(BaseModel):
    """Search filters for workouts."""
    
    user_id: Optional[UUID] = None
    status: Optional[WorkoutStatus] = None
    is_template: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    name_contains: Optional[str] = None
