"""
Base entity class with common functionality.

Provides common attributes and methods that all domain entities share,
following DDD principles.
"""

from abc import ABC
from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4


class BaseEntity(ABC):
    """Base class for all domain entities."""
    
    def __init__(
        self,
        id: Optional[UUID] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        deleted_at: Optional[datetime] = None
    ) -> None:
        """Initialize base entity with common attributes."""
        self.id = id or uuid4()
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
        self.deleted_at = deleted_at
    
    def mark_as_updated(self) -> None:
        """Mark entity as updated with current timestamp."""
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self) -> None:
        """Soft delete the entity by setting deleted_at timestamp."""
        self.deleted_at = datetime.utcnow()
        self.mark_as_updated()
    
    def is_deleted(self) -> bool:
        """Check if entity is soft deleted."""
        return self.deleted_at is not None
    
    def restore(self) -> None:
        """Restore soft deleted entity."""
        self.deleted_at = None
        self.mark_as_updated()
    
    def __eq__(self, other: object) -> bool:
        """Compare entities by ID."""
        if not isinstance(other, BaseEntity):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """Hash entity by ID."""
        return hash(self.id)
    
    def __repr__(self) -> str:
        """String representation of entity."""
        return f"{self.__class__.__name__}(id={self.id})"
