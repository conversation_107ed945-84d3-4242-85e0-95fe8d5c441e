"""
Security tests for authentication system.

Tests security vulnerabilities, attack vectors, and security best practices.
No mocking - tests against real security implementations.
"""

import base64
import json
import time
from typing import Dict, Any

import pytest
from httpx import AsyncClient, ASGITransport

from app.main import app
from tests.factories import UserFactory


@pytest.mark.security
@pytest.mark.asyncio
class TestAuthenticationSecurity:
    """Security tests for authentication system."""

    @pytest.fixture
    async def async_client(self) -> AsyncClient:
        """Create async client for security testing."""
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as client:
            yield client

    async def test_password_hashing_security(self, async_client: AsyncClient) -> None:
        """Test password hashing uses secure algorithms."""
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Verify password is never returned in response
        response_data = response.json()
        response_str = json.dumps(response_data)
        
        assert "password" not in response_str.lower()
        assert "hashed_password" not in response_str.lower()
        assert user_data["password"] not in response_str

    async def test_sql_injection_protection(self, async_client: AsyncClient) -> None:
        """Test protection against SQL injection attacks."""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; INSERT INTO users (email) VALUES ('<EMAIL>'); --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "admin'/*",
            "' OR 1=1#",
        ]
        
        for payload in sql_injection_payloads:
            # Test in email field
            malicious_data = {
                "email": payload,
                "password": "SecurePass123!"
            }
            
            response = await async_client.post("/api/v1/auth/register", json=malicious_data)
            # Should return validation error, not crash or succeed
            assert response.status_code in [400, 422]
            
            # Test in login
            login_response = await async_client.post("/api/v1/auth/login", json=malicious_data)
            assert login_response.status_code in [400, 401, 422]

    async def test_xss_protection(self, async_client: AsyncClient) -> None:
        """Test protection against XSS attacks."""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
            "<svg onload=alert('xss')>",
        ]
        
        for payload in xss_payloads:
            user_data = {
                "email": f"xss_test_{hash(payload)}@example.com",
                "password": "SecurePass123!",
                "first_name": payload,
                "last_name": payload
            }
            
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            
            if response.status_code == 201:
                # If registration succeeds, verify XSS payload is sanitized
                response_data = response.json()
                user_info = response_data["user"]
                
                # Should not contain raw script tags or javascript
                assert "<script>" not in str(user_info).lower()
                assert "javascript:" not in str(user_info).lower()
                assert "onerror=" not in str(user_info).lower()

    async def test_brute_force_protection(self, async_client: AsyncClient) -> None:
        """Test protection against brute force attacks."""
        # Register a user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Attempt multiple failed logins
        failed_attempts = 0
        for attempt in range(20):
            login_data = {
                "email": user_data["email"],
                "password": f"wrong_password_{attempt}"
            }
            
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            
            if response.status_code == 401:
                failed_attempts += 1
            elif response.status_code == 429:  # Rate limited
                # Rate limiting should kick in
                assert failed_attempts >= 3, "Rate limiting should activate after multiple failures"
                break
            
            # Small delay between attempts
            time.sleep(0.1)

    async def test_jwt_token_security(self, async_client: AsyncClient) -> None:
        """Test JWT token security properties."""
        # Register and login user
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 201
        
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        response_data = login_response.json()
        access_token = response_data["access_token"]
        
        # Verify token structure
        token_parts = access_token.split(".")
        assert len(token_parts) == 3, "JWT should have 3 parts (header.payload.signature)"
        
        # Verify token is not easily decodable without signature verification
        try:
            # Decode header and payload (without verification)
            header = json.loads(base64.urlsafe_b64decode(token_parts[0] + "=="))
            payload = json.loads(base64.urlsafe_b64decode(token_parts[1] + "=="))
            
            # Verify security properties
            assert "alg" in header
            assert header["alg"] != "none", "Algorithm should not be 'none'"
            assert "exp" in payload, "Token should have expiration"
            assert "sub" in payload, "Token should have subject"
            
            # Verify no sensitive data in payload
            payload_str = json.dumps(payload)
            assert "password" not in payload_str.lower()
            assert "hashed_password" not in payload_str.lower()
            
        except Exception:
            # If decoding fails, that's actually good for security
            pass

    async def test_token_tampering_protection(self, async_client: AsyncClient) -> None:
        """Test protection against token tampering."""
        # Get valid token
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 201
        
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        access_token = login_response.json()["access_token"]
        
        # Test various tampering attempts
        tampered_tokens = [
            access_token[:-5] + "XXXXX",  # Modify signature
            access_token.replace(".", "X"),  # Replace dots
            access_token + "extra",  # Append data
            "fake.token.signature",  # Completely fake token
            "",  # Empty token
            "Bearer " + access_token,  # Wrong format
        ]
        
        for tampered_token in tampered_tokens:
            headers = {"Authorization": f"Bearer {tampered_token}"}
            response = await async_client.get("/api/v1/auth/me", headers=headers)
            
            # Should reject tampered tokens
            assert response.status_code in [401, 422], f"Tampered token should be rejected: {tampered_token[:20]}..."

    async def test_sensitive_data_exposure(self, async_client: AsyncClient) -> None:
        """Test that sensitive data is not exposed in responses."""
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        
        # Test registration response
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 201
        
        register_data = register_response.json()
        register_str = json.dumps(register_data)
        
        # Verify sensitive data is not exposed
        sensitive_fields = [
            "password", "hashed_password", "deleted_at", "secret_key",
            "private_key", "salt", "hash", "secret"
        ]
        
        for field in sensitive_fields:
            assert field not in register_str.lower(), f"Sensitive field '{field}' found in response"
        
        # Test login response
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        login_response_data = login_response.json()
        login_str = json.dumps(login_response_data)
        
        for field in sensitive_fields:
            assert field not in login_str.lower(), f"Sensitive field '{field}' found in login response"

    async def test_cors_security(self, async_client: AsyncClient) -> None:
        """Test CORS configuration security."""
        # Test preflight request
        headers = {
            "Origin": "https://malicious-site.com",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Content-Type"
        }
        
        response = await async_client.options("/api/v1/auth/register", headers=headers)
        
        # Check CORS headers
        cors_headers = {k.lower(): v for k, v in response.headers.items()}
        
        # Should not allow arbitrary origins
        if "access-control-allow-origin" in cors_headers:
            allowed_origin = cors_headers["access-control-allow-origin"]
            assert allowed_origin != "*", "CORS should not allow all origins for authenticated endpoints"

    async def test_input_validation_security(self, async_client: AsyncClient) -> None:
        """Test input validation prevents security issues."""
        # Test extremely long inputs
        long_string = "A" * 10000
        
        malicious_inputs = {
            "email": long_string + "@example.com",
            "password": long_string,
            "first_name": long_string,
            "last_name": long_string,
        }
        
        response = await async_client.post("/api/v1/auth/register", json=malicious_inputs)
        # Should reject overly long inputs
        assert response.status_code in [400, 422]
        
        # Test null bytes and special characters
        null_byte_inputs = {
            "email": "test\<EMAIL>",
            "password": "SecurePass123!\x00",
            "first_name": "Test\x00User",
        }
        
        response = await async_client.post("/api/v1/auth/register", json=null_byte_inputs)
        # Should handle null bytes safely
        assert response.status_code in [400, 422] or response.status_code == 201

    async def test_timing_attack_protection(self, async_client: AsyncClient) -> None:
        """Test protection against timing attacks."""
        # Register a user
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Measure timing for valid vs invalid emails
        valid_times = []
        invalid_times = []
        
        for _ in range(10):
            # Valid email, wrong password
            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/login", json={
                "email": user_data["email"],
                "password": "wrong_password"
            })
            end_time = time.perf_counter()
            valid_times.append(end_time - start_time)
            assert response.status_code == 401
            
            # Invalid email
            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "wrong_password"
            })
            end_time = time.perf_counter()
            invalid_times.append(end_time - start_time)
            assert response.status_code == 401
        
        # Timing should be similar to prevent user enumeration
        avg_valid_time = sum(valid_times) / len(valid_times)
        avg_invalid_time = sum(invalid_times) / len(invalid_times)
        
        # Allow some variance but should be reasonably close
        time_ratio = max(avg_valid_time, avg_invalid_time) / min(avg_valid_time, avg_invalid_time)
        assert time_ratio < 3.0, f"Timing difference too large: {time_ratio:.2f}x"

    async def test_session_management_security(self, async_client: AsyncClient) -> None:
        """Test session management security features."""
        # Register and login user
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 201

        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200

        tokens = login_response.json()
        access_token = tokens["access_token"]
        refresh_token = tokens["refresh_token"]

        # Test access token works
        headers = {"Authorization": f"Bearer {access_token}"}
        profile_response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert profile_response.status_code == 200

        # Test refresh token rotation
        refresh_data = {"refresh_token": refresh_token}
        refresh_response = await async_client.post("/api/v1/auth/refresh", json=refresh_data)
        assert refresh_response.status_code == 200

        new_tokens = refresh_response.json()
        assert new_tokens["access_token"] != access_token
        assert new_tokens["refresh_token"] != refresh_token

    async def test_password_policy_enforcement(self, async_client: AsyncClient) -> None:
        """Test password policy enforcement."""
        weak_passwords = [
            "123",           # Too short
            "password",      # Common password
            "12345678",      # Only numbers
            "abcdefgh",      # Only letters
            "PASSWORD",      # Only uppercase
        ]

        for weak_password in weak_passwords:
            user_data = {
                "email": f"weak_{hash(weak_password)}@example.com",
                "password": weak_password
            }

            response = await async_client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 422, f"Weak password '{weak_password}' should be rejected"
