Feature: User Profile Management
  As a registered user
  I want to manage my profile information
  So that I can keep my training data accurate and up-to-date

  Background:
    Given the Forge Protocol API is running
    And the database is clean
    And I have a registered user account

  Scenario: View complete user profile
    Given I am logged in with a complete profile
    When I request my user profile
    Then I should see my complete profile information
    And the response should include my email
    And the response should include my first name
    And the response should include my last name
    And the response should include my training experience
    And the response should include my account status
    And the response should not include sensitive information

  Scenario: View minimal user profile
    Given I am logged in with a minimal profile
    When I request my user profile
    Then I should see my basic profile information
    And optional fields should be null
    And the response should still be complete for mobile display

  Scenario: Update profile with all fields
    Given I am logged in
    When I update my profile with:
      | field                      | value    |
      | first_name                 | Updated  |
      | last_name                  | Name     |
      | training_experience_years  | 5        |
    Then my profile should be updated successfully
    And I should see the updated information
    And the updated_at timestamp should be recent

  Scenario: Update profile with partial fields
    Given I am logged in
    When I update only my first name to "New<PERSON><PERSON>"
    Then my first name should be updated
    And other fields should remain unchanged
    And the update should be successful

  Scenario: Update profile with invalid training experience
    Given I am logged in
    When I try to update my training experience to -1 years
    Then I should receive a validation error
    And my profile should not be changed
    And the error should explain the valid range

  Scenario: Update profile with special characters
    Given I am logged in
    When I update my profile with:
      | field      | value           |
      | first_name | José            |
      | last_name  | O'Connor-Smith  |
    Then my profile should be updated successfully
    And the special characters should be preserved
    And the profile should display correctly

  Scenario: Clear profile fields
    Given I am logged in with a complete profile
    When I update my profile with:
      | field      | value |
      | first_name |       |
      | last_name  |       |
    Then the fields should be set to null
    And my profile should be updated successfully
    And the mobile app should handle null values gracefully

  Scenario: Profile update with concurrent requests
    Given I am logged in
    When I make multiple profile update requests simultaneously
    Then all updates should be processed
    And the final state should be consistent
    And no data should be corrupted

  Scenario: Profile update performance for mobile
    Given I am logged in
    When I update my profile
    Then the response should be received within 300ms
    And the response payload should be under 1KB
    And the mobile app should update smoothly

  Scenario: Profile validation edge cases
    Given I am logged in
    When I try to update my profile with edge case values:
      | field                      | value | expected_result |
      | training_experience_years  | 0     | success         |
      | training_experience_years  | 50    | success         |
      | training_experience_years  | 51    | validation_error|
      | first_name                 | A     | success         |
      | first_name                 | 100chars | success     |
      | first_name                 | 101chars | validation_error |
    Then I should get the expected result for each case

  Scenario: Profile update with authentication token expiry
    Given I am logged in
    And my access token is about to expire
    When I update my profile
    Then I should receive an authentication error
    And I should be prompted to refresh my token
    And the mobile app should handle this gracefully

  Scenario: Profile update without authentication
    Given I am not logged in
    When I try to update a profile
    Then I should receive an authentication error
    And no profile data should be changed
    And the error should be clear for mobile display

  Scenario: Profile data consistency after update
    Given I am logged in
    When I update my profile
    And I immediately request my profile again
    Then the data should be consistent
    And all updates should be reflected
    And there should be no race conditions

  Scenario: Profile update with network interruption simulation
    Given I am logged in
    When I start a profile update
    And the network connection is interrupted
    Then the mobile app should handle the interruption gracefully
    And the user should be informed of the status
    And retry mechanisms should work properly

  Scenario: Profile update audit trail
    Given I am logged in
    When I update my profile multiple times
    Then each update should be tracked
    And the updated_at timestamp should reflect the latest change
    And the system should maintain data integrity

  Scenario: Profile update with maximum field lengths
    Given I am logged in
    When I update my profile with maximum length values
    Then the update should succeed if within limits
    And the mobile app should display the data correctly
    And text truncation should work properly if needed

  Scenario: Profile update rollback on validation failure
    Given I am logged in
    When I update my profile with mixed valid and invalid data
    Then the entire update should be rejected
    And no partial updates should be applied
    And my original profile should remain unchanged

  Scenario: Profile caching behavior
    Given I am logged in
    When I update my profile
    And I request my profile from different endpoints
    Then all endpoints should return consistent data
    And caching should not show stale information
    And the mobile app should see immediate updates
