Feature: API Health Monitoring
  As a system administrator or mobile app
  I want to monitor the health of the Forge Protocol API
  So that I can ensure reliable service for users

  Background:
    Given the Forge Protocol API is running

  Scenario: Basic health check
    When I check the basic health endpoint
    Then I should receive a healthy status
    And the response should include service information
    And the response should include timestamp
    And the response should be fast (under 100ms)

  Scenario: Database health check
    Given the database is available
    When I check the database health endpoint
    Then I should receive a healthy database status
    And the response should include connection information
    And the database response time should be acceptable

  Scenario: Database health check when database is unavailable
    Given the database is unavailable
    When I check the database health endpoint
    Then I should receive an unhealthy database status
    And the response should include error information
    And the API should still respond (not crash)

  Scenario: Detailed health check with all components
    Given all system components are available
    When I check the detailed health endpoint
    Then I should receive overall healthy status
    And the response should include database status
    And the response should include cache status
    And the response should include system metrics

  Scenario: Detailed health check with degraded service
    Given the cache service is unavailable
    When I check the detailed health endpoint
    Then I should receive degraded status
    And the response should indicate which service is down
    And the core functionality should still be available

  Scenario: Health check response format for mobile
    When I check any health endpoint
    Then the response should be in JSON format
    And the response should have consistent structure
    And the response should be minimal for mobile bandwidth

  Scenario: Health check performance under load
    Given the system is under normal load
    When I check the health endpoint repeatedly
    Then all responses should be fast
    And the health check should not impact performance
    And the responses should remain consistent

  Scenario: Health check authentication requirements
    When I check the basic health endpoint
    Then no authentication should be required
    And the endpoint should be publicly accessible

  Scenario: Health check during system startup
    Given the system is starting up
    When I check the health endpoint
    Then I should receive appropriate startup status
    And the response should indicate initialization progress

  Scenario: Health check during system shutdown
    Given the system is shutting down gracefully
    When I check the health endpoint
    Then I should receive appropriate shutdown status
    And the response should indicate shutdown progress

  Scenario: Health check with version information
    When I check the health endpoint
    Then the response should include API version
    And the response should include build information
    And the response should include environment details

  Scenario: Health check monitoring integration
    When external monitoring systems check health
    Then the responses should be machine-readable
    And the status codes should follow HTTP standards
    And the format should support monitoring tools
