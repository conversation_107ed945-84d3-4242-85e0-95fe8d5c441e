Feature: User Registration
  As a new user
  I want to register for an account
  So that I can access the RP training system

  Background:
    Given the RP training API is running
    And the database is clean

  Scenario: Successful user registration
    Given I am a new user
    When I register with valid credentials:
      | email              | password        | first_name | last_name |
      | <EMAIL>   | SecurePass123!  | <PERSON>       | Doe       |
    Then I should receive a successful registration response
    And I should receive an access token
    And I should receive a refresh token
    And my user profile should be created

  Scenario: Registration with invalid email
    Given I am a new user
    When I register with invalid email "invalid-email"
    Then I should receive a validation error
    And the error should mention "Invalid email format"

  Scenario: Registration with weak password
    Given I am a new user
    When I register with weak password "weak"
    Then I should receive a validation error
    And the error should mention "Password must be at least 8 characters long"

  Scenario: Registration with existing email
    Given a user already exists with email "<EMAIL>"
    When I register with email "<EMAIL>"
    Then I should receive a conflict error
    And the error should mention "already exists"

  Scenario: User login after registration
    Given I have registered with:
      | email              | password        | first_name | last_name |
      | <EMAIL>   | SecurePass123!  | <PERSON>       | <PERSON>     |
    When I login with email "<EMAIL>" and password "SecurePass123!"
    Then I should receive a successful login response
    And I should receive an access token
    And my user profile should be returned
