Feature: User Registration
  As a fitness enthusiast
  I want to register for the Forge Protocol app
  So that I can start tracking my training progress with evidence-based methods

  Background:
    Given the RP training API is running
    And the database is clean

  Scenario: Successful user registration
    Given I am a new user
    When I register with valid credentials:
      | email              | password        | first_name | last_name |
      | <EMAIL>   | SecurePass123!  | John       | Doe       |
    Then I should receive a successful registration response
    And I should receive an access token
    And I should receive a refresh token
    And my user profile should be created

  Scenario: Registration with invalid email
    Given I am a new user
    When I register with invalid email "invalid-email"
    Then I should receive a validation error
    And the error should mention "Invalid email format"

  Scenario: Registration with weak password
    Given I am a new user
    When I register with weak password "weak"
    Then I should receive a validation error
    And the error should mention "Password must be at least 8 characters long"

  Scenario: Registration with existing email
    Given a user already exists with email "<EMAIL>"
    When I register with email "<EMAIL>"
    Then I should receive a conflict error
    And the error should mention "already exists"

  Scenario: User login after registration
    Given I have registered with:
      | email              | password        | first_name | last_name |
      | <EMAIL>   | SecurePass123!  | Jane       | <PERSON>     |
    When I login with email "<EMAIL>" and password "SecurePass123!"
    Then I should receive a successful login response
    And I should receive an access token
    And my user profile should be returned

  Sc<PERSON>rio: Registration with training experience
    Given I am a new user
    When I register with training experience of 3 years
    Then I should receive a successful registration response
    And my training experience should be recorded as 3 years

  Scenario: Registration with minimal profile data
    Given I am a new user
    When I register with only email and password
    Then I should receive a successful registration response
    And my profile should have null values for optional fields

  Scenario: Mobile-optimized registration response
    Given I am a new user
    When I register with valid credentials
    Then the response should be optimized for mobile clients
    And the response should include compact user data
    And the response should not include sensitive information

  Scenario: Registration with special characters in names
    Given I am a new user
    When I register with name containing special characters
    Then I should receive a successful registration response
    And my name should be stored correctly

  Scenario: Registration with various training experience levels
    Given I am a new user
    When I register with training experience of <experience> years
    Then I should receive a successful registration response

    Examples:
      | experience |
      | 0          |
      | 1          |
      | 5          |
      | 10         |
      | 15         |

  Scenario: Concurrent user registrations
    Given multiple users are registering simultaneously
    When 3 users register with different emails at the same time
    Then all registrations should succeed
    And each user should receive unique tokens
