Feature: User Authentication
  As a registered user
  I want to authenticate with the Forge Protocol app
  So that I can securely access my training data

  Background:
    Given the Forge Protocol API is running
    And the database is clean
    And I have a registered user account

  Scenario: Successful login with valid credentials
    Given I have valid login credentials
    When I login with my email and password
    Then I should receive a successful login response
    And I should receive an access token
    And I should receive a refresh token
    And the tokens should be valid for API access

  Scenario: Login with invalid email
    Given I have an invalid email address
    When I attempt to login
    Then I should receive an authentication error
    And the error should mention "Invalid email or password"

  Scenario: Login with invalid password
    Given I have a valid email but wrong password
    When I attempt to login
    Then I should receive an authentication error
    And the error should mention "Invalid email or password"

  Scenario: Login with inactive account
    Given my account has been deactivated
    When I attempt to login
    Then I should receive an authentication error
    And the error should mention "account is inactive"

  Scenario: Token refresh functionality
    Given I have a valid refresh token
    When I request a new access token
    Then I should receive a new access token
    And the new token should be valid for API access

  Scenario: Access protected endpoint with valid token
    Given I am logged in with a valid access token
    When I access my user profile
    Then I should receive my profile data
    And the response should include my user information

  Scenario: Access protected endpoint with expired token
    Given I have an expired access token
    When I access my user profile
    Then I should receive an authentication error
    And the error should mention "token expired"

  Scenario: Access protected endpoint without token
    Given I am not authenticated
    When I access my user profile
    Then I should receive an authentication error
    And the error should mention "authentication required"

  Scenario: Logout functionality
    Given I am logged in
    When I logout
    Then my tokens should be invalidated
    And I should not be able to access protected endpoints

  Scenario: Multiple concurrent logins
    Given I have valid credentials
    When I login from multiple devices simultaneously
    Then all logins should succeed
    And each device should receive unique tokens

  Scenario: Login with case-insensitive email
    Given I registered with email "<EMAIL>"
    When I login with email "<EMAIL>"
    Then I should receive a successful login response

  Scenario: Password security validation during login
    Given I have valid credentials
    When I login successfully
    Then my password should never be returned in the response
    And only the hashed password should be stored

  Scenario: Rate limiting protection
    Given I have invalid credentials
    When I attempt to login multiple times rapidly
    Then the system should implement rate limiting
    And excessive attempts should be blocked

  Scenario: Mobile-optimized authentication flow
    Given I am using a mobile client
    When I complete the authentication flow
    Then the responses should be optimized for mobile
    And the token format should be mobile-friendly
    And the payload should be minimal but complete
