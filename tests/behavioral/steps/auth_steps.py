"""
Behavioral test steps for authentication scenarios.

Implements step definitions for user registration and login features.
"""

import json
from behave import given, when, then
from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.main import app


@given('the RP training API is running')
def step_api_running(context):
    """Ensure the API is available for testing."""
    context.client = TestClient(app)
    # Test basic connectivity
    response = context.client.get("/api/v1/health")
    assert response.status_code == 200


@given('the database is clean')
def step_database_clean(context):
    """Ensure database is in clean state for testing."""
    # In a real implementation, this would clean the test database
    # For now, we'll use in-memory database for each test
    pass


@given('I am a new user')
def step_new_user(context):
    """Set up context for a new user."""
    context.user_data = {}
    context.response = None
    context.tokens = {}


@given('a user already exists with email "{email}"')
def step_existing_user(context, email):
    """Create an existing user for conflict testing."""
    user_data = {
        "email": email,
        "password": "ExistingPass123!",
        "first_name": "Existing",
        "last_name": "User"
    }
    
    response = context.client.post("/api/v1/auth/register", json=user_data)
    # We expect this to succeed for setup
    assert response.status_code == 201


@given('I have registered with')
def step_registered_user(context):
    """Register a user for subsequent login testing."""
    for row in context.table:
        user_data = {
            "email": row["email"],
            "password": row["password"],
            "first_name": row["first_name"],
            "last_name": row["last_name"]
        }
        
        response = context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        context.user_data = user_data
        context.registration_response = response.json()


@when('I register with valid credentials')
def step_register_valid(context):
    """Register with valid user credentials."""
    for row in context.table:
        user_data = {
            "email": row["email"],
            "password": row["password"],
            "first_name": row["first_name"],
            "last_name": row["last_name"]
        }
        
        context.user_data = user_data
        context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with invalid email "{email}"')
def step_register_invalid_email(context, email):
    """Register with invalid email format."""
    user_data = {
        "email": email,
        "password": "SecurePass123!",
        "first_name": "Test",
        "last_name": "User"
    }
    
    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with weak password "{password}"')
def step_register_weak_password(context, password):
    """Register with weak password."""
    user_data = {
        "email": "<EMAIL>",
        "password": password,
        "first_name": "Test",
        "last_name": "User"
    }
    
    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with email "{email}"')
def step_register_with_email(context, email):
    """Register with specific email (for conflict testing)."""
    user_data = {
        "email": email,
        "password": "SecurePass123!",
        "first_name": "Test",
        "last_name": "User"
    }
    
    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I login with email "{email}" and password "{password}"')
def step_login(context, email, password):
    """Login with email and password."""
    login_data = {
        "email": email,
        "password": password
    }
    
    context.response = context.client.post("/api/v1/auth/login", json=login_data)


@then('I should receive a successful registration response')
def step_successful_registration(context):
    """Verify successful registration response."""
    assert context.response.status_code == 201
    response_data = context.response.json()
    assert "user" in response_data
    assert "access_token" in response_data
    assert "refresh_token" in response_data


@then('I should receive a successful login response')
def step_successful_login(context):
    """Verify successful login response."""
    assert context.response.status_code == 200
    response_data = context.response.json()
    assert "user" in response_data
    assert "access_token" in response_data
    assert "refresh_token" in response_data


@then('I should receive an access token')
def step_access_token(context):
    """Verify access token is present."""
    response_data = context.response.json()
    assert "access_token" in response_data
    assert response_data["access_token"] is not None
    assert len(response_data["access_token"]) > 0
    context.tokens["access"] = response_data["access_token"]


@then('I should receive a refresh token')
def step_refresh_token(context):
    """Verify refresh token is present."""
    response_data = context.response.json()
    assert "refresh_token" in response_data
    assert response_data["refresh_token"] is not None
    assert len(response_data["refresh_token"]) > 0
    context.tokens["refresh"] = response_data["refresh_token"]


@then('my user profile should be created')
def step_profile_created(context):
    """Verify user profile is created correctly."""
    response_data = context.response.json()
    user = response_data["user"]
    
    assert user["email"] == context.user_data["email"]
    assert user["first_name"] == context.user_data["first_name"]
    assert user["last_name"] == context.user_data["last_name"]
    assert user["is_active"] is True
    assert user["is_verified"] is False


@then('my user profile should be returned')
def step_profile_returned(context):
    """Verify user profile is returned in response."""
    response_data = context.response.json()
    user = response_data["user"]
    
    assert user["email"] == context.user_data["email"]
    assert user["first_name"] == context.user_data["first_name"]
    assert user["last_name"] == context.user_data["last_name"]


@then('I should receive a validation error')
def step_validation_error(context):
    """Verify validation error response."""
    assert context.response.status_code == 422


@then('I should receive a conflict error')
def step_conflict_error(context):
    """Verify conflict error response."""
    assert context.response.status_code == 409


@then('the error should mention "{message}"')
def step_error_message(context, message):
    """Verify error message contains expected text."""
    response_data = context.response.json()
    error_detail = response_data.get("detail", "")
    assert message.lower() in error_detail.lower()
