"""Behavioral test steps for authentication scenarios.

Implements step definitions for user registration and login features.
"""

import json
from concurrent.futures import ThreadPoolExecutor

from behave import given, then, when
from fastapi.testclient import TestClient

from app.main import app
from tests.factories import UserFactory


@given("the RP training API is running")
def step_api_running(context):
    """Ensure the API is available for testing."""
    context.client = TestClient(app)
    # Test basic connectivity
    response = context.client.get("/api/v1/health")
    assert response.status_code == 200


@given("the database is clean")
def step_database_clean(context):
    """Ensure database is in clean state for testing."""
    # In a real implementation, this would clean the test database
    # For now, we'll use in-memory database for each test


@given("I am a new user")
def step_new_user(context):
    """Set up context for a new user."""
    context.user_data = {}
    context.response = None
    context.tokens = {}


@given('a user already exists with email "{email}"')
def step_existing_user(context, email):
    """Create an existing user for conflict testing."""
    user_data = {
        "email": email,
        "password": "ExistingPass123!",
        "first_name": "Existing",
        "last_name": "User",
    }

    response = context.client.post("/api/v1/auth/register", json=user_data)
    # We expect this to succeed for setup
    assert response.status_code == 201


@given("I have registered with")
def step_registered_user(context):
    """Register a user for subsequent login testing."""
    for row in context.table:
        user_data = {
            "email": row["email"],
            "password": row["password"],
            "first_name": row["first_name"],
            "last_name": row["last_name"],
        }

        response = context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201

        context.user_data = user_data
        context.registration_response = response.json()


@when("I register with valid credentials")
def step_register_valid(context):
    """Register with valid user credentials."""
    for row in context.table:
        user_data = {
            "email": row["email"],
            "password": row["password"],
            "first_name": row["first_name"],
            "last_name": row["last_name"],
        }

        context.user_data = user_data
        context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with invalid email "{email}"')
def step_register_invalid_email(context, email):
    """Register with invalid email format."""
    user_data = {
        "email": email,
        "password": "SecurePass123!",
        "first_name": "Test",
        "last_name": "User",
    }

    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with weak password "{password}"')
def step_register_weak_password(context, password):
    """Register with weak password."""
    user_data = {
        "email": "<EMAIL>",
        "password": password,
        "first_name": "Test",
        "last_name": "User",
    }

    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I register with email "{email}"')
def step_register_with_email(context, email):
    """Register with specific email (for conflict testing)."""
    user_data = {
        "email": email,
        "password": "SecurePass123!",
        "first_name": "Test",
        "last_name": "User",
    }

    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@when('I login with email "{email}" and password "{password}"')
def step_login(context, email, password):
    """Login with email and password."""
    login_data = {"email": email, "password": password}

    context.response = context.client.post("/api/v1/auth/login", json=login_data)


@then("I should receive a successful registration response")
def step_successful_registration(context):
    """Verify successful registration response."""
    assert context.response.status_code == 201
    response_data = context.response.json()
    assert "user" in response_data
    assert "access_token" in response_data
    assert "refresh_token" in response_data


@then("I should receive a successful login response")
def step_successful_login(context):
    """Verify successful login response."""
    assert context.response.status_code == 200
    response_data = context.response.json()
    assert "user" in response_data
    assert "access_token" in response_data
    assert "refresh_token" in response_data


@then("I should receive an access token")
def step_access_token(context):
    """Verify access token is present."""
    response_data = context.response.json()
    assert "access_token" in response_data
    assert response_data["access_token"] is not None
    assert len(response_data["access_token"]) > 0
    context.tokens["access"] = response_data["access_token"]


@then("I should receive a refresh token")
def step_refresh_token(context):
    """Verify refresh token is present."""
    response_data = context.response.json()
    assert "refresh_token" in response_data
    assert response_data["refresh_token"] is not None
    assert len(response_data["refresh_token"]) > 0
    context.tokens["refresh"] = response_data["refresh_token"]


@then("my user profile should be created")
def step_profile_created(context):
    """Verify user profile is created correctly."""
    response_data = context.response.json()
    user = response_data["user"]

    assert user["email"] == context.user_data["email"]
    assert user["first_name"] == context.user_data["first_name"]
    assert user["last_name"] == context.user_data["last_name"]
    assert user["is_active"] is True
    assert user["is_verified"] is False


@then("my user profile should be returned")
def step_profile_returned(context):
    """Verify user profile is returned in response."""
    response_data = context.response.json()
    user = response_data["user"]

    assert user["email"] == context.user_data["email"]
    assert user["first_name"] == context.user_data["first_name"]
    assert user["last_name"] == context.user_data["last_name"]


@then("I should receive a validation error")
def step_validation_error(context):
    """Verify validation error response."""
    assert context.response.status_code == 422


@then("I should receive a conflict error")
def step_conflict_error(context):
    """Verify conflict error response."""
    assert context.response.status_code == 409


@then('the error should mention "{message}"')
def step_error_message(context, message):
    """Verify error message contains expected text."""
    response_data = context.response.json()
    error_detail = response_data.get("detail", "")
    assert message.lower() in error_detail.lower()


# Enhanced step definitions for comprehensive testing


@given("the database is clean")
def step_database_clean(context):
    """Ensure database is in clean state for testing."""
    # In a real implementation, this would clean test database
    context.registered_users = {}
    context.tokens = {}


@when("I register with training experience of {experience:d} years")
def step_register_with_experience(context, experience):
    """Register user with specific training experience."""
    user_data = UserFactory.create_user_data(training_experience_years=experience)
    context.response = context.client.post("/api/v1/auth/register", json=user_data)


@then("my training experience should be recorded as {experience:d} years")
def step_verify_training_experience(context, experience):
    """Verify training experience is correctly recorded."""
    response_data = context.response.json()
    user_data = response_data["user"]
    assert user_data["training_experience_years"] == experience


@when("I register with only email and password")
def step_register_minimal(context):
    """Register with minimal required data."""
    minimal_data = {"email": "<EMAIL>", "password": "SecurePass123!"}
    context.response = context.client.post("/api/v1/auth/register", json=minimal_data)


@then("my profile should have null values for optional fields")
def step_verify_minimal_profile(context):
    """Verify optional fields are null in minimal profile."""
    response_data = context.response.json()
    user_data = response_data["user"]
    assert user_data["first_name"] is None
    assert user_data["last_name"] is None
    assert user_data["training_experience_years"] is None


@when("I register with name containing special characters")
def step_register_special_chars(context):
    """Register with special characters in name."""
    special_data = {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "first_name": "José",
        "last_name": "O'Connor",
    }
    context.response = context.client.post("/api/v1/auth/register", json=special_data)


@then("my name should be stored correctly")
def step_verify_special_chars(context):
    """Verify special characters are stored correctly."""
    response_data = context.response.json()
    user_data = response_data["user"]
    assert user_data["first_name"] == "José"
    assert user_data["last_name"] == "O'Connor"


@given("multiple users are registering simultaneously")
def step_setup_concurrent_users(context):
    """Setup for concurrent user registration test."""
    context.concurrent_users = [
        UserFactory.create_user_data(email=f"user{i}@example.com") for i in range(3)
    ]


@when("{count:d} users register with different emails at the same time")
def step_concurrent_registration(context, count):
    """Perform concurrent user registrations."""

    def register_user(user_data):
        return context.client.post("/api/v1/auth/register", json=user_data)

    with ThreadPoolExecutor(max_workers=count) as executor:
        futures = [
            executor.submit(register_user, user_data)
            for user_data in context.concurrent_users[:count]
        ]
        context.concurrent_responses = [future.result() for future in futures]


@then("all registrations should succeed")
def step_verify_concurrent_success(context):
    """Verify all concurrent registrations succeeded."""
    for response in context.concurrent_responses:
        assert response.status_code == 201


@then("each user should receive unique tokens")
def step_verify_unique_tokens(context):
    """Verify each user receives unique tokens."""
    tokens = set()
    for response in context.concurrent_responses:
        data = response.json()
        access_token = data["access_token"]
        refresh_token = data["refresh_token"]

        assert access_token not in tokens
        assert refresh_token not in tokens

        tokens.add(access_token)
        tokens.add(refresh_token)


@then("the response should be optimized for mobile clients")
def step_verify_mobile_optimization(context):
    """Verify response is optimized for mobile."""
    response_data = context.response.json()

    # Check response structure is mobile-friendly
    assert "user" in response_data
    assert "access_token" in response_data
    assert "refresh_token" in response_data
    assert "token_type" in response_data

    # Verify no sensitive data is exposed
    user_data = response_data["user"]
    assert "hashed_password" not in user_data
    assert "deleted_at" not in user_data


@then("the response should not include sensitive information")
def step_verify_no_sensitive_data(context):
    """Verify no sensitive information is in response."""
    response_data = context.response.json()
    response_str = json.dumps(response_data)

    # Check that sensitive fields are not present
    assert "hashed_password" not in response_str
    assert "password" not in response_str
    assert "deleted_at" not in response_str
