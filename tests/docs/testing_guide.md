# RP Training API Testing Guide

This comprehensive guide covers all aspects of testing in the RP Training API project, from unit tests to behavior-driven development (BDD) scenarios.

## 📊 Testing Overview

The RP Training API employs a **multi-layered testing strategy** that ensures comprehensive coverage and quality assurance:

### **Testing Pyramid**

```
                    🔺 BDD Tests (E2E)
                   /                  \
                  /   Integration Tests \
                 /                      \
                /      Unit Tests        \
               /________________________\
```

### **Current Test Metrics**

- **Total Coverage**: **88.24%** 
- **Total Tests**: **255+ comprehensive tests**
- **Test Categories**: **14 distinct test markers**
- **BDD Scenarios**: **100+ behavior scenarios**

### **Test Categories**

| Test Type | Count | Coverage | Purpose |
|-----------|-------|----------|---------|
| **Unit Tests** | 240+ | 88.24% | Component isolation testing |
| **Integration Tests** | 15+ | Cross-layer | Component interaction testing |
| **BDD Tests** | 100+ | End-to-end | User behavior validation |

## 🧪 Unit Testing

Unit tests form the foundation of our testing strategy, providing fast feedback and ensuring individual components work correctly in isolation.

### **Directory Structure**

```
tests/unit/
├── __init__.py
├── application/           # Application layer tests
│   ├── dto/              # Data Transfer Object tests
│   └── use_cases/        # Business logic tests
├── domain/               # Domain layer tests
│   ├── entities/         # Entity tests
│   ├── services/         # Domain service tests
│   └── repositories/     # Repository interface tests
├── infrastructure/       # Infrastructure layer tests
│   ├── auth/            # Authentication tests
│   ├── database/        # Database tests
│   └── repositories/    # Repository implementation tests
├── presentation/         # Presentation layer tests
│   ├── api/             # API endpoint tests
│   └── schemas/         # Schema validation tests
├── test_config.py       # Configuration tests
└── test_main.py         # Main application tests
```

### **Running Unit Tests**

```bash
# Run all unit tests
python -m pytest tests/unit/

# Run with coverage
python -m pytest tests/unit/ --cov=app --cov-report=term-missing

# Run specific test file
python -m pytest tests/unit/domain/test_user_entity.py

# Run with verbose output
python -m pytest tests/unit/ -v

# Run tests matching pattern
python -m pytest tests/unit/ -k "test_user"
```

### **Unit Test Examples**

#### **Domain Entity Testing**

```python
# tests/unit/domain/test_user_entity.py
import pytest
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import WeakPasswordError

class TestUserEntity:
    def test_create_user_with_valid_data(self):
        """Test user creation with valid data."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe"
        )
        
        assert user.email == "<EMAIL>"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.is_active is True
        assert user.hashed_password != "SecurePass123!"  # Should be hashed
    
    def test_create_user_with_weak_password(self):
        """Test user creation with weak password raises exception."""
        with pytest.raises(WeakPasswordError):
            User.create(
                email="<EMAIL>",
                password="weak",
                first_name="John",
                last_name="Doe"
            )
```

#### **Use Case Testing**

```python
# tests/unit/application/use_cases/test_register_user.py
import pytest
from unittest.mock import AsyncMock
from app.application.use_cases.register_user import RegisterUserUseCase
from app.domain.exceptions.auth_exceptions import UserAlreadyExistsError

class TestRegisterUserUseCase:
    @pytest.fixture
    def mock_user_repository(self):
        return AsyncMock()
    
    @pytest.fixture
    def mock_password_handler(self):
        return AsyncMock()
    
    @pytest.fixture
    def use_case(self, mock_user_repository, mock_password_handler):
        return RegisterUserUseCase(
            user_repository=mock_user_repository,
            password_handler=mock_password_handler
        )
    
    async def test_register_user_success(self, use_case, mock_user_repository, mock_password_handler):
        """Test successful user registration."""
        # Setup mocks
        mock_user_repository.exists_by_email.return_value = False
        mock_password_handler.hash_password.return_value = "hashed_password"
        mock_user_repository.create.return_value = None
        
        # Execute
        result = await use_case.execute(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe"
        )
        
        # Verify
        assert result.user.email == "<EMAIL>"
        mock_user_repository.create.assert_called_once()
```

### **Test Fixtures and Factories**

We use factories to create consistent test data:

```python
# tests/factories.py
import factory
from faker import Faker
from app.domain.entities.user import User

fake = Faker()

class UserFactory:
    @staticmethod
    def create_user_data(**overrides):
        """Create user data for testing."""
        data = {
            "email": fake.email(),
            "password": "SecurePass123!",
            "first_name": fake.first_name(),
            "last_name": fake.last_name(),
            "training_experience_years": fake.random_int(min=0, max=10)
        }
        data.update(overrides)
        return data
    
    @staticmethod
    def create_user_entity(**overrides):
        """Create user entity for testing."""
        data = UserFactory.create_user_data(**overrides)
        return User.create(**data)
```

## 🔗 Integration Testing

Integration tests validate that different components work together correctly, testing the interactions between layers and external dependencies.

### **Directory Structure**

```
tests/integration/
├── __init__.py
├── test_api_endpoints_comprehensive.py    # Complete API testing
├── test_cross_layer_integration.py        # Layer interaction tests
├── test_database_operations.py            # Database integration
├── test_real_world_scenarios.py           # User journey tests
├── test_service_integration.py            # Service integration
└── test_api_coverage_boost.py            # Coverage enhancement tests
```

### **Running Integration Tests**

```bash
# Run all integration tests
python -m pytest tests/integration/

# Run specific integration test file
python -m pytest tests/integration/test_api_endpoints_comprehensive.py

# Run with database setup
python -m pytest tests/integration/ --setup-show

# Run integration tests with coverage
python -m pytest tests/integration/ --cov=app
```

### **Integration Test Examples**

#### **API Endpoint Integration**

```python
# tests/integration/test_api_endpoints_comprehensive.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.integration
@pytest.mark.api
class TestAuthAPIEndpoints:
    async def test_user_registration_flow(self):
        """Test complete user registration flow."""
        async with AsyncClient(app=app, base_url="http://testserver") as client:
            # Register user
            user_data = {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "first_name": "Integration",
                "last_name": "Test",
                "training_experience_years": 2
            }
            
            response = await client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 201
            
            # Verify response structure
            data = response.json()
            assert "user" in data
            assert "access_token" in data
            assert data["user"]["email"] == user_data["email"]
```

#### **Cross-Layer Integration**

```python
# tests/integration/test_cross_layer_integration.py
@pytest.mark.integration
@pytest.mark.cross_layer
class TestCrossLayerIntegration:
    async def test_complete_user_creation_flow(self):
        """Test user creation from API to database."""
        # This test validates the complete flow:
        # API → Use Case → Domain Service → Repository → Database
        
        async with AsyncClient(app=app, base_url="http://testserver") as client:
            user_data = UserFactory.create_user_data()
            
            # Make API request
            response = await client.post("/api/v1/auth/register", json=user_data)
            
            # Verify API response
            assert response.status_code == 201
            
            # Verify data persisted to database
            # (This would check actual database state)
```

### **Database Integration Testing**

```python
# tests/integration/test_database_operations.py
@pytest.mark.integration
@pytest.mark.database
class TestDatabaseOperations:
    async def test_user_repository_crud_operations(self):
        """Test complete CRUD operations on user repository."""
        # Create
        user_entity = UserFactory.create_user_entity()
        created_user = await user_repository.create(user_entity)
        assert created_user.id is not None
        
        # Read
        retrieved_user = await user_repository.get_by_id(created_user.id)
        assert retrieved_user.email == user_entity.email
        
        # Update
        retrieved_user.first_name = "Updated"
        updated_user = await user_repository.update(retrieved_user)
        assert updated_user.first_name == "Updated"
        
        # Delete
        deleted = await user_repository.delete(created_user.id)
        assert deleted is True
```

## 🎭 Behavior-Driven Development (BDD) Testing

BDD tests provide human-readable acceptance criteria and validate complete user workflows using the Gherkin language.

### **BDD Framework Structure**

```
features/
├── environment.py                      # Test environment setup
├── steps/                             # Step definitions
│   ├── authentication_steps.py        # Authentication workflows
│   ├── profile_steps.py              # Profile management
│   ├── health_monitoring_steps.py    # Health monitoring
│   └── error_performance_steps.py    # Error handling & performance
├── user_authentication.feature        # Authentication scenarios
├── user_profile_management.feature    # Profile scenarios
├── api_health_monitoring.feature      # Health monitoring scenarios
├── error_handling_edge_cases.feature  # Error handling scenarios
├── performance_security.feature       # Performance & security scenarios
└── README.md                          # BDD documentation
```

### **Running BDD Tests**

```bash
# Run all BDD tests
python run_bdd_tests.py

# Run specific feature
python run_bdd_tests.py --features user_authentication.feature

# Run by tags
python run_bdd_tests.py --tags @authentication
python run_bdd_tests.py --tags @smoke
python run_bdd_tests.py --tags "@critical and @api"

# Run with verbose output
python run_bdd_tests.py --verbose --no-capture

# Generate reports
python run_bdd_tests.py --junit
```

### **BDD Test Examples**

#### **Feature File Example**

```gherkin
# features/user_authentication.feature
Feature: User Authentication
  As a fitness enthusiast
  I want to register and authenticate with the RP Training API
  So that I can access personalized training features

  @authentication @registration @smoke
  Scenario: Successful user registration
    Given I am a new user
    When I register with valid credentials:
      | email                    | password      | first_name | last_name | training_experience |
      | <EMAIL>     | SecurePass123!| John       | Doe       | 2                   |
    Then I should receive a successful registration response
    And I should get an authentication token
    And my user profile should be created

  @authentication @login @critical
  Scenario: Successful user login
    Given I am a registered user with credentials:
      | email                | password      |
      | <EMAIL>    | SecurePass123!|
    When I login with correct credentials
    Then I should receive a successful login response
    And I should get an authentication token
    And the token should be valid for API access
```

#### **Step Definition Example**

```python
# features/steps/authentication_steps.py
from behave import given, when, then
from features.environment import get_bdd_utils

@given('I am a new user')
def step_new_user(context):
    """Set up context for a new user registration."""
    bdd_context = get_context(context)
    bdd_context.user_data = UserFactory.create_user_data()

@when('I register with valid credentials')
def step_register_valid_credentials(context):
    """Register with valid user credentials from table."""
    bdd_context = get_context(context)
    
    # Extract data from table
    row = context.table[0]
    user_data = {
        "email": row["email"],
        "password": row["password"],
        "first_name": row["first_name"],
        "last_name": row["last_name"],
        "training_experience_years": int(row["training_experience"])
    }
    
    # Make registration request
    response = bdd_context.test_client.post("/api/v1/auth/register", json=user_data)
    bdd_context.response = response

@then('I should receive a successful registration response')
def step_successful_registration_response(context):
    """Verify successful registration response."""
    bdd_context = get_context(context)
    utils = get_bdd_utils()
    
    utils.assert_response_status(bdd_context.response, 201)
```

### **BDD Test Tags**

We use comprehensive tagging for organized test execution:

#### **Feature Tags**
- `@authentication` - Authentication and login scenarios
- `@profile` - User profile management scenarios
- `@health` - Health check and monitoring scenarios
- `@error_handling` - Error handling and edge cases
- `@performance` - Performance testing scenarios
- `@security` - Security testing scenarios

#### **Priority Tags**
- `@critical` - Critical functionality that must work
- `@high` - High priority features
- `@medium` - Medium priority features
- `@low` - Low priority features

#### **Test Type Tags**
- `@smoke` - Quick smoke tests for basic functionality
- `@regression` - Comprehensive regression test suite
- `@integration` - Integration testing scenarios
- `@acceptance` - User acceptance tests

## 🎯 Test Execution Strategies

### **Development Workflow**

```bash
# Quick feedback during development
python -m pytest tests/unit/ -x --ff

# Pre-commit testing
python -m pytest tests/unit/ tests/integration/ --cov=app

# Full test suite
python run_bdd_tests.py && python -m pytest tests/ --cov=app
```

### **Continuous Integration**

```bash
# CI pipeline testing
python -m pytest tests/ --cov=app --cov-report=xml --junit-xml=reports/junit.xml
python run_bdd_tests.py --junit --tags "not @slow"
```

### **Test Categories by Speed**

| Category | Speed | When to Run |
|----------|-------|-------------|
| **Unit Tests** | Fast (< 1s each) | Every code change |
| **Integration Tests** | Medium (1-5s each) | Pre-commit |
| **BDD Tests** | Slow (5-30s each) | Pre-push, CI |

## 📊 Test Coverage Analysis

### **Coverage Targets**

- **Overall Coverage**: 88.24% (Target: 85%+) ✅
- **Critical Paths**: 95%+ coverage required
- **New Code**: 90%+ coverage required

### **Coverage by Layer**

| Layer | Coverage | Status |
|-------|----------|--------|
| **Domain** | 97.5% | ✅ Excellent |
| **Application** | 100% | ✅ Perfect |
| **Infrastructure** | 88.5% | ✅ Good |
| **Presentation** | 82.3% | ✅ Good |

### **Generating Coverage Reports**

```bash
# Terminal coverage report
python -m pytest --cov=app --cov-report=term-missing

# HTML coverage report
python -m pytest --cov=app --cov-report=html
open htmlcov/index.html

# XML coverage report (for CI)
python -m pytest --cov=app --cov-report=xml
```

## 🔧 Test Configuration

### **pytest Configuration** (`pytest.ini`)

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    auth: Authentication and authorization tests
    database: Database-related tests
    api: API endpoint tests
    security: Security-focused tests
    performance: Performance and load tests
    slow: Tests that take longer to run
    smoke: Quick smoke tests
    critical: Critical functionality tests
```

### **Test Environment Setup**

```python
# tests/conftest.py
import pytest
import asyncio
from httpx import AsyncClient
from app.main import app

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def async_client():
    """Create an async HTTP client for testing."""
    async with AsyncClient(app=app, base_url="http://testserver") as client:
        yield client

@pytest.fixture
def user_factory():
    """Provide user factory for tests."""
    return UserFactory
```

## 🚀 Best Practices

### **Unit Testing Best Practices**

1. **Test Isolation**: Each test should be independent
2. **Clear Naming**: Test names should describe what they test
3. **Arrange-Act-Assert**: Structure tests clearly
4. **Mock External Dependencies**: Use mocks for external services
5. **Test Edge Cases**: Include boundary conditions and error cases

### **Integration Testing Best Practices**

1. **Test Real Interactions**: Use actual implementations where possible
2. **Database Cleanup**: Ensure tests don't interfere with each other
3. **Environment Consistency**: Use consistent test environments
4. **Performance Awareness**: Monitor test execution time
5. **Realistic Data**: Use realistic test data and scenarios

### **BDD Testing Best Practices**

1. **Business Language**: Write scenarios in business terms
2. **Stakeholder Readable**: Ensure non-technical stakeholders can understand
3. **Focused Scenarios**: Each scenario should test one specific behavior
4. **Reusable Steps**: Create reusable step definitions
5. **Living Documentation**: Keep scenarios current with features

## 📚 Additional Resources

- **pytest Documentation**: https://docs.pytest.org/
- **Behave Documentation**: https://behave.readthedocs.io/
- **FastAPI Testing**: https://fastapi.tiangolo.com/tutorial/testing/
- **Test-Driven Development**: Best practices and methodologies
- **Clean Code Testing**: Writing maintainable test code

## 🎯 Next Steps

1. **Explore Test Files**: Browse the test directories to understand patterns
2. **Run Tests Locally**: Execute different test categories
3. **Write New Tests**: Follow the patterns for new features
4. **Contribute**: Help improve test coverage and quality
5. **Monitor Coverage**: Keep track of coverage metrics

For more detailed information, see the individual test files and the comprehensive BDD documentation in the `features/` directory.
