"""
Database migration tests for Forge Protocol.

Tests database schema migrations, constraints, and data integrity.
No mocking - tests against real database operations.
"""

import asyncio
import subprocess
from typing import List, Dict, Any

import pytest
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.infrastructure.database import get_database_session, db_manager
from tests.factories import UserFactory


@pytest.mark.database
@pytest.mark.asyncio
class TestDatabaseMigrations:
    """Test database migrations and schema integrity."""

    @pytest.fixture(scope="class", autouse=True)
    async def setup_database(self):
        """Setup database for migration testing."""
        db_manager.initialize()
        yield
        await db_manager.close()

    async def test_migration_up_and_down(self) -> None:
        """Test migration can be applied and rolled back."""
        # Get current migration state
        current_result = subprocess.run([
            "python", "-m", "alembic", "current"
        ], capture_output=True, text=True, cwd=".")
        
        assert current_result.returncode == 0, f"Failed to get current migration: {current_result.stderr}"
        
        # Test migration down (if possible)
        downgrade_result = subprocess.run([
            "python", "-m", "alembic", "downgrade", "-1"
        ], capture_output=True, text=True, cwd=".")
        
        # Test migration up
        upgrade_result = subprocess.run([
            "python", "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True, cwd=".")
        
        assert upgrade_result.returncode == 0, f"Migration upgrade failed: {upgrade_result.stderr}"

    async def test_database_schema_integrity(self) -> None:
        """Test database schema has correct structure."""
        async for session in get_database_session():
            # Test users table exists and has correct columns
            result = await session.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'users'
                ORDER BY column_name
            """))
            
            columns = result.fetchall()
            column_info = {row[0]: {"type": row[1], "nullable": row[2]} for row in columns}
            
            # Verify required columns exist
            required_columns = {
                "id": {"nullable": "NO"},
                "email": {"nullable": "NO"},
                "hashed_password": {"nullable": "NO"},
                "first_name": {"nullable": "YES"},
                "last_name": {"nullable": "YES"},
                "training_experience_years": {"nullable": "YES"},
                "is_active": {"nullable": "NO"},
                "is_verified": {"nullable": "NO"},
                "created_at": {"nullable": "NO"},
                "updated_at": {"nullable": "NO"},
                "deleted_at": {"nullable": "YES"},
            }
            
            for col_name, requirements in required_columns.items():
                assert col_name in column_info, f"Column {col_name} missing from users table"
                assert column_info[col_name]["nullable"] == requirements["nullable"], \
                    f"Column {col_name} nullable constraint incorrect"
            
            break

    async def test_database_constraints(self) -> None:
        """Test database constraints are properly enforced."""
        async for session in get_database_session():
            # Test unique constraint on email
            user1_data = UserFactory.create_user_data(email="<EMAIL>")
            user2_data = UserFactory.create_user_data(email="<EMAIL>")  # Same email
            
            # First user should succeed
            await session.execute(text("""
                INSERT INTO users (id, email, hashed_password, is_active, is_verified, created_at, updated_at)
                VALUES (gen_random_uuid(), :email, :password, true, false, now(), now())
            """), {"email": user1_data["email"], "password": "hashed_password"})
            
            await session.commit()
            
            # Second user with same email should fail
            with pytest.raises(Exception):  # Should raise integrity error
                await session.execute(text("""
                    INSERT INTO users (id, email, hashed_password, is_active, is_verified, created_at, updated_at)
                    VALUES (gen_random_uuid(), :email, :password, true, false, now(), now())
                """), {"email": user2_data["email"], "password": "hashed_password"})
                await session.commit()
            
            await session.rollback()
            break

    async def test_database_indexes(self) -> None:
        """Test database indexes exist for performance."""
        async for session in get_database_session():
            # Check for email index (critical for login performance)
            result = await session.execute(text("""
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'users' AND indexdef LIKE '%email%'
            """))
            
            indexes = result.fetchall()
            
            # Should have at least one index on email
            assert len(indexes) > 0, "Email column should have an index for performance"
            
            # Check for primary key index
            pk_result = await session.execute(text("""
                SELECT indexname
                FROM pg_indexes 
                WHERE tablename = 'users' AND indexdef LIKE '%PRIMARY KEY%' OR indexdef LIKE '%UNIQUE%'
            """))
            
            pk_indexes = pk_result.fetchall()
            assert len(pk_indexes) > 0, "Users table should have primary key or unique indexes"
            
            break

    async def test_database_performance(self) -> None:
        """Test database query performance meets requirements."""
        async for session in get_database_session():
            # Insert test data
            test_users = []
            for i in range(100):
                user_data = UserFactory.create_user_data(email=f"perf_test_{i}@example.com")
                test_users.append(user_data)
            
            # Batch insert for performance
            for user_data in test_users:
                await session.execute(text("""
                    INSERT INTO users (id, email, hashed_password, first_name, last_name, 
                                     training_experience_years, is_active, is_verified, created_at, updated_at)
                    VALUES (gen_random_uuid(), :email, :password, :first_name, :last_name, 
                           :experience, true, false, now(), now())
                """), {
                    "email": user_data["email"],
                    "password": "hashed_password",
                    "first_name": user_data.get("first_name"),
                    "last_name": user_data.get("last_name"),
                    "experience": user_data.get("training_experience_years")
                })
            
            await session.commit()
            
            # Test query performance
            import time
            
            # Test email lookup (most common query)
            start_time = time.perf_counter()
            result = await session.execute(text("""
                SELECT id, email, first_name, last_name 
                FROM users 
                WHERE email = :email
            """), {"email": test_users[50]["email"]})
            end_time = time.perf_counter()
            
            user = result.fetchone()
            assert user is not None, "User should be found"
            
            query_time = (end_time - start_time) * 1000  # Convert to ms
            assert query_time < 50, f"Email lookup took {query_time:.2f}ms (should be < 50ms)"
            
            # Test count query performance
            start_time = time.perf_counter()
            count_result = await session.execute(text("SELECT COUNT(*) FROM users"))
            end_time = time.perf_counter()
            
            count = count_result.scalar()
            assert count >= 100, "Should have at least 100 users"
            
            count_time = (end_time - start_time) * 1000
            assert count_time < 100, f"Count query took {count_time:.2f}ms (should be < 100ms)"
            
            break

    async def test_database_data_types(self) -> None:
        """Test database handles all data types correctly."""
        async for session in get_database_session():
            # Test various data type scenarios
            test_cases = [
                {
                    "email": "<EMAIL>",
                    "first_name": "Test",
                    "last_name": "User",
                    "experience": 0
                },
                {
                    "email": "<EMAIL>",
                    "first_name": "José",
                    "last_name": "O'Connor",
                    "experience": 15
                },
                {
                    "email": "<EMAIL>",
                    "first_name": None,
                    "last_name": None,
                    "experience": None
                },
                {
                    "email": "<EMAIL>",
                    "first_name": "A" * 100,  # Test long names
                    "last_name": "B" * 100,
                    "experience": 25
                }
            ]
            
            for test_case in test_cases:
                await session.execute(text("""
                    INSERT INTO users (id, email, hashed_password, first_name, last_name, 
                                     training_experience_years, is_active, is_verified, created_at, updated_at)
                    VALUES (gen_random_uuid(), :email, 'hashed_password', :first_name, :last_name, 
                           :experience, true, false, now(), now())
                """), test_case)
            
            await session.commit()
            
            # Verify data was stored correctly
            for test_case in test_cases:
                result = await session.execute(text("""
                    SELECT first_name, last_name, training_experience_years
                    FROM users 
                    WHERE email = :email
                """), {"email": test_case["email"]})
                
                row = result.fetchone()
                assert row is not None, f"User {test_case['email']} not found"
                
                assert row[0] == test_case["first_name"], "First name mismatch"
                assert row[1] == test_case["last_name"], "Last name mismatch"
                assert row[2] == test_case["experience"], "Experience mismatch"
            
            break

    async def test_database_transaction_integrity(self) -> None:
        """Test database transaction rollback works correctly."""
        async for session in get_database_session():
            # Start transaction
            initial_count_result = await session.execute(text("SELECT COUNT(*) FROM users"))
            initial_count = initial_count_result.scalar()
            
            try:
                # Insert a user
                await session.execute(text("""
                    INSERT INTO users (id, email, hashed_password, is_active, is_verified, created_at, updated_at)
                    VALUES (gen_random_uuid(), '<EMAIL>', 'hashed_password', 
                           true, false, now(), now())
                """))
                
                # Verify user was inserted
                check_result = await session.execute(text("""
                    SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'
                """))
                assert check_result.scalar() == 1, "User should be inserted in transaction"
                
                # Force rollback
                await session.rollback()
                
                # Verify user was rolled back
                final_count_result = await session.execute(text("SELECT COUNT(*) FROM users"))
                final_count = final_count_result.scalar()
                
                assert final_count == initial_count, "Transaction should have been rolled back"
                
            except Exception:
                await session.rollback()
                raise
            
            break

    async def test_database_connection_pooling(self) -> None:
        """Test database connection pooling works correctly."""
        # Test multiple concurrent connections
        async def test_connection():
            async for session in get_database_session():
                result = await session.execute(text("SELECT 1"))
                assert result.scalar() == 1
                break
        
        # Run multiple concurrent database operations
        tasks = [test_connection() for _ in range(10)]
        await asyncio.gather(*tasks)
        
        # All should succeed without connection issues
