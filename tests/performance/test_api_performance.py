"""Performance tests for Forge Protocol API.

Tests API response times, throughput, and scalability under various loads.
No mocking - tests against real services.
"""

import asyncio
import statistics
import time
from typing import Any

import pytest
from httpx import ASGITransport, AsyncClient

from app.main import app
from tests.factories import UserFactory


@pytest.mark.performance
@pytest.mark.asyncio
class TestAPIPerformance:
    """Performance tests for API endpoints."""

    @pytest.fixture
    async def async_client(self) -> AsyncClient:
        """Create async client for performance testing."""
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as client:
            yield client

    async def test_health_endpoint_response_time(
        self, async_client: AsyncClient
    ) -> None:
        """Test health endpoint response time meets mobile requirements."""
        response_times = []

        # Warm up
        for _ in range(5):
            await async_client.get("/api/v1/health")

        # Measure response times
        for _ in range(50):
            start_time = time.perf_counter()
            response = await async_client.get("/api/v1/health")
            end_time = time.perf_counter()

            assert response.status_code == 200
            response_times.append((end_time - start_time) * 1000)  # Convert to ms

        # Verify performance requirements
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[
            18
        ]  # 95th percentile

        assert (
            avg_response_time < 50
        ), f"Average response time {avg_response_time:.2f}ms exceeds 50ms"
        assert (
            p95_response_time < 100
        ), f"95th percentile {p95_response_time:.2f}ms exceeds 100ms"
        assert (
            max(response_times) < 200
        ), f"Max response time {max(response_times):.2f}ms exceeds 200ms"

    async def test_user_registration_performance(
        self, async_client: AsyncClient
    ) -> None:
        """Test user registration performance under load."""
        response_times = []

        # Test multiple registrations
        for i in range(20):
            user_data = UserFactory.create_user_data(email=f"perf_test_{i}@example.com")

            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            end_time = time.perf_counter()

            assert response.status_code == 201
            response_times.append((end_time - start_time) * 1000)

        # Verify performance requirements
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]

        assert (
            avg_response_time < 200
        ), f"Registration avg time {avg_response_time:.2f}ms exceeds 200ms"
        assert (
            p95_response_time < 500
        ), f"Registration 95th percentile {p95_response_time:.2f}ms exceeds 500ms"

    async def test_authentication_performance(self, async_client: AsyncClient) -> None:
        """Test authentication performance for mobile users."""
        # First register a user
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        login_data = {"email": user_data["email"], "password": user_data["password"]}

        response_times = []

        # Test multiple logins
        for _ in range(30):
            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            end_time = time.perf_counter()

            assert response.status_code == 200
            response_times.append((end_time - start_time) * 1000)

        # Verify performance requirements
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]

        assert (
            avg_response_time < 150
        ), f"Login avg time {avg_response_time:.2f}ms exceeds 150ms"
        assert (
            p95_response_time < 300
        ), f"Login 95th percentile {p95_response_time:.2f}ms exceeds 300ms"

    async def test_concurrent_user_registrations(
        self, async_client: AsyncClient
    ) -> None:
        """Test concurrent user registrations performance."""
        concurrent_users = 10

        async def register_user(user_id: int) -> dict[str, Any]:
            user_data = UserFactory.create_user_data(
                email=f"concurrent_{user_id}@example.com"
            )
            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            end_time = time.perf_counter()

            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000,
                "user_id": user_id,
            }

        # Execute concurrent registrations
        start_time = time.perf_counter()
        tasks = [register_user(i) for i in range(concurrent_users)]
        results = await asyncio.gather(*tasks)
        total_time = (time.perf_counter() - start_time) * 1000

        # Verify all registrations succeeded
        for result in results:
            assert result["status_code"] == 201

        # Verify performance
        response_times = [result["response_time"] for result in results]
        avg_response_time = statistics.mean(response_times)
        throughput = concurrent_users / (total_time / 1000)  # requests per second

        assert (
            avg_response_time < 500
        ), f"Concurrent avg time {avg_response_time:.2f}ms exceeds 500ms"
        assert throughput > 5, f"Throughput {throughput:.2f} req/s is below 5 req/s"

    async def test_api_memory_usage_stability(self, async_client: AsyncClient) -> None:
        """Test API memory usage remains stable under load."""
        # Simulate sustained load
        for batch in range(5):
            tasks = []

            # Health checks
            for _ in range(20):
                tasks.append(async_client.get("/api/v1/health"))

            # User registrations
            for i in range(10):
                user_data = UserFactory.create_user_data(
                    email=f"memory_test_{batch}_{i}@example.com"
                )
                tasks.append(async_client.post("/api/v1/auth/register", json=user_data))

            # Execute batch
            responses = await asyncio.gather(*tasks)

            # Verify all requests succeeded
            for response in responses:
                assert response.status_code in [200, 201]

            # Small delay between batches
            await asyncio.sleep(0.1)

    async def test_response_payload_size_optimization(
        self, async_client: AsyncClient
    ) -> None:
        """Test response payload sizes are optimized for mobile."""
        # Register user
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201

        # Check registration response size
        registration_payload = response.content
        registration_size = len(registration_payload)

        # Should be under 2KB for mobile optimization
        assert (
            registration_size < 2048
        ), f"Registration payload {registration_size} bytes exceeds 2KB"

        # Login and check response size
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200

        login_payload = login_response.content
        login_size = len(login_payload)

        # Should be under 1.5KB for mobile optimization
        assert login_size < 1536, f"Login payload {login_size} bytes exceeds 1.5KB"

    async def test_database_query_performance(self, async_client: AsyncClient) -> None:
        """Test database query performance for mobile responsiveness."""
        # Create multiple users first
        user_emails = []
        for i in range(50):
            user_data = UserFactory.create_user_data(email=f"db_perf_{i}@example.com")
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 201
            user_emails.append(user_data["email"])

        # Test login performance with existing users
        response_times = []

        for email in user_emails[:20]:  # Test subset
            login_data = {"email": email, "password": "SecurePass123!"}

            start_time = time.perf_counter()
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            end_time = time.perf_counter()

            assert response.status_code == 200
            response_times.append((end_time - start_time) * 1000)

        # Verify database query performance
        avg_response_time = statistics.mean(response_times)
        assert (
            avg_response_time < 100
        ), f"DB query avg time {avg_response_time:.2f}ms exceeds 100ms"

    async def test_error_handling_performance(self, async_client: AsyncClient) -> None:
        """Test error handling doesn't impact performance."""
        error_response_times = []

        # Test various error scenarios
        error_scenarios = [
            {"email": "invalid-email", "password": "SecurePass123!"},  # Invalid email
            {"email": "<EMAIL>", "password": "weak"},  # Weak password
            {"email": "", "password": "SecurePass123!"},  # Empty email
        ]

        for scenario in error_scenarios:
            for _ in range(10):
                start_time = time.perf_counter()
                response = await async_client.post(
                    "/api/v1/auth/register", json=scenario
                )
                end_time = time.perf_counter()

                assert response.status_code == 422  # Validation error
                error_response_times.append((end_time - start_time) * 1000)

        # Error responses should be fast
        avg_error_time = statistics.mean(error_response_times)
        assert (
            avg_error_time < 50
        ), f"Error handling avg time {avg_error_time:.2f}ms exceeds 50ms"
