"""Pytest configuration and shared fixtures.

Provides common test fixtures and configuration for all test modules.
"""

import asyncio
from collections.abc import AsyncGenerator, Generator
from typing import Any
from unittest.mock import AsyncMock
from uuid import uuid4

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

from app.config import settings
from app.infrastructure.auth.jwt_handler import jwt_handler
from app.infrastructure.auth.password_handler import password_handler
from app.infrastructure.database.connection import Base, get_database_session
from app.infrastructure.database.models.user_model import UserModel
from app.main import app

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    # Create test engine
    engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False,
    )

    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Create session factory
    session_factory = async_sessionmaker(
        bind=engine, class_=AsyncSession, expire_on_commit=False
    )

    # Create session
    async with session_factory() as session:
        yield session

    # Clean up
    await engine.dispose()


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(
    test_db_session: AsyncSession,
) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client with database session override."""

    async def override_get_db() -> AsyncGenerator[AsyncSession, None]:
        yield test_db_session

    app.dependency_overrides[get_database_session] = override_get_db

    async with AsyncClient(app=app, base_url="http://testserver") as client:
        yield client

    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def mock_user_repository() -> AsyncMock:
    """Create a mock user repository for testing."""
    return AsyncMock()


@pytest.fixture
def mock_token_generator() -> AsyncMock:
    """Create a mock token generator for testing."""
    mock = AsyncMock()
    mock.generate_access_token.return_value = "mock_access_token"
    mock.generate_refresh_token.return_value = "mock_refresh_token"
    return mock


# Test data factories
@pytest.fixture
def sample_user_data() -> dict[str, Any]:
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "first_name": "John",
        "last_name": "Doe",
        "training_experience_years": 2,
    }


@pytest.fixture
def sample_user_update_data() -> dict[str, Any]:
    """Sample user update data for testing."""
    return {"first_name": "Jane", "last_name": "Smith", "training_experience_years": 5}


@pytest_asyncio.fixture
async def created_user(
    test_db_session: AsyncSession, sample_user_data: dict[str, Any]
) -> UserModel:
    """Create a user in the test database."""
    hashed_password = password_handler.hash_password(sample_user_data["password"])

    user_model = UserModel(
        email=sample_user_data["email"],
        hashed_password=hashed_password,
        first_name=sample_user_data["first_name"],
        last_name=sample_user_data["last_name"],
        training_experience_years=sample_user_data["training_experience_years"],
    )

    test_db_session.add(user_model)
    await test_db_session.commit()
    await test_db_session.refresh(user_model)

    return user_model


@pytest_asyncio.fixture
async def auth_headers(created_user: UserModel) -> dict[str, str]:
    """Generate authentication headers for testing."""
    access_token = await jwt_handler.generate_access_token(str(created_user.id))
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def invalid_auth_headers() -> dict[str, str]:
    """Generate invalid authentication headers for testing."""
    return {"Authorization": "Bearer invalid_token"}


@pytest.fixture
def expired_auth_headers() -> dict[str, str]:
    """Generate expired authentication headers for testing."""
    # Create a token that's already expired
    from datetime import datetime, timedelta

    import jwt

    payload = {
        "sub": str(uuid4()),
        "type": "access",
        "exp": datetime.utcnow() - timedelta(hours=1),  # Expired 1 hour ago
        "iat": datetime.utcnow() - timedelta(hours=2),
    }

    expired_token = jwt.encode(
        payload, settings.secret_key, algorithm=settings.algorithm
    )
    return {"Authorization": f"Bearer {expired_token}"}


# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.auth = pytest.mark.auth
pytest.mark.database = pytest.mark.database
pytest.mark.api = pytest.mark.api
pytest.mark.security = pytest.mark.security
pytest.mark.performance = pytest.mark.performance
