"""
Unit tests for user value objects.

Tests value objects used in the user domain without external dependencies.
"""

import pytest
from typing import Optional

from app.domain.value_objects.email import Email
from app.domain.exceptions.validation_exceptions import ValidationError


@pytest.mark.unit
@pytest.mark.domain
class TestEmail:
    """Test cases for Email value object."""

    def test_create_valid_email(self) -> None:
        """Test creating email with valid format."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]
        
        for email_str in valid_emails:
            email = Email(email_str)
            assert email.value == email_str.lower()
            assert str(email) == email_str.lower()

    def test_create_email_normalizes_case(self) -> None:
        """Test email normalization to lowercase."""
        email = Email("<EMAIL>")
        assert email.value == "<EMAIL>"
        assert str(email) == "<EMAIL>"

    def test_create_email_strips_whitespace(self) -> None:
        """Test email strips leading/trailing whitespace."""
        email = Email("  <EMAIL>  ")
        assert email.value == "<EMAIL>"

    def test_invalid_email_formats(self) -> None:
        """Test invalid email formats raise ValidationError."""
        invalid_emails = [
            "",
            "   ",
            "invalid",
            "@example.com",
            "user@",
            "<EMAIL>",
            "<EMAIL>",
            "user@example.",
            "<EMAIL>",
            "user <EMAIL>",  # Space in local part
            "user@exam ple.com",      # Space in domain
            "user@",
            "@",
            "user@@example.com",      # Double @
            "user@example@com",       # Multiple @
            "a" * 65 + "@example.com",  # Local part too long
            "user@" + "a" * 250 + ".com",  # Domain too long
        ]
        
        for invalid_email in invalid_emails:
            with pytest.raises(ValidationError, match="Invalid email format"):
                Email(invalid_email)

    def test_email_equality(self) -> None:
        """Test email equality comparison."""
        email1 = Email("<EMAIL>")
        email2 = Email("<EMAIL>")
        email3 = Email("<EMAIL>")
        
        assert email1 == email2  # Case insensitive
        assert email1 != email3
        assert email1 == "<EMAIL>"
        assert email1 != "<EMAIL>"

    def test_email_hash(self) -> None:
        """Test email can be used as dictionary key."""
        email1 = Email("<EMAIL>")
        email2 = Email("<EMAIL>")
        
        email_dict = {email1: "value1"}
        email_dict[email2] = "value2"
        
        # Should have only one key due to equality
        assert len(email_dict) == 1
        assert email_dict[email1] == "value2"

    def test_email_domain_extraction(self) -> None:
        """Test extracting domain from email."""
        email = Email("<EMAIL>")
        assert email.domain == "example.com"
        
        email_subdomain = Email("<EMAIL>")
        assert email_subdomain.domain == "mail.example.com"

    def test_email_local_part_extraction(self) -> None:
        """Test extracting local part from email."""
        email = Email("<EMAIL>")
        assert email.local_part == "user.name+tag"

    def test_email_validation_edge_cases(self) -> None:
        """Test email validation edge cases."""
        # Valid edge cases
        valid_edge_cases = [
            "<EMAIL>",  # Minimum valid email
            "test@localhost",  # No TLD (technically valid)
            "<EMAIL>",  # Multiple plus signs
            "<EMAIL>",  # Multiple dots
            "<EMAIL>",  # Numeric local part
            "<EMAIL>",  # Numeric domain
        ]
        
        for email_str in valid_edge_cases:
            email = Email(email_str)
            assert email.value == email_str.lower()

    def test_email_internationalization(self) -> None:
        """Test email with international characters."""
        # Note: This tests ASCII-only for now
        # Future: Add support for internationalized domain names
        with pytest.raises(ValidationError):
            Email("user@exämple.com")

    def test_email_security_considerations(self) -> None:
        """Test email validation prevents security issues."""
        # Test potential injection attempts
        malicious_emails = [
            "<EMAIL>; DROP TABLE users;",
            "<EMAIL><script>alert('xss')</script>",
            "<EMAIL>\nBCC: <EMAIL>",
            "<EMAIL>\r\nTo: <EMAIL>",
        ]
        
        for malicious_email in malicious_emails:
            with pytest.raises(ValidationError):
                Email(malicious_email)

    def test_email_performance_large_input(self) -> None:
        """Test email validation performance with large input."""
        # Test with very long but invalid email
        long_invalid = "a" * 1000 + "@" + "b" * 1000 + ".com"
        
        with pytest.raises(ValidationError):
            Email(long_invalid)

    def test_email_repr(self) -> None:
        """Test email string representation."""
        email = Email("<EMAIL>")
        assert repr(email) == "Email('<EMAIL>')"

    def test_email_immutability(self) -> None:
        """Test email value object is immutable."""
        email = Email("<EMAIL>")
        
        # Should not be able to modify value
        with pytest.raises(AttributeError):
            email.value = "<EMAIL>"

    def test_email_none_value(self) -> None:
        """Test email with None value."""
        with pytest.raises(ValidationError):
            Email(None)

    def test_email_type_validation(self) -> None:
        """Test email with non-string types."""
        invalid_types = [123, [], {}, True, 12.34]
        
        for invalid_type in invalid_types:
            with pytest.raises(ValidationError):
                Email(invalid_type)

    def test_email_common_typos(self) -> None:
        """Test common email typos are caught."""
        typo_emails = [
            "<EMAIL>",  # Should be gmail.com
            "<EMAIL>",  # Should be yahoo.com
            "<EMAIL>",  # Should be hotmail.com
            "<EMAIL>",    # Should be gmail.com
        ]
        
        # These are technically valid emails, just typos
        # The validation should pass, but we document the behavior
        for typo_email in typo_emails:
            email = Email(typo_email)
            assert email.value == typo_email.lower()

    def test_email_mobile_optimization(self) -> None:
        """Test email validation is optimized for mobile input."""
        # Test common mobile input scenarios
        mobile_scenarios = [
            ("<EMAIL> ", "<EMAIL>"),  # Trailing space
            (" <EMAIL>", "<EMAIL>"),  # Leading space
            ("<EMAIL>", "<EMAIL>"),   # Mixed case
        ]
        
        for input_email, expected in mobile_scenarios:
            email = Email(input_email)
            assert email.value == expected
