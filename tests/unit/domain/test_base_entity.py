"""Tests for BaseEntity domain entity."""

from datetime import datetime
from uuid import UUID, uuid4


from app.domain.entities.base import BaseEntity


class ConcreteEntity(BaseEntity):
    """Concrete implementation of BaseEntity for testing."""

    def __init__(self, name: str, **kwargs):
        """Initialize concrete entity."""
        super().__init__(**kwargs)
        self.name = name


class TestBaseEntity:
    """Test cases for BaseEntity."""

    def test_create_entity_with_auto_generated_id(self):
        """Test creating entity with auto-generated ID."""
        entity = ConcreteEntity(name="Test Entity")

        assert entity.id is not None
        assert isinstance(entity.id, UUID)
        assert entity.created_at is not None
        assert entity.updated_at is not None
        assert entity.deleted_at is None
        assert entity.created_at == entity.updated_at

    def test_create_entity_with_provided_id(self):
        """Test creating entity with provided ID."""
        entity_id = uuid4()
        entity = ConcreteEntity(name="Test Entity", id=entity_id)

        assert entity.id == entity_id

    def test_create_entity_with_provided_timestamps(self):
        """Test creating entity with provided timestamps."""
        created_time = datetime(2023, 1, 1, 12, 0, 0)
        updated_time = datetime(2023, 1, 2, 12, 0, 0)
        deleted_time = datetime(2023, 1, 3, 12, 0, 0)

        entity = ConcreteEntity(
            name="Test Entity",
            created_at=created_time,
            updated_at=updated_time,
            deleted_at=deleted_time,
        )

        assert entity.created_at == created_time
        assert entity.updated_at == updated_time
        assert entity.deleted_at == deleted_time

    def test_mark_as_updated(self):
        """Test mark_as_updated method."""
        entity = ConcreteEntity(name="Test Entity")
        original_updated_at = entity.updated_at

        # Wait a tiny bit to ensure timestamp difference
        import time

        time.sleep(0.001)

        entity.mark_as_updated()

        assert entity.updated_at > original_updated_at

    def test_soft_delete(self):
        """Test soft_delete method."""
        entity = ConcreteEntity(name="Test Entity")

        assert entity.deleted_at is None
        assert entity.is_deleted() is False

        entity.soft_delete()

        assert entity.deleted_at is not None
        assert entity.is_deleted() is True

    def test_restore(self):
        """Test restore method."""
        entity = ConcreteEntity(name="Test Entity")
        entity.soft_delete()

        assert entity.is_deleted() is True

        entity.restore()

        assert entity.deleted_at is None
        assert entity.is_deleted() is False

    def test_is_deleted_method(self):
        """Test is_deleted method."""
        entity = ConcreteEntity(name="Test Entity")

        assert entity.is_deleted() is False

        entity.deleted_at = datetime.now()
        assert entity.is_deleted() is True

        entity.deleted_at = None
        assert entity.is_deleted() is False

    def test_entity_equality(self):
        """Test entity equality based on ID."""
        entity_id = uuid4()
        entity1 = ConcreteEntity(name="Entity 1", id=entity_id)
        entity2 = ConcreteEntity(name="Entity 2", id=entity_id)
        entity3 = ConcreteEntity(name="Entity 3")

        assert entity1 == entity2  # Same ID
        assert entity1 != entity3  # Different ID
        assert entity2 != entity3  # Different ID

    def test_entity_equality_with_none(self):
        """Test entity equality with None."""
        entity = ConcreteEntity(name="Test Entity")

        assert entity != None
        assert entity != None

    def test_entity_equality_with_different_type(self):
        """Test entity equality with different type."""
        entity = ConcreteEntity(name="Test Entity")

        assert entity != "string"
        assert entity != 123
        assert entity != {}

    def test_entity_hash(self):
        """Test entity hash based on ID."""
        entity_id = uuid4()
        entity1 = ConcreteEntity(name="Entity 1", id=entity_id)
        entity2 = ConcreteEntity(name="Entity 2", id=entity_id)

        assert hash(entity1) == hash(entity2)

        # Test that entities can be used in sets
        entity_set = {entity1, entity2}
        assert len(entity_set) == 1  # Should be treated as same entity

    def test_entity_string_representation(self):
        """Test entity string representation."""
        entity = ConcreteEntity(name="Test Entity")

        str_repr = str(entity)
        assert "ConcreteEntity" in str_repr
        assert str(entity.id) in str_repr

    def test_entity_repr(self):
        """Test entity repr."""
        entity = ConcreteEntity(name="Test Entity")

        repr_str = repr(entity)
        assert "ConcreteEntity" in repr_str
        assert str(entity.id) in repr_str

    def test_restore_updates_timestamp(self):
        """Test that restore updates the updated_at timestamp."""
        entity = ConcreteEntity(name="Test Entity")
        entity.soft_delete()
        original_updated_at = entity.updated_at

        # Wait a tiny bit to ensure timestamp difference
        import time

        time.sleep(0.001)

        entity.restore()

        assert entity.updated_at > original_updated_at

    def test_soft_delete_updates_timestamp(self):
        """Test that soft_delete updates the updated_at timestamp."""
        entity = ConcreteEntity(name="Test Entity")
        original_updated_at = entity.updated_at

        # Wait a tiny bit to ensure timestamp difference
        import time

        time.sleep(0.001)

        entity.soft_delete()

        assert entity.updated_at > original_updated_at
