"""Unit tests for AuthService domain service.

Tests the business logic of authentication operations including
user registration, authentication, and profile management.
"""

from unittest.mock import AsyncMock, Mock
from uuid import uuid4

import pytest

from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import (
    InactiveUserError,
    InvalidCredentialsError,
    UserAlreadyExistsError,
    UserNotFoundError,
)
from app.domain.services.auth_service import AuthService


@pytest.mark.unit
@pytest.mark.auth
class TestAuthServiceRegistration:
    """Test user registration business logic."""

    @pytest.fixture
    def mock_user_repository(self) -> AsyncMock:
        """Mock user repository for testing."""
        return AsyncMock()

    @pytest.fixture
    def mock_password_handler(self) -> Mock:
        """Mock password handler for testing."""
        mock = Mock()
        mock.hash_password.return_value = "hashed_password"
        mock.verify_password.return_value = True
        return mock

    @pytest.fixture
    def auth_service(
        self, mock_user_repository: AsyncMock, mock_password_handler: Mock
    ) -> AuthService:
        """Create AuthService instance with mocked dependencies."""
        return AuthService(mock_user_repository, mock_password_handler)

    async def test_register_user_success(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test successful user registration."""
        # Setup
        mock_user_repository.exists_by_email.return_value = False
        mock_user_repository.create.return_value = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe",
        )

        # Execute
        result = await auth_service.register_user(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe",
            training_experience_years=2,
        )

        # Verify
        assert result.email == "<EMAIL>"
        assert result.first_name == "John"
        assert result.last_name == "Doe"
        assert result.training_experience_years == 2

        mock_user_repository.exists_by_email.assert_called_once_with("<EMAIL>")
        mock_user_repository.create.assert_called_once()

    async def test_register_user_duplicate_email(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test registration with existing email."""
        # Setup
        mock_user_repository.exists_by_email.return_value = True

        # Execute & Verify
        with pytest.raises(UserAlreadyExistsError) as exc_info:
            await auth_service.register_user(
                email="<EMAIL>", password="SecurePass123!"
            )

        assert "already exists" in str(exc_info.value)
        mock_user_repository.exists_by_email.assert_called_once_with(
            "<EMAIL>"
        )
        mock_user_repository.create.assert_not_called()

    async def test_register_user_minimal_data(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test registration with minimal required data."""
        # Setup
        mock_user_repository.exists_by_email.return_value = False
        mock_user_repository.create.return_value = User.create(
            email="<EMAIL>", password="SecurePass123!"
        )

        # Execute
        result = await auth_service.register_user(
            email="<EMAIL>", password="SecurePass123!"
        )

        # Verify
        assert result.email == "<EMAIL>"
        assert result.first_name is None
        assert result.last_name is None
        assert result.training_experience_years is None


@pytest.mark.unit
@pytest.mark.auth
class TestAuthServiceAuthentication:
    """Test user authentication business logic."""

    @pytest.fixture
    def mock_user_repository(self) -> AsyncMock:
        """Mock user repository for testing."""
        return AsyncMock()

    @pytest.fixture
    def mock_password_handler(self) -> Mock:
        """Mock password handler for testing."""
        mock = Mock()
        mock.verify_password.return_value = True
        return mock

    @pytest.fixture
    def auth_service(
        self, mock_user_repository: AsyncMock, mock_password_handler: Mock
    ) -> AuthService:
        """Create AuthService instance with mocked dependencies."""
        return AuthService(mock_user_repository, mock_password_handler)

    @pytest.fixture
    def sample_user(self) -> User:
        """Create a sample user for testing."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe",
            is_active=True,
            is_verified=False,
        )

    async def test_authenticate_user_success(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        mock_password_handler: Mock,
        sample_user: User,
    ) -> None:
        """Test successful user authentication."""
        # Setup
        mock_user_repository.get_by_email.return_value = sample_user
        mock_password_handler.verify_password.return_value = True

        # Execute
        result = await auth_service.authenticate_user(
            email="<EMAIL>", password="correct_password"
        )

        # Verify
        assert result == sample_user
        mock_user_repository.get_by_email.assert_called_once_with("<EMAIL>")
        mock_password_handler.verify_password.assert_called_once_with(
            "correct_password", "hashed_password"
        )

    async def test_authenticate_user_not_found(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test authentication with non-existent user."""
        # Setup
        mock_user_repository.get_by_email.return_value = None

        # Execute & Verify
        with pytest.raises(InvalidCredentialsError) as exc_info:
            await auth_service.authenticate_user(
                email="<EMAIL>", password="password"
            )

        assert "Invalid email or password" in str(exc_info.value)
        mock_user_repository.get_by_email.assert_called_once_with(
            "<EMAIL>"
        )

    async def test_authenticate_user_wrong_password(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        mock_password_handler: Mock,
        sample_user: User,
    ) -> None:
        """Test authentication with wrong password."""
        # Setup
        mock_user_repository.get_by_email.return_value = sample_user
        mock_password_handler.verify_password.return_value = False

        # Execute & Verify
        with pytest.raises(InvalidCredentialsError) as exc_info:
            await auth_service.authenticate_user(
                email="<EMAIL>", password="wrong_password"
            )

        assert "Invalid email or password" in str(exc_info.value)
        mock_password_handler.verify_password.assert_called_once_with(
            "wrong_password", "hashed_password"
        )

    async def test_authenticate_inactive_user(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test authentication with inactive user."""
        # Setup
        sample_user.deactivate()
        mock_user_repository.get_by_email.return_value = sample_user

        # Execute & Verify
        with pytest.raises(InactiveUserError) as exc_info:
            await auth_service.authenticate_user(
                email="<EMAIL>", password="password"
            )

        assert "inactive" in str(exc_info.value).lower()

    async def test_authenticate_deleted_user(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test authentication with soft-deleted user."""
        # Setup
        sample_user.soft_delete()
        mock_user_repository.get_by_email.return_value = sample_user

        # Execute & Verify
        with pytest.raises(InvalidCredentialsError) as exc_info:
            await auth_service.authenticate_user(
                email="<EMAIL>", password="password"
            )

        assert "Invalid email or password" in str(exc_info.value)


@pytest.mark.unit
@pytest.mark.auth
class TestAuthServiceUserManagement:
    """Test user management business logic."""

    @pytest.fixture
    def mock_user_repository(self) -> AsyncMock:
        """Mock user repository for testing."""
        return AsyncMock()

    @pytest.fixture
    def auth_service(self, mock_user_repository: AsyncMock) -> AuthService:
        """Create AuthService instance with mocked dependencies."""
        return AuthService(mock_user_repository)

    @pytest.fixture
    def sample_user(self) -> User:
        """Create a sample user for testing."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe",
            is_active=True,
        )

    async def test_get_user_by_id_success(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test successful user retrieval by ID."""
        # Setup
        mock_user_repository.get_by_id.return_value = sample_user

        # Execute
        result = await auth_service.get_user_by_id(sample_user.id)

        # Verify
        assert result == sample_user
        mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)

    async def test_get_user_by_id_not_found(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test user retrieval with non-existent ID."""
        # Setup
        user_id = uuid4()
        mock_user_repository.get_by_id.return_value = None

        # Execute & Verify
        with pytest.raises(UserNotFoundError) as exc_info:
            await auth_service.get_user_by_id(user_id)

        assert str(user_id) in str(exc_info.value)
        mock_user_repository.get_by_id.assert_called_once_with(user_id)

    async def test_get_user_by_id_deleted_user(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test user retrieval for soft-deleted user."""
        # Setup
        sample_user.soft_delete()
        mock_user_repository.get_by_id.return_value = sample_user

        # Execute & Verify
        with pytest.raises(UserNotFoundError):
            await auth_service.get_user_by_id(sample_user.id)

    async def test_update_user_profile_success(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test successful user profile update."""
        # Setup
        mock_user_repository.get_by_id.return_value = sample_user
        updated_user = User(
            id=sample_user.id,
            email=sample_user.email,
            hashed_password=sample_user.hashed_password,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
            is_active=sample_user.is_active,
        )
        mock_user_repository.update.return_value = updated_user

        # Execute
        result = await auth_service.update_user_profile(
            user_id=sample_user.id,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
        )

        # Verify
        assert result.first_name == "Jane"
        assert result.last_name == "Smith"
        assert result.training_experience_years == 5

        mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)
        mock_user_repository.update.assert_called_once()

    async def test_update_user_profile_partial(
        self,
        auth_service: AuthService,
        mock_user_repository: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test partial user profile update."""
        # Setup
        mock_user_repository.get_by_id.return_value = sample_user
        mock_user_repository.update.return_value = sample_user

        # Execute
        result = await auth_service.update_user_profile(
            user_id=sample_user.id, first_name="UpdatedName"
        )

        # Verify
        mock_user_repository.get_by_id.assert_called_once_with(sample_user.id)
        mock_user_repository.update.assert_called_once()

    async def test_update_user_profile_not_found(
        self, auth_service: AuthService, mock_user_repository: AsyncMock
    ) -> None:
        """Test profile update for non-existent user."""
        # Setup
        user_id = uuid4()
        mock_user_repository.get_by_id.return_value = None

        # Execute & Verify
        with pytest.raises(UserNotFoundError):
            await auth_service.update_user_profile(
                user_id=user_id, first_name="New Name"
            )
