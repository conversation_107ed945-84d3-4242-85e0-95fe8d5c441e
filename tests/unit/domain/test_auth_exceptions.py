"""Tests for auth domain exceptions."""

import pytest

from app.domain.exceptions.auth_exceptions import (
    AuthenticationError,
    InactiveUserError,
    InvalidCredentialsError,
    InvalidEmailError,
    InvalidTokenError,
    TokenExpiredError,
    UserAlreadyExistsError,
    UserNotFoundError,
    WeakPasswordError,
)


class TestAuthExceptions:
    """Test cases for auth exceptions."""

    def test_invalid_email_error(self):
        """Test InvalidEmailError exception."""
        message = "Invalid email format"
        error = InvalidEmailError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_weak_password_error(self):
        """Test WeakPasswordError exception."""
        message = "Password is too weak"
        error = WeakPasswordError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_user_not_found_error(self):
        """Test UserNotFoundError exception."""
        message = "User not found"
        error = UserNotFoundError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_invalid_credentials_error(self):
        """Test InvalidCredentialsError exception."""
        message = "Invalid credentials"
        error = InvalidCredentialsError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_user_already_exists_error(self):
        """Test UserAlreadyExistsError exception."""
        message = "User already exists"
        error = UserAlreadyExistsError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_token_expired_error(self):
        """Test TokenExpiredError exception."""
        message = "Token has expired"
        error = TokenExpiredError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_invalid_token_error(self):
        """Test InvalidTokenError exception."""
        message = "Invalid token"
        error = InvalidTokenError(message)

        assert str(error) == message
        assert isinstance(error, ValueError)

    def test_inactive_user_error(self):
        """Test InactiveUserError exception."""
        message = "User account is inactive"
        error = InactiveUserError(message)

        assert str(error) == message
        assert isinstance(error, AuthenticationError)

    def test_authentication_error_base(self):
        """Test AuthenticationError base exception."""
        message = "Authentication failed"
        error = AuthenticationError(message)

        assert str(error) == message
        assert isinstance(error, Exception)

    def test_exceptions_can_be_raised_and_caught(self):
        """Test that exceptions can be properly raised and caught."""
        with pytest.raises(InvalidEmailError):
            raise InvalidEmailError("Test error")

        with pytest.raises(WeakPasswordError):
            raise WeakPasswordError("Test error")

        with pytest.raises(UserNotFoundError):
            raise UserNotFoundError("Test error")

        with pytest.raises(InvalidCredentialsError):
            raise InvalidCredentialsError("Test error")

        with pytest.raises(UserAlreadyExistsError):
            raise UserAlreadyExistsError("Test error")

        with pytest.raises(TokenExpiredError):
            raise TokenExpiredError("Test error")

        with pytest.raises(InvalidTokenError):
            raise InvalidTokenError("Test error")

        with pytest.raises(InactiveUserError):
            raise InactiveUserError("Test error")

        with pytest.raises(AuthenticationError):
            raise AuthenticationError("Test error")

    def test_exceptions_inherit_from_authentication_error(self):
        """Test that all auth exceptions inherit from AuthenticationError."""
        exceptions = [
            InvalidEmailError("test"),
            WeakPasswordError("test"),
            UserNotFoundError("test"),
            InvalidCredentialsError("test"),
            UserAlreadyExistsError("test"),
            TokenExpiredError("test"),
            InvalidTokenError("test"),
            InactiveUserError("test"),
        ]

        for exception in exceptions:
            assert isinstance(exception, AuthenticationError)

    def test_exceptions_with_empty_message(self):
        """Test exceptions with empty message."""
        error = InvalidEmailError("")
        assert str(error) == ""

    def test_exceptions_with_none_message(self):
        """Test exceptions with None message."""
        # This should work as Exception accepts None
        error = InvalidEmailError(None)
        assert str(error) == "None"
