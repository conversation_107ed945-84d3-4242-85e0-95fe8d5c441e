"""
Unit tests for workout domain entities.

Tests for workout and exercise set entities including validation,
business logic, and state management.
"""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4

from app.domain.entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    RangeOfMotion,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutStatus,
    WorkoutUpdateRequest,
)


@pytest.mark.unit
@pytest.mark.domain
class TestWorkoutEntity:
    """Test workout domain entity."""
    
    def test_workout_creation(self):
        """Test workout entity creation."""
        workout_id = uuid4()
        user_id = uuid4()
        now = datetime.utcnow()
        
        workout = Workout(
            id=workout_id,
            user_id=user_id,
            name="Test Workout",
            workout_date=now,
            created_at=now,
            updated_at=now,
        )
        
        assert workout.id == workout_id
        assert workout.user_id == user_id
        assert workout.name == "Test Workout"
        assert workout.workout_date == now
        assert workout.status == WorkoutStatus.PLANNED
        assert not workout.is_active
        assert workout.total_sets == 0
        assert workout.total_exercises == 0
    
    def test_workout_status_planned(self):
        """Test workout status when planned."""
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=datetime.utcnow(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert workout.status == WorkoutStatus.PLANNED
        assert not workout.is_active
    
    def test_workout_status_in_progress(self):
        """Test workout status when in progress."""
        now = datetime.utcnow()
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=now,
            started_at=now,
            created_at=now,
            updated_at=now,
        )
        
        assert workout.status == WorkoutStatus.IN_PROGRESS
        assert workout.is_active
    
    def test_workout_status_completed(self):
        """Test workout status when completed."""
        now = datetime.utcnow()
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=now,
            started_at=now,
            completed_at=now + timedelta(hours=1),
            created_at=now,
            updated_at=now,
        )
        
        assert workout.status == WorkoutStatus.COMPLETED
        assert not workout.is_active
    
    def test_workout_status_cancelled(self):
        """Test workout status when cancelled."""
        now = datetime.utcnow()
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=now,
            deleted_at=now,
            created_at=now,
            updated_at=now,
        )
        
        assert workout.status == WorkoutStatus.CANCELLED
        assert not workout.is_active
    
    def test_start_workout(self):
        """Test starting a workout."""
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=datetime.utcnow(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert workout.status == WorkoutStatus.PLANNED
        
        workout.start_workout()
        
        assert workout.status == WorkoutStatus.IN_PROGRESS
        assert workout.started_at is not None
        assert workout.is_active
    
    def test_start_workout_invalid_state(self):
        """Test starting workout in invalid state."""
        now = datetime.utcnow()
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=now,
            started_at=now,
            created_at=now,
            updated_at=now,
        )
        
        with pytest.raises(ValueError, match="Can only start planned workouts"):
            workout.start_workout()
    
    def test_complete_workout(self):
        """Test completing a workout."""
        now = datetime.utcnow()
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=now,
            started_at=now,
            created_at=now,
            updated_at=now,
        )
        
        # Add some exercise sets
        exercise_sets = [
            ExerciseSet(
                id=uuid4(),
                workout_id=workout.id,
                exercise_id=uuid4(),
                set_number=1,
                weight_kg=Decimal("100.0"),
                reps_completed=10,
                rpe=Decimal("8.0"),
                created_at=now,
                updated_at=now,
            ),
            ExerciseSet(
                id=uuid4(),
                workout_id=workout.id,
                exercise_id=uuid4(),
                set_number=2,
                weight_kg=Decimal("80.0"),
                reps_completed=12,
                rpe=Decimal("7.5"),
                created_at=now,
                updated_at=now,
            ),
        ]
        workout.exercise_sets = exercise_sets
        
        workout.complete_workout()
        
        assert workout.status == WorkoutStatus.COMPLETED
        assert workout.completed_at is not None
        assert not workout.is_active
        
        # Check calculated statistics
        assert workout.total_sets == 2
        assert workout.total_exercises == 2  # Different exercise IDs
        assert workout.total_volume_load == Decimal("1960.0")  # (100*10) + (80*12)
        assert workout.average_rpe == Decimal("7.75")  # (8.0 + 7.5) / 2
        assert workout.duration_minutes is not None
    
    def test_complete_workout_invalid_state(self):
        """Test completing workout in invalid state."""
        workout = Workout(
            id=uuid4(),
            user_id=uuid4(),
            workout_date=datetime.utcnow(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        with pytest.raises(ValueError, match="Can only complete in-progress workouts"):
            workout.complete_workout()


@pytest.mark.unit
@pytest.mark.domain
class TestExerciseSetEntity:
    """Test exercise set domain entity."""
    
    def test_exercise_set_creation(self):
        """Test exercise set entity creation."""
        set_id = uuid4()
        workout_id = uuid4()
        exercise_id = uuid4()
        now = datetime.utcnow()
        
        exercise_set = ExerciseSet(
            id=set_id,
            workout_id=workout_id,
            exercise_id=exercise_id,
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
            reps_target=10,
            rir_target=2,
            rir_actual=1,
            rpe=Decimal("8.5"),
            rest_seconds=120,
            tempo="3-1-2-1",
            range_of_motion=RangeOfMotion.FULL,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            notes="Good set",
            created_at=now,
            updated_at=now,
        )
        
        assert exercise_set.id == set_id
        assert exercise_set.workout_id == workout_id
        assert exercise_set.exercise_id == exercise_id
        assert exercise_set.set_number == 1
        assert exercise_set.weight_kg == Decimal("100.0")
        assert exercise_set.reps_completed == 10
        assert exercise_set.volume_load == Decimal("1000.0")
        assert exercise_set.tempo == "3-1-2-1"
        assert exercise_set.range_of_motion == RangeOfMotion.FULL
    
    def test_exercise_set_volume_load_calculation(self):
        """Test volume load calculation."""
        exercise_set = ExerciseSet(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("75.5"),
            reps_completed=8,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert exercise_set.volume_load == Decimal("604.0")
    
    def test_exercise_set_volume_load_none(self):
        """Test volume load when weight or reps missing."""
        exercise_set = ExerciseSet(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=None,
            reps_completed=8,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        assert exercise_set.volume_load is None
    
    def test_tempo_validation_valid(self):
        """Test valid tempo validation."""
        request = ExerciseSetCreateRequest(
            exercise_id=uuid4(),
            set_number=1,
            tempo="3-1-2-1",
        )

        # Should not raise an exception
        assert request.tempo == "3-1-2-1"


@pytest.mark.unit
@pytest.mark.domain
class TestWorkoutRequests:
    """Test workout request models."""
    
    def test_workout_create_request(self):
        """Test workout create request."""
        now = datetime.utcnow()
        request = WorkoutCreateRequest(
            name="Test Workout",
            workout_date=now,
            notes="Test notes",
            is_template=False,
        )
        
        assert request.name == "Test Workout"
        assert request.workout_date == now
        assert request.notes == "Test notes"
        assert not request.is_template
    
    def test_workout_update_request(self):
        """Test workout update request."""
        now = datetime.utcnow()
        request = WorkoutUpdateRequest(
            name="Updated Workout",
            workout_date=now,
            notes="Updated notes",
        )
        
        assert request.name == "Updated Workout"
        assert request.workout_date == now
        assert request.notes == "Updated notes"
    
    def test_workout_search_filters(self):
        """Test workout search filters."""
        user_id = uuid4()
        now = datetime.utcnow()
        
        filters = WorkoutSearchFilters(
            user_id=user_id,
            status=WorkoutStatus.COMPLETED,
            is_template=False,
            date_from=now - timedelta(days=30),
            date_to=now,
            name_contains="Push",
        )
        
        assert filters.user_id == user_id
        assert filters.status == WorkoutStatus.COMPLETED
        assert not filters.is_template
        assert filters.name_contains == "Push"
