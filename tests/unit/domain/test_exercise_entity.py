"""Tests for Exercise domain entity."""

from datetime import datetime
from uuid import uuid4

import pytest

from app.domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    Exercise,
    ExerciseCreateRequest,
    ExerciseMedia,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
    MediaType,
    MovementPattern,
    MuscleGroup,
)


class TestExercise:
    """Test cases for Exercise entity."""

    def test_create_exercise_with_minimal_data(self):
        """Test creating exercise with minimal required data."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Push-up",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert exercise.name == "Push-up"
        assert exercise.primary_muscle_group == MuscleGroup.CHEST
        assert exercise.movement_pattern == MovementPattern.PUSH
        assert exercise.difficulty_level == DifficultyLevel.BEGINNER
        assert exercise.version == 1
        assert exercise.is_current_version is True
        assert exercise.is_active is True
        assert exercise.is_approved is False
        assert exercise.approval_status == ApprovalStatus.PENDING

    def test_create_exercise_with_full_data(self):
        """Test creating exercise with all data fields."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Barbell Bench Press",
            description="Classic compound chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS, MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL, Equipment.PLATE],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            video_url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            form_cues=["Keep back flat", "Control the weight"],
            setup_instructions="Lie on bench, grip bar shoulder-width apart",
            execution_steps=["Lower bar to chest", "Press up explosively"],
            common_mistakes=["Bouncing off chest", "Flaring elbows too wide"],
            safety_notes="Always use a spotter for heavy weights",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert exercise.name == "Barbell Bench Press"
        assert exercise.description == "Classic compound chest exercise"
        assert exercise.secondary_muscle_groups == [
            MuscleGroup.TRICEPS,
            MuscleGroup.SHOULDERS,
        ]
        assert exercise.equipment_required == [Equipment.BARBELL, Equipment.PLATE]
        assert len(exercise.form_cues) == 2
        assert len(exercise.execution_steps) == 2
        assert len(exercise.common_mistakes) == 2

    def test_exercise_validation_secondary_muscles_cannot_include_primary(self):
        """Test that secondary muscle groups cannot include primary muscle group."""
        with pytest.raises(
            ValueError,
            match="Secondary muscle groups cannot include the primary muscle group",
        ):
            Exercise(
                id=uuid4(),
                exercise_uuid=uuid4(),
                name="Test Exercise",
                primary_muscle_group=MuscleGroup.CHEST,
                secondary_muscle_groups=[MuscleGroup.CHEST, MuscleGroup.TRICEPS],
                movement_pattern=MovementPattern.PUSH,
                difficulty_level=DifficultyLevel.BEGINNER,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

    def test_exercise_validation_equipment_no_duplicates(self):
        """Test that equipment list cannot contain duplicates."""
        with pytest.raises(
            ValueError, match="Equipment list cannot contain duplicates"
        ):
            Exercise(
                id=uuid4(),
                exercise_uuid=uuid4(),
                name="Test Exercise",
                primary_muscle_group=MuscleGroup.CHEST,
                movement_pattern=MovementPattern.PUSH,
                difficulty_level=DifficultyLevel.BEGINNER,
                equipment_required=[Equipment.BARBELL, Equipment.BARBELL],
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

    def test_exercise_validation_empty_instruction_lists_become_none(self):
        """Test that empty instruction lists are converted to None."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            form_cues=[],
            execution_steps=[],
            common_mistakes=[],
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert exercise.form_cues is None
        assert exercise.execution_steps is None
        assert exercise.common_mistakes is None

    def test_is_deleted_method(self):
        """Test is_deleted method."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert exercise.is_deleted() is False

        exercise.deleted_at = datetime.now()
        assert exercise.is_deleted() is True

    def test_is_published_method(self):
        """Test is_published method."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Not published by default
        assert exercise.is_published() is False

        # Make it approved and active
        exercise.is_approved = True
        exercise.is_active = True
        exercise.approval_status = ApprovalStatus.APPROVED
        assert exercise.is_published() is True

        # Test with deleted exercise
        exercise.deleted_at = datetime.now()
        assert exercise.is_published() is False

    def test_get_all_muscle_groups(self):
        """Test get_all_muscle_groups method."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS, MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        all_muscles = exercise.get_all_muscle_groups()
        assert len(all_muscles) == 3
        assert MuscleGroup.CHEST in all_muscles
        assert MuscleGroup.TRICEPS in all_muscles
        assert MuscleGroup.SHOULDERS in all_muscles

    def test_get_all_muscle_groups_no_secondary(self):
        """Test get_all_muscle_groups with no secondary muscles."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        all_muscles = exercise.get_all_muscle_groups()
        assert len(all_muscles) == 1
        assert all_muscles[0] == MuscleGroup.CHEST

    def test_get_primary_media_with_no_media(self):
        """Test get_primary_media when exercise has no media."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert exercise.get_primary_media() is None

    def test_get_primary_media_with_primary_media(self):
        """Test get_primary_media when exercise has primary media."""
        media1 = ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO,
            url="https://example.com/video1.mp4",
            title="Primary Video",
            is_primary=True,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        media2 = ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.IMAGE,
            url="https://example.com/image1.jpg",
            title="Secondary Image",
            is_primary=False,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            media=[media1, media2],
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        primary_media = exercise.get_primary_media()
        assert primary_media is not None
        assert primary_media.title == "Primary Video"
        assert primary_media.is_primary is True

    def test_get_media_by_type_with_no_media(self):
        """Test get_media_by_type when exercise has no media."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        videos = exercise.get_media_by_type(MediaType.VIDEO)
        assert videos == []

    def test_get_media_by_type_with_matching_media(self):
        """Test get_media_by_type when exercise has matching media."""
        video_media = ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO,
            url="https://example.com/video1.mp4",
            title="Video Demo",
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        image_media = ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.IMAGE,
            url="https://example.com/image1.jpg",
            title="Image Demo",
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            media=[video_media, image_media],
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        videos = exercise.get_media_by_type(MediaType.VIDEO)
        assert len(videos) == 1
        assert videos[0].title == "Video Demo"

        images = exercise.get_media_by_type(MediaType.IMAGE)
        assert len(images) == 1
        assert images[0].title == "Image Demo"


class TestExerciseMedia:
    """Test cases for ExerciseMedia entity."""

    def test_create_exercise_media(self):
        """Test creating exercise media."""
        media = ExerciseMedia(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            title="Exercise Demo",
            description="Demonstration of proper form",
            file_size_bytes=1024000,
            duration_seconds=60,
            width_pixels=1920,
            height_pixels=1080,
            mime_type="video/mp4",
            sort_order=1,
            is_primary=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        assert media.media_type == MediaType.VIDEO
        assert media.title == "Exercise Demo"
        assert media.file_size_bytes == 1024000
        assert media.duration_seconds == 60
        assert media.is_primary is True
        assert media.is_active is True


class TestExerciseSearchFilters:
    """Test cases for ExerciseSearchFilters."""

    def test_create_search_filters(self):
        """Test creating search filters."""
        filters = ExerciseSearchFilters(
            name="bench press",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            is_active=True,
            is_approved=True,
        )

        assert filters.name == "bench press"
        assert filters.primary_muscle_group == MuscleGroup.CHEST
        assert filters.movement_pattern == MovementPattern.PUSH
        assert filters.difficulty_level == DifficultyLevel.INTERMEDIATE
        assert filters.current_version_only is True
        assert filters.include_deleted is False


class TestExerciseCreateRequest:
    """Test cases for ExerciseCreateRequest."""

    def test_create_request_minimal(self):
        """Test creating exercise create request with minimal data."""
        request = ExerciseCreateRequest(
            name="Push-up",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
        )

        assert request.name == "Push-up"
        assert request.primary_muscle_group == MuscleGroup.CHEST
        assert request.movement_pattern == MovementPattern.PUSH
        assert request.difficulty_level == DifficultyLevel.BEGINNER


class TestExerciseUpdateRequest:
    """Test cases for ExerciseUpdateRequest."""

    def test_update_request(self):
        """Test creating exercise update request."""
        request = ExerciseUpdateRequest(
            name="Updated Exercise Name",
            description="Updated description",
            change_reason=ChangeReason.CONTENT_UPDATE,
        )

        assert request.name == "Updated Exercise Name"
        assert request.description == "Updated description"
        assert request.change_reason == ChangeReason.CONTENT_UPDATE
