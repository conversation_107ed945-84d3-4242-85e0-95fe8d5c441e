"""
Unit tests for User domain entity.

Tests the business logic and validation rules of the User entity.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import (
    InvalidEmailError,
    WeakPasswordError
)


class TestUserEntity:
    """Test cases for User entity."""
    
    def test_create_user_with_valid_data(self) -> None:
        """Test creating a user with valid data."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            training_experience_years=2
        )
        
        assert user.email == "<EMAIL>"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.training_experience_years == 2
        assert user.is_active is True
        assert user.is_verified is False
        assert user.id is not None
        assert user.created_at is not None
        assert user.updated_at is not None
        assert user.deleted_at is None
    
    def test_create_user_with_invalid_email(self) -> None:
        """Test creating a user with invalid email raises exception."""
        with pytest.raises(InvalidEmailError):
            User.create(
                email="invalid-email",
                password="SecurePass123!"
            )
    
    def test_create_user_with_empty_email(self) -> None:
        """Test creating a user with empty email raises exception."""
        with pytest.raises(InvalidEmailError):
            User.create(
                email="",
                password="SecurePass123!"
            )
    
    def test_create_user_with_weak_password(self) -> None:
        """Test creating a user with weak password raises exception."""
        with pytest.raises(WeakPasswordError):
            User.create(
                email="<EMAIL>",
                password="weak"
            )
    
    def test_create_user_with_password_no_uppercase(self) -> None:
        """Test password validation requires uppercase letter."""
        with pytest.raises(WeakPasswordError, match="uppercase"):
            User.create(
                email="<EMAIL>",
                password="nouppercase123!"
            )
    
    def test_create_user_with_password_no_lowercase(self) -> None:
        """Test password validation requires lowercase letter."""
        with pytest.raises(WeakPasswordError, match="lowercase"):
            User.create(
                email="<EMAIL>",
                password="NOLOWERCASE123!"
            )
    
    def test_create_user_with_password_no_digit(self) -> None:
        """Test password validation requires digit."""
        with pytest.raises(WeakPasswordError, match="digit"):
            User.create(
                email="<EMAIL>",
                password="NoDigitHere!"
            )
    
    def test_email_normalization(self) -> None:
        """Test that email is normalized to lowercase."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.email == "<EMAIL>"
    
    def test_update_profile(self) -> None:
        """Test updating user profile."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        original_updated_at = user.updated_at
        
        user.update_profile(
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5
        )
        
        assert user.first_name == "Jane"
        assert user.last_name == "Smith"
        assert user.training_experience_years == 5
        assert user.updated_at > original_updated_at
    
    def test_get_full_name(self) -> None:
        """Test getting user's full name."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe"
        )
        
        assert user.get_full_name() == "John Doe"
    
    def test_get_full_name_first_only(self) -> None:
        """Test getting full name with only first name."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John"
        )
        
        assert user.get_full_name() == "John"
    
    def test_get_full_name_last_only(self) -> None:
        """Test getting full name with only last name."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            last_name="Doe"
        )
        
        assert user.get_full_name() == "Doe"
    
    def test_get_full_name_none(self) -> None:
        """Test getting full name with no names."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.get_full_name() is None
    
    def test_activate_deactivate(self) -> None:
        """Test activating and deactivating user."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.is_active is True
        
        user.deactivate()
        assert user.is_active is False
        
        user.activate()
        assert user.is_active is True
    
    def test_verify_email(self) -> None:
        """Test email verification."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.is_verified is False
        
        user.verify_email()
        assert user.is_verified is True
    
    def test_can_authenticate_active_user(self) -> None:
        """Test that active user can authenticate."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.can_authenticate() is True
    
    def test_can_authenticate_inactive_user(self) -> None:
        """Test that inactive user cannot authenticate."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        user.deactivate()
        assert user.can_authenticate() is False
    
    def test_can_authenticate_deleted_user(self) -> None:
        """Test that deleted user cannot authenticate."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        user.soft_delete()
        assert user.can_authenticate() is False
    
    def test_soft_delete_and_restore(self) -> None:
        """Test soft delete and restore functionality."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert user.is_deleted() is False
        
        user.soft_delete()
        assert user.is_deleted() is True
        assert user.deleted_at is not None
        
        user.restore()
        assert user.is_deleted() is False
        assert user.deleted_at is None
    
    def test_entity_equality(self) -> None:
        """Test entity equality based on ID."""
        user_id = uuid4()
        
        user1 = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        user2 = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="different_password"
        )
        
        assert user1 == user2  # Same ID
        
        user3 = User(
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        assert user1 != user3  # Different ID
