"""Unit tests for User domain entity.

Tests the business logic and validation rules of the User entity.
"""

from uuid import uuid4

import pytest

from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import InvalidEmailError, WeakPasswordError


class TestUserEntity:
    """Test cases for User entity."""

    def test_create_user_with_valid_data(self) -> None:
        """Test creating a user with valid data."""
        user = User.create(
            email="<EMAIL>",
            hashed_password="hashed_SecurePass123!",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            training_experience_years=2,
        )

        assert user.email == "<EMAIL>"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.training_experience_years == 2
        assert user.is_active is True
        assert user.is_verified is False
        assert user.id is not None
        assert user.created_at is not None
        assert user.updated_at is not None
        assert user.deleted_at is None

    def test_create_user_with_invalid_email(self) -> None:
        """Test creating a user with invalid email raises exception."""
        with pytest.raises(InvalidEmailError):
            User.create(email="invalid-email", hashed_password="hashed_SecurePass123!")

    def test_create_user_with_empty_email(self) -> None:
        """Test creating a user with empty email raises exception."""
        with pytest.raises(InvalidEmailError):
            User.create(email="", hashed_password="hashed_SecurePass123!")

    def test_password_validation_static_method(self) -> None:
        """Test password validation static method."""
        # Valid password
        User._validate_password("SecurePass123!")

        # Invalid passwords
        with pytest.raises(WeakPasswordError):
            User._validate_password("weak")

        with pytest.raises(WeakPasswordError, match="uppercase"):
            User._validate_password("nouppercase123!")

        with pytest.raises(WeakPasswordError, match="lowercase"):
            User._validate_password("NOLOWERCASE123!")

        with pytest.raises(WeakPasswordError, match="digit"):
            User._validate_password("NoDigitHere!")

    def test_email_normalization(self) -> None:
        """Test that email is normalized to lowercase."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.email == "<EMAIL>"

    def test_update_profile(self) -> None:
        """Test updating user profile."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        original_updated_at = user.updated_at

        user.update_profile(
            first_name="Jane", last_name="Smith", training_experience_years=5
        )

        assert user.first_name == "Jane"
        assert user.last_name == "Smith"
        assert user.training_experience_years == 5
        assert user.updated_at > original_updated_at

    def test_get_full_name(self) -> None:
        """Test getting user's full name."""
        user = User.create(
            email="<EMAIL>",
            hashed_password="hashed_SecurePass123!",
            first_name="John",
            last_name="Doe",
        )

        assert user.get_full_name() == "John Doe"

    def test_get_full_name_first_only(self) -> None:
        """Test getting full name with only first name."""
        user = User.create(
            email="<EMAIL>",
            hashed_password="hashed_SecurePass123!",
            first_name="John",
        )

        assert user.get_full_name() == "John"

    def test_get_full_name_last_only(self) -> None:
        """Test getting full name with only last name."""
        user = User.create(
            email="<EMAIL>",
            hashed_password="hashed_SecurePass123!",
            last_name="Doe",
        )

        assert user.get_full_name() == "Doe"

    def test_get_full_name_none(self) -> None:
        """Test getting full name with no names."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.get_full_name() is None

    def test_activate_deactivate(self) -> None:
        """Test activating and deactivating user."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.is_active is True

        user.deactivate()
        assert user.is_active is False

        user.activate()
        assert user.is_active is True

    def test_verify_email(self) -> None:
        """Test email verification."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.is_verified is False

        user.verify_email()
        assert user.is_verified is True

    def test_can_authenticate_active_user(self) -> None:
        """Test that active user can authenticate."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.can_authenticate() is True

    def test_can_authenticate_inactive_user(self) -> None:
        """Test that inactive user cannot authenticate."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        user.deactivate()
        assert user.can_authenticate() is False

    def test_can_authenticate_deleted_user(self) -> None:
        """Test that deleted user cannot authenticate."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        user.soft_delete()
        assert user.can_authenticate() is False

    def test_soft_delete_and_restore(self) -> None:
        """Test soft delete and restore functionality."""
        user = User.create(
            email="<EMAIL>", hashed_password="hashed_SecurePass123!"
        )

        assert user.is_deleted() is False

        user.soft_delete()
        assert user.is_deleted() is True
        assert user.deleted_at is not None

        user.restore()
        assert user.is_deleted() is False
        assert user.deleted_at is None

    def test_entity_equality(self) -> None:
        """Test entity equality based on ID."""
        user_id = uuid4()

        user1 = User(
            id=user_id, email="<EMAIL>", hashed_password="hashed_password"
        )

        user2 = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="different_password",
        )

        assert user1 == user2  # Same ID

        user3 = User(email="<EMAIL>", hashed_password="hashed_password")

        assert user1 != user3  # Different ID

    def test_entity_hash(self) -> None:
        """Test entity hash based on ID."""
        user_id = uuid4()

        user1 = User(
            id=user_id, email="<EMAIL>", hashed_password="hashed_password"
        )

        user2 = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="different_password",
        )

        assert hash(user1) == hash(user2)  # Same ID, same hash

    def test_entity_string_representation(self) -> None:
        """Test entity string representation."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="Test",
            last_name="User",
        )

        user_str = str(user)
        assert "User" in user_str
        assert "<EMAIL>" in user_str

    def test_entity_repr(self) -> None:
        """Test entity repr representation."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="Test",
            last_name="User",
        )

        user_repr = repr(user)
        assert "User" in user_repr
        assert str(user.id) in user_repr

    def test_update_profile_method(self) -> None:
        """Test update profile method."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="Original",
            last_name="Name",
            training_experience_years=1,
        )

        # Update profile
        user.update_profile(
            first_name="Updated", last_name="NewName", training_experience_years=5
        )

        assert user.first_name == "Updated"
        assert user.last_name == "NewName"
        assert user.training_experience_years == 5

    def test_password_update_via_property(self) -> None:
        """Test password update via property."""
        user = User.create(email="<EMAIL>", password="SecurePass123!")

        original_hash = user.hashed_password
        new_hash = "new_hashed_password"

        # Update password hash directly
        user.hashed_password = new_hash

        assert user.hashed_password == new_hash
        assert user.hashed_password != original_hash

    def test_full_name_property(self) -> None:
        """Test full name property if it exists."""
        user = User.create(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe",
        )

        if hasattr(user, "full_name"):
            assert user.full_name == "John Doe"
        else:
            # Test manual full name creation
            full_name = f"{user.first_name} {user.last_name}"
            assert full_name == "John Doe"

    def test_entity_equality_with_non_entity(self) -> None:
        """Test entity equality comparison with non-entity object."""
        user = User.create(email="<EMAIL>", password="SecurePass123!")

        # Compare with non-entity objects
        assert user != "string"
        assert user != 123
        assert user != None
        assert user != {"id": user.id}
        assert user != [user.id]

    def test_create_user_with_empty_password(self) -> None:
        """Test user creation with empty password."""
        with pytest.raises(WeakPasswordError, match="Password cannot be empty"):
            User.create(email="<EMAIL>", password="")
