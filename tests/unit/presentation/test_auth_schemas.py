"""Tests for auth presentation schemas."""

from datetime import datetime
from uuid import uuid4


from app.application.dto.user_dto import UserDTO
from app.presentation.schemas.auth_schema import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    TokenResponse,
)


class TestLoginRequest:
    """Test cases for LoginRequest schema."""

    def test_create_login_request_valid(self):
        """Test creating LoginRequest with valid data."""
        request = LoginRequest(
            email="<EMAIL>",
            password="SecurePass123!",
        )

        assert request.email == "<EMAIL>"
        assert request.password == "SecurePass123!"

    def test_login_request_email_validation(self):
        """Test LoginRequest email validation."""
        # Valid emails should work
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        for email in valid_emails:
            request = LoginRequest(email=email, password="password123")
            assert request.email == email

    def test_login_request_serialization(self):
        """Test LoginRequest serialization."""
        request = LoginRequest(
            email="<EMAIL>",
            password="SecurePass123!",
        )

        # Test that it can be converted to dict
        request_dict = request.model_dump()
        assert request_dict["email"] == "<EMAIL>"
        assert request_dict["password"] == "SecurePass123!"


class TestTokenResponse:
    """Test cases for TokenResponse schema."""

    def test_create_token_response(self):
        """Test creating TokenResponse."""
        response = TokenResponse(
            access_token="access_token_123",
            refresh_token="refresh_token_456",
            token_type="bearer",
        )

        assert response.access_token == "access_token_123"
        assert response.refresh_token == "refresh_token_456"
        assert response.token_type == "bearer"

    def test_token_response_default_token_type(self):
        """Test TokenResponse default token type."""
        response = TokenResponse(
            access_token="access_token_123",
            refresh_token="refresh_token_456",
        )

        assert response.token_type == "bearer"


class TestLoginResponse:
    """Test cases for LoginResponse schema."""

    def test_create_login_response(self):
        """Test creating LoginResponse."""
        user_dto = UserDTO(
            id=uuid4(),
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            is_active=True,
            is_verified=True,
            training_experience_years=3,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        response = LoginResponse(
            user=user_dto,
            access_token="access_token_123",
            refresh_token="refresh_token_456",
            token_type="bearer",
        )

        assert response.user == user_dto
        assert response.access_token == "access_token_123"
        assert response.refresh_token == "refresh_token_456"
        assert response.token_type == "bearer"

    def test_login_response_default_token_type(self):
        """Test LoginResponse default token type."""
        user_dto = UserDTO(
            id=uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        response = LoginResponse(
            user=user_dto,
            access_token="access_token_123",
            refresh_token="refresh_token_456",
        )

        assert response.token_type == "bearer"


class TestRefreshTokenRequest:
    """Test cases for RefreshTokenRequest schema."""

    def test_create_refresh_token_request(self):
        """Test creating RefreshTokenRequest."""
        request = RefreshTokenRequest(
            refresh_token="refresh_token_123",
        )

        assert request.refresh_token == "refresh_token_123"

    def test_refresh_token_request_serialization(self):
        """Test RefreshTokenRequest serialization."""
        request = RefreshTokenRequest(
            refresh_token="refresh_token_123",
        )

        # Test that it can be converted to dict
        request_dict = request.model_dump()
        assert request_dict["refresh_token"] == "refresh_token_123"
