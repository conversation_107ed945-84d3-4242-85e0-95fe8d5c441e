"""Tests for auth presentation schemas."""

from datetime import datetime
from uuid import uuid4


from app.presentation.schemas.auth_schema import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
)
from app.presentation.schemas.user_schema import UserResponse


class TestLoginRequest:
    """Test cases for LoginRequest schema."""

    def test_create_login_request_valid(self):
        """Test creating LoginRequest with valid data."""
        request = LoginRequest(
            email="<EMAIL>",
            password="SecurePass123!",
        )

        assert request.email == "<EMAIL>"
        assert request.password == "SecurePass123!"

    def test_login_request_email_validation(self):
        """Test LoginRequest email validation."""
        # Valid emails should work
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        for email in valid_emails:
            request = LoginRequest(email=email, password="password123")
            assert request.email == email

    def test_login_request_serialization(self):
        """Test LoginRequest serialization."""
        request = LoginRequest(
            email="<EMAIL>",
            password="SecurePass123!",
        )

        # Test that it can be converted to dict
        request_dict = request.model_dump()
        assert request_dict["email"] == "<EMAIL>"
        assert request_dict["password"] == "SecurePass123!"


class TestRefreshTokenResponse:
    """Test cases for RefreshTokenResponse schema."""

    def test_create_refresh_token_response(self):
        """Test creating RefreshTokenResponse."""
        response = RefreshTokenResponse(
            access_token="access_token_123",
            refresh_token="refresh_token_456",
            token_type="bearer",
        )

        assert response.access_token == "access_token_123"
        assert response.refresh_token == "refresh_token_456"
        assert response.token_type == "bearer"

    def test_refresh_token_response_default_token_type(self):
        """Test RefreshTokenResponse default token type."""
        response = RefreshTokenResponse(
            access_token="access_token_123",
            refresh_token="refresh_token_456",
        )

        assert response.token_type == "bearer"


class TestLoginResponse:
    """Test cases for LoginResponse schema."""

    def test_create_login_response(self):
        """Test creating LoginResponse."""
        user_response = UserResponse(
            id=uuid4(),
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            is_active=True,
            is_verified=True,
            training_experience_years=3,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        response = LoginResponse(
            user=user_response,
            access_token="access_token_123",
            refresh_token="refresh_token_456",
            token_type="bearer",
        )

        assert response.user == user_response
        assert response.access_token == "access_token_123"
        assert response.refresh_token == "refresh_token_456"
        assert response.token_type == "bearer"

    def test_login_response_default_token_type(self):
        """Test LoginResponse default token type."""
        user_response = UserResponse(
            id=uuid4(),
            email="<EMAIL>",
            first_name="Jane",
            last_name="Smith",
            is_active=True,
            is_verified=False,
            training_experience_years=1,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        response = LoginResponse(
            user=user_response,
            access_token="access_token_123",
            refresh_token="refresh_token_456",
        )

        assert response.token_type == "bearer"


class TestRefreshTokenRequest:
    """Test cases for RefreshTokenRequest schema."""

    def test_create_refresh_token_request(self):
        """Test creating RefreshTokenRequest."""
        request = RefreshTokenRequest(
            refresh_token="refresh_token_123",
        )

        assert request.refresh_token == "refresh_token_123"

    def test_refresh_token_request_serialization(self):
        """Test RefreshTokenRequest serialization."""
        request = RefreshTokenRequest(
            refresh_token="refresh_token_123",
        )

        # Test that it can be converted to dict
        request_dict = request.model_dump()
        assert request_dict["refresh_token"] == "refresh_token_123"
