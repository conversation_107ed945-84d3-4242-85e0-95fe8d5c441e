"""
Unit tests for FastAPI dependencies.

Tests dependency injection, authentication dependencies, and service creation.
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import uuid4
from fastapi import HTTPException
from fastapi.security import HTTPAuthorizationCredentials

from app.presentation.api.dependencies import (
    get_current_user_id,
    get_current_user,
    get_user_repository,
    get_auth_service,
    get_register_user_use_case,
    get_authenticate_user_use_case,
    get_user_profile_use_case,
    get_update_user_profile_use_case
)
from app.domain.exceptions.auth_exceptions import (
    TokenExpiredError,
    InvalidTokenError,
    UserNotFoundError
)


@pytest.mark.unit
@pytest.mark.auth
class TestAuthenticationDependencies:
    """Test authentication-related dependencies."""
    
    @pytest.fixture
    def mock_credentials(self) -> HTTPAuthorizationCredentials:
        """Mock HTTP authorization credentials."""
        return HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="valid_token_123"
        )
    
    @pytest.fixture
    def mock_invalid_credentials(self) -> HTTPAuthorizationCredentials:
        """Mock invalid HTTP authorization credentials."""
        return HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="invalid_token"
        )
    
    @pytest.fixture
    def mock_expired_credentials(self) -> HTTPAuthorizationCredentials:
        """Mock expired HTTP authorization credentials."""
        return HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="expired_token"
        )
    
    @patch('app.presentation.api.dependencies.jwt_handler')
    async def test_get_current_user_id_success(
        self,
        mock_jwt_handler: Mock,
        mock_credentials: HTTPAuthorizationCredentials
    ) -> None:
        """Test successful user ID extraction from token."""
        # Setup
        user_id = uuid4()
        mock_jwt_handler.get_token_type.return_value = "access"
        mock_jwt_handler.get_user_id_from_token.return_value = user_id
        
        # Execute
        result = await get_current_user_id(mock_credentials)
        
        # Verify
        assert result == user_id
        mock_jwt_handler.get_token_type.assert_called_once_with("valid_token_123")
        mock_jwt_handler.get_user_id_from_token.assert_called_once_with("valid_token_123")
    
    @patch('app.presentation.api.dependencies.jwt_handler')
    async def test_get_current_user_id_wrong_token_type(
        self,
        mock_jwt_handler: Mock,
        mock_credentials: HTTPAuthorizationCredentials
    ) -> None:
        """Test user ID extraction with wrong token type."""
        # Setup
        mock_jwt_handler.get_token_type.return_value = "refresh"
        
        # Execute & Verify
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_id(mock_credentials)
        
        assert exc_info.value.status_code == 401
        assert "Invalid token type" in exc_info.value.detail
    
    @patch('app.presentation.api.dependencies.jwt_handler')
    async def test_get_current_user_id_expired_token(
        self,
        mock_jwt_handler: Mock,
        mock_expired_credentials: HTTPAuthorizationCredentials
    ) -> None:
        """Test user ID extraction with expired token."""
        # Setup
        mock_jwt_handler.get_token_type.side_effect = TokenExpiredError("Token has expired")
        
        # Execute & Verify
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_id(mock_expired_credentials)
        
        assert exc_info.value.status_code == 401
        assert "expired" in exc_info.value.detail.lower()
    
    @patch('app.presentation.api.dependencies.jwt_handler')
    async def test_get_current_user_id_invalid_token(
        self,
        mock_jwt_handler: Mock,
        mock_invalid_credentials: HTTPAuthorizationCredentials
    ) -> None:
        """Test user ID extraction with invalid token."""
        # Setup
        mock_jwt_handler.get_token_type.side_effect = InvalidTokenError("Invalid token")
        
        # Execute & Verify
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_id(mock_invalid_credentials)
        
        assert exc_info.value.status_code == 401
        assert "Invalid token" in exc_info.value.detail
    
    async def test_get_current_user_success(self) -> None:
        """Test successful current user retrieval."""
        # Setup
        user_id = uuid4()
        mock_get_profile_use_case = AsyncMock()
        mock_get_profile_use_case.execute.return_value = Mock()  # User DTO
        
        # Execute
        result = await get_current_user(user_id, mock_get_profile_use_case)
        
        # Verify
        assert result == user_id
        mock_get_profile_use_case.execute.assert_called_once_with(user_id)
    
    async def test_get_current_user_not_found(self) -> None:
        """Test current user retrieval when user not found."""
        # Setup
        user_id = uuid4()
        mock_get_profile_use_case = AsyncMock()
        mock_get_profile_use_case.execute.side_effect = UserNotFoundError("User not found")
        
        # Execute & Verify
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(user_id, mock_get_profile_use_case)
        
        assert exc_info.value.status_code == 401
        assert "not found" in exc_info.value.detail.lower()


@pytest.mark.unit
class TestServiceDependencies:
    """Test service creation dependencies."""
    
    async def test_get_user_repository(self) -> None:
        """Test user repository dependency creation."""
        # Setup
        mock_db_session = AsyncMock()
        
        # Execute
        result = await get_user_repository(mock_db_session)
        
        # Verify
        from app.infrastructure.database.repositories.user_repository_impl import UserRepositoryImpl
        assert isinstance(result, UserRepositoryImpl)
    
    async def test_get_auth_service(self) -> None:
        """Test auth service dependency creation."""
        # Setup
        mock_user_repo = AsyncMock()
        
        # Execute
        result = await get_auth_service(mock_user_repo)
        
        # Verify
        from app.domain.services.auth_service import AuthService
        assert isinstance(result, AuthService)
    
    async def test_get_register_user_use_case(self) -> None:
        """Test register user use case dependency creation."""
        # Setup
        mock_auth_service = AsyncMock()
        
        # Execute
        result = await get_register_user_use_case(mock_auth_service)
        
        # Verify
        from app.application.use_cases.register_user import RegisterUserUseCase
        assert isinstance(result, RegisterUserUseCase)
    
    async def test_get_authenticate_user_use_case(self) -> None:
        """Test authenticate user use case dependency creation."""
        # Setup
        mock_auth_service = AsyncMock()
        
        # Execute
        result = await get_authenticate_user_use_case(mock_auth_service)
        
        # Verify
        from app.application.use_cases.authenticate_user import AuthenticateUserUseCase
        assert isinstance(result, AuthenticateUserUseCase)
    
    async def test_get_user_profile_use_case(self) -> None:
        """Test get user profile use case dependency creation."""
        # Setup
        mock_auth_service = AsyncMock()
        
        # Execute
        result = await get_user_profile_use_case(mock_auth_service)
        
        # Verify
        from app.application.use_cases.get_user_profile import GetUserProfileUseCase
        assert isinstance(result, GetUserProfileUseCase)
    
    async def test_get_update_user_profile_use_case(self) -> None:
        """Test update user profile use case dependency creation."""
        # Setup
        mock_auth_service = AsyncMock()
        
        # Execute
        result = await get_update_user_profile_use_case(mock_auth_service)
        
        # Verify
        from app.application.use_cases.update_user_profile import UpdateUserProfileUseCase
        assert isinstance(result, UpdateUserProfileUseCase)


@pytest.mark.unit
class TestDependencyInjection:
    """Test dependency injection patterns."""
    
    def test_dependency_chain_structure(self) -> None:
        """Test that dependency chain is properly structured."""
        # This test verifies that dependencies can be resolved
        # without circular dependencies
        
        # Database session -> Repository -> Service -> Use Case
        # This should work without issues
        
        from app.infrastructure.database.repositories.user_repository_impl import UserRepositoryImpl
        from app.domain.services.auth_service import AuthService
        from app.application.use_cases.register_user import RegisterUserUseCase
        
        # Should be able to import all dependencies
        assert UserRepositoryImpl is not None
        assert AuthService is not None
        assert RegisterUserUseCase is not None
    
    def test_dependency_isolation(self) -> None:
        """Test that dependencies are properly isolated."""
        # Each dependency should be independent and testable
        
        # Mock dependencies
        mock_db = AsyncMock()
        mock_repo = AsyncMock()
        mock_service = AsyncMock()
        
        # Should be able to create each component independently
        from app.infrastructure.database.repositories.user_repository_impl import UserRepositoryImpl
        from app.domain.services.auth_service import AuthService
        
        repo = UserRepositoryImpl(mock_db)
        service = AuthService(mock_repo)
        
        assert repo is not None
        assert service is not None
    
    async def test_async_dependency_handling(self) -> None:
        """Test that async dependencies are properly handled."""
        # All dependency functions should be async
        
        mock_db = AsyncMock()
        mock_repo = AsyncMock()
        mock_service = AsyncMock()
        
        # Should be able to await all dependency functions
        repo = await get_user_repository(mock_db)
        service = await get_auth_service(mock_repo)
        use_case = await get_register_user_use_case(mock_service)
        
        assert repo is not None
        assert service is not None
        assert use_case is not None
