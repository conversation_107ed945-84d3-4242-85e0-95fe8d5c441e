"""
Unit tests for Pydantic schemas.

Tests validation, serialization, and deserialization of API schemas.
"""

import pytest
from datetime import datetime
from uuid import uuid4
from pydantic import ValidationError

from app.presentation.schemas.user_schema import (
    UserResponse,
    UserCreateRequest,
    UserUpdateRequest
)
from app.presentation.schemas.auth_schema import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse
)


@pytest.mark.unit
class TestUserSchemas:
    """Test user-related Pydantic schemas."""
    
    def test_user_response_valid(self) -> None:
        """Test UserResponse with valid data."""
        user_data = {
            "id": str(uuid4()),
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>e",
            "is_active": True,
            "is_verified": False,
            "training_experience_years": 2,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        response = UserResponse(**user_data)
        
        assert response.email == "<EMAIL>"
        assert response.first_name == "John"
        assert response.last_name == "Doe"
        assert response.is_active is True
        assert response.is_verified is False
        assert response.training_experience_years == 2
    
    def test_user_response_with_none_values(self) -> None:
        """Test UserResponse with None values for optional fields."""
        user_data = {
            "id": str(uuid4()),
            "email": "<EMAIL>",
            "first_name": None,
            "last_name": None,
            "is_active": True,
            "is_verified": False,
            "training_experience_years": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        response = UserResponse(**user_data)
        
        assert response.first_name is None
        assert response.last_name is None
        assert response.training_experience_years is None
    
    def test_user_create_request_valid(self) -> None:
        """Test UserCreateRequest with valid data."""
        request_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "John",
            "last_name": "Doe",
            "training_experience_years": 2
        }
        
        request = UserCreateRequest(**request_data)
        
        assert request.email == "<EMAIL>"
        assert request.password == "SecurePass123!"
        assert request.first_name == "John"
        assert request.last_name == "Doe"
        assert request.training_experience_years == 2
    
    def test_user_create_request_minimal(self) -> None:
        """Test UserCreateRequest with minimal required fields."""
        request_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!"
        }
        
        request = UserCreateRequest(**request_data)
        
        assert request.email == "<EMAIL>"
        assert request.password == "SecurePass123!"
        assert request.first_name is None
        assert request.last_name is None
        assert request.training_experience_years is None
    
    @pytest.mark.parametrize("invalid_email", [
        "invalid-email",
        "@example.com",
        "test@",
        "test.example.com",
        "",
    ])
    def test_user_create_request_invalid_email(self, invalid_email: str) -> None:
        """Test UserCreateRequest with invalid email formats."""
        request_data = {
            "email": invalid_email,
            "password": "SecurePass123!"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            UserCreateRequest(**request_data)
        
        assert "email" in str(exc_info.value).lower()
    
    @pytest.mark.parametrize("weak_password", [
        "weak",
        "nouppercase123!",
        "NOLOWERCASE123!",
        "NoDigitHere!",
        "",
    ])
    def test_user_create_request_weak_password(self, weak_password: str) -> None:
        """Test UserCreateRequest with weak passwords."""
        request_data = {
            "email": "<EMAIL>",
            "password": weak_password
        }
        
        with pytest.raises(ValidationError) as exc_info:
            UserCreateRequest(**request_data)
        
        error_msg = str(exc_info.value).lower()
        assert "password" in error_msg
    
    def test_user_create_request_invalid_training_years(self) -> None:
        """Test UserCreateRequest with invalid training experience years."""
        request_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "training_experience_years": -1
        }
        
        with pytest.raises(ValidationError) as exc_info:
            UserCreateRequest(**request_data)
        
        assert "training_experience_years" in str(exc_info.value)
    
    def test_user_create_request_name_validation(self) -> None:
        """Test UserCreateRequest name field validation."""
        # Test with whitespace-only names
        request_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "   ",
            "last_name": "   "
        }
        
        request = UserCreateRequest(**request_data)
        
        # Should be converted to None
        assert request.first_name is None
        assert request.last_name is None
    
    def test_user_update_request_valid(self) -> None:
        """Test UserUpdateRequest with valid data."""
        request_data = {
            "first_name": "Jane",
            "last_name": "Smith",
            "training_experience_years": 5
        }
        
        request = UserUpdateRequest(**request_data)
        
        assert request.first_name == "Jane"
        assert request.last_name == "Smith"
        assert request.training_experience_years == 5
    
    def test_user_update_request_partial(self) -> None:
        """Test UserUpdateRequest with partial data."""
        request_data = {"first_name": "Jane"}
        
        request = UserUpdateRequest(**request_data)
        
        assert request.first_name == "Jane"
        assert request.last_name is None
        assert request.training_experience_years is None
    
    def test_user_update_request_empty(self) -> None:
        """Test UserUpdateRequest with no data."""
        request = UserUpdateRequest()
        
        assert request.first_name is None
        assert request.last_name is None
        assert request.training_experience_years is None


@pytest.mark.unit
class TestAuthSchemas:
    """Test authentication-related Pydantic schemas."""
    
    def test_login_request_valid(self) -> None:
        """Test LoginRequest with valid data."""
        request_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!"
        }
        
        request = LoginRequest(**request_data)
        
        assert request.email == "<EMAIL>"
        assert request.password == "SecurePass123!"
    
    def test_login_request_invalid_email(self) -> None:
        """Test LoginRequest with invalid email."""
        request_data = {
            "email": "invalid-email",
            "password": "SecurePass123!"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            LoginRequest(**request_data)
        
        assert "email" in str(exc_info.value).lower()
    
    def test_login_response_valid(self) -> None:
        """Test LoginResponse with valid data."""
        user_data = {
            "id": str(uuid4()),
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "is_active": True,
            "is_verified": False,
            "training_experience_years": 2,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        response_data = {
            "user": user_data,
            "access_token": "access_token_123",
            "refresh_token": "refresh_token_456",
            "token_type": "bearer"
        }
        
        response = LoginResponse(**response_data)
        
        assert response.user.email == "<EMAIL>"
        assert response.access_token == "access_token_123"
        assert response.refresh_token == "refresh_token_456"
        assert response.token_type == "bearer"
    
    def test_refresh_token_request_valid(self) -> None:
        """Test RefreshTokenRequest with valid data."""
        request_data = {
            "refresh_token": "refresh_token_123"
        }
        
        request = RefreshTokenRequest(**request_data)
        
        assert request.refresh_token == "refresh_token_123"
    
    def test_refresh_token_response_valid(self) -> None:
        """Test RefreshTokenResponse with valid data."""
        response_data = {
            "access_token": "new_access_token_123",
            "refresh_token": "new_refresh_token_456",
            "token_type": "bearer"
        }
        
        response = RefreshTokenResponse(**response_data)
        
        assert response.access_token == "new_access_token_123"
        assert response.refresh_token == "new_refresh_token_456"
        assert response.token_type == "bearer"
    
    def test_schema_json_serialization(self) -> None:
        """Test that schemas can be serialized to JSON."""
        user_data = {
            "id": str(uuid4()),
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "is_active": True,
            "is_verified": False,
            "training_experience_years": 2,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        response = UserResponse(**user_data)
        json_str = response.json()
        
        assert isinstance(json_str, str)
        assert "<EMAIL>" in json_str
        assert "John" in json_str
