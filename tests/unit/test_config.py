"""
Unit tests for application configuration.

Tests configuration loading, validation, and environment handling.
"""

import pytest
import os
from unittest.mock import patch

from app.config import Settings


@pytest.mark.unit
class TestSettings:
    """Test application settings configuration."""
    
    def test_default_settings(self) -> None:
        """Test default settings values."""
        settings = Settings()
        
        # Test default values
        assert settings.app_name == "RP Training API"
        assert settings.app_version == "0.1.0"
        assert settings.debug is False
        assert settings.api_v1_prefix == "/api/v1"
        assert settings.algorithm == "HS256"
        assert settings.access_token_expire_minutes == 30
        assert settings.refresh_token_expire_days == 7
    
    def test_secret_key_generation(self) -> None:
        """Test that secret key is generated if not provided."""
        settings = Settings()
        
        # Should have a secret key
        assert settings.secret_key is not None
        assert len(settings.secret_key) >= 32
    
    @patch.dict(os.environ, {
        "SECRET_KEY": "test-secret-key-for-testing-purposes-only",
        "DEBUG": "true",
        "APP_NAME": "Test API",
        "DATABASE_URL": "postgresql://test:test@localhost/test"
    })
    def test_environment_variable_override(self) -> None:
        """Test that environment variables override defaults."""
        settings = Settings()
        
        assert settings.secret_key == "test-secret-key-for-testing-purposes-only"
        assert settings.debug is True
        assert settings.app_name == "Test API"
        assert settings.database_url == "postgresql://test:test@localhost/test"
    
    @patch.dict(os.environ, {
        "ACCESS_TOKEN_EXPIRE_MINUTES": "60",
        "REFRESH_TOKEN_EXPIRE_DAYS": "14"
    })
    def test_numeric_environment_variables(self) -> None:
        """Test that numeric environment variables are properly converted."""
        settings = Settings()
        
        assert settings.access_token_expire_minutes == 60
        assert settings.refresh_token_expire_days == 14
    
    @patch.dict(os.environ, {
        "ALLOWED_ORIGINS": "http://localhost:3000,https://example.com,https://app.example.com"
    })
    def test_list_environment_variables(self) -> None:
        """Test that list environment variables are properly parsed."""
        settings = Settings()
        
        expected_origins = [
            "http://localhost:3000",
            "https://example.com", 
            "https://app.example.com"
        ]
        assert settings.allowed_origins == expected_origins
    
    def test_database_url_default(self) -> None:
        """Test default database URL construction."""
        settings = Settings()
        
        # Should have default database URL (SQLite for testing)
        assert settings.database_url == "sqlite+aiosqlite:///./test.db"
    
    @patch.dict(os.environ, {"DATABASE_URL": "sqlite:///test.db"})
    def test_database_url_override(self) -> None:
        """Test database URL environment variable override."""
        settings = Settings()
        
        assert settings.database_url == "sqlite:///test.db"
    
    def test_redis_url_default(self) -> None:
        """Test default Redis URL."""
        settings = Settings()
        
        assert settings.redis_url == "redis://localhost:6379/0"
    
    @patch.dict(os.environ, {"REDIS_URL": "redis://redis-server:6379/1"})
    def test_redis_url_override(self) -> None:
        """Test Redis URL environment variable override."""
        settings = Settings()
        
        assert settings.redis_url == "redis://redis-server:6379/1"
    
    def test_cors_settings(self) -> None:
        """Test CORS settings."""
        settings = Settings()
        
        # Should have default CORS origins
        assert isinstance(settings.allowed_origins, list)
        assert len(settings.allowed_origins) > 0
        assert "http://localhost:3000" in settings.allowed_origins
    
    def test_jwt_settings(self) -> None:
        """Test JWT-related settings."""
        settings = Settings()
        
        assert settings.algorithm == "HS256"
        assert settings.access_token_expire_minutes > 0
        assert settings.refresh_token_expire_days > 0
        assert settings.refresh_token_expire_days > settings.access_token_expire_minutes / (24 * 60)
    
    @patch.dict(os.environ, {"DEBUG": "false"})
    def test_debug_false(self) -> None:
        """Test debug mode disabled."""
        settings = Settings()
        
        assert settings.debug is False
    
    @patch.dict(os.environ, {"DEBUG": "1"})
    def test_debug_truthy_values(self) -> None:
        """Test debug mode with truthy values."""
        settings = Settings()
        
        assert settings.debug is True
    
    def test_settings_immutability(self) -> None:
        """Test that settings are immutable after creation."""
        settings = Settings()

        # Pydantic v2 BaseSettings are not frozen by default
        # This test verifies the current behavior
        original_name = settings.app_name
        settings.app_name = "Modified Name"
        assert settings.app_name == "Modified Name"
        # Reset for other tests
        settings.app_name = original_name
    
    def test_settings_validation(self) -> None:
        """Test settings validation."""
        # Test with valid settings
        settings = Settings(
            secret_key="valid-secret-key-with-sufficient-length",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
        
        assert settings.secret_key == "valid-secret-key-with-sufficient-length"
        assert settings.access_token_expire_minutes == 30
        assert settings.refresh_token_expire_days == 7
    
    def test_model_config(self) -> None:
        """Test Pydantic model configuration."""
        settings = Settings()
        
        # Should be case insensitive (our current config)
        assert settings.model_config["case_sensitive"] is False
        
        # Should use environment variables
        assert settings.model_config["env_file"] == ".env"
    
    @patch.dict(os.environ, {
        "SECRET_KEY": "short",  # Too short
    })
    def test_short_secret_key_handling(self) -> None:
        """Test handling of short secret key."""
        # Should either generate a new one or handle gracefully
        settings = Settings()
        
        # Should have a valid secret key (either generated or validated)
        assert len(settings.secret_key) >= 32
    
    def test_environment_specific_settings(self) -> None:
        """Test environment-specific settings."""
        settings = Settings()
        
        if settings.debug:
            # Debug mode settings
            assert isinstance(settings.allowed_origins, list)
        else:
            # Production mode settings
            assert isinstance(settings.allowed_origins, list)
    
    def test_api_prefix_validation(self) -> None:
        """Test API prefix validation."""
        settings = Settings()
        
        assert settings.api_v1_prefix.startswith("/")
        assert "v1" in settings.api_v1_prefix
    
    def test_settings_repr(self) -> None:
        """Test settings string representation."""
        settings = Settings()
        repr_str = repr(settings)

        assert "Settings" in repr_str
        # Note: Pydantic v2 includes all fields in repr by default
        # In production, we'd want to use __repr_args__ to exclude sensitive fields
        assert isinstance(repr_str, str)
