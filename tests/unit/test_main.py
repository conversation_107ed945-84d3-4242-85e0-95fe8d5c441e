"""
Unit tests for main application module.

Tests FastAPI application creation, configuration, and middleware setup.
"""

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from app.main import app, create_application
from app.config import settings


@pytest.mark.unit
class TestMainApplication:
    """Test main FastAPI application."""
    
    def test_app_creation(self) -> None:
        """Test that app is properly created."""
        assert isinstance(app, FastAPI)
        assert app.title == settings.app_name
        assert app.version == settings.app_version
    
    def test_create_application_function(self) -> None:
        """Test create_application function."""
        test_app = create_application()
        
        assert isinstance(test_app, FastAPI)
        assert test_app.title == settings.app_name
        assert test_app.version == settings.app_version
    
    def test_app_metadata(self) -> None:
        """Test application metadata."""
        assert app.title == "RP Training API"
        assert app.description is not None
        assert "Renaissance Periodization" in app.description
        assert app.version == "0.1.0"
    
    def test_app_routes_registered(self) -> None:
        """Test that routes are properly registered."""
        # Get all routes
        routes = [route.path for route in app.routes]
        
        # Should have health routes
        assert any("/health" in route for route in routes)
        
        # Should have auth routes
        assert any("/auth" in route for route in routes)
        
        # Should have API v1 prefix routes
        assert any("/api/v1" in route for route in routes)
    
    def test_cors_middleware_configured(self) -> None:
        """Test that CORS middleware is configured."""
        # Check if CORS middleware is in the middleware stack
        middleware_types = [type(middleware.cls) for middleware in app.user_middleware]
        
        # Should have CORS middleware
        from fastapi.middleware.cors import CORSMiddleware
        assert CORSMiddleware in middleware_types
    
    def test_app_openapi_configuration(self) -> None:
        """Test OpenAPI documentation configuration."""
        openapi_schema = app.openapi()
        
        assert openapi_schema["info"]["title"] == settings.app_name
        assert openapi_schema["info"]["version"] == settings.app_version
        assert "paths" in openapi_schema
        assert len(openapi_schema["paths"]) > 0
    
    def test_app_startup_events(self) -> None:
        """Test application startup events."""
        # Check if startup events are configured
        assert hasattr(app, 'router')
        
        # Should have startup event handlers
        startup_handlers = app.router.on_startup
        assert len(startup_handlers) >= 0  # May have startup handlers
    
    def test_app_shutdown_events(self) -> None:
        """Test application shutdown events."""
        # Check if shutdown events are configured
        shutdown_handlers = app.router.on_shutdown
        assert len(shutdown_handlers) >= 0  # May have shutdown handlers
    
    def test_app_exception_handlers(self) -> None:
        """Test that exception handlers are configured."""
        # Should have exception handlers configured
        assert hasattr(app, 'exception_handlers')
    
    def test_app_middleware_order(self) -> None:
        """Test middleware order and configuration."""
        # Get middleware stack
        middleware_stack = app.user_middleware
        
        # Should have middleware configured
        assert len(middleware_stack) >= 1  # At least CORS middleware
    
    def test_app_debug_mode(self) -> None:
        """Test application debug mode configuration."""
        # Debug mode should match settings
        if settings.debug:
            # In debug mode, should have additional features
            assert app.debug == settings.debug
        else:
            # In production mode
            assert app.debug == settings.debug


@pytest.mark.unit
class TestApplicationIntegration:
    """Test application integration with test client."""
    
    def test_app_test_client_creation(self) -> None:
        """Test that test client can be created."""
        client = TestClient(app)
        assert client is not None
    
    def test_app_basic_functionality(self) -> None:
        """Test basic application functionality."""
        client = TestClient(app)
        
        # Test that app responds to requests
        response = client.get("/api/v1/health")
        assert response.status_code in [200, 404]  # Either works or route not found
    
    def test_app_openapi_endpoint(self) -> None:
        """Test OpenAPI documentation endpoint."""
        client = TestClient(app)
        
        # Test OpenAPI schema endpoint
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == settings.app_name
    
    def test_app_docs_endpoint(self) -> None:
        """Test documentation endpoint."""
        client = TestClient(app)
        
        # Test Swagger UI endpoint
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_app_redoc_endpoint(self) -> None:
        """Test ReDoc documentation endpoint."""
        client = TestClient(app)
        
        # Test ReDoc endpoint
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_app_cors_headers(self) -> None:
        """Test CORS headers in responses."""
        client = TestClient(app)
        
        # Test OPTIONS request for CORS
        response = client.options("/api/v1/health")
        
        # Should have CORS headers (if CORS is configured)
        if response.status_code == 200:
            headers = response.headers
            # May have CORS headers
            assert "access-control-allow-origin" in headers or response.status_code == 405
    
    def test_app_error_handling(self) -> None:
        """Test application error handling."""
        client = TestClient(app)
        
        # Test non-existent endpoint
        response = client.get("/non-existent-endpoint")
        assert response.status_code == 404
        
        # Should return JSON error response
        if response.headers.get("content-type", "").startswith("application/json"):
            data = response.json()
            assert "detail" in data
    
    def test_app_method_not_allowed(self) -> None:
        """Test method not allowed handling."""
        client = TestClient(app)
        
        # Test wrong HTTP method on existing endpoint
        response = client.post("/docs")  # GET endpoint called with POST
        assert response.status_code == 405  # Method Not Allowed
    
    def test_app_request_validation(self) -> None:
        """Test request validation handling."""
        client = TestClient(app)
        
        # Test invalid JSON request (if we have POST endpoints)
        response = client.post(
            "/api/v1/auth/register",
            json={"invalid": "data"},
            headers={"Content-Type": "application/json"}
        )
        
        # Should handle validation errors gracefully
        assert response.status_code in [400, 404, 422]  # Various validation errors
