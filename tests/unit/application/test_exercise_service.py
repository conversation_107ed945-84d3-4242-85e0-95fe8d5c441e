"""
Unit tests for exercise application service.

Tests for exercise service operations including CRUD, search,
approval workflows, and media management.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock
from uuid import uuid4

from app.application.exceptions import BusinessRuleViolationError, NotFoundError
from app.application.services.exercise_service import ExerciseService
from app.domain.entities.exercise import (
    ApprovalStatus,
    Exercise,
    ExerciseCreateRequest,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
    MuscleGroup,
)
from app.domain.repositories.exercise_repository import ExerciseRepository


@pytest.mark.unit
@pytest.mark.service
class TestExerciseService:
    """Test exercise application service."""
    
    @pytest.fixture
    def mock_repository(self):
        """Create mock exercise repository."""
        return AsyncMock(spec=ExerciseRepository)
    
    @pytest.fixture
    def exercise_service(self, mock_repository):
        """Create exercise service with mock repository."""
        return ExerciseService(mock_repository)
    
    @pytest.fixture
    def sample_exercise(self):
        """Create sample exercise entity."""
        now = datetime.utcnow()
        return Exercise(
            id=uuid4(),
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS],
            equipment_needed=["Barbell", "Bench"],
            difficulty_level=3,
            instructions=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_exercise_success(self, exercise_service, mock_repository, sample_exercise):
        """Test successful exercise creation."""
        request = ExerciseCreateRequest(
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS],
            equipment_needed=["Barbell"],
            difficulty_level=3,
            instructions=["Lie on bench"],
        )
        created_by = uuid4()
        
        mock_repository.create_exercise.return_value = sample_exercise
        
        result = await exercise_service.create_exercise(request, created_by)
        
        assert result == sample_exercise
        mock_repository.create_exercise.assert_called_once_with(request, created_by)
    
    async def test_get_exercise_success(self, exercise_service, mock_repository, sample_exercise):
        """Test successful exercise retrieval."""
        exercise_id = sample_exercise.id
        mock_repository.get_exercise_by_id.return_value = sample_exercise
        
        result = await exercise_service.get_exercise(exercise_id)
        
        assert result == sample_exercise
        mock_repository.get_exercise_by_id.assert_called_once_with(exercise_id)
    
    async def test_get_exercise_not_found(self, exercise_service, mock_repository):
        """Test exercise retrieval when not found."""
        exercise_id = uuid4()
        mock_repository.get_exercise_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_service.get_exercise(exercise_id)
        
        mock_repository.get_exercise_by_id.assert_called_once_with(exercise_id)
    
    async def test_update_exercise_success(self, exercise_service, mock_repository, sample_exercise):
        """Test successful exercise update."""
        exercise_id = sample_exercise.id
        request = ExerciseUpdateRequest(name="Updated Exercise")
        updated_by = uuid4()
        
        mock_repository.update_exercise.return_value = sample_exercise
        
        result = await exercise_service.update_exercise(exercise_id, request, updated_by)
        
        assert result == sample_exercise
        mock_repository.update_exercise.assert_called_once_with(exercise_id, request, updated_by)
    
    async def test_update_exercise_not_found(self, exercise_service, mock_repository):
        """Test exercise update when not found."""
        exercise_id = uuid4()
        request = ExerciseUpdateRequest(name="Updated Exercise")
        updated_by = uuid4()
        
        mock_repository.update_exercise.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_service.update_exercise(exercise_id, request, updated_by)
    
    async def test_delete_exercise_success(self, exercise_service, mock_repository):
        """Test successful exercise deletion."""
        exercise_id = uuid4()
        deleted_by = uuid4()
        
        mock_repository.delete_exercise.return_value = True
        
        result = await exercise_service.delete_exercise(exercise_id, deleted_by)
        
        assert result is True
        mock_repository.delete_exercise.assert_called_once_with(exercise_id, deleted_by)
    
    async def test_delete_exercise_not_found(self, exercise_service, mock_repository):
        """Test exercise deletion when not found."""
        exercise_id = uuid4()
        deleted_by = uuid4()
        
        mock_repository.delete_exercise.return_value = False
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_service.delete_exercise(exercise_id, deleted_by)
    
    async def test_search_exercises(self, exercise_service, mock_repository, sample_exercise):
        """Test searching exercises."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.CHEST)
        exercises = [sample_exercise]
        
        mock_repository.search_exercises.return_value = exercises
        
        result = await exercise_service.search_exercises(filters)
        
        assert result == exercises
        mock_repository.search_exercises.assert_called_once_with(filters, 50, 0)
    
    async def test_search_exercises_with_pagination(self, exercise_service, mock_repository, sample_exercise):
        """Test searching exercises with pagination."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.CHEST)
        exercises = [sample_exercise]
        
        mock_repository.search_exercises.return_value = exercises
        
        result = await exercise_service.search_exercises(filters, limit=20, offset=10)
        
        assert result == exercises
        mock_repository.search_exercises.assert_called_once_with(filters, 20, 10)
    
    async def test_approve_exercise_success(self, exercise_service, mock_repository, sample_exercise):
        """Test successful exercise approval."""
        exercise_id = sample_exercise.id
        approved_by = uuid4()
        notes = "Looks good"
        
        mock_repository.approve_exercise.return_value = sample_exercise
        
        result = await exercise_service.approve_exercise(exercise_id, approved_by, notes)
        
        assert result == sample_exercise
        mock_repository.approve_exercise.assert_called_once_with(exercise_id, approved_by, notes)
    
    async def test_approve_exercise_not_found(self, exercise_service, mock_repository):
        """Test exercise approval when not found."""
        exercise_id = uuid4()
        approved_by = uuid4()
        
        mock_repository.approve_exercise.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_service.approve_exercise(exercise_id, approved_by)
    
    async def test_reject_exercise_success(self, exercise_service, mock_repository, sample_exercise):
        """Test successful exercise rejection."""
        exercise_id = sample_exercise.id
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        mock_repository.reject_exercise.return_value = sample_exercise
        
        result = await exercise_service.reject_exercise(exercise_id, rejected_by, reason)
        
        assert result == sample_exercise
        mock_repository.reject_exercise.assert_called_once_with(exercise_id, rejected_by, reason)
    
    async def test_reject_exercise_not_found(self, exercise_service, mock_repository):
        """Test exercise rejection when not found."""
        exercise_id = uuid4()
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        mock_repository.reject_exercise.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_service.reject_exercise(exercise_id, rejected_by, reason)
    
    async def test_get_exercises_by_muscle_group(self, exercise_service, mock_repository, sample_exercise):
        """Test getting exercises by muscle group."""
        muscle_group = MuscleGroup.CHEST
        exercises = [sample_exercise]
        
        # Mock the search_exercises method
        with pytest.mock.patch.object(exercise_service, 'search_exercises') as mock_search:
            mock_search.return_value = exercises
            
            result = await exercise_service.get_exercises_by_muscle_group(muscle_group)
            
            assert result == exercises
            # Verify the correct filters were used
            call_args = mock_search.call_args[0][0]  # First positional argument (filters)
            assert call_args.muscle_group == muscle_group
            assert call_args.approval_status == ApprovalStatus.APPROVED
    
    async def test_get_pending_exercises(self, exercise_service, mock_repository, sample_exercise):
        """Test getting pending exercises."""
        exercises = [sample_exercise]
        
        # Mock the search_exercises method
        with pytest.mock.patch.object(exercise_service, 'search_exercises') as mock_search:
            mock_search.return_value = exercises
            
            result = await exercise_service.get_pending_exercises()
            
            assert result == exercises
            # Verify the correct filters were used
            call_args = mock_search.call_args[0][0]  # First positional argument (filters)
            assert call_args.approval_status == ApprovalStatus.PENDING
    
    async def test_get_user_exercises(self, exercise_service, mock_repository, sample_exercise):
        """Test getting user exercises."""
        user_id = uuid4()
        exercises = [sample_exercise]
        
        # Mock the search_exercises method
        with pytest.mock.patch.object(exercise_service, 'search_exercises') as mock_search:
            mock_search.return_value = exercises
            
            result = await exercise_service.get_user_exercises(user_id)
            
            assert result == exercises
            # Verify the correct filters were used
            call_args = mock_search.call_args[0][0]  # First positional argument (filters)
            assert call_args.created_by == user_id
    
    async def test_exercise_exists(self, exercise_service, mock_repository):
        """Test checking if exercise exists."""
        exercise_id = uuid4()
        mock_repository.exists.return_value = True
        
        result = await exercise_service.exercise_exists(exercise_id)
        
        assert result is True
        mock_repository.exists.assert_called_once_with(exercise_id)
    
    async def test_exercise_not_exists(self, exercise_service, mock_repository):
        """Test checking if exercise doesn't exist."""
        exercise_id = uuid4()
        mock_repository.exists.return_value = False
        
        result = await exercise_service.exercise_exists(exercise_id)
        
        assert result is False
        mock_repository.exists.assert_called_once_with(exercise_id)
    
    async def test_get_exercise_statistics(self, exercise_service, mock_repository):
        """Test getting exercise statistics."""
        exercise_id = uuid4()
        stats = {"usage_count": 10, "average_rating": 4.5}
        
        mock_repository.get_exercise_statistics.return_value = stats
        
        result = await exercise_service.get_exercise_statistics(exercise_id)
        
        assert result == stats
        mock_repository.get_exercise_statistics.assert_called_once_with(exercise_id)
    
    async def test_bulk_approve_exercises(self, exercise_service, mock_repository):
        """Test bulk approving exercises."""
        exercise_ids = [uuid4(), uuid4(), uuid4()]
        approved_by = uuid4()
        
        mock_repository.bulk_approve_exercises.return_value = 3
        
        result = await exercise_service.bulk_approve_exercises(exercise_ids, approved_by)
        
        assert result == 3
        mock_repository.bulk_approve_exercises.assert_called_once_with(exercise_ids, approved_by)
    
    async def test_get_popular_exercises(self, exercise_service, mock_repository, sample_exercise):
        """Test getting popular exercises."""
        exercises = [sample_exercise]
        
        mock_repository.get_popular_exercises.return_value = exercises
        
        result = await exercise_service.get_popular_exercises()
        
        assert result == exercises
        mock_repository.get_popular_exercises.assert_called_once_with(10)
    
    async def test_get_popular_exercises_with_limit(self, exercise_service, mock_repository, sample_exercise):
        """Test getting popular exercises with custom limit."""
        exercises = [sample_exercise]
        
        mock_repository.get_popular_exercises.return_value = exercises
        
        result = await exercise_service.get_popular_exercises(limit=5)
        
        assert result == exercises
        mock_repository.get_popular_exercises.assert_called_once_with(5)
