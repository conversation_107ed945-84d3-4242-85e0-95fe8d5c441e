"""
Unit tests for application layer exceptions.

Tests for custom exceptions used in the application layer
including validation, business rule violations, and not found errors.
"""

import pytest

from app.application.exceptions import (
    ApplicationError,
    BusinessRuleViolationError,
    ConfigurationError,
    ConflictError,
    ConcurrencyError,
    DataIntegrityError,
    ExternalServiceError,
    ForbiddenError,
    NotFoundError,
    RateLimitError,
    UnauthorizedError,
    ValidationError,
)


@pytest.mark.unit
@pytest.mark.application
class TestApplicationExceptions:
    """Test application layer exceptions."""
    
    def test_application_error_base(self):
        """Test base application error."""
        message = "Something went wrong"
        error = ApplicationError(message)

        assert str(error) == message
        assert error.message == message
        assert error.error_code == "ApplicationError"
        assert isinstance(error, Exception)

    def test_application_error_with_error_code(self):
        """Test application error with custom error code."""
        message = "Validation failed"
        error_code = "CUSTOM_ERROR"
        error = ApplicationError(message, error_code)

        assert str(error) == message
        assert error.message == message
        assert error.error_code == error_code
    
    def test_validation_error(self):
        """Test validation error."""
        message = "Invalid input data"
        field = "email"
        error = ValidationError(message, field)

        assert str(error) == message
        assert error.message == message
        assert error.field == field
        assert error.error_code == "VALIDATION_ERROR"
        assert isinstance(error, ApplicationError)

    def test_validation_error_no_field(self):
        """Test validation error without field."""
        message = "General validation error"
        error = ValidationError(message)

        assert str(error) == message
        assert error.message == message
        assert error.field is None
        assert error.error_code == "VALIDATION_ERROR"
    
    def test_not_found_error(self):
        """Test not found error."""
        message = "User not found"
        resource_type = "User"
        resource_id = "123"
        error = NotFoundError(message, resource_type, resource_id)

        assert str(error) == message
        assert error.message == message
        assert error.resource_type == resource_type
        assert error.resource_id == resource_id
        assert error.error_code == "NOT_FOUND"
        assert isinstance(error, ApplicationError)

    def test_not_found_error_simple(self):
        """Test not found error with just message."""
        message = "Resource not found"
        error = NotFoundError(message)

        assert str(error) == message
        assert error.message == message
        assert error.resource_type is None
        assert error.resource_id is None
    
    def test_business_rule_violation_error(self):
        """Test business rule violation error."""
        message = "User cannot have multiple active workouts"
        rule = "single_active_workout"
        error = BusinessRuleViolationError(message, rule)

        assert str(error) == message
        assert error.message == message
        assert error.rule == rule
        assert error.error_code == "BUSINESS_RULE_VIOLATION"
        assert isinstance(error, ApplicationError)

    def test_business_rule_violation_error_no_rule(self):
        """Test business rule violation error without rule."""
        message = "Business rule violated"
        error = BusinessRuleViolationError(message)

        assert str(error) == message
        assert error.message == message
        assert error.rule is None
    
    def test_unauthorized_error(self):
        """Test unauthorized error."""
        message = "Access denied"
        error = UnauthorizedError(message)

        assert str(error) == message
        assert error.message == message
        assert error.error_code == "UNAUTHORIZED"
        assert isinstance(error, ApplicationError)

    def test_unauthorized_error_default(self):
        """Test unauthorized error with default message."""
        error = UnauthorizedError()

        assert str(error) == "Unauthorized access"
        assert error.message == "Unauthorized access"
        assert error.error_code == "UNAUTHORIZED"
    
    def test_conflict_error(self):
        """Test conflict error."""
        message = "Resource already exists"
        conflicting_resource = "User with email"
        error = ConflictError(message, conflicting_resource)

        assert str(error) == message
        assert error.message == message
        assert error.conflicting_resource == conflicting_resource
        assert error.error_code == "CONFLICT"
        assert isinstance(error, ApplicationError)

    def test_forbidden_error(self):
        """Test forbidden error."""
        message = "Access forbidden"
        error = ForbiddenError(message)

        assert str(error) == message
        assert error.message == message
        assert error.error_code == "FORBIDDEN"
        assert isinstance(error, ApplicationError)

    def test_external_service_error(self):
        """Test external service error."""
        message = "Service unavailable"
        service_name = "payment_service"
        status_code = 503
        error = ExternalServiceError(message, service_name, status_code)

        assert str(error) == message
        assert error.message == message
        assert error.service_name == service_name
        assert error.status_code == status_code
        assert error.error_code == "EXTERNAL_SERVICE_ERROR"
        assert isinstance(error, ApplicationError)

    def test_rate_limit_error(self):
        """Test rate limit error."""
        message = "Too many requests"
        retry_after = 60
        error = RateLimitError(message, retry_after)

        assert str(error) == message
        assert error.message == message
        assert error.retry_after == retry_after
        assert error.error_code == "RATE_LIMIT_EXCEEDED"
        assert isinstance(error, ApplicationError)
    
    def test_data_integrity_error(self):
        """Test data integrity error."""
        message = "Foreign key constraint violated"
        constraint = "fk_user_workout"
        error = DataIntegrityError(message, constraint)

        assert str(error) == message
        assert error.message == message
        assert error.constraint == constraint
        assert error.error_code == "DATA_INTEGRITY_ERROR"
        assert isinstance(error, ApplicationError)

    def test_concurrency_error(self):
        """Test concurrency error."""
        message = "Resource was modified by another user"
        error = ConcurrencyError(message)

        assert str(error) == message
        assert error.message == message
        assert error.error_code == "CONCURRENCY_ERROR"
        assert isinstance(error, ApplicationError)

    def test_configuration_error(self):
        """Test configuration error."""
        message = "Missing required configuration"
        config_key = "database_url"
        error = ConfigurationError(message, config_key)

        assert str(error) == message
        assert error.message == message
        assert error.config_key == config_key
        assert error.error_code == "CONFIGURATION_ERROR"
        assert isinstance(error, ApplicationError)

    def test_exception_hierarchy(self):
        """Test exception inheritance hierarchy."""
        # Test that all custom exceptions inherit from ApplicationError
        assert issubclass(ValidationError, ApplicationError)
        assert issubclass(NotFoundError, ApplicationError)
        assert issubclass(BusinessRuleViolationError, ApplicationError)
        assert issubclass(UnauthorizedError, ApplicationError)
        assert issubclass(ConflictError, ApplicationError)
        assert issubclass(ForbiddenError, ApplicationError)
        assert issubclass(ExternalServiceError, ApplicationError)
        assert issubclass(RateLimitError, ApplicationError)
        assert issubclass(DataIntegrityError, ApplicationError)
        assert issubclass(ConcurrencyError, ApplicationError)
        assert issubclass(ConfigurationError, ApplicationError)

        # Test that all inherit from base Exception
        assert issubclass(ApplicationError, Exception)
    
    def test_exception_equality(self):
        """Test exception equality comparison."""
        error1 = ValidationError("Invalid data", "email")
        error2 = ValidationError("Invalid data", "email")
        error3 = ValidationError("Invalid data", "password")

        # Same type and message should be equal
        assert error1.args == error2.args
        assert error1.field == error2.field

        # Different field should not be equal
        assert error1.field != error3.field

    def test_exception_string_representation(self):
        """Test exception string representation."""
        error = BusinessRuleViolationError(
            "User cannot exceed workout limit",
            "workout_limit_exceeded"
        )

        assert str(error) == "User cannot exceed workout limit"
        assert error.message == "User cannot exceed workout limit"
        assert error.rule == "workout_limit_exceeded"

    def test_exception_with_none_values(self):
        """Test exceptions with None values."""
        # Test NotFoundError with None values
        error1 = NotFoundError("Not found")
        assert error1.resource_type is None
        assert error1.resource_id is None

        # Test ValidationError with None field
        error2 = ValidationError("Error", None)
        assert error2.field is None

        # Test BusinessRuleViolationError with None rule
        error3 = BusinessRuleViolationError("Rule violation", None)
        assert error3.rule is None

    def test_error_codes(self):
        """Test that all exceptions have correct error codes."""
        assert ValidationError("test").error_code == "VALIDATION_ERROR"
        assert NotFoundError("test").error_code == "NOT_FOUND"
        assert BusinessRuleViolationError("test").error_code == "BUSINESS_RULE_VIOLATION"
        assert UnauthorizedError("test").error_code == "UNAUTHORIZED"
        assert ConflictError("test").error_code == "CONFLICT"
        assert ForbiddenError("test").error_code == "FORBIDDEN"
        assert ExternalServiceError("test").error_code == "EXTERNAL_SERVICE_ERROR"
        assert RateLimitError("test").error_code == "RATE_LIMIT_EXCEEDED"
        assert DataIntegrityError("test").error_code == "DATA_INTEGRITY_ERROR"
        assert ConcurrencyError("test").error_code == "CONCURRENCY_ERROR"
        assert ConfigurationError("test").error_code == "CONFIGURATION_ERROR"
