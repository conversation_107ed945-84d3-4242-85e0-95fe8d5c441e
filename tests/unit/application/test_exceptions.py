"""
Unit tests for application layer exceptions.

Tests for custom exceptions used in the application layer
including validation, business rule violations, and not found errors.
"""

import pytest

from app.application.exceptions import (
    ApplicationError,
    BusinessRuleViolationError,
    ExerciseNotFoundError,
    InvalidExerciseDataError,
    NotFoundError,
    UnauthorizedError,
    ValidationError,
    WorkoutNotFoundError,
)


@pytest.mark.unit
@pytest.mark.application
class TestApplicationExceptions:
    """Test application layer exceptions."""
    
    def test_application_error_base(self):
        """Test base application error."""
        message = "Something went wrong"
        error = ApplicationError(message)
        
        assert str(error) == message
        assert isinstance(error, Exception)
    
    def test_application_error_with_details(self):
        """Test application error with details."""
        message = "Validation failed"
        details = {"field": "name", "error": "required"}
        error = ApplicationError(message, details)
        
        assert str(error) == message
        assert error.details == details
    
    def test_validation_error(self):
        """Test validation error."""
        message = "Invalid input data"
        field = "email"
        error = ValidationError(message, field)
        
        assert str(error) == message
        assert error.field == field
        assert isinstance(error, ApplicationError)
    
    def test_validation_error_with_details(self):
        """Test validation error with details."""
        message = "Multiple validation errors"
        field = "form"
        details = {"email": "invalid format", "password": "too weak"}
        error = ValidationError(message, field, details)
        
        assert str(error) == message
        assert error.field == field
        assert error.details == details
    
    def test_not_found_error(self):
        """Test not found error."""
        resource = "User"
        identifier = "123"
        error = NotFoundError(resource, identifier)
        
        expected_message = f"{resource} with identifier '{identifier}' not found"
        assert str(error) == expected_message
        assert error.resource == resource
        assert error.identifier == identifier
        assert isinstance(error, ApplicationError)
    
    def test_not_found_error_custom_message(self):
        """Test not found error with custom message."""
        message = "Custom not found message"
        error = NotFoundError(message=message)
        
        assert str(error) == message
        assert error.resource is None
        assert error.identifier is None
    
    def test_business_rule_violation_error(self):
        """Test business rule violation error."""
        rule = "User cannot have multiple active workouts"
        context = {"user_id": "123", "active_workouts": 2}
        error = BusinessRuleViolationError(rule, context)
        
        assert str(error) == rule
        assert error.rule == rule
        assert error.context == context
        assert isinstance(error, ApplicationError)
    
    def test_business_rule_violation_error_no_context(self):
        """Test business rule violation error without context."""
        rule = "Exercise must have at least one muscle group"
        error = BusinessRuleViolationError(rule)
        
        assert str(error) == rule
        assert error.rule == rule
        assert error.context is None
    
    def test_unauthorized_error(self):
        """Test unauthorized error."""
        action = "delete exercise"
        resource = "Exercise"
        error = UnauthorizedError(action, resource)
        
        expected_message = f"Unauthorized to {action} {resource}"
        assert str(error) == expected_message
        assert error.action == action
        assert error.resource == resource
        assert isinstance(error, ApplicationError)
    
    def test_unauthorized_error_custom_message(self):
        """Test unauthorized error with custom message."""
        message = "Access denied"
        error = UnauthorizedError(message=message)
        
        assert str(error) == message
        assert error.action is None
        assert error.resource is None
    
    def test_exercise_not_found_error(self):
        """Test exercise not found error."""
        exercise_id = "exercise-123"
        error = ExerciseNotFoundError(exercise_id)
        
        expected_message = f"Exercise with ID '{exercise_id}' not found"
        assert str(error) == expected_message
        assert error.exercise_id == exercise_id
        assert isinstance(error, NotFoundError)
    
    def test_workout_not_found_error(self):
        """Test workout not found error."""
        workout_id = "workout-456"
        error = WorkoutNotFoundError(workout_id)
        
        expected_message = f"Workout with ID '{workout_id}' not found"
        assert str(error) == expected_message
        assert error.workout_id == workout_id
        assert isinstance(error, NotFoundError)
    
    def test_invalid_exercise_data_error(self):
        """Test invalid exercise data error."""
        field = "difficulty_level"
        value = "invalid"
        error = InvalidExerciseDataError(field, value)
        
        expected_message = f"Invalid exercise data for field '{field}': {value}"
        assert str(error) == expected_message
        assert error.field == field
        assert error.value == value
        assert isinstance(error, ValidationError)
    
    def test_invalid_exercise_data_error_with_reason(self):
        """Test invalid exercise data error with reason."""
        field = "muscle_groups"
        value = []
        reason = "At least one muscle group is required"
        error = InvalidExerciseDataError(field, value, reason)
        
        expected_message = f"Invalid exercise data for field '{field}': {reason}"
        assert str(error) == expected_message
        assert error.field == field
        assert error.value == value
        assert error.reason == reason
    
    def test_exception_hierarchy(self):
        """Test exception inheritance hierarchy."""
        # Test that all custom exceptions inherit from ApplicationError
        assert issubclass(ValidationError, ApplicationError)
        assert issubclass(NotFoundError, ApplicationError)
        assert issubclass(BusinessRuleViolationError, ApplicationError)
        assert issubclass(UnauthorizedError, ApplicationError)
        
        # Test that specific exceptions inherit from their base types
        assert issubclass(ExerciseNotFoundError, NotFoundError)
        assert issubclass(WorkoutNotFoundError, NotFoundError)
        assert issubclass(InvalidExerciseDataError, ValidationError)
        
        # Test that all inherit from base Exception
        assert issubclass(ApplicationError, Exception)
    
    def test_exception_equality(self):
        """Test exception equality comparison."""
        error1 = ValidationError("Invalid data", "email")
        error2 = ValidationError("Invalid data", "email")
        error3 = ValidationError("Invalid data", "password")
        
        # Same type and message should be equal
        assert error1.args == error2.args
        assert error1.field == error2.field
        
        # Different field should not be equal
        assert error1.field != error3.field
    
    def test_exception_string_representation(self):
        """Test exception string representation."""
        error = BusinessRuleViolationError(
            "User cannot exceed workout limit",
            {"user_id": "123", "current_workouts": 5, "limit": 3}
        )
        
        assert str(error) == "User cannot exceed workout limit"
        assert error.rule == "User cannot exceed workout limit"
        assert "user_id" in error.context
        assert error.context["limit"] == 3
    
    def test_exception_with_none_values(self):
        """Test exceptions with None values."""
        # Test NotFoundError with None values
        error1 = NotFoundError(None, None)
        assert error1.resource is None
        assert error1.identifier is None
        
        # Test ValidationError with None field
        error2 = ValidationError("Error", None)
        assert error2.field is None
        
        # Test BusinessRuleViolationError with None context
        error3 = BusinessRuleViolationError("Rule", None)
        assert error3.context is None
    
    def test_exception_details_handling(self):
        """Test exception details handling."""
        details = {
            "validation_errors": [
                {"field": "name", "message": "required"},
                {"field": "email", "message": "invalid format"}
            ],
            "error_code": "VALIDATION_FAILED"
        }
        
        error = ApplicationError("Multiple validation errors", details)
        
        assert error.details == details
        assert len(error.details["validation_errors"]) == 2
        assert error.details["error_code"] == "VALIDATION_FAILED"
    
    def test_nested_exception_context(self):
        """Test nested exception context."""
        context = {
            "user": {
                "id": "user-123",
                "email": "<EMAIL>"
            },
            "workout": {
                "id": "workout-456",
                "status": "active"
            },
            "violation": "multiple_active_workouts"
        }
        
        error = BusinessRuleViolationError(
            "User already has an active workout",
            context
        )
        
        assert error.context["user"]["id"] == "user-123"
        assert error.context["workout"]["status"] == "active"
        assert error.context["violation"] == "multiple_active_workouts"
