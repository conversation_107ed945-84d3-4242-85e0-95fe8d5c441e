"""
Unit tests for exercise use cases.

Tests for exercise business logic including creation, retrieval,
updates, approval workflows, and search functionality.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock
from uuid import uuid4

from app.application.exceptions import NotFoundError, ValidationError, BusinessRuleViolationError
from app.application.use_cases.exercise_use_cases import ExerciseUseCases
from app.domain.entities.exercise import (
    ApprovalStatus,
    DifficultyLevel,
    Exercise,
    ExerciseCreateRequest,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
    MuscleGroup,
    MovementPattern,
)
from app.domain.repositories.exercise_repository import ExerciseRepository


@pytest.mark.unit
@pytest.mark.application
class TestExerciseUseCases:
    """Test exercise use cases."""
    
    @pytest.fixture
    def mock_repository(self):
        """Create mock exercise repository."""
        return AsyncMock(spec=ExerciseRepository)
    
    @pytest.fixture
    def exercise_use_cases(self, mock_repository):
        """Create exercise use cases with mock repository."""
        return ExerciseUseCases(mock_repository)
    
    @pytest.fixture
    def sample_exercise(self):
        """Create sample exercise entity."""
        now = datetime.utcnow()
        return Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=["Barbell", "Bench"],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise creation."""
        request = ExerciseCreateRequest(
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=["Barbell"],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Lie on bench"],
        )
        created_by = uuid4()
        
        mock_repository.create.return_value = sample_exercise
        mock_repository.exists_by_name.return_value = False
        
        result = await exercise_use_cases.create_exercise(request, created_by)
        
        assert result == sample_exercise
        mock_repository.exists_by_name.assert_called_once_with(request.name)
        mock_repository.create.assert_called_once()
    
    async def test_create_exercise_duplicate_name(self, exercise_use_cases, mock_repository):
        """Test exercise creation with duplicate name."""
        request = ExerciseCreateRequest(
            name="Existing Exercise",
            description="Test exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
        )
        created_by = uuid4()
        
        mock_repository.exists_by_name.return_value = True
        
        with pytest.raises(BusinessRuleViolationError, match="already exists"):
            await exercise_use_cases.create_exercise(request, created_by)
    
    async def test_get_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise retrieval."""
        exercise_id = sample_exercise.id
        
        mock_repository.get_by_id.return_value = sample_exercise
        
        result = await exercise_use_cases.get_exercise(exercise_id)
        
        assert result == sample_exercise
        mock_repository.get_by_id.assert_called_once_with(exercise_id)
    
    async def test_get_exercise_not_found(self, exercise_use_cases, mock_repository):
        """Test exercise retrieval when not found."""
        exercise_id = uuid4()
        
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_use_cases.get_exercise(exercise_id)
    
    async def test_update_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise update."""
        exercise_id = sample_exercise.id
        request = ExerciseUpdateRequest(
            name="Updated Exercise",
            description="Updated description"
        )
        updated_by = uuid4()
        
        mock_repository.get_by_id.return_value = sample_exercise
        mock_repository.update.return_value = sample_exercise
        mock_repository.exists_by_name.return_value = False
        
        result = await exercise_use_cases.update_exercise(exercise_id, request, updated_by)
        
        assert result == sample_exercise
        mock_repository.get_by_id.assert_called_once_with(exercise_id)
        mock_repository.update.assert_called_once()
    
    async def test_update_exercise_not_found(self, exercise_use_cases, mock_repository):
        """Test exercise update when not found."""
        exercise_id = uuid4()
        request = ExerciseUpdateRequest(name="Updated Exercise")
        updated_by = uuid4()
        
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_use_cases.update_exercise(exercise_id, request, updated_by)
    
    async def test_update_exercise_duplicate_name(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test exercise update with duplicate name."""
        exercise_id = sample_exercise.id
        request = ExerciseUpdateRequest(name="Existing Exercise")
        updated_by = uuid4()
        
        mock_repository.get_by_id.return_value = sample_exercise
        mock_repository.exists_by_name.return_value = True
        
        with pytest.raises(BusinessRuleViolationError, match="already exists"):
            await exercise_use_cases.update_exercise(exercise_id, request, updated_by)
    
    async def test_delete_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise deletion."""
        exercise_id = sample_exercise.id
        deleted_by = uuid4()
        
        mock_repository.get_by_id.return_value = sample_exercise
        mock_repository.soft_delete.return_value = True
        
        result = await exercise_use_cases.delete_exercise(exercise_id, deleted_by)
        
        assert result is True
        mock_repository.get_by_id.assert_called_once_with(exercise_id)
        mock_repository.soft_delete.assert_called_once_with(exercise_id, deleted_by)
    
    async def test_delete_exercise_not_found(self, exercise_use_cases, mock_repository):
        """Test exercise deletion when not found."""
        exercise_id = uuid4()
        deleted_by = uuid4()
        
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_use_cases.delete_exercise(exercise_id, deleted_by)
    
    async def test_search_exercises_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise search."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.CHEST)
        exercises = [sample_exercise]
        
        mock_repository.search.return_value = exercises
        mock_repository.count.return_value = 1
        
        result_exercises, total_count = await exercise_use_cases.search_exercises(filters)
        
        assert result_exercises == exercises
        assert total_count == 1
        mock_repository.search.assert_called_once_with(filters, 50, 0)
        mock_repository.count.assert_called_once_with(filters)
    
    async def test_search_exercises_with_pagination(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test exercise search with pagination."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.CHEST)
        exercises = [sample_exercise]
        
        mock_repository.search.return_value = exercises
        mock_repository.count.return_value = 25
        
        result_exercises, total_count = await exercise_use_cases.search_exercises(filters, limit=10, offset=5)
        
        assert result_exercises == exercises
        assert total_count == 25
        mock_repository.search.assert_called_once_with(filters, 10, 5)
        mock_repository.count.assert_called_once_with(filters)
    
    async def test_approve_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise approval."""
        exercise_id = sample_exercise.id
        approved_by = uuid4()
        notes = "Looks good"
        
        mock_repository.get_by_id.return_value = sample_exercise
        mock_repository.approve.return_value = sample_exercise
        
        result = await exercise_use_cases.approve_exercise(exercise_id, approved_by, notes)
        
        assert result == sample_exercise
        mock_repository.get_by_id.assert_called_once_with(exercise_id)
        mock_repository.approve.assert_called_once_with(exercise_id, approved_by, notes)
    
    async def test_approve_exercise_not_found(self, exercise_use_cases, mock_repository):
        """Test exercise approval when not found."""
        exercise_id = uuid4()
        approved_by = uuid4()
        
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_use_cases.approve_exercise(exercise_id, approved_by)
    
    async def test_approve_exercise_already_approved(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test exercise approval when already approved."""
        exercise_id = sample_exercise.id
        approved_by = uuid4()
        
        # Exercise is already approved
        sample_exercise.approval_status = ApprovalStatus.APPROVED
        mock_repository.get_by_id.return_value = sample_exercise
        
        with pytest.raises(BusinessRuleViolationError, match="already approved"):
            await exercise_use_cases.approve_exercise(exercise_id, approved_by)
    
    async def test_reject_exercise_success(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test successful exercise rejection."""
        exercise_id = sample_exercise.id
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        # Set exercise as pending
        sample_exercise.approval_status = ApprovalStatus.PENDING
        mock_repository.get_by_id.return_value = sample_exercise
        mock_repository.reject.return_value = sample_exercise
        
        result = await exercise_use_cases.reject_exercise(exercise_id, rejected_by, reason)
        
        assert result == sample_exercise
        mock_repository.get_by_id.assert_called_once_with(exercise_id)
        mock_repository.reject.assert_called_once_with(exercise_id, rejected_by, reason)
    
    async def test_reject_exercise_not_found(self, exercise_use_cases, mock_repository):
        """Test exercise rejection when not found."""
        exercise_id = uuid4()
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        mock_repository.get_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await exercise_use_cases.reject_exercise(exercise_id, rejected_by, reason)
    
    async def test_reject_exercise_already_rejected(self, exercise_use_cases, mock_repository, sample_exercise):
        """Test exercise rejection when already rejected."""
        exercise_id = sample_exercise.id
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        # Exercise is already rejected
        sample_exercise.approval_status = ApprovalStatus.REJECTED
        mock_repository.get_by_id.return_value = sample_exercise
        
        with pytest.raises(BusinessRuleViolationError, match="already rejected"):
            await exercise_use_cases.reject_exercise(exercise_id, rejected_by, reason)
    
    async def test_search_exercises_empty_result(self, exercise_use_cases, mock_repository):
        """Test exercise search with empty result."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.LEGS)
        
        mock_repository.search.return_value = []
        mock_repository.count.return_value = 0
        
        result_exercises, total_count = await exercise_use_cases.search_exercises(filters)
        
        assert result_exercises == []
        assert total_count == 0
    
    async def test_create_exercise_validation_error(self, exercise_use_cases, mock_repository):
        """Test exercise creation with validation error."""
        request = ExerciseCreateRequest(
            name="",  # Empty name should cause validation error
            description="Test exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
        )
        created_by = uuid4()
        
        with pytest.raises(ValidationError):
            await exercise_use_cases.create_exercise(request, created_by)
