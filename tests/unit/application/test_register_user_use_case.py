"""
Unit tests for RegisterUserUseCase.

Tests the application logic for user registration including
orchestration of domain services and token generation.
"""

from unittest.mock import AsyncMock, Mock
from uuid import uuid4

import pytest

from app.application.dto.user_dto import CreateUserDTO, UserDTO
from app.application.use_cases.register_user import (
    RegisterUserResponse,
    RegisterUserUseCase,
)
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import (
    InvalidEmailError,
    UserAlreadyExistsError,
    WeakPasswordError,
)


@pytest.mark.unit
@pytest.mark.auth
class TestRegisterUserUseCase:
    """Test user registration use case."""

    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Mock authentication service."""
        return AsyncMock()

    @pytest.fixture
    def mock_token_generator(self) -> AsyncMock:
        """Mock token generator."""
        mock = AsyncMock()
        mock.generate_access_token.return_value = "access_token_123"
        mock.generate_refresh_token.return_value = "refresh_token_456"
        return mock

    @pytest.fixture
    def use_case(
        self, mock_auth_service: AsyncMock, mock_token_generator: AsyncMock
    ) -> RegisterUserUseCase:
        """Create use case instance with mocked dependencies."""
        return RegisterUserUseCase(mock_auth_service, mock_token_generator)

    @pytest.fixture
    def sample_create_dto(self) -> CreateUserDTO:
        """Sample user creation DTO."""
        return CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe",
            training_experience_years=2,
        )

    @pytest.fixture
    def sample_user(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=False,
        )

    async def test_execute_success(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_create_dto: CreateUserDTO,
        sample_user: User,
    ) -> None:
        """Test successful user registration."""
        # Setup
        mock_auth_service.register_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_create_dto)

        # Verify
        assert isinstance(result, RegisterUserResponse)
        assert isinstance(result.user, UserDTO)
        assert result.user.email == sample_user.email
        assert result.user.first_name == sample_user.first_name
        assert result.user.last_name == sample_user.last_name
        assert result.access_token == "access_token_123"
        assert result.refresh_token == "refresh_token_456"
        assert result.token_type == "bearer"

        # Verify service calls
        mock_auth_service.register_user.assert_called_once_with(
            email=sample_create_dto.email,
            password=sample_create_dto.password,
            first_name=sample_create_dto.first_name,
            last_name=sample_create_dto.last_name,
            training_experience_years=sample_create_dto.training_experience_years,
        )

        mock_token_generator.generate_access_token.assert_called_once_with(
            str(sample_user.id)
        )
        mock_token_generator.generate_refresh_token.assert_called_once_with(
            str(sample_user.id)
        )

    async def test_execute_minimal_data(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
    ) -> None:
        """Test registration with minimal data."""
        # Setup
        minimal_dto = CreateUserDTO(
            email="<EMAIL>", password="SecurePass123!"
        )

        minimal_user = User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_active=True,
            is_verified=False,
        )

        mock_auth_service.register_user.return_value = minimal_user

        # Execute
        result = await use_case.execute(minimal_dto)

        # Verify
        assert result.user.email == minimal_user.email
        assert result.user.first_name is None
        assert result.user.last_name is None
        assert result.user.training_experience_years is None

        # Verify service call with None values
        mock_auth_service.register_user.assert_called_once_with(
            email=minimal_dto.email,
            password=minimal_dto.password,
            first_name=None,
            last_name=None,
            training_experience_years=None,
        )

    async def test_execute_user_already_exists(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        sample_create_dto: CreateUserDTO,
    ) -> None:
        """Test registration when user already exists."""
        # Setup
        mock_auth_service.register_user.side_effect = UserAlreadyExistsError(
            "User already exists"
        )

        # Execute & Verify
        with pytest.raises(UserAlreadyExistsError):
            await use_case.execute(sample_create_dto)

        # Verify service was called
        mock_auth_service.register_user.assert_called_once()

    async def test_execute_invalid_email(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        sample_create_dto: CreateUserDTO,
    ) -> None:
        """Test registration with invalid email."""
        # Setup
        mock_auth_service.register_user.side_effect = InvalidEmailError(
            "Invalid email format"
        )

        # Execute & Verify
        with pytest.raises(InvalidEmailError):
            await use_case.execute(sample_create_dto)

    async def test_execute_weak_password(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        sample_create_dto: CreateUserDTO,
    ) -> None:
        """Test registration with weak password."""
        # Setup
        mock_auth_service.register_user.side_effect = WeakPasswordError(
            "Password too weak"
        )

        # Execute & Verify
        with pytest.raises(WeakPasswordError):
            await use_case.execute(sample_create_dto)

    async def test_token_generation_failure(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_create_dto: CreateUserDTO,
        sample_user: User,
    ) -> None:
        """Test handling of token generation failure."""
        # Setup
        mock_auth_service.register_user.return_value = sample_user
        mock_token_generator.generate_access_token.side_effect = Exception(
            "Token generation failed"
        )

        # Execute & Verify
        with pytest.raises(Exception) as exc_info:
            await use_case.execute(sample_create_dto)

        assert "Token generation failed" in str(exc_info.value)

        # Verify user was created but token generation failed
        mock_auth_service.register_user.assert_called_once()
        mock_token_generator.generate_access_token.assert_called_once()

    async def test_user_dto_conversion(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_create_dto: CreateUserDTO,
        sample_user: User,
    ) -> None:
        """Test proper conversion from User entity to UserDTO."""
        # Setup
        mock_auth_service.register_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_create_dto)

        # Verify DTO conversion
        user_dto = result.user
        assert user_dto.id == sample_user.id
        assert user_dto.email == sample_user.email
        assert user_dto.first_name == sample_user.first_name
        assert user_dto.last_name == sample_user.last_name
        assert user_dto.is_active == sample_user.is_active
        assert user_dto.is_verified == sample_user.is_verified
        assert (
            user_dto.training_experience_years == sample_user.training_experience_years
        )
        assert user_dto.created_at == sample_user.created_at
        assert user_dto.updated_at == sample_user.updated_at

    async def test_response_structure(
        self,
        use_case: RegisterUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_create_dto: CreateUserDTO,
        sample_user: User,
    ) -> None:
        """Test the structure of the registration response."""
        # Setup
        mock_auth_service.register_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_create_dto)

        # Verify response structure
        assert hasattr(result, "user")
        assert hasattr(result, "access_token")
        assert hasattr(result, "refresh_token")
        assert hasattr(result, "token_type")

        assert isinstance(result.user, UserDTO)
        assert isinstance(result.access_token, str)
        assert isinstance(result.refresh_token, str)
        assert isinstance(result.token_type, str)

        assert len(result.access_token) > 0
        assert len(result.refresh_token) > 0
        assert result.token_type == "bearer"
