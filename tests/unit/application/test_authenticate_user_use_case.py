"""Unit tests for AuthenticateUserUseCase.

Tests the application logic for user authentication including
orchestration of domain services and token generation.
"""

from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from app.application.dto.user_dto import UserDTO
from app.application.use_cases.authenticate_user import (
    AuthenticateUserRequest,
    AuthenticateUserResponse,
    AuthenticateUserUseCase,
)
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import (
    InactiveUserError,
    InvalidCredentialsError,
)


@pytest.mark.unit
@pytest.mark.auth
class TestAuthenticateUserUseCase:
    """Test user authentication use case."""

    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Mock authentication service."""
        return AsyncMock()

    @pytest.fixture
    def mock_token_generator(self) -> AsyncMock:
        """Mock token generator."""
        mock = AsyncMock()
        mock.generate_access_token.return_value = "access_token_123"
        mock.generate_refresh_token.return_value = "refresh_token_456"
        return mock

    @pytest.fixture
    def use_case(
        self, mock_auth_service: AsyncMock, mock_token_generator: AsyncMock
    ) -> AuthenticateUserUseCase:
        """Create use case instance with mocked dependencies."""
        return AuthenticateUserUseCase(mock_auth_service, mock_token_generator)

    @pytest.fixture
    def sample_auth_request(self) -> AuthenticateUserRequest:
        """Sample authentication request."""
        return AuthenticateUserRequest(
            email="<EMAIL>", password="SecurePass123!"
        )

    @pytest.fixture
    def sample_user(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe",
            is_active=True,
            is_verified=False,
        )

    async def test_execute_success(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
        sample_user: User,
    ) -> None:
        """Test successful user authentication."""
        # Setup
        mock_auth_service.authenticate_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_auth_request)

        # Verify
        assert isinstance(result, AuthenticateUserResponse)
        assert isinstance(result.user, UserDTO)
        assert result.user.email == sample_user.email
        assert result.user.first_name == sample_user.first_name
        assert result.access_token == "access_token_123"
        assert result.refresh_token == "refresh_token_456"
        assert result.token_type == "bearer"

        # Verify service calls
        mock_auth_service.authenticate_user.assert_called_once_with(
            email=sample_auth_request.email, password=sample_auth_request.password
        )

        mock_token_generator.generate_access_token.assert_called_once_with(
            str(sample_user.id)
        )
        mock_token_generator.generate_refresh_token.assert_called_once_with(
            str(sample_user.id)
        )

    async def test_execute_invalid_credentials(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
    ) -> None:
        """Test authentication with invalid credentials."""
        # Setup
        mock_auth_service.authenticate_user.side_effect = InvalidCredentialsError(
            "Invalid credentials"
        )

        # Execute & Verify
        with pytest.raises(InvalidCredentialsError):
            await use_case.execute(sample_auth_request)

        # Verify service was called
        mock_auth_service.authenticate_user.assert_called_once()

    async def test_execute_inactive_user(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
    ) -> None:
        """Test authentication with inactive user."""
        # Setup
        mock_auth_service.authenticate_user.side_effect = InactiveUserError(
            "User account is inactive"
        )

        # Execute & Verify
        with pytest.raises(InactiveUserError):
            await use_case.execute(sample_auth_request)

    async def test_token_generation_failure(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
        sample_user: User,
    ) -> None:
        """Test handling of token generation failure."""
        # Setup
        mock_auth_service.authenticate_user.return_value = sample_user
        mock_token_generator.generate_access_token.side_effect = Exception(
            "Token generation failed"
        )

        # Execute & Verify
        with pytest.raises(Exception) as exc_info:
            await use_case.execute(sample_auth_request)

        assert "Token generation failed" in str(exc_info.value)

        # Verify user was authenticated but token generation failed
        mock_auth_service.authenticate_user.assert_called_once()
        mock_token_generator.generate_access_token.assert_called_once()

    async def test_user_dto_conversion(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
        sample_user: User,
    ) -> None:
        """Test proper conversion from User entity to UserDTO."""
        # Setup
        mock_auth_service.authenticate_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_auth_request)

        # Verify DTO conversion
        user_dto = result.user
        assert user_dto.id == sample_user.id
        assert user_dto.email == sample_user.email
        assert user_dto.first_name == sample_user.first_name
        assert user_dto.last_name == sample_user.last_name
        assert user_dto.is_active == sample_user.is_active
        assert user_dto.is_verified == sample_user.is_verified

    async def test_response_structure(
        self,
        use_case: AuthenticateUserUseCase,
        mock_auth_service: AsyncMock,
        mock_token_generator: AsyncMock,
        sample_auth_request: AuthenticateUserRequest,
        sample_user: User,
    ) -> None:
        """Test the structure of the authentication response."""
        # Setup
        mock_auth_service.authenticate_user.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_auth_request)

        # Verify response structure
        assert hasattr(result, "user")
        assert hasattr(result, "access_token")
        assert hasattr(result, "refresh_token")
        assert hasattr(result, "token_type")

        assert isinstance(result.user, UserDTO)
        assert isinstance(result.access_token, str)
        assert isinstance(result.refresh_token, str)
        assert isinstance(result.token_type, str)

        assert len(result.access_token) > 0
        assert len(result.refresh_token) > 0
        assert result.token_type == "bearer"
