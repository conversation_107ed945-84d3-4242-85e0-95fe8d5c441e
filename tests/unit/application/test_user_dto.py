"""
Unit tests for User DTOs.

Tests data transfer objects for user-related operations.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.application.dto.user_dto import UserDTO, CreateUserDTO, UpdateUserDTO
from app.domain.entities.user import User


@pytest.mark.unit
class TestUserDTO:
    """Test UserDTO functionality."""
    
    @pytest.fixture
    def sample_user(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            training_experience_years=2,
            is_active=True,
            is_verified=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    def test_from_entity(self, sample_user: User) -> None:
        """Test creating UserDTO from User entity."""
        dto = UserDTO.from_entity(sample_user)
        
        assert dto.id == sample_user.id
        assert dto.email == sample_user.email
        assert dto.first_name == sample_user.first_name
        assert dto.last_name == sample_user.last_name
        assert dto.training_experience_years == sample_user.training_experience_years
        assert dto.is_active == sample_user.is_active
        assert dto.is_verified == sample_user.is_verified
        assert dto.created_at == sample_user.created_at
        assert dto.updated_at == sample_user.updated_at
    
    def test_from_entity_with_none_values(self) -> None:
        """Test creating UserDTO from User entity with None values."""
        user = User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name=None,
            last_name=None,
            training_experience_years=None,
            is_active=True,
            is_verified=False
        )
        
        dto = UserDTO.from_entity(user)
        
        assert dto.first_name is None
        assert dto.last_name is None
        assert dto.training_experience_years is None
    
    def test_dto_immutability(self, sample_user: User) -> None:
        """Test that DTO is immutable (frozen dataclass)."""
        dto = UserDTO.from_entity(sample_user)

        # Should not be able to modify DTO fields (Pydantic v2 raises ValidationError)
        with pytest.raises(Exception):  # ValidationError or AttributeError
            dto.email = "<EMAIL>"
    
    def test_dto_equality(self, sample_user: User) -> None:
        """Test DTO equality comparison."""
        dto1 = UserDTO.from_entity(sample_user)
        dto2 = UserDTO.from_entity(sample_user)
        
        assert dto1 == dto2
    
    def test_dto_string_representation(self, sample_user: User) -> None:
        """Test DTO string representation."""
        dto = UserDTO.from_entity(sample_user)
        str_repr = str(dto)

        # Pydantic v2 doesn't include class name in str representation by default
        assert sample_user.email in str_repr
        assert "<EMAIL>" in str_repr


@pytest.mark.unit
class TestCreateUserDTO:
    """Test CreateUserDTO functionality."""
    
    def test_create_with_all_fields(self) -> None:
        """Test creating DTO with all fields."""
        dto = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John",
            last_name="Doe",
            training_experience_years=2
        )
        
        assert dto.email == "<EMAIL>"
        assert dto.password == "SecurePass123!"
        assert dto.first_name == "John"
        assert dto.last_name == "Doe"
        assert dto.training_experience_years == 2
    
    def test_create_with_minimal_fields(self) -> None:
        """Test creating DTO with minimal required fields."""
        dto = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert dto.email == "<EMAIL>"
        assert dto.password == "SecurePass123!"
        assert dto.first_name is None
        assert dto.last_name is None
        assert dto.training_experience_years is None
    
    def test_create_dto_immutability(self) -> None:
        """Test that CreateUserDTO is immutable."""
        dto = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!"
        )

        # Should not be able to modify DTO fields (Pydantic v2 raises ValidationError)
        with pytest.raises(Exception):  # ValidationError or AttributeError
            dto.email = "<EMAIL>"
    
    def test_create_dto_equality(self) -> None:
        """Test CreateUserDTO equality comparison."""
        dto1 = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John"
        )
        dto2 = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!",
            first_name="John"
        )
        
        assert dto1 == dto2
    
    def test_create_dto_inequality(self) -> None:
        """Test CreateUserDTO inequality comparison."""
        dto1 = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        dto2 = CreateUserDTO(
            email="<EMAIL>",
            password="SecurePass123!"
        )
        
        assert dto1 != dto2


@pytest.mark.unit
class TestUpdateUserDTO:
    """Test UpdateUserDTO functionality."""
    
    def test_update_with_all_fields(self) -> None:
        """Test creating UpdateUserDTO with all fields."""
        dto = UpdateUserDTO(
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5
        )
        
        assert dto.first_name == "Jane"
        assert dto.last_name == "Smith"
        assert dto.training_experience_years == 5
    
    def test_update_with_partial_fields(self) -> None:
        """Test creating UpdateUserDTO with partial fields."""
        dto = UpdateUserDTO(first_name="Jane")
        
        assert dto.first_name == "Jane"
        assert dto.last_name is None
        assert dto.training_experience_years is None
    
    def test_update_with_no_fields(self) -> None:
        """Test creating UpdateUserDTO with no fields (all None)."""
        dto = UpdateUserDTO()
        
        assert dto.first_name is None
        assert dto.last_name is None
        assert dto.training_experience_years is None
    
    def test_update_dto_immutability(self) -> None:
        """Test that UpdateUserDTO is immutable."""
        dto = UpdateUserDTO(first_name="Jane")

        # Should not be able to modify DTO fields (Pydantic v2 raises ValidationError)
        with pytest.raises(Exception):  # ValidationError or AttributeError
            dto.first_name = "John"
    
    def test_update_dto_equality(self) -> None:
        """Test UpdateUserDTO equality comparison."""
        dto1 = UpdateUserDTO(
            first_name="Jane",
            training_experience_years=5
        )
        dto2 = UpdateUserDTO(
            first_name="Jane",
            training_experience_years=5
        )
        
        assert dto1 == dto2
    
    def test_update_dto_dict_conversion(self) -> None:
        """Test converting UpdateUserDTO to dictionary."""
        dto = UpdateUserDTO(
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5
        )
        
        dto_dict = dto.dict()
        
        assert dto_dict["first_name"] == "Jane"
        assert dto_dict["last_name"] == "Smith"
        assert dto_dict["training_experience_years"] == 5
    
    def test_update_dto_dict_exclude_none(self) -> None:
        """Test converting UpdateUserDTO to dictionary excluding None values."""
        dto = UpdateUserDTO(first_name="Jane")
        
        dto_dict = dto.dict(exclude_none=True)
        
        assert dto_dict == {"first_name": "Jane"}
        assert "last_name" not in dto_dict
        assert "training_experience_years" not in dto_dict
