"""
Unit tests for workout application service.

Tests for workout service operations including CRUD, session management,
and statistics calculation.
"""

import pytest
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, Mock
from uuid import uuid4

from app.application.exceptions import BusinessRuleViolationError, NotFoundError
from app.application.services.workout_service import WorkoutService
from app.domain.entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutStatus,
    WorkoutUpdateRequest,
)
from app.domain.repositories.workout_repository import WorkoutRepository


@pytest.mark.unit
@pytest.mark.service
class TestWorkoutService:
    """Test workout application service."""
    
    @pytest.fixture
    def mock_repository(self):
        """Create mock workout repository."""
        return AsyncMock(spec=WorkoutRepository)
    
    @pytest.fixture
    def workout_service(self, mock_repository):
        """Create workout service with mock repository."""
        return WorkoutService(mock_repository)
    
    @pytest.fixture
    def sample_workout(self):
        """Create sample workout entity."""
        now = datetime.utcnow()
        return Workout(
            id=uuid4(),
            user_id=uuid4(),
            name="Test Workout",
            workout_date=now,
            created_at=now,
            updated_at=now,
        )
    
    @pytest.fixture
    def sample_exercise_set(self):
        """Create sample exercise set entity."""
        now = datetime.utcnow()
        return ExerciseSet(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout creation."""
        user_id = uuid4()
        request = WorkoutCreateRequest(
            name="Test Workout",
            workout_date=datetime.utcnow(),
        )
        
        # Mock no active workout
        mock_repository.get_active_workout.return_value = None
        mock_repository.create_workout.return_value = sample_workout
        
        result = await workout_service.create_workout(request, user_id)
        
        assert result == sample_workout
        mock_repository.get_active_workout.assert_called_once_with(user_id)
        mock_repository.create_workout.assert_called_once_with(request, user_id)
    
    async def test_create_workout_with_active_workout(self, workout_service, mock_repository, sample_workout):
        """Test workout creation when user has active workout."""
        user_id = uuid4()
        request = WorkoutCreateRequest(
            name="Test Workout",
            workout_date=datetime.utcnow(),
        )
        
        # Mock active workout exists
        mock_repository.get_active_workout.return_value = sample_workout
        
        with pytest.raises(BusinessRuleViolationError, match="already has an active workout"):
            await workout_service.create_workout(request, user_id)
        
        mock_repository.get_active_workout.assert_called_once_with(user_id)
        mock_repository.create_workout.assert_not_called()
    
    async def test_get_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout retrieval."""
        workout_id = sample_workout.id
        mock_repository.get_workout_by_id.return_value = sample_workout
        
        result = await workout_service.get_workout(workout_id)
        
        assert result == sample_workout
        mock_repository.get_workout_by_id.assert_called_once_with(workout_id)
    
    async def test_get_workout_not_found(self, workout_service, mock_repository):
        """Test workout retrieval when not found."""
        workout_id = uuid4()
        mock_repository.get_workout_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.get_workout(workout_id)
        
        mock_repository.get_workout_by_id.assert_called_once_with(workout_id)
    
    async def test_update_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout update."""
        workout_id = sample_workout.id
        request = WorkoutUpdateRequest(name="Updated Workout")
        
        mock_repository.update_workout.return_value = sample_workout
        
        result = await workout_service.update_workout(workout_id, request)
        
        assert result == sample_workout
        mock_repository.update_workout.assert_called_once_with(workout_id, request)
    
    async def test_update_workout_not_found(self, workout_service, mock_repository):
        """Test workout update when not found."""
        workout_id = uuid4()
        request = WorkoutUpdateRequest(name="Updated Workout")
        
        mock_repository.update_workout.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.update_workout(workout_id, request)
    
    async def test_delete_workout_success(self, workout_service, mock_repository):
        """Test successful workout deletion."""
        workout_id = uuid4()
        mock_repository.delete_workout.return_value = True
        
        result = await workout_service.delete_workout(workout_id)
        
        assert result is True
        mock_repository.delete_workout.assert_called_once_with(workout_id)
    
    async def test_delete_workout_not_found(self, workout_service, mock_repository):
        """Test workout deletion when not found."""
        workout_id = uuid4()
        mock_repository.delete_workout.return_value = False
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.delete_workout(workout_id)
    
    async def test_start_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout start."""
        workout_id = sample_workout.id
        mock_repository.start_workout.return_value = sample_workout
        
        result = await workout_service.start_workout(workout_id)
        
        assert result == sample_workout
        mock_repository.start_workout.assert_called_once_with(workout_id)
    
    async def test_start_workout_not_found(self, workout_service, mock_repository):
        """Test workout start when not found."""
        workout_id = uuid4()
        mock_repository.start_workout.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.start_workout(workout_id)
    
    async def test_complete_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout completion."""
        workout_id = sample_workout.id
        mock_repository.complete_workout.return_value = sample_workout
        
        result = await workout_service.complete_workout(workout_id)
        
        assert result == sample_workout
        mock_repository.complete_workout.assert_called_once_with(workout_id)
    
    async def test_complete_workout_not_found(self, workout_service, mock_repository):
        """Test workout completion when not found."""
        workout_id = uuid4()
        mock_repository.complete_workout.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.complete_workout(workout_id)
    
    async def test_get_user_workouts(self, workout_service, mock_repository, sample_workout):
        """Test getting user workouts."""
        user_id = uuid4()
        workouts = [sample_workout]
        mock_repository.get_user_workouts.return_value = workouts
        
        result = await workout_service.get_user_workouts(user_id)
        
        assert result == workouts
        mock_repository.get_user_workouts.assert_called_once_with(user_id, 50, 0)
    
    async def test_get_active_workout(self, workout_service, mock_repository, sample_workout):
        """Test getting active workout."""
        user_id = uuid4()
        mock_repository.get_active_workout.return_value = sample_workout
        
        result = await workout_service.get_active_workout(user_id)
        
        assert result == sample_workout
        mock_repository.get_active_workout.assert_called_once_with(user_id)
    
    async def test_search_workouts(self, workout_service, mock_repository, sample_workout):
        """Test searching workouts."""
        filters = WorkoutSearchFilters(user_id=uuid4())
        workouts = [sample_workout]
        
        mock_repository.search_workouts.return_value = workouts
        mock_repository.count_workouts.return_value = 1
        
        result_workouts, total_count = await workout_service.search_workouts(filters)
        
        assert result_workouts == workouts
        assert total_count == 1
        mock_repository.search_workouts.assert_called_once_with(filters, 50, 0)
        mock_repository.count_workouts.assert_called_once_with(filters)
    
    async def test_add_exercise_set_success(self, workout_service, mock_repository, sample_workout, sample_exercise_set):
        """Test successful exercise set addition."""
        workout_id = sample_workout.id
        request = ExerciseSetCreateRequest(
            exercise_id=uuid4(),
            set_number=1,
        )
        
        mock_repository.get_workout_by_id.return_value = sample_workout
        mock_repository.create_exercise_set.return_value = sample_exercise_set
        
        result = await workout_service.add_exercise_set(workout_id, request)
        
        assert result == sample_exercise_set
        mock_repository.get_workout_by_id.assert_called_once_with(workout_id)
        mock_repository.create_exercise_set.assert_called_once_with(workout_id, request)
    
    async def test_add_exercise_set_workout_not_found(self, workout_service, mock_repository):
        """Test exercise set addition when workout not found."""
        workout_id = uuid4()
        request = ExerciseSetCreateRequest(
            exercise_id=uuid4(),
            set_number=1,
        )
        
        mock_repository.get_workout_by_id.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.add_exercise_set(workout_id, request)
    
    async def test_update_exercise_set_success(self, workout_service, mock_repository, sample_exercise_set):
        """Test successful exercise set update."""
        set_id = sample_exercise_set.id
        request = ExerciseSetUpdateRequest(weight_kg=Decimal("120.0"))
        
        mock_repository.update_exercise_set.return_value = sample_exercise_set
        
        result = await workout_service.update_exercise_set(set_id, request)
        
        assert result == sample_exercise_set
        mock_repository.update_exercise_set.assert_called_once_with(set_id, request)
    
    async def test_update_exercise_set_not_found(self, workout_service, mock_repository):
        """Test exercise set update when not found."""
        set_id = uuid4()
        request = ExerciseSetUpdateRequest(weight_kg=Decimal("120.0"))
        
        mock_repository.update_exercise_set.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.update_exercise_set(set_id, request)
    
    async def test_delete_exercise_set_success(self, workout_service, mock_repository):
        """Test successful exercise set deletion."""
        set_id = uuid4()
        mock_repository.delete_exercise_set.return_value = True
        
        result = await workout_service.delete_exercise_set(set_id)
        
        assert result is True
        mock_repository.delete_exercise_set.assert_called_once_with(set_id)
    
    async def test_delete_exercise_set_not_found(self, workout_service, mock_repository):
        """Test exercise set deletion when not found."""
        set_id = uuid4()
        mock_repository.delete_exercise_set.return_value = False
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.delete_exercise_set(set_id)
    
    async def test_get_workout_sets(self, workout_service, mock_repository, sample_exercise_set):
        """Test getting workout sets."""
        workout_id = uuid4()
        sets = [sample_exercise_set]
        mock_repository.get_workout_sets.return_value = sets
        
        result = await workout_service.get_workout_sets(workout_id)
        
        assert result == sets
        mock_repository.get_workout_sets.assert_called_once_with(workout_id)
    
    async def test_get_workout_templates(self, workout_service, mock_repository, sample_workout):
        """Test getting workout templates."""
        user_id = uuid4()
        templates = [sample_workout]
        mock_repository.get_workout_templates.return_value = templates
        
        result = await workout_service.get_workout_templates(user_id)
        
        assert result == templates
        mock_repository.get_workout_templates.assert_called_once_with(user_id)
    
    async def test_create_template_from_workout_success(self, workout_service, mock_repository, sample_workout):
        """Test successful template creation from workout."""
        workout_id = sample_workout.id
        template_name = "My Template"
        
        mock_repository.copy_workout_as_template.return_value = sample_workout
        
        result = await workout_service.create_template_from_workout(workout_id, template_name)
        
        assert result == sample_workout
        mock_repository.copy_workout_as_template.assert_called_once_with(workout_id, template_name)
    
    async def test_create_template_from_workout_not_found(self, workout_service, mock_repository):
        """Test template creation when workout not found."""
        workout_id = uuid4()
        template_name = "My Template"
        
        mock_repository.copy_workout_as_template.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.create_template_from_workout(workout_id, template_name)
    
    async def test_create_workout_from_template_success(self, workout_service, mock_repository, sample_workout):
        """Test successful workout creation from template."""
        template_id = uuid4()
        user_id = uuid4()
        workout_date = datetime.utcnow()
        
        mock_repository.create_workout_from_template.return_value = sample_workout
        
        result = await workout_service.create_workout_from_template(template_id, user_id, workout_date)
        
        assert result == sample_workout
        mock_repository.create_workout_from_template.assert_called_once_with(template_id, user_id, workout_date)
    
    async def test_create_workout_from_template_not_found(self, workout_service, mock_repository):
        """Test workout creation when template not found."""
        template_id = uuid4()
        user_id = uuid4()
        
        mock_repository.create_workout_from_template.return_value = None
        
        with pytest.raises(NotFoundError, match="not found"):
            await workout_service.create_workout_from_template(template_id, user_id)
    
    async def test_get_workout_statistics(self, workout_service, mock_repository):
        """Test getting workout statistics."""
        workout_id = uuid4()
        stats = {"total_sets": 5, "total_volume": 1000.0}
        
        mock_repository.get_workout_statistics.return_value = stats
        
        result = await workout_service.get_workout_statistics(workout_id)
        
        assert result == stats
        mock_repository.get_workout_statistics.assert_called_once_with(workout_id)
