"""Tests for Exercise DTOs."""

from datetime import datetime
from uuid import uuid4


from app.application.dto.exercise_dto import (
    ExerciseCreateDTO,
    ExerciseDTO,
    ExerciseMediaDTO,
    ExerciseUpdateDTO,
)
from app.domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    MediaType,
    MovementPattern,
    MuscleGroup,
)


class TestExerciseMediaDTO:
    """Test cases for ExerciseMediaDTO."""

    def test_create_exercise_media_dto_minimal(self):
        """Test creating ExerciseMediaDTO with minimal data."""
        media_id = uuid4()
        exercise_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        dto = ExerciseMediaDTO(
            id=media_id,
            exercise_id=exercise_id,
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            created_at=created_at,
            updated_at=updated_at,
        )

        assert dto.id == media_id
        assert dto.exercise_id == exercise_id
        assert dto.media_type == MediaType.VIDEO
        assert dto.url == "https://example.com/video.mp4"
        assert dto.thumbnail_url is None
        assert dto.title is None
        assert dto.description is None
        assert dto.sort_order == 0
        assert dto.is_primary is False
        assert dto.is_active is True

    def test_create_exercise_media_dto_full(self):
        """Test creating ExerciseMediaDTO with full data."""
        media_id = uuid4()
        exercise_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        dto = ExerciseMediaDTO(
            id=media_id,
            exercise_id=exercise_id,
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            title="Exercise Demo",
            description="Demonstration video",
            file_size_bytes=1024000,
            duration_seconds=60,
            width_pixels=1920,
            height_pixels=1080,
            mime_type="video/mp4",
            sort_order=1,
            is_primary=True,
            is_active=True,
            created_at=created_at,
            updated_at=updated_at,
        )

        assert dto.title == "Exercise Demo"
        assert dto.description == "Demonstration video"
        assert dto.file_size_bytes == 1024000
        assert dto.duration_seconds == 60
        assert dto.width_pixels == 1920
        assert dto.height_pixels == 1080
        assert dto.mime_type == "video/mp4"
        assert dto.sort_order == 1
        assert dto.is_primary is True


class TestExerciseDTO:
    """Test cases for ExerciseDTO."""

    def test_create_exercise_dto_minimal(self):
        """Test creating ExerciseDTO with minimal data."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        dto = ExerciseDTO(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            name="Push-up",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            created_at=created_at,
            updated_at=updated_at,
        )

        assert dto.id == exercise_id
        assert dto.exercise_uuid == exercise_uuid
        assert dto.name == "Push-up"
        assert dto.primary_muscle_group == MuscleGroup.CHEST
        assert dto.movement_pattern == MovementPattern.PUSH
        assert dto.difficulty_level == DifficultyLevel.BEGINNER
        assert dto.version == 1
        assert dto.is_current_version is True
        assert dto.is_active is True
        assert dto.is_approved is False
        assert dto.approval_status == ApprovalStatus.PENDING

    def test_create_exercise_dto_full(self):
        """Test creating ExerciseDTO with full data."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_by = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        dto = ExerciseDTO(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            version=2,
            is_current_version=True,
            name="Barbell Bench Press",
            description="Classic compound chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS, MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL, Equipment.PLATE],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            video_url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            form_cues=["Keep back flat", "Control the weight"],
            setup_instructions="Lie on bench, grip bar shoulder-width apart",
            execution_steps=["Lower bar to chest", "Press up explosively"],
            common_mistakes=["Bouncing off chest", "Flaring elbows too wide"],
            safety_notes="Always use a spotter for heavy weights",
            is_active=True,
            is_approved=True,
            approval_status=ApprovalStatus.APPROVED,
            created_by=created_by,
            created_at=created_at,
            updated_at=updated_at,
            version_notes="Updated form cues",
            change_reason=ChangeReason.CONTENT_UPDATE,
        )

        assert dto.version == 2
        assert dto.description == "Classic compound chest exercise"
        assert dto.secondary_muscle_groups == [
            MuscleGroup.TRICEPS,
            MuscleGroup.SHOULDERS,
        ]
        assert dto.equipment_required == [Equipment.BARBELL, Equipment.PLATE]
        assert len(dto.form_cues) == 2
        assert len(dto.execution_steps) == 2
        assert len(dto.common_mistakes) == 2
        assert dto.is_approved is True
        assert dto.approval_status == ApprovalStatus.APPROVED
        assert dto.created_by == created_by
        assert dto.version_notes == "Updated form cues"
        assert dto.change_reason == ChangeReason.CONTENT_UPDATE

    def test_exercise_dto_with_media(self):
        """Test ExerciseDTO with media."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        media_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        media_dto = ExerciseMediaDTO(
            id=media_id,
            exercise_id=exercise_id,
            media_type=MediaType.VIDEO,
            url="https://example.com/video.mp4",
            is_primary=True,
            created_at=created_at,
            updated_at=updated_at,
        )

        dto = ExerciseDTO(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            name="Test Exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
            media=[media_dto],
            created_at=created_at,
            updated_at=updated_at,
        )

        assert len(dto.media) == 1
        assert dto.media[0].id == media_id
        assert dto.media[0].is_primary is True


class TestExerciseCreateDTO:
    """Test cases for ExerciseCreateDTO."""

    def test_create_exercise_create_dto_minimal(self):
        """Test creating ExerciseCreateDTO with minimal data."""
        dto = ExerciseCreateDTO(
            name="Push-up",
            primary_muscle_group=MuscleGroup.CHEST,
            movement_pattern=MovementPattern.PUSH,
            difficulty_level=DifficultyLevel.BEGINNER,
        )

        assert dto.name == "Push-up"
        assert dto.primary_muscle_group == MuscleGroup.CHEST
        assert dto.movement_pattern == MovementPattern.PUSH
        assert dto.difficulty_level == DifficultyLevel.BEGINNER
        assert dto.description is None
        assert dto.secondary_muscle_groups is None
        assert dto.equipment_required is None

    def test_create_exercise_create_dto_full(self):
        """Test creating ExerciseCreateDTO with full data."""
        dto = ExerciseCreateDTO(
            name="Barbell Bench Press",
            description="Classic compound chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS, MuscleGroup.SHOULDERS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL, Equipment.PLATE],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            video_url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            form_cues=["Keep back flat", "Control the weight"],
            setup_instructions="Lie on bench, grip bar shoulder-width apart",
            execution_steps=["Lower bar to chest", "Press up explosively"],
            common_mistakes=["Bouncing off chest", "Flaring elbows too wide"],
            safety_notes="Always use a spotter for heavy weights",
            version_notes="Initial creation",
        )

        assert dto.description == "Classic compound chest exercise"
        assert dto.secondary_muscle_groups == [
            MuscleGroup.TRICEPS,
            MuscleGroup.SHOULDERS,
        ]
        assert dto.equipment_required == [Equipment.BARBELL, Equipment.PLATE]
        assert len(dto.form_cues) == 2
        assert len(dto.execution_steps) == 2
        assert len(dto.common_mistakes) == 2
        assert dto.safety_notes == "Always use a spotter for heavy weights"
        assert dto.version_notes == "Initial creation"


class TestExerciseUpdateDTO:
    """Test cases for ExerciseUpdateDTO."""

    def test_create_exercise_update_dto_empty(self):
        """Test creating empty ExerciseUpdateDTO."""
        dto = ExerciseUpdateDTO()

        assert dto.name is None
        assert dto.description is None
        assert dto.primary_muscle_group is None
        assert dto.secondary_muscle_groups is None
        assert dto.movement_pattern is None
        assert dto.equipment_required is None
        assert dto.difficulty_level is None
        assert dto.change_reason == ChangeReason.CONTENT_UPDATE

    def test_create_exercise_update_dto_partial(self):
        """Test creating ExerciseUpdateDTO with partial data."""
        dto = ExerciseUpdateDTO(
            name="Updated Exercise Name",
            description="Updated description",
            change_reason=ChangeReason.CORRECTION,
        )

        assert dto.name == "Updated Exercise Name"
        assert dto.description == "Updated description"
        assert dto.change_reason == ChangeReason.CORRECTION
        assert dto.primary_muscle_group is None
        assert dto.movement_pattern is None

    def test_create_exercise_update_dto_full(self):
        """Test creating ExerciseUpdateDTO with full data."""
        dto = ExerciseUpdateDTO(
            name="Updated Barbell Bench Press",
            description="Updated classic compound chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL],
            difficulty_level=DifficultyLevel.ADVANCED,
            video_url="https://example.com/new-video.mp4",
            form_cues=["Updated form cue"],
            version_notes="Major update",
            change_reason=ChangeReason.CONTENT_UPDATE,
        )

        assert dto.name == "Updated Barbell Bench Press"
        assert dto.description == "Updated classic compound chest exercise"
        assert dto.primary_muscle_group == MuscleGroup.CHEST
        assert dto.secondary_muscle_groups == [MuscleGroup.TRICEPS]
        assert dto.movement_pattern == MovementPattern.PUSH
        assert dto.equipment_required == [Equipment.BARBELL]
        assert dto.difficulty_level == DifficultyLevel.ADVANCED
        assert dto.video_url == "https://example.com/new-video.mp4"
        assert dto.form_cues == ["Updated form cue"]
        assert dto.version_notes == "Major update"
        assert dto.change_reason == ChangeReason.CONTENT_UPDATE
