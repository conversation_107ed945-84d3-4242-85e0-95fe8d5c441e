"""
Unit tests for GetUserProfileUseCase.

Tests the application logic for retrieving user profiles.
"""

import pytest
from unittest.mock import AsyncMock
from uuid import uuid4

from app.application.use_cases.get_user_profile import GetUserProfileUseCase
from app.application.dto.user_dto import UserDTO
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserNotFoundError


@pytest.mark.unit
@pytest.mark.auth
class TestGetUserProfileUseCase:
    """Test get user profile use case."""
    
    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Mock authentication service."""
        return AsyncMock()
    
    @pytest.fixture
    def use_case(self, mock_auth_service: AsyncMock) -> GetUserProfileUseCase:
        """Create use case instance with mocked dependencies."""
        return GetUserProfileUseCase(mock_auth_service)
    
    @pytest.fixture
    def sample_user(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="<PERSON>",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=False
        )
    
    async def test_execute_success(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User
    ) -> None:
        """Test successful user profile retrieval."""
        # Setup
        mock_auth_service.get_user_by_id.return_value = sample_user
        
        # Execute
        result = await use_case.execute(sample_user.id)
        
        # Verify
        assert isinstance(result, UserDTO)
        assert result.id == sample_user.id
        assert result.email == sample_user.email
        assert result.first_name == sample_user.first_name
        assert result.last_name == sample_user.last_name
        assert result.training_experience_years == sample_user.training_experience_years
        assert result.is_active == sample_user.is_active
        assert result.is_verified == sample_user.is_verified
        
        # Verify service call
        mock_auth_service.get_user_by_id.assert_called_once_with(sample_user.id)
    
    async def test_execute_user_not_found(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock
    ) -> None:
        """Test profile retrieval for non-existent user."""
        # Setup
        user_id = uuid4()
        mock_auth_service.get_user_by_id.side_effect = UserNotFoundError(
            f"User with ID {user_id} not found"
        )
        
        # Execute & Verify
        with pytest.raises(UserNotFoundError) as exc_info:
            await use_case.execute(user_id)
        
        assert str(user_id) in str(exc_info.value)
        mock_auth_service.get_user_by_id.assert_called_once_with(user_id)
    
    async def test_dto_conversion_completeness(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User
    ) -> None:
        """Test that all user fields are properly converted to DTO."""
        # Setup
        mock_auth_service.get_user_by_id.return_value = sample_user
        
        # Execute
        result = await use_case.execute(sample_user.id)
        
        # Verify all fields are present
        assert hasattr(result, 'id')
        assert hasattr(result, 'email')
        assert hasattr(result, 'first_name')
        assert hasattr(result, 'last_name')
        assert hasattr(result, 'training_experience_years')
        assert hasattr(result, 'is_active')
        assert hasattr(result, 'is_verified')
        assert hasattr(result, 'created_at')
        assert hasattr(result, 'updated_at')
        
        # Verify values match
        assert result.id == sample_user.id
        assert result.email == sample_user.email
        assert result.first_name == sample_user.first_name
        assert result.last_name == sample_user.last_name
        assert result.training_experience_years == sample_user.training_experience_years
        assert result.is_active == sample_user.is_active
        assert result.is_verified == sample_user.is_verified
        assert result.created_at == sample_user.created_at
        assert result.updated_at == sample_user.updated_at
