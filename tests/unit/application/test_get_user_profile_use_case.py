"""
Unit tests for get user profile use case.

Tests the business logic for retrieving user profiles without external dependencies.
"""

import pytest
from unittest.mock import AsyncMock
from uuid import uuid4

from app.application.use_cases.get_user_profile import GetUserProfileUseCase
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserNotFoundError
from tests.factories import UserFactory


@pytest.mark.unit
@pytest.mark.auth
class TestGetUserProfileUseCase:
    """Test cases for get user profile use case."""

    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Create mock auth service."""
        return AsyncMock()

    @pytest.fixture
    def use_case(self, mock_auth_service: AsyncMock) -> GetUserProfileUseCase:
        """Create get user profile use case."""
        return GetUserProfileUseCase(mock_auth_service)

    @pytest.fixture
    def sample_user(self) -> User:
        """Create sample user for testing."""
        return UserFactory.create_user_entity(
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON>",
            training_experience_years=3,
            is_active=True,
            is_verified=True,
        )

    async def test_get_user_profile_success(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test successful user profile retrieval."""
        # Setup
        user_id = sample_user.id
        mock_auth_service.get_user_by_id.return_value = sample_user

        # Execute
        result = await use_case.execute(user_id)

        # Verify
        assert result.id == user_id
        assert result.email == "<EMAIL>"
        assert result.first_name == "Jane"
        assert result.last_name == "Smith"
        assert result.training_experience_years == 3
        assert result.is_active is True
        assert result.is_verified is True
        assert result.created_at is not None
        assert result.updated_at is not None

        # Verify service call
        mock_auth_service.get_user_by_id.assert_called_once_with(user_id)

    async def test_get_user_profile_not_found(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval for non-existent user."""
        # Setup
        user_id = uuid4()
        mock_auth_service.get_user_by_id.side_effect = UserNotFoundError(
            f"User with ID {user_id} not found"
        )

        # Execute & Verify
        with pytest.raises(UserNotFoundError, match=f"User with ID {user_id} not found"):
            await use_case.execute(user_id)

        mock_auth_service.get_user_by_id.assert_called_once_with(user_id)

    async def test_get_user_profile_minimal_data(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval for user with minimal data."""
        # Setup - user with only required fields
        minimal_user = UserFactory.create_user_entity(
            email="<EMAIL>",
            first_name=None,
            last_name=None,
            training_experience_years=None,
        )
        mock_auth_service.get_user_by_id.return_value = minimal_user

        # Execute
        result = await use_case.execute(minimal_user.id)

        # Verify
        assert result.email == "<EMAIL>"
        assert result.first_name is None
        assert result.last_name is None
        assert result.training_experience_years is None
        assert result.full_name is None  # Should handle None names gracefully

    async def test_get_user_profile_inactive_user(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval for inactive user."""
        # Setup
        inactive_user = UserFactory.create_user_entity(
            email="<EMAIL>",
            is_active=False,
        )
        mock_auth_service.get_user_by_id.return_value = inactive_user

        # Execute
        result = await use_case.execute(inactive_user.id)

        # Verify - should still return profile even if inactive
        assert result.email == "<EMAIL>"
        assert result.is_active is False

    async def test_get_user_profile_unverified_user(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval for unverified user."""
        # Setup
        unverified_user = UserFactory.create_user_entity(
            email="<EMAIL>",
            is_verified=False,
        )
        mock_auth_service.get_user_by_id.return_value = unverified_user

        # Execute
        result = await use_case.execute(unverified_user.id)

        # Verify
        assert result.email == "<EMAIL>"
        assert result.is_verified is False

    async def test_get_user_profile_mobile_optimized_response(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test that profile response is optimized for mobile clients."""
        # Setup
        mock_auth_service.get_user_by_id.return_value = sample_user

        # Execute
        result = await use_case.execute(sample_user.id)

        # Verify mobile-optimized fields are present
        assert hasattr(result, "id")
        assert hasattr(result, "email")
        assert hasattr(result, "first_name")
        assert hasattr(result, "last_name")
        assert hasattr(result, "full_name")
        assert hasattr(result, "training_experience_years")
        assert hasattr(result, "is_active")
        assert hasattr(result, "is_verified")
        assert hasattr(result, "created_at")
        assert hasattr(result, "updated_at")

        # Verify sensitive fields are not exposed
        assert not hasattr(result, "hashed_password")
        assert not hasattr(result, "deleted_at")

    async def test_get_user_profile_with_training_experience(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval with various training experience levels."""
        # Test different experience levels
        experience_levels = [0, 1, 2, 5, 10, 15, 20]
        
        for experience in experience_levels:
            # Setup
            user = UserFactory.create_user_entity(
                email=f"user{experience}@example.com",
                training_experience_years=experience,
            )
            mock_auth_service.get_user_by_id.return_value = user

            # Execute
            result = await use_case.execute(user.id)

            # Verify
            assert result.training_experience_years == experience

    async def test_get_user_profile_full_name_combinations(
        self,
        use_case: GetUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile retrieval with different name combinations."""
        test_cases = [
            ("John", "Doe", "John Doe"),
            ("Jane", None, "Jane"),
            (None, "Smith", "Smith"),
            (None, None, None),
            ("", "", None),
            ("  ", "  ", None),
        ]

        for first_name, last_name, expected_full_name in test_cases:
            # Setup
            user = UserFactory.create_user_entity(
                email=f"test{hash((first_name, last_name))}@example.com",
                first_name=first_name,
                last_name=last_name,
            )
            mock_auth_service.get_user_by_id.return_value = user

            # Execute
            result = await use_case.execute(user.id)

            # Verify
            assert result.full_name == expected_full_name
