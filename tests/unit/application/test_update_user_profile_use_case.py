"""
Unit tests for UpdateUserProfileUseCase.

Tests the application logic for updating user profiles.
"""

import pytest
from unittest.mock import AsyncMock
from uuid import uuid4

from app.application.use_cases.update_user_profile import UpdateUserProfileUseCase
from app.application.dto.user_dto import UpdateUserDTO, UserDTO
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserNotFoundError


@pytest.mark.unit
@pytest.mark.auth
class TestUpdateUserProfileUseCase:
    """Test update user profile use case."""
    
    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Mock authentication service."""
        return AsyncMock()
    
    @pytest.fixture
    def use_case(self, mock_auth_service: AsyncMock) -> UpdateUserProfileUseCase:
        """Create use case instance with mocked dependencies."""
        return UpdateUserProfileUseCase(mock_auth_service)
    
    @pytest.fixture
    def sample_user(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="<PERSON>",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=False
        )
    
    @pytest.fixture
    def sample_update_dto(self) -> UpdateUserDTO:
        """Sample update DTO."""
        return UpdateUserDTO(
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5
        )
    
    async def test_execute_success(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
        sample_update_dto: UpdateUserDTO
    ) -> None:
        """Test successful user profile update."""
        # Setup - create updated user
        updated_user = User(
            id=sample_user.id,
            email=sample_user.email,
            hashed_password=sample_user.hashed_password,
            first_name=sample_update_dto.first_name,
            last_name=sample_update_dto.last_name,
            training_experience_years=sample_update_dto.training_experience_years,
            is_active=sample_user.is_active,
            is_verified=sample_user.is_verified
        )
        
        mock_auth_service.update_user_profile.return_value = updated_user
        
        # Execute
        result = await use_case.execute(sample_user.id, sample_update_dto)
        
        # Verify
        assert isinstance(result, UserDTO)
        assert result.id == sample_user.id
        assert result.first_name == sample_update_dto.first_name
        assert result.last_name == sample_update_dto.last_name
        assert result.training_experience_years == sample_update_dto.training_experience_years
        
        # Verify service call
        mock_auth_service.update_user_profile.assert_called_once_with(
            user_id=sample_user.id,
            first_name=sample_update_dto.first_name,
            last_name=sample_update_dto.last_name,
            training_experience_years=sample_update_dto.training_experience_years
        )
    
    async def test_execute_partial_update(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User
    ) -> None:
        """Test partial user profile update."""
        # Setup - partial update DTO
        partial_update = UpdateUserDTO(first_name="UpdatedName")
        
        updated_user = User(
            id=sample_user.id,
            email=sample_user.email,
            hashed_password=sample_user.hashed_password,
            first_name="UpdatedName",
            last_name=sample_user.last_name,
            training_experience_years=sample_user.training_experience_years,
            is_active=sample_user.is_active,
            is_verified=sample_user.is_verified
        )
        
        mock_auth_service.update_user_profile.return_value = updated_user
        
        # Execute
        result = await use_case.execute(sample_user.id, partial_update)
        
        # Verify
        assert result.first_name == "UpdatedName"
        assert result.last_name == sample_user.last_name  # Unchanged
        assert result.training_experience_years == sample_user.training_experience_years  # Unchanged
        
        # Verify service call with None values for unchanged fields
        mock_auth_service.update_user_profile.assert_called_once_with(
            user_id=sample_user.id,
            first_name="UpdatedName",
            last_name=None,
            training_experience_years=None
        )
    
    async def test_execute_user_not_found(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_update_dto: UpdateUserDTO
    ) -> None:
        """Test profile update for non-existent user."""
        # Setup
        user_id = uuid4()
        mock_auth_service.update_user_profile.side_effect = UserNotFoundError(
            f"User with ID {user_id} not found"
        )
        
        # Execute & Verify
        with pytest.raises(UserNotFoundError) as exc_info:
            await use_case.execute(user_id, sample_update_dto)
        
        assert str(user_id) in str(exc_info.value)
        mock_auth_service.update_user_profile.assert_called_once()
    
    async def test_execute_empty_update(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User
    ) -> None:
        """Test update with empty DTO (all None values)."""
        # Setup - empty update DTO
        empty_update = UpdateUserDTO()
        
        mock_auth_service.update_user_profile.return_value = sample_user
        
        # Execute
        result = await use_case.execute(sample_user.id, empty_update)
        
        # Verify
        assert isinstance(result, UserDTO)
        assert result.id == sample_user.id
        
        # Verify service call with all None values
        mock_auth_service.update_user_profile.assert_called_once_with(
            user_id=sample_user.id,
            first_name=None,
            last_name=None,
            training_experience_years=None
        )
    
    async def test_dto_conversion_after_update(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
        sample_update_dto: UpdateUserDTO
    ) -> None:
        """Test that updated user is properly converted to DTO."""
        # Setup
        updated_user = User(
            id=sample_user.id,
            email=sample_user.email,
            hashed_password=sample_user.hashed_password,
            first_name=sample_update_dto.first_name,
            last_name=sample_update_dto.last_name,
            training_experience_years=sample_update_dto.training_experience_years,
            is_active=sample_user.is_active,
            is_verified=sample_user.is_verified,
            created_at=sample_user.created_at,
            updated_at=sample_user.updated_at
        )
        
        mock_auth_service.update_user_profile.return_value = updated_user
        
        # Execute
        result = await use_case.execute(sample_user.id, sample_update_dto)
        
        # Verify all fields are properly converted
        assert result.id == updated_user.id
        assert result.email == updated_user.email
        assert result.first_name == updated_user.first_name
        assert result.last_name == updated_user.last_name
        assert result.training_experience_years == updated_user.training_experience_years
        assert result.is_active == updated_user.is_active
        assert result.is_verified == updated_user.is_verified
        assert result.created_at == updated_user.created_at
        assert result.updated_at == updated_user.updated_at
