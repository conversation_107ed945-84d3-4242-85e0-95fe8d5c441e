"""
Unit tests for update user profile use case.

Tests the business logic for updating user profiles without external dependencies.
"""

import pytest
from unittest.mock import AsyncMock
from uuid import uuid4

from app.application.use_cases.update_user_profile import (
    UpdateUserProfileUseCase,
    UpdateUserProfileRequest,
    UpdateUserProfileResponse,
)
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserNotFoundError, ValidationError
from tests.factories import UserFactory


@pytest.mark.unit
@pytest.mark.auth
class TestUpdateUserProfileUseCase:
    """Test cases for update user profile use case."""

    @pytest.fixture
    def mock_auth_service(self) -> AsyncMock:
        """Create mock auth service."""
        return AsyncMock()

    @pytest.fixture
    def use_case(self, mock_auth_service: AsyncMock) -> UpdateUserProfileUseCase:
        """Create update user profile use case."""
        return UpdateUserProfileUseCase(mock_auth_service)

    @pytest.fixture
    def sample_user(self) -> User:
        """Create sample user for testing."""
        return UserFactory.create_user_entity(
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=True,
        )

    async def test_update_profile_success_all_fields(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test successful profile update with all fields."""
        # Setup
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
            is_active=True,
            is_verified=True,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
        )

        # Execute
        result = await use_case.execute(request)

        # Verify
        assert isinstance(result, UpdateUserProfileResponse)
        assert result.user.id == sample_user.id
        assert result.user.first_name == "Jane"
        assert result.user.last_name == "Smith"
        assert result.user.training_experience_years == 5
        assert result.success is True

        # Verify service call
        mock_auth_service.update_user_profile.assert_called_once_with(
            user_id=sample_user.id,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
        )

    async def test_update_profile_partial_fields(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with only some fields."""
        # Setup
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name="Jane",
            last_name=sample_user.last_name,
            training_experience_years=sample_user.training_experience_years,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="Jane",
        )

        # Execute
        result = await use_case.execute(request)

        # Verify
        assert result.user.first_name == "Jane"
        assert result.user.last_name == sample_user.last_name  # Unchanged
        assert result.success is True

    async def test_update_profile_user_not_found(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
    ) -> None:
        """Test profile update for non-existent user."""
        # Setup
        user_id = uuid4()
        mock_auth_service.update_user_profile.side_effect = UserNotFoundError(
            f"User with ID {user_id} not found"
        )

        request = UpdateUserProfileRequest(
            user_id=user_id,
            first_name="Jane",
        )

        # Execute & Verify
        with pytest.raises(UserNotFoundError, match=f"User with ID {user_id} not found"):
            await use_case.execute(request)

    async def test_update_profile_invalid_training_experience(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with invalid training experience."""
        # Setup
        mock_auth_service.update_user_profile.side_effect = ValidationError(
            "Training experience must be between 0 and 50 years"
        )

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            training_experience_years=-1,
        )

        # Execute & Verify
        with pytest.raises(ValidationError, match="Training experience must be between 0 and 50 years"):
            await use_case.execute(request)

    async def test_update_profile_empty_names(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with empty names."""
        # Setup
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name=None,
            last_name=None,
            training_experience_years=sample_user.training_experience_years,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="",
            last_name="",
        )

        # Execute
        result = await use_case.execute(request)

        # Verify - empty strings should be converted to None
        assert result.user.first_name is None
        assert result.user.last_name is None

    async def test_update_profile_whitespace_names(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with whitespace-only names."""
        # Setup
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name=None,
            last_name=None,
            training_experience_years=sample_user.training_experience_years,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="   ",
            last_name="   ",
        )

        # Execute
        result = await use_case.execute(request)

        # Verify - whitespace-only strings should be converted to None
        assert result.user.first_name is None
        assert result.user.last_name is None

    async def test_update_profile_long_names(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with very long names."""
        # Setup
        long_name = "A" * 100
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name=long_name,
            last_name=long_name,
            training_experience_years=sample_user.training_experience_years,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name=long_name,
            last_name=long_name,
        )

        # Execute
        result = await use_case.execute(request)

        # Verify
        assert result.user.first_name == long_name
        assert result.user.last_name == long_name

    async def test_update_profile_special_characters(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with special characters in names."""
        # Setup
        special_first_name = "José"
        special_last_name = "O'Connor-Smith"
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name=special_first_name,
            last_name=special_last_name,
            training_experience_years=sample_user.training_experience_years,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name=special_first_name,
            last_name=special_last_name,
        )

        # Execute
        result = await use_case.execute(request)

        # Verify
        assert result.user.first_name == special_first_name
        assert result.user.last_name == special_last_name

    async def test_update_profile_training_experience_edge_cases(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with training experience edge cases."""
        edge_cases = [0, 1, 25, 50]
        
        for experience in edge_cases:
            # Setup
            updated_user = UserFactory.create_user_entity(
                id=sample_user.id,
                email=sample_user.email,
                first_name=sample_user.first_name,
                last_name=sample_user.last_name,
                training_experience_years=experience,
            )
            mock_auth_service.update_user_profile.return_value = updated_user

            request = UpdateUserProfileRequest(
                user_id=sample_user.id,
                training_experience_years=experience,
            )

            # Execute
            result = await use_case.execute(request)

            # Verify
            assert result.user.training_experience_years == experience

    async def test_update_profile_no_changes(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update with no actual changes."""
        # Setup
        mock_auth_service.update_user_profile.return_value = sample_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name=sample_user.first_name,
            last_name=sample_user.last_name,
            training_experience_years=sample_user.training_experience_years,
        )

        # Execute
        result = await use_case.execute(request)

        # Verify
        assert result.user.first_name == sample_user.first_name
        assert result.user.last_name == sample_user.last_name
        assert result.user.training_experience_years == sample_user.training_experience_years
        assert result.success is True

    async def test_update_profile_mobile_optimized_response(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update response is optimized for mobile."""
        # Setup
        updated_user = UserFactory.create_user_entity(
            id=sample_user.id,
            email=sample_user.email,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
        )
        mock_auth_service.update_user_profile.return_value = updated_user

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="Jane",
            last_name="Smith",
            training_experience_years=5,
        )

        # Execute
        result = await use_case.execute(request)

        # Verify mobile-optimized response structure
        assert hasattr(result, "user")
        assert hasattr(result, "success")
        
        # Verify user data is properly serialized
        assert result.user.id is not None
        assert result.user.email == sample_user.email
        assert result.user.created_at is not None
        assert result.user.updated_at is not None
        
        # Verify no sensitive data is exposed
        assert not hasattr(result.user, "hashed_password")
        assert not hasattr(result.user, "deleted_at")

    async def test_update_profile_concurrent_updates(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update handles concurrent updates gracefully."""
        # Setup - simulate optimistic locking conflict
        mock_auth_service.update_user_profile.side_effect = ValidationError(
            "Profile was updated by another request. Please refresh and try again."
        )

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="Jane",
        )

        # Execute & Verify
        with pytest.raises(ValidationError, match="Profile was updated by another request"):
            await use_case.execute(request)

    async def test_update_profile_service_failure(
        self,
        use_case: UpdateUserProfileUseCase,
        mock_auth_service: AsyncMock,
        sample_user: User,
    ) -> None:
        """Test profile update handles service failures."""
        # Setup
        mock_auth_service.update_user_profile.side_effect = Exception("Database connection failed")

        request = UpdateUserProfileRequest(
            user_id=sample_user.id,
            first_name="Jane",
        )

        # Execute & Verify
        with pytest.raises(Exception, match="Database connection failed"):
            await use_case.execute(request)
