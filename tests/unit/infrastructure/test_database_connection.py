"""
Unit tests for database connection management.

Tests database connection pooling, session management, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.infrastructure.database.connection import DatabaseManager, get_database_session
from app.config import settings


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseManager:
    """Test cases for database manager."""

    @pytest.fixture
    def db_manager(self) -> DatabaseManager:
        """Create database manager instance."""
        return DatabaseManager()

    def test_database_manager_initialization(self, db_manager: DatabaseManager) -> None:
        """Test database manager initialization."""
        assert db_manager._engine is None
        assert db_manager._session_factory is None

    @patch("app.infrastructure.database.connection.create_async_engine")
    @patch("app.infrastructure.database.connection.async_sessionmaker")
    def test_initialize_success(
        self,
        mock_sessionmaker: MagicMock,
        mock_create_engine: MagicMock,
        db_manager: DatabaseManager,
    ) -> None:
        """Test successful database initialization."""
        # Setup
        mock_engine = MagicMock()
        mock_session_factory = MagicMock()
        mock_create_engine.return_value = mock_engine
        mock_sessionmaker.return_value = mock_session_factory

        # Execute
        db_manager.initialize()

        # Verify
        assert db_manager._engine == mock_engine
        assert db_manager._session_factory == mock_session_factory
        
        # Verify engine creation with correct parameters
        mock_create_engine.assert_called_once_with(
            settings.database_url,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            echo=settings.debug,
        )
        
        # Verify session factory creation
        mock_sessionmaker.assert_called_once_with(
            bind=mock_engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )

    def test_initialize_already_initialized(self, db_manager: DatabaseManager) -> None:
        """Test initialization when already initialized."""
        # Setup - simulate already initialized
        db_manager._engine = MagicMock()
        db_manager._session_factory = MagicMock()
        original_engine = db_manager._engine
        original_factory = db_manager._session_factory

        # Execute
        db_manager.initialize()

        # Verify - should not reinitialize
        assert db_manager._engine == original_engine
        assert db_manager._session_factory == original_factory

    @patch("app.infrastructure.database.connection.create_async_engine")
    def test_initialize_engine_creation_failure(
        self,
        mock_create_engine: MagicMock,
        db_manager: DatabaseManager,
    ) -> None:
        """Test initialization failure during engine creation."""
        # Setup
        mock_create_engine.side_effect = Exception("Database connection failed")

        # Execute & Verify
        with pytest.raises(Exception, match="Database connection failed"):
            db_manager.initialize()

        assert db_manager._engine is None
        assert db_manager._session_factory is None

    async def test_close_success(self, db_manager: DatabaseManager) -> None:
        """Test successful database connection closure."""
        # Setup
        mock_engine = AsyncMock()
        db_manager._engine = mock_engine

        # Execute
        await db_manager.close()

        # Verify
        mock_engine.dispose.assert_called_once()
        assert db_manager._engine is None
        assert db_manager._session_factory is None

    async def test_close_not_initialized(self, db_manager: DatabaseManager) -> None:
        """Test closing when not initialized."""
        # Execute - should not raise exception
        await db_manager.close()

        # Verify
        assert db_manager._engine is None
        assert db_manager._session_factory is None

    async def test_close_engine_disposal_failure(self, db_manager: DatabaseManager) -> None:
        """Test closure when engine disposal fails."""
        # Setup
        mock_engine = AsyncMock()
        mock_engine.dispose.side_effect = Exception("Disposal failed")
        db_manager._engine = mock_engine

        # Execute & Verify - should handle gracefully
        await db_manager.close()

        # Verify cleanup still happens
        assert db_manager._engine is None
        assert db_manager._session_factory is None

    def test_get_session_factory_success(self, db_manager: DatabaseManager) -> None:
        """Test successful session factory retrieval."""
        # Setup
        mock_factory = MagicMock()
        db_manager._session_factory = mock_factory

        # Execute
        result = db_manager.get_session_factory()

        # Verify
        assert result == mock_factory

    def test_get_session_factory_not_initialized(self, db_manager: DatabaseManager) -> None:
        """Test session factory retrieval when not initialized."""
        # Execute & Verify
        with pytest.raises(RuntimeError, match="Database not initialized"):
            db_manager.get_session_factory()

    def test_is_initialized_true(self, db_manager: DatabaseManager) -> None:
        """Test is_initialized when database is initialized."""
        # Setup
        db_manager._engine = MagicMock()
        db_manager._session_factory = MagicMock()

        # Execute & Verify
        assert db_manager.is_initialized() is True

    def test_is_initialized_false(self, db_manager: DatabaseManager) -> None:
        """Test is_initialized when database is not initialized."""
        # Execute & Verify
        assert db_manager.is_initialized() is False

    def test_is_initialized_partial(self, db_manager: DatabaseManager) -> None:
        """Test is_initialized when partially initialized."""
        # Setup - only engine initialized
        db_manager._engine = MagicMock()

        # Execute & Verify
        assert db_manager.is_initialized() is False


@pytest.mark.unit
@pytest.mark.database
class TestGetDatabaseSession:
    """Test cases for get_database_session dependency."""

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_success(self, mock_db_manager: MagicMock) -> None:
        """Test successful database session retrieval."""
        # Setup
        mock_session = AsyncMock()
        mock_session_factory = AsyncMock()
        mock_session_factory.return_value.__aenter__.return_value = mock_session
        mock_db_manager.get_session_factory.return_value = mock_session_factory

        # Execute
        async for session in get_database_session():
            # Verify
            assert session == mock_session
            break

        # Verify session factory was called
        mock_db_manager.get_session_factory.assert_called_once()

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_not_initialized(self, mock_db_manager: MagicMock) -> None:
        """Test database session retrieval when not initialized."""
        # Setup
        mock_db_manager.get_session_factory.side_effect = RuntimeError("Database not initialized")

        # Execute & Verify
        with pytest.raises(RuntimeError, match="Database not initialized"):
            async for session in get_database_session():
                break

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_factory_failure(self, mock_db_manager: MagicMock) -> None:
        """Test database session retrieval when session factory fails."""
        # Setup
        mock_session_factory = AsyncMock()
        mock_session_factory.side_effect = Exception("Session creation failed")
        mock_db_manager.get_session_factory.return_value = mock_session_factory

        # Execute & Verify
        with pytest.raises(Exception, match="Session creation failed"):
            async for session in get_database_session():
                break

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_cleanup(self, mock_db_manager: MagicMock) -> None:
        """Test that database session is properly cleaned up."""
        # Setup
        mock_session = AsyncMock()
        mock_session_factory = AsyncMock()
        mock_session_factory.return_value.__aenter__.return_value = mock_session
        mock_session_factory.return_value.__aexit__.return_value = None
        mock_db_manager.get_session_factory.return_value = mock_session_factory

        # Execute
        async for session in get_database_session():
            assert session == mock_session
            break

        # Verify cleanup was called
        mock_session_factory.return_value.__aexit__.assert_called_once()
