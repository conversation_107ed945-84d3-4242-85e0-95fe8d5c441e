"""Unit tests for database connection management.

Tests database session creation, connection pooling, and health checks.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from app.config import settings
from app.infrastructure.database.connection import (
    DatabaseManager,
    db_manager,
    get_database_session,
)


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseManager:
    """Test DatabaseManager functionality."""

    @pytest.fixture
    def mock_engine(self) -> AsyncMock:
        """Mock async database engine."""
        return AsyncMock(spec=AsyncEngine)

    @pytest.fixture
    def database_manager(self, mock_engine: AsyncMock) -> DatabaseManager:
        """Create DatabaseManager with mocked engine."""
        manager = DatabaseManager()
        manager._engine = mock_engine
        return manager

    def test_database_manager_initialization(self) -> None:
        """Test DatabaseManager initialization."""
        manager = DatabaseManager()

        assert manager is not None
        assert hasattr(manager, "_engine")
        assert hasattr(manager, "_session_factory")

    def test_database_manager_singleton(self) -> None:
        """Test DatabaseManager singleton pattern."""
        manager1 = DatabaseManager()
        manager2 = DatabaseManager()

        # Should be the same instance
        assert manager1 is manager2

    @patch("app.infrastructure.database.connection.create_async_engine")
    def test_initialize_engine(self, mock_create_engine: Mock) -> None:
        """Test engine initialization."""
        mock_engine = AsyncMock()
        mock_create_engine.return_value = mock_engine

        manager = DatabaseManager()
        manager.initialize()

        mock_create_engine.assert_called_once_with(
            settings.database_url,
            echo=settings.debug,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
        assert manager._engine == mock_engine

    async def test_get_session_success(self, database_manager: DatabaseManager) -> None:
        """Test successful session creation."""
        mock_session = AsyncMock(spec=AsyncSession)
        database_manager._session_factory = Mock(return_value=mock_session)

        session = await database_manager.get_session()

        assert session == mock_session
        database_manager._session_factory.assert_called_once()

    async def test_get_session_failure(self, database_manager: DatabaseManager) -> None:
        """Test session creation failure."""
        database_manager._session_factory = Mock(
            side_effect=SQLAlchemyError("Connection failed")
        )

        with pytest.raises(SQLAlchemyError):
            await database_manager.get_session()

    async def test_health_check_success(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test successful health check."""
        mock_session = AsyncMock()
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        database_manager.get_session = AsyncMock(return_value=mock_session)

        result = await database_manager.health_check()

        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.close.assert_called_once()

    async def test_health_check_failure(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test health check failure."""
        database_manager.get_session = AsyncMock(
            side_effect=SQLAlchemyError("Connection failed")
        )

        result = await database_manager.health_check()

        assert result is False

    async def test_close_success(self, database_manager: DatabaseManager) -> None:
        """Test successful database close."""
        mock_engine = AsyncMock()
        database_manager._engine = mock_engine

        await database_manager.close()

        mock_engine.dispose.assert_called_once()

    async def test_close_no_engine(self, database_manager: DatabaseManager) -> None:
        """Test close when no engine exists."""
        database_manager._engine = None

        # Should not raise exception
        await database_manager.close()

    def test_database_url_configuration(self) -> None:
        """Test database URL configuration."""
        DatabaseManager()

        # Should use settings database URL
        assert hasattr(settings, "database_url")
        assert isinstance(settings.database_url, str)
        assert len(settings.database_url) > 0

    @patch("app.infrastructure.database.connection.sessionmaker")
    def test_session_factory_configuration(self, mock_sessionmaker: Mock) -> None:
        """Test session factory configuration."""
        mock_engine = AsyncMock()
        mock_sessionmaker.return_value = Mock()

        manager = DatabaseManager()
        manager._engine = mock_engine
        manager._create_session_factory()

        mock_sessionmaker.assert_called_once_with(
            bind=mock_engine, class_=AsyncSession, expire_on_commit=False
        )


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseSessionDependency:
    """Test database session dependency function."""

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_success(
        self, mock_db_manager: AsyncMock
    ) -> None:
        """Test successful database session dependency."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_db_manager.get_session.return_value = mock_session

        # Test the async generator
        session_generator = get_database_session()
        session = await session_generator.__anext__()

        assert session == mock_session
        mock_db_manager.get_session.assert_called_once()

        # Test cleanup
        try:
            await session_generator.__anext__()
        except StopAsyncIteration:
            pass

        mock_session.close.assert_called_once()

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_failure(
        self, mock_db_manager: AsyncMock
    ) -> None:
        """Test database session dependency failure."""
        mock_db_manager.get_session.side_effect = SQLAlchemyError("Connection failed")

        session_generator = get_database_session()

        with pytest.raises(SQLAlchemyError):
            await session_generator.__anext__()

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_get_database_session_cleanup_on_exception(
        self, mock_db_manager: AsyncMock
    ) -> None:
        """Test database session cleanup on exception."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session.close.side_effect = Exception("Close failed")
        mock_db_manager.get_session.return_value = mock_session

        session_generator = get_database_session()
        session = await session_generator.__anext__()

        assert session == mock_session

        # Test cleanup with exception (should not propagate)
        try:
            await session_generator.__anext__()
        except StopAsyncIteration:
            pass

        mock_session.close.assert_called_once()


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseManagerGlobal:
    """Test global database manager instance."""

    def test_db_manager_instance(self) -> None:
        """Test global db_manager instance."""
        assert db_manager is not None
        assert isinstance(db_manager, DatabaseManager)

    def test_db_manager_singleton_consistency(self) -> None:
        """Test db_manager is consistent with DatabaseManager singleton."""
        manager = DatabaseManager()
        assert db_manager is manager

    @patch("app.infrastructure.database.connection.db_manager")
    async def test_db_manager_methods_available(
        self, mock_db_manager: AsyncMock
    ) -> None:
        """Test db_manager has required methods."""
        # Test that all required methods exist
        required_methods = ["initialize", "get_session", "health_check", "close"]

        for method_name in required_methods:
            assert hasattr(db_manager, method_name)
            method = getattr(db_manager, method_name)
            assert callable(method)


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseConfiguration:
    """Test database configuration and settings."""

    def test_database_url_format(self) -> None:
        """Test database URL format."""
        db_url = settings.database_url

        # Should be a valid database URL
        assert isinstance(db_url, str)
        assert len(db_url) > 0

        # Should contain database scheme
        valid_schemes = ["sqlite", "postgresql", "mysql"]
        assert any(scheme in db_url for scheme in valid_schemes)

    def test_database_debug_setting(self) -> None:
        """Test database debug setting."""
        debug_setting = settings.debug

        assert isinstance(debug_setting, bool)

    @patch.dict("os.environ", {"DATABASE_URL": "postgresql://test:test@localhost/test"})
    def test_database_url_environment_override(self) -> None:
        """Test database URL can be overridden by environment."""
        from app.config import Settings

        test_settings = Settings()
        assert test_settings.database_url == "postgresql://test:test@localhost/test"

    def test_database_connection_parameters(self) -> None:
        """Test database connection parameters."""
        manager = DatabaseManager()

        # Should have reasonable defaults
        assert hasattr(manager, "_engine")
        assert hasattr(manager, "_session_factory")

    @patch("app.infrastructure.database.connection.create_async_engine")
    def test_engine_configuration_parameters(self, mock_create_engine: Mock) -> None:
        """Test engine configuration parameters."""
        manager = DatabaseManager()
        manager.initialize()

        # Verify engine created with correct parameters
        call_args = mock_create_engine.call_args
        assert call_args[0][0] == settings.database_url

        kwargs = call_args[1]
        assert "echo" in kwargs
        assert "pool_pre_ping" in kwargs
        assert "pool_recycle" in kwargs
        assert kwargs["pool_pre_ping"] is True
        assert kwargs["pool_recycle"] == 3600

    async def test_database_manager_close_with_engine(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test database manager close with engine."""
        mock_engine = AsyncMock()
        database_manager._engine = mock_engine

        await database_manager.close()

        mock_engine.dispose.assert_called_once()

    async def test_database_manager_close_without_engine(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test database manager close without engine."""
        database_manager._engine = None

        # Should not raise exception
        await database_manager.close()

    def test_database_manager_create_session_factory(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test session factory creation."""
        mock_engine = AsyncMock()
        database_manager._engine = mock_engine

        with patch(
            "app.infrastructure.database.connection.sessionmaker"
        ) as mock_sessionmaker:
            mock_session_factory = Mock()
            mock_sessionmaker.return_value = mock_session_factory

            database_manager._create_session_factory()

            mock_sessionmaker.assert_called_once()
            call_args = mock_sessionmaker.call_args[1]
            assert call_args["bind"] == mock_engine
            assert call_args["expire_on_commit"] is False
            assert database_manager._session_factory == mock_session_factory

    async def test_database_manager_get_session_with_factory(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test get session with session factory."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session_factory = Mock(return_value=mock_session)
        database_manager._session_factory = mock_session_factory

        session = await database_manager.get_session()

        assert session == mock_session
        mock_session_factory.assert_called_once()

    async def test_database_manager_get_session_without_factory(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test get session without session factory."""
        database_manager._session_factory = None

        with pytest.raises(RuntimeError, match="Database not initialized"):
            await database_manager.get_session()

    async def test_database_manager_health_check_with_session(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test health check with successful session."""
        mock_session = AsyncMock()
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        database_manager.get_session = AsyncMock(return_value=mock_session)

        result = await database_manager.health_check()

        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.close.assert_called_once()

    async def test_database_manager_health_check_with_exception(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test health check with exception."""
        database_manager.get_session = AsyncMock(
            side_effect=Exception("Connection failed")
        )

        result = await database_manager.health_check()

        assert result is False

    async def test_database_manager_health_check_with_session_exception(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test health check with session execution exception."""
        mock_session = AsyncMock()
        mock_session.execute.side_effect = Exception("Query failed")

        database_manager.get_session = AsyncMock(return_value=mock_session)

        result = await database_manager.health_check()

        assert result is False
        mock_session.close.assert_called_once()

    def test_database_manager_initialize_already_initialized(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test initialize when already initialized."""
        # Set up as already initialized
        database_manager._engine = AsyncMock()
        database_manager._session_factory = Mock()

        with patch(
            "app.infrastructure.database.connection.create_async_engine"
        ) as mock_create_engine:
            database_manager.initialize()

            # Should not create new engine
            mock_create_engine.assert_not_called()

    def test_database_manager_initialize_first_time(
        self, database_manager: DatabaseManager
    ) -> None:
        """Test initialize for the first time."""
        # Ensure not initialized
        database_manager._engine = None
        database_manager._session_factory = None

        with (
            patch(
                "app.infrastructure.database.connection.create_async_engine"
            ) as mock_create_engine,
            patch.object(
                database_manager, "_create_session_factory"
            ) as mock_create_factory,
        ):
            mock_engine = AsyncMock()
            mock_create_engine.return_value = mock_engine

            database_manager.initialize()

            mock_create_engine.assert_called_once()
            mock_create_factory.assert_called_once()
            assert database_manager._engine == mock_engine
