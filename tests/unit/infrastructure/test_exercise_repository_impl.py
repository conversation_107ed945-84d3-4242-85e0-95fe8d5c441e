"""
Unit tests for exercise repository implementation.

Tests for SQLAlchemy exercise repository implementation including
CRUD operations, search, approval workflows, and statistics.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func

from app.domain.entities.exercise import (
    ApprovalStatus,
    ChangeReason,
    DifficultyLevel,
    Equipment,
    Exercise,
    ExerciseSearchFilters,
    MuscleGroup,
    MovementPattern,
)
from app.infrastructure.database.models.exercise_model import ExerciseModel
from app.infrastructure.database.repositories.exercise_repository_impl import ExerciseRepositoryImpl


@pytest.mark.unit
@pytest.mark.infrastructure
class TestExerciseRepositoryImpl:
    """Test exercise repository implementation."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def exercise_repository(self, mock_session):
        """Create exercise repository with mock session."""
        return ExerciseRepositoryImpl(mock_session)
    
    @pytest.fixture
    def sample_exercise_model(self):
        """Create sample exercise model."""
        now = datetime.utcnow()
        return ExerciseModel(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST.value,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS.value, MuscleGroup.TRICEPS.value],
            movement_pattern=MovementPattern.PUSH.value,
            equipment_required=[Equipment.BARBELL.value, Equipment.MACHINE.value],
            difficulty_level=DifficultyLevel.INTERMEDIATE.value,
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED.value,
            is_active=True,
            is_approved=True,
            change_reason=ChangeReason.INITIAL_CREATION.value,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )

    @pytest.fixture
    def sample_exercise_entity(self):
        """Create sample exercise entity."""
        now = datetime.utcnow()
        return Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS],
            movement_pattern=MovementPattern.PUSH,
            equipment_required=[Equipment.BARBELL, Equipment.MACHINE],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED,
            is_active=True,
            is_approved=True,
            change_reason=ChangeReason.INITIAL_CREATION,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_exercise(self, exercise_repository, mock_session):
        """Test exercise creation."""
        # Create a simple exercise entity without enum conversion issues
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            version=1,
            is_current_version=True,
            name="Test Exercise",
            description="Test description",
            primary_muscle_group="chest",  # Use string directly
            secondary_muscle_groups=["shoulders"],
            movement_pattern="push",
            equipment_required=["barbell"],
            difficulty_level="intermediate",
            execution_steps=["Step 1"],
            approval_status="pending",
            is_active=True,
            is_approved=False,
            change_reason="initial_creation",
            created_by=uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the audit logging
        with patch.object(exercise_repository, 'log_audit') as mock_audit:
            mock_audit.return_value = None

            result = await exercise_repository.create(exercise)

            assert result == exercise
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
            mock_audit.assert_called_once()

    async def test_get_by_id_found(self, exercise_repository, mock_session, sample_exercise_model):
        """Test getting exercise by ID when found."""
        exercise_id = sample_exercise_model.id

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_id(exercise_id)

        assert isinstance(result, Exercise)
        assert result.id == exercise_id
        mock_session.execute.assert_called_once()

    async def test_get_by_id_not_found(self, exercise_repository, mock_session):
        """Test getting exercise by ID when not found."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_id(exercise_id)

        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_get_by_uuid_found(self, exercise_repository, mock_session, sample_exercise_model):
        """Test getting exercise by UUID when found."""
        exercise_uuid = sample_exercise_model.exercise_uuid

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_uuid(exercise_uuid)

        assert isinstance(result, Exercise)
        assert result.exercise_uuid == exercise_uuid
        mock_session.execute.assert_called_once()

    async def test_get_by_uuid_not_found(self, exercise_repository, mock_session):
        """Test getting exercise by UUID when not found."""
        exercise_uuid = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_by_uuid(exercise_uuid)

        assert result is None
        mock_session.execute.assert_called_once()

    async def test_get_versions(self, exercise_repository, mock_session, sample_exercise_model):
        """Test getting all versions of an exercise."""
        exercise_uuid = sample_exercise_model.exercise_uuid

        # Mock query result with multiple versions
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model, sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.get_versions(exercise_uuid)

        assert len(result) == 2
        assert all(isinstance(ex, Exercise) for ex in result)
        mock_session.execute.assert_called_once()
    
    async def test_search_exercises(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise search."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters)

        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()

    async def test_search_exercises_with_pagination(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise search with pagination."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.search(filters, limit=20, offset=10)

        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()

    async def test_count_exercises(self, exercise_repository, mock_session):
        """Test counting exercises."""
        filters = ExerciseSearchFilters(primary_muscle_group=MuscleGroup.CHEST)

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 5
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.count(filters)

        assert result == 5
        mock_session.execute.assert_called_once()
    
    async def test_update_exercise(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test exercise update."""
        # Mock get_by_id to return existing exercise
        with patch.object(exercise_repository, 'get_by_id') as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock create_version method
            with patch.object(exercise_repository, 'create_version') as mock_create_version:
                mock_create_version.return_value = sample_exercise_entity

                result = await exercise_repository.update(sample_exercise_entity)

                assert result == sample_exercise_entity
                mock_get.assert_called_once_with(sample_exercise_entity.id)
                mock_create_version.assert_called_once()

    async def test_update_exercise_not_found(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test exercise update when not found."""
        # Mock get_by_id to return None
        with patch.object(exercise_repository, 'get_by_id') as mock_get:
            mock_get.return_value = None

            with pytest.raises(ValueError, match="not found"):
                await exercise_repository.update(sample_exercise_entity)
    
    async def test_soft_delete_exercise(self, exercise_repository, mock_session):
        """Test exercise soft deletion."""
        exercise_id = uuid4()
        deleted_by = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock audit logging
        with patch.object(exercise_repository, 'log_audit') as mock_audit:
            mock_audit.return_value = None

            result = await exercise_repository.soft_delete(exercise_id, deleted_by)

            assert result is True
            mock_session.execute.assert_called_once()
            mock_audit.assert_called_once()

    async def test_soft_delete_exercise_not_found(self, exercise_repository, mock_session):
        """Test exercise soft deletion when not found."""
        exercise_id = uuid4()
        deleted_by = uuid4()

        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.soft_delete(exercise_id, deleted_by)

        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_approve_exercise(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test exercise approval."""
        exercise_id = sample_exercise_entity.id
        approved_by = uuid4()
        notes = "Looks good"

        # Mock get_by_id to return exercise
        with patch.object(exercise_repository, 'get_by_id') as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock update result
            mock_result = Mock()
            mock_result.rowcount = 1
            mock_session.execute.return_value = mock_result

            # Mock audit logging
            with patch.object(exercise_repository, 'log_audit') as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.approve(exercise_id, approved_by, notes)

                assert result == sample_exercise_entity
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()

    async def test_reject_exercise(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test exercise rejection."""
        exercise_id = sample_exercise_entity.id
        rejected_by = uuid4()
        notes = "Needs improvement"

        # Mock get_by_id to return exercise
        with patch.object(exercise_repository, 'get_by_id') as mock_get:
            mock_get.return_value = sample_exercise_entity

            # Mock update result
            mock_result = Mock()
            mock_result.rowcount = 1
            mock_session.execute.return_value = mock_result

            # Mock audit logging
            with patch.object(exercise_repository, 'log_audit') as mock_audit:
                mock_audit.return_value = None

                result = await exercise_repository.reject(exercise_id, rejected_by, notes)

                assert result == sample_exercise_entity
                mock_session.execute.assert_called_once()
                mock_audit.assert_called_once()
    
    async def test_exists_exercise(self, exercise_repository, mock_session):
        """Test checking if exercise exists."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists(exercise_id)

        assert result is True
        mock_session.execute.assert_called_once()

    async def test_not_exists_exercise(self, exercise_repository, mock_session):
        """Test checking if exercise doesn't exist."""
        exercise_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists(exercise_id)

        assert result is False
        mock_session.execute.assert_called_once()

    async def test_exists_by_name(self, exercise_repository, mock_session):
        """Test checking if exercise exists by name."""
        name = "Bench Press"

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists_by_name(name)

        assert result is True
        mock_session.execute.assert_called_once()

    async def test_exists_by_name_with_exclusion(self, exercise_repository, mock_session):
        """Test checking if exercise exists by name with exclusion."""
        name = "Bench Press"
        exclude_id = uuid4()

        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result

        result = await exercise_repository.exists_by_name(name, exclude_id)

        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_create_version(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test creating a new version of an exercise."""
        exercise_uuid = sample_exercise_entity.exercise_uuid
        created_by = uuid4()

        # Mock get_current_version
        with patch.object(exercise_repository, 'get_current_version') as mock_get_current:
            mock_get_current.return_value = sample_exercise_entity

            # Mock create method
            with patch.object(exercise_repository, 'create') as mock_create:
                mock_create.return_value = sample_exercise_entity

                result = await exercise_repository.create_version(
                    exercise_uuid, sample_exercise_entity, created_by
                )

                assert result == sample_exercise_entity
                mock_get_current.assert_called_once_with(exercise_uuid)
                mock_create.assert_called_once()

    async def test_get_current_version(self, exercise_repository, mock_session, sample_exercise_entity):
        """Test getting current version of an exercise."""
        exercise_uuid = sample_exercise_entity.exercise_uuid

        # Mock get_by_uuid
        with patch.object(exercise_repository, 'get_by_uuid') as mock_get:
            mock_get.return_value = sample_exercise_entity

            result = await exercise_repository.get_current_version(exercise_uuid)

            assert result == sample_exercise_entity
            mock_get.assert_called_once_with(exercise_uuid, current_only=True)

    async def test_set_current_version(self, exercise_repository, mock_session):
        """Test setting current version of an exercise."""
        exercise_uuid = uuid4()
        version = 2
        updated_by = uuid4()

        # Mock update results
        mock_result1 = Mock()
        mock_result1.rowcount = 1
        mock_result2 = Mock()
        mock_result2.rowcount = 1
        mock_session.execute.side_effect = [mock_result1, mock_result2]

        result = await exercise_repository.set_current_version(exercise_uuid, version, updated_by)

        assert result is True
        assert mock_session.execute.call_count == 2
    
    async def test_restore_exercise(self, exercise_repository, mock_session):
        """Test restoring a soft-deleted exercise."""
        exercise_id = uuid4()
        restored_by = uuid4()

        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result

        # Mock audit logging
        with patch.object(exercise_repository, 'log_audit') as mock_audit:
            mock_audit.return_value = None

            result = await exercise_repository.restore(exercise_id, restored_by)

            assert result is True
            mock_session.execute.assert_called_once()
            mock_audit.assert_called_once()

    def test_model_to_entity_conversion(self, exercise_repository, sample_exercise_model):
        """Test exercise model to entity conversion."""
        result = exercise_repository._model_to_entity(sample_exercise_model)

        assert isinstance(result, Exercise)
        assert result.id == sample_exercise_model.id
        assert result.name == sample_exercise_model.name
        assert result.primary_muscle_group == MuscleGroup.CHEST
        assert MuscleGroup.SHOULDERS in result.secondary_muscle_groups
        assert result.approval_status == ApprovalStatus.APPROVED
        assert result.movement_pattern == MovementPattern.PUSH
        assert Equipment.BARBELL in result.equipment_required
        assert result.difficulty_level == DifficultyLevel.INTERMEDIATE

    def test_entity_to_dict_conversion(self, exercise_repository, sample_exercise_entity):
        """Test exercise entity to dict conversion."""
        result = exercise_repository._entity_to_dict(sample_exercise_entity)

        assert isinstance(result, dict)
        assert result["name"] == sample_exercise_entity.name
        assert result["description"] == sample_exercise_entity.description
        assert result["primary_muscle_group"] == sample_exercise_entity.primary_muscle_group.value
        assert result["approval_status"] == sample_exercise_entity.approval_status.value
