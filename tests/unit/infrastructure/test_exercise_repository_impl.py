"""
Unit tests for exercise repository implementation.

Tests for SQLAlchemy exercise repository implementation including
CRUD operations, search, approval workflows, and statistics.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.entities.exercise import (
    ApprovalStatus,
    DifficultyLevel,
    Exercise,
    ExerciseCreateRequest,
    ExerciseSearchFilters,
    ExerciseUpdateRequest,
    MuscleGroup,
)
from app.infrastructure.database.models.exercise_model import ExerciseModel
from app.infrastructure.database.repositories.exercise_repository_impl import ExerciseRepositoryImpl


@pytest.mark.unit
@pytest.mark.infrastructure
class TestExerciseRepositoryImpl:
    """Test exercise repository implementation."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def exercise_repository(self, mock_session):
        """Create exercise repository with mock session."""
        return ExerciseRepositoryImpl(mock_session)
    
    @pytest.fixture
    def sample_exercise_model(self):
        """Create sample exercise model."""
        now = datetime.utcnow()
        return ExerciseModel(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST.value,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS.value, MuscleGroup.TRICEPS.value],
            movement_pattern="push",
            equipment_required=["Barbell", "Bench"],
            difficulty_level="intermediate",
            execution_steps=["Lie on bench", "Lower bar to chest", "Press up"],
            approval_status=ApprovalStatus.APPROVED.value,
            created_by=uuid4(),
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_exercise(self, exercise_repository, mock_session):
        """Test exercise creation."""
        exercise = Exercise(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS],
            equipment_required=["Barbell"],
            difficulty_level=DifficultyLevel.INTERMEDIATE,
            execution_steps=["Lie on bench"],
            approval_status=ApprovalStatus.PENDING,
            created_by=uuid4(),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the _model_to_entity method
        with patch.object(exercise_repository, '_model_to_entity') as mock_convert:
            mock_convert.return_value = exercise

            result = await exercise_repository.create(exercise)

            assert result == exercise
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
    
    async def test_get_exercise_by_id_found(self, exercise_repository, mock_session, sample_exercise_model):
        """Test getting exercise by ID when found."""
        exercise_id = sample_exercise_model.id
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_model
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.get_exercise_by_id(exercise_id)
        
        assert isinstance(result, Exercise)
        assert result.id == exercise_id
        mock_session.execute.assert_called_once()
    
    async def test_get_exercise_by_id_not_found(self, exercise_repository, mock_session):
        """Test getting exercise by ID when not found."""
        exercise_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.get_exercise_by_id(exercise_id)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_update_exercise(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise update."""
        exercise_id = sample_exercise_model.id
        updated_by = uuid4()
        request = ExerciseUpdateRequest(name="Updated Exercise")
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_exercise_by_id for return value
        with patch.object(exercise_repository, 'get_exercise_by_id') as mock_get:
            mock_exercise = Exercise(
                id=exercise_id,
                name="Updated Exercise",
                description="Test description",
                primary_muscle_group=MuscleGroup.CHEST,
                secondary_muscle_groups=[],
                equipment_needed=[],
                difficulty_level=1,
                instructions=[],
                approval_status=ApprovalStatus.PENDING,
                created_by=uuid4(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_exercise
            
            result = await exercise_repository.update_exercise(exercise_id, request, updated_by)
            
            assert result == mock_exercise
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(exercise_id)
    
    async def test_update_exercise_not_found(self, exercise_repository, mock_session):
        """Test exercise update when not found."""
        exercise_id = uuid4()
        updated_by = uuid4()
        request = ExerciseUpdateRequest(name="Updated Exercise")
        
        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.update_exercise(exercise_id, request, updated_by)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_delete_exercise(self, exercise_repository, mock_session):
        """Test exercise deletion."""
        exercise_id = uuid4()
        deleted_by = uuid4()
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.delete_exercise(exercise_id, deleted_by)
        
        assert result is True
        mock_session.execute.assert_called_once()
    
    async def test_delete_exercise_not_found(self, exercise_repository, mock_session):
        """Test exercise deletion when not found."""
        exercise_id = uuid4()
        deleted_by = uuid4()
        
        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.delete_exercise(exercise_id, deleted_by)
        
        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_search_exercises(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise search."""
        filters = ExerciseSearchFilters(muscle_group=MuscleGroup.CHEST)
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.search_exercises(filters)
        
        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()
    
    async def test_search_exercises_with_filters(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise search with multiple filters."""
        filters = ExerciseSearchFilters(
            muscle_group=MuscleGroup.CHEST,
            approval_status=ApprovalStatus.APPROVED,
            difficulty_level=3,
            name_contains="bench",
            created_by=uuid4(),
        )
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_model]
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.search_exercises(filters, limit=20, offset=10)
        
        assert len(result) == 1
        assert isinstance(result[0], Exercise)
        mock_session.execute.assert_called_once()
    
    async def test_approve_exercise(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise approval."""
        exercise_id = sample_exercise_model.id
        approved_by = uuid4()
        notes = "Looks good"
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_exercise_by_id for return value
        with patch.object(exercise_repository, 'get_exercise_by_id') as mock_get:
            mock_exercise = Exercise(
                id=exercise_id,
                name="Test Exercise",
                description="Test description",
                primary_muscle_group=MuscleGroup.CHEST,
                secondary_muscle_groups=[],
                equipment_needed=[],
                difficulty_level=1,
                instructions=[],
                approval_status=ApprovalStatus.APPROVED,
                created_by=uuid4(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_exercise
            
            result = await exercise_repository.approve_exercise(exercise_id, approved_by, notes)
            
            assert result == mock_exercise
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(exercise_id)
    
    async def test_reject_exercise(self, exercise_repository, mock_session, sample_exercise_model):
        """Test exercise rejection."""
        exercise_id = sample_exercise_model.id
        rejected_by = uuid4()
        reason = "Needs more detail"
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_exercise_by_id for return value
        with patch.object(exercise_repository, 'get_exercise_by_id') as mock_get:
            mock_exercise = Exercise(
                id=exercise_id,
                name="Test Exercise",
                description="Test description",
                primary_muscle_group=MuscleGroup.CHEST,
                secondary_muscle_groups=[],
                equipment_needed=[],
                difficulty_level=1,
                instructions=[],
                approval_status=ApprovalStatus.REJECTED,
                created_by=uuid4(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_exercise
            
            result = await exercise_repository.reject_exercise(exercise_id, rejected_by, reason)
            
            assert result == mock_exercise
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(exercise_id)
    
    async def test_exists(self, exercise_repository, mock_session):
        """Test checking if exercise exists."""
        exercise_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 1
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.exists(exercise_id)
        
        assert result is True
        mock_session.execute.assert_called_once()
    
    async def test_not_exists(self, exercise_repository, mock_session):
        """Test checking if exercise doesn't exist."""
        exercise_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 0
        mock_session.execute.return_value = mock_result
        
        result = await exercise_repository.exists(exercise_id)
        
        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_model_to_entity_conversion(self, exercise_repository, sample_exercise_model):
        """Test exercise model to entity conversion."""
        result = exercise_repository._model_to_entity(sample_exercise_model)
        
        assert isinstance(result, Exercise)
        assert result.id == sample_exercise_model.id
        assert result.name == sample_exercise_model.name
        assert result.primary_muscle_group == MuscleGroup.CHEST
        assert MuscleGroup.SHOULDERS in result.secondary_muscle_groups
        assert result.approval_status == ApprovalStatus.APPROVED
