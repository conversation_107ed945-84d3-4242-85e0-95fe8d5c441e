"""
Unit tests for database models.

Tests for SQLAlchemy models including validation,
relationships, and constraints.
"""

import pytest
from datetime import datetime
from uuid import uuid4

from app.domain.entities.exercise import MuscleGroup
from app.infrastructure.database.models.exercise_model import ExerciseModel, ExerciseMediaModel
from app.infrastructure.database.models.user_model import UserModel
from app.infrastructure.database.models.workout_model import WorkoutModel, ExerciseSetModel


@pytest.mark.unit
@pytest.mark.infrastructure
class TestDatabaseModels:
    """Test database model creation and validation."""
    
    def test_user_model_creation(self):
        """Test user model creation."""
        user_id = uuid4()
        now = datetime.utcnow()
        
        user = UserModel(
            id=user_id,
            email="<EMAIL>",
            username="testuser",
            first_name="Test",
            last_name="User",
            is_active=True,
            is_verified=False,
            created_at=now,
            updated_at=now,
        )
        
        assert user.id == user_id
        assert user.email == "<EMAIL>"
        assert user.username == "testuser"
        assert user.full_name == "Test User"
        assert user.is_active is True
        assert user.is_verified is False
    
    def test_user_model_repr(self):
        """Test user model string representation."""
        user = UserModel(
            id=uuid4(),
            email="<EMAIL>",
            username="testuser",
        )
        
        repr_str = repr(user)
        assert "UserModel" in repr_str
        assert "testuser" in repr_str
        assert "<EMAIL>" in repr_str
    
    def test_exercise_model_creation(self):
        """Test exercise model creation."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_by = uuid4()
        now = datetime.utcnow()
        
        exercise = ExerciseModel(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            name="Bench Press",
            description="Chest exercise",
            primary_muscle_group=MuscleGroup.CHEST.value,
            secondary_muscle_groups=[MuscleGroup.SHOULDERS.value],
            movement_pattern="push",
            equipment_required=["Barbell", "Bench"],
            difficulty_level="intermediate",
            execution_steps=["Lie on bench", "Lower bar", "Press up"],
            approval_status="approved",
            created_by=created_by,
            created_at=now,
            updated_at=now,
        )
        
        assert exercise.id == exercise_id
        assert exercise.exercise_uuid == exercise_uuid
        assert exercise.name == "Bench Press"
        assert exercise.primary_muscle_group == MuscleGroup.CHEST.value
        assert MuscleGroup.SHOULDERS.value in exercise.secondary_muscle_groups
        assert exercise.movement_pattern == "push"
        assert "Barbell" in exercise.equipment_required
        assert exercise.difficulty_level == "intermediate"
        assert exercise.approval_status == "approved"
        assert exercise.created_by == created_by
    
    def test_exercise_model_repr(self):
        """Test exercise model string representation."""
        exercise = ExerciseModel(
            id=uuid4(),
            name="Bench Press",
            version=1,
        )
        
        repr_str = repr(exercise)
        assert "ExerciseModel" in repr_str
        assert "Bench Press" in repr_str
        assert "version=1" in repr_str
    
    def test_exercise_media_model_creation(self):
        """Test exercise media model creation."""
        media_id = uuid4()
        exercise_id = uuid4()
        now = datetime.utcnow()
        
        media = ExerciseMediaModel(
            id=media_id,
            exercise_id=exercise_id,
            media_type="video",
            url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            title="Exercise Demo",
            description="Demonstration video",
            file_size_bytes=1024000,
            duration_seconds=60,
            width_pixels=1920,
            height_pixels=1080,
            mime_type="video/mp4",
            sort_order=1,
            is_primary=True,
            is_active=True,
            created_at=now,
            updated_at=now,
        )
        
        assert media.id == media_id
        assert media.exercise_id == exercise_id
        assert media.media_type == "video"
        assert media.url == "https://example.com/video.mp4"
        assert media.title == "Exercise Demo"
        assert media.file_size_bytes == 1024000
        assert media.duration_seconds == 60
        assert media.is_primary is True
        assert media.is_active is True
    
    def test_exercise_media_model_repr(self):
        """Test exercise media model string representation."""
        media = ExerciseMediaModel(
            id=uuid4(),
            exercise_id=uuid4(),
            media_type="video",
        )
        
        repr_str = repr(media)
        assert "ExerciseMediaModel" in repr_str
        assert "type=video" in repr_str
    
    def test_workout_model_creation(self):
        """Test workout model creation."""
        workout_id = uuid4()
        user_id = uuid4()
        now = datetime.utcnow()
        
        workout = WorkoutModel(
            id=workout_id,
            user_id=user_id,
            name="Push Day",
            workout_date=now,
            started_at=now,
            completed_at=None,
            total_volume_load=1500.0,
            average_rpe=7.5,
            total_sets=12,
            total_exercises=4,
            duration_minutes=60,
            notes="Good workout",
            is_template=False,
            created_at=now,
            updated_at=now,
        )
        
        assert workout.id == workout_id
        assert workout.user_id == user_id
        assert workout.name == "Push Day"
        assert workout.total_volume_load == 1500.0
        assert workout.average_rpe == 7.5
        assert workout.total_sets == 12
        assert workout.total_exercises == 4
        assert workout.duration_minutes == 60
        assert workout.notes == "Good workout"
        assert workout.is_template is False
    
    def test_workout_model_repr(self):
        """Test workout model string representation."""
        workout = WorkoutModel(
            id=uuid4(),
            name="Push Day",
            user_id=uuid4(),
        )
        
        repr_str = repr(workout)
        assert "WorkoutModel" in repr_str
        assert "Push Day" in repr_str
    
    def test_exercise_set_model_creation(self):
        """Test exercise set model creation."""
        set_id = uuid4()
        workout_id = uuid4()
        exercise_id = uuid4()
        now = datetime.utcnow()
        
        exercise_set = ExerciseSetModel(
            id=set_id,
            workout_id=workout_id,
            exercise_id=exercise_id,
            set_number=1,
            weight_kg=100.0,
            reps_completed=10,
            reps_target=10,
            rir_target=2,
            rir_actual=1,
            rpe=8.0,
            rest_seconds=120,
            tempo="3-1-2-1",
            range_of_motion="full",
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            notes="Good set",
            created_at=now,
            updated_at=now,
        )
        
        assert exercise_set.id == set_id
        assert exercise_set.workout_id == workout_id
        assert exercise_set.exercise_id == exercise_id
        assert exercise_set.set_number == 1
        assert exercise_set.weight_kg == 100.0
        assert exercise_set.reps_completed == 10
        assert exercise_set.reps_target == 10
        assert exercise_set.rir_target == 2
        assert exercise_set.rir_actual == 1
        assert exercise_set.rpe == 8.0
        assert exercise_set.rest_seconds == 120
        assert exercise_set.tempo == "3-1-2-1"
        assert exercise_set.range_of_motion == "full"
        assert exercise_set.is_warmup is False
        assert exercise_set.is_dropset is False
        assert exercise_set.is_failure is False
        assert exercise_set.notes == "Good set"
    
    def test_exercise_set_model_repr(self):
        """Test exercise set model string representation."""
        exercise_set = ExerciseSetModel(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=100.0,
            reps_completed=10,
        )
        
        repr_str = repr(exercise_set)
        assert "ExerciseSetModel" in repr_str
        assert "set_number=1" in repr_str
        assert "100.0" in repr_str
        assert "reps=10" in repr_str
    
    def test_model_defaults(self):
        """Test model default values."""
        # Test user model defaults
        user = UserModel(email="<EMAIL>", username="test")
        assert user.is_active is True
        assert user.is_verified is False
        assert user.is_superuser is False
        
        # Test exercise model defaults
        exercise = ExerciseModel(
            exercise_uuid=uuid4(),
            name="Test",
            primary_muscle_group="chest",
            movement_pattern="push",
            difficulty_level="beginner",
        )
        assert exercise.version == 1
        assert exercise.is_current_version is True
        assert exercise.is_active is True
        assert exercise.is_approved is False
        assert exercise.approval_status == "pending"
        
        # Test workout model defaults
        workout = WorkoutModel(
            user_id=uuid4(),
            name="Test Workout",
            workout_date=datetime.utcnow(),
        )
        assert workout.is_template is False
        
        # Test exercise set model defaults
        exercise_set = ExerciseSetModel(
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
        )
        assert exercise_set.is_warmup is False
        assert exercise_set.is_dropset is False
        assert exercise_set.is_failure is False
    
    def test_model_constraints(self):
        """Test model constraints and validation."""
        # Test that required fields are enforced (this would be caught by SQLAlchemy)
        # In a real test with a database, these would raise IntegrityError
        
        # Test enum constraints
        exercise = ExerciseModel(
            exercise_uuid=uuid4(),
            name="Test",
            primary_muscle_group="chest",  # Valid enum value
            movement_pattern="push",       # Valid enum value
            difficulty_level="beginner",   # Valid enum value
            approval_status="pending",     # Valid enum value
        )
        
        # These should be valid
        assert exercise.primary_muscle_group == "chest"
        assert exercise.movement_pattern == "push"
        assert exercise.difficulty_level == "beginner"
        assert exercise.approval_status == "pending"
