"""
Unit tests for JWT handler.

Tests JWT token generation, validation, and security features.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
import jwt

from app.infrastructure.auth.jwt_handler import <PERSON><PERSON><PERSON>and<PERSON>
from app.domain.exceptions.auth_exceptions import (
    TokenExpiredError,
    InvalidTokenError
)
from app.config import settings


@pytest.mark.unit
@pytest.mark.auth
class TestJWTHandler:
    """Test JWT token handling functionality."""
    
    @pytest.fixture
    def jwt_handler(self) -> J<PERSON>THandler:
        """Create JWT handler instance."""
        return JWTHandler()
    
    @pytest.fixture
    def sample_user_id(self) -> str:
        """Sample user ID for testing."""
        return str(uuid4())
    
    async def test_generate_access_token(
        self, 
        jwt_handler: J<PERSON>THand<PERSON>, 
        sample_user_id: str
    ) -> None:
        """Test access token generation."""
        token = await jwt_handler.generate_access_token(sample_user_id)
        
        # Verify token structure
        assert isinstance(token, str)
        assert len(token.split('.')) == 3  # JWT has 3 parts
        
        # Decode and verify payload
        payload = jwt_handler.decode_token(token)
        assert payload["sub"] == sample_user_id
        assert payload["type"] == "access"
        assert "exp" in payload
        assert "iat" in payload
    
    async def test_generate_refresh_token(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test refresh token generation."""
        token = await jwt_handler.generate_refresh_token(sample_user_id)
        
        # Verify token structure
        assert isinstance(token, str)
        assert len(token.split('.')) == 3
        
        # Decode and verify payload
        payload = jwt_handler.decode_token(token)
        assert payload["sub"] == sample_user_id
        assert payload["type"] == "refresh"
        assert "exp" in payload
        assert "iat" in payload
    
    async def test_tokens_are_different(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test that access and refresh tokens are different."""
        access_token = await jwt_handler.generate_access_token(sample_user_id)
        refresh_token = await jwt_handler.generate_refresh_token(sample_user_id)
        
        assert access_token != refresh_token
        
        # Verify different types
        access_payload = jwt_handler.decode_token(access_token)
        refresh_payload = jwt_handler.decode_token(refresh_token)
        
        assert access_payload["type"] == "access"
        assert refresh_payload["type"] == "refresh"
    
    async def test_token_expiry_times(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test that tokens have correct expiry times."""
        access_token = await jwt_handler.generate_access_token(sample_user_id)
        refresh_token = await jwt_handler.generate_refresh_token(sample_user_id)
        
        access_payload = jwt_handler.decode_token(access_token)
        refresh_payload = jwt_handler.decode_token(refresh_token)
        
        access_exp = datetime.fromtimestamp(access_payload["exp"])
        refresh_exp = datetime.fromtimestamp(refresh_payload["exp"])
        
        # Refresh token should expire much later than access token
        assert refresh_exp > access_exp
        
        # Verify approximate expiry times (with some tolerance)
        now = datetime.utcnow()
        expected_access_exp = now + timedelta(minutes=settings.access_token_expire_minutes)
        expected_refresh_exp = now + timedelta(days=settings.refresh_token_expire_days)
        
        # Allow 1 minute tolerance
        assert abs((access_exp - expected_access_exp).total_seconds()) < 60
        assert abs((refresh_exp - expected_refresh_exp).total_seconds()) < 60
    
    def test_decode_valid_token(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test decoding a valid token."""
        # Create a valid token manually
        payload = {
            "sub": str(uuid4()),
            "type": "access",
            "exp": datetime.utcnow() + timedelta(minutes=30),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, settings.secret_key, algorithm=settings.algorithm)
        
        # Decode using handler
        decoded = jwt_handler.decode_token(token)
        
        assert decoded["sub"] == payload["sub"]
        assert decoded["type"] == payload["type"]
    
    def test_decode_expired_token(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test decoding an expired token."""
        # Create an expired token
        payload = {
            "sub": str(uuid4()),
            "type": "access",
            "exp": datetime.utcnow() - timedelta(hours=1),  # Expired 1 hour ago
            "iat": datetime.utcnow() - timedelta(hours=2)
        }
        
        token = jwt.encode(payload, settings.secret_key, algorithm=settings.algorithm)
        
        # Should raise TokenExpiredError
        with pytest.raises(TokenExpiredError) as exc_info:
            jwt_handler.decode_token(token)
        
        assert "expired" in str(exc_info.value).lower()
    
    def test_decode_invalid_token(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test decoding invalid tokens."""
        invalid_tokens = [
            "invalid.token.here",
            "not.a.jwt",
            "",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
        ]
        
        for invalid_token in invalid_tokens:
            with pytest.raises(InvalidTokenError):
                jwt_handler.decode_token(invalid_token)
    
    def test_decode_token_wrong_secret(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test decoding token with wrong secret."""
        # Create token with different secret
        payload = {
            "sub": str(uuid4()),
            "type": "access",
            "exp": datetime.utcnow() + timedelta(minutes=30),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, "wrong_secret", algorithm=settings.algorithm)
        
        with pytest.raises(InvalidTokenError):
            jwt_handler.decode_token(token)
    
    async def test_get_user_id_from_token(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test extracting user ID from token."""
        token = await jwt_handler.generate_access_token(sample_user_id)
        
        extracted_id = jwt_handler.get_user_id_from_token(token)
        
        assert str(extracted_id) == sample_user_id
    
    def test_get_user_id_from_invalid_token(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test extracting user ID from invalid token."""
        with pytest.raises(InvalidTokenError):
            jwt_handler.get_user_id_from_token("invalid.token")
    
    def test_get_user_id_from_token_without_sub(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test extracting user ID from token without sub claim."""
        # Create token without sub claim
        payload = {
            "type": "access",
            "exp": datetime.utcnow() + timedelta(minutes=30),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, settings.secret_key, algorithm=settings.algorithm)
        
        with pytest.raises(InvalidTokenError) as exc_info:
            jwt_handler.get_user_id_from_token(token)
        
        assert "user ID" in str(exc_info.value)
    
    async def test_get_token_type(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test getting token type."""
        access_token = await jwt_handler.generate_access_token(sample_user_id)
        refresh_token = await jwt_handler.generate_refresh_token(sample_user_id)
        
        assert jwt_handler.get_token_type(access_token) == "access"
        assert jwt_handler.get_token_type(refresh_token) == "refresh"
    
    def test_get_token_type_missing(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test getting token type from token without type claim."""
        # Create token without type claim
        payload = {
            "sub": str(uuid4()),
            "exp": datetime.utcnow() + timedelta(minutes=30),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, settings.secret_key, algorithm=settings.algorithm)
        
        with pytest.raises(InvalidTokenError) as exc_info:
            jwt_handler.get_token_type(token)
        
        assert "type" in str(exc_info.value)
    
    async def test_is_token_expired(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test token expiry checking."""
        # Valid token
        valid_token = await jwt_handler.generate_access_token(sample_user_id)
        assert jwt_handler.is_token_expired(valid_token) is False
        
        # Expired token
        expired_payload = {
            "sub": sample_user_id,
            "type": "access",
            "exp": datetime.utcnow() - timedelta(hours=1),
            "iat": datetime.utcnow() - timedelta(hours=2)
        }
        
        expired_token = jwt.encode(expired_payload, settings.secret_key, algorithm=settings.algorithm)
        assert jwt_handler.is_token_expired(expired_token) is True
        
        # Invalid token
        assert jwt_handler.is_token_expired("invalid.token") is True
    
    async def test_get_token_expiry(
        self, 
        jwt_handler: JWTHandler, 
        sample_user_id: str
    ) -> None:
        """Test getting token expiry time."""
        token = await jwt_handler.generate_access_token(sample_user_id)
        
        expiry = jwt_handler.get_token_expiry(token)
        
        assert expiry is not None
        assert isinstance(expiry, datetime)
        assert expiry > datetime.utcnow()  # Should be in the future
    
    def test_get_token_expiry_invalid(
        self, 
        jwt_handler: JWTHandler
    ) -> None:
        """Test getting expiry from invalid token."""
        expiry = jwt_handler.get_token_expiry("invalid.token")
        assert expiry is None
