"""
Unit tests for UserRepositoryImpl.

Tests the database repository implementation with mocked database sessions.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4
from datetime import datetime

from app.infrastructure.database.repositories.user_repository_impl import UserRepositoryImpl
from app.infrastructure.database.models.user_model import UserModel
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserAlreadyExistsError, UserNotFoundError


@pytest.mark.unit
@pytest.mark.database
class TestUserRepositoryImpl:
    """Test UserRepositoryImpl functionality."""
    
    @pytest.fixture
    def mock_session(self) -> AsyncMock:
        """Mock database session."""
        return AsyncMock()
    
    @pytest.fixture
    def repository(self, mock_session: AsyncMock) -> UserRepositoryImpl:
        """Create repository instance with mocked session."""
        return UserRepositoryImpl(mock_session)
    
    @pytest.fixture
    def sample_user_entity(self) -> User:
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="<PERSON>",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=False
        )
    
    @pytest.fixture
    def sample_user_model(self) -> UserModel:
        """Sample user model."""
        return UserModel(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="John",
            last_name="Doe",
            training_experience_years=2,
            is_active=True,
            is_verified=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    async def test_create_success(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User
    ) -> None:
        """Test successful user creation."""
        # Mock exists_by_email to return False (user doesn't exist)
        with patch.object(repository, 'exists_by_email', return_value=False):
            # Setup session mocks
            mock_session.flush = AsyncMock()
            mock_session.refresh = AsyncMock()

            # Mock the created user model
            created_model = UserModel(
                id=sample_user_entity.id,
                email=sample_user_entity.email,
                hashed_password="hashed_SecurePass123!",
                first_name=sample_user_entity.first_name,
                last_name=sample_user_entity.last_name,
                training_experience_years=sample_user_entity.training_experience_years,
                is_active=sample_user_entity.is_active,
                is_verified=sample_user_entity.is_verified,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            # Mock session.refresh to update the model
            async def mock_refresh(model):
                model.created_at = created_model.created_at
                model.updated_at = created_model.updated_at

            mock_session.refresh.side_effect = mock_refresh

            # Execute
            result = await repository.create(sample_user_entity)

            # Verify
            assert isinstance(result, User)
            assert result.email == sample_user_entity.email
            assert result.first_name == sample_user_entity.first_name

            # Verify session calls
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
            mock_session.refresh.assert_called_once()
    
    async def test_create_user_already_exists(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User
    ) -> None:
        """Test user creation when user already exists."""
        # Mock exists_by_email to return True (user exists)
        with patch.object(repository, 'exists_by_email', return_value=True):
            # Execute & Verify
            with pytest.raises(UserAlreadyExistsError) as exc_info:
                await repository.create(sample_user_entity)

            assert sample_user_entity.email in str(exc_info.value)
            mock_session.add.assert_not_called()
    
    async def test_get_by_id_success(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_model: UserModel
    ) -> None:
        """Test successful user retrieval by ID."""
        # Setup
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_user_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(sample_user_model.id)

        # Verify
        assert isinstance(result, User)
        assert result.id == sample_user_model.id
        assert result.email == sample_user_model.email
        assert result.first_name == sample_user_model.first_name

        # Verify session call
        mock_session.execute.assert_called_once()
    
    async def test_get_by_id_not_found(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test user retrieval when user doesn't exist."""
        # Setup
        user_id = uuid4()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_id(user_id)

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_get_by_email_success(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_model: UserModel
    ) -> None:
        """Test successful user retrieval by email."""
        # Setup
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_user_model
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_email(sample_user_model.email)

        # Verify
        assert isinstance(result, User)
        assert result.email == sample_user_model.email

        # Verify session call
        mock_session.execute.assert_called_once()
    
    async def test_get_by_email_not_found(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test user retrieval by email when user doesn't exist."""
        # Setup
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Execute
        result = await repository.get_by_email("<EMAIL>")

        # Verify
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_update_success(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User,
        sample_user_model: UserModel
    ) -> None:
        """Test successful user update."""
        # Setup
        mock_session.execute.return_value.scalar_one_or_none.return_value = sample_user_model
        
        # Execute
        result = await repository.update(sample_user_entity)
        
        # Verify
        assert isinstance(result, User)
        assert result.id == sample_user_entity.id
        
        # Verify session call
        mock_session.execute.assert_called_once()
    
    async def test_update_user_not_found(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User
    ) -> None:
        """Test user update when user doesn't exist."""
        # Setup
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # Execute & Verify
        with pytest.raises(UserNotFoundError) as exc_info:
            await repository.update(sample_user_entity)
        
        assert str(sample_user_entity.id) in str(exc_info.value)
    
    async def test_delete_success(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test successful user soft delete."""
        # Setup
        user_id = uuid4()
        mock_result = MagicMock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.delete(user_id)
        
        # Verify
        assert result is True
        mock_session.execute.assert_called_once()
    
    async def test_delete_user_not_found(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test user delete when user doesn't exist."""
        # Setup
        user_id = uuid4()
        mock_result = MagicMock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.delete(user_id)
        
        # Verify
        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_exists_by_email_true(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test exists_by_email when user exists."""
        # Setup
        mock_session.execute.return_value.scalar_one_or_none.return_value = uuid4()
        
        # Execute
        result = await repository.exists_by_email("<EMAIL>")
        
        # Verify
        assert result is True
        mock_session.execute.assert_called_once()
    
    async def test_exists_by_email_false(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock
    ) -> None:
        """Test exists_by_email when user doesn't exist."""
        # Setup
        mock_session.execute.return_value.scalar_one_or_none.return_value = None
        
        # Execute
        result = await repository.exists_by_email("<EMAIL>")
        
        # Verify
        assert result is False
        mock_session.execute.assert_called_once()
    
    def test_model_to_entity_conversion(
        self,
        repository: UserRepositoryImpl,
        sample_user_model: UserModel
    ) -> None:
        """Test conversion from UserModel to User entity."""
        # Execute
        result = repository._model_to_entity(sample_user_model)
        
        # Verify
        assert isinstance(result, User)
        assert result.id == sample_user_model.id
        assert result.email == sample_user_model.email
        assert result.hashed_password == sample_user_model.hashed_password
        assert result.first_name == sample_user_model.first_name
        assert result.last_name == sample_user_model.last_name
        assert result.training_experience_years == sample_user_model.training_experience_years
        assert result.is_active == sample_user_model.is_active
        assert result.is_verified == sample_user_model.is_verified
        assert result.created_at == sample_user_model.created_at
        assert result.updated_at == sample_user_model.updated_at
        assert result.deleted_at == sample_user_model.deleted_at

    def test_entity_to_model_conversion(
        self,
        repository: UserRepositoryImpl,
        sample_user_entity: User
    ) -> None:
        """Test conversion from User entity to UserModel."""
        # Execute
        result = repository._entity_to_model(sample_user_entity)

        # Verify
        assert isinstance(result, UserModel)
        assert result.id == sample_user_entity.id
        assert result.email == sample_user_entity.email
        assert result.password_hash == sample_user_entity.password_hash
        assert result.first_name == sample_user_entity.first_name
        assert result.last_name == sample_user_entity.last_name
        assert result.training_experience_years == sample_user_entity.training_experience_years
        assert result.is_active == sample_user_entity.is_active
        assert result.is_verified == sample_user_entity.is_verified
        assert result.created_at == sample_user_entity.created_at
        assert result.updated_at == sample_user_entity.updated_at
        assert result.deleted_at == sample_user_entity.deleted_at

    async def test_create_with_integrity_error_handling(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User
    ) -> None:
        """Test create user with integrity error handling."""
        from sqlalchemy.exc import IntegrityError

        # Setup - email doesn't exist initially
        mock_session.execute.return_value.scalar_one_or_none.return_value = None

        # But commit raises integrity error
        mock_session.commit.side_effect = IntegrityError("", "", "")

        # Execute & Verify
        with pytest.raises(UserAlreadyExistsError):
            await repository.create(sample_user_entity)

    async def test_update_with_rowcount_check(
        self,
        repository: UserRepositoryImpl,
        mock_session: AsyncMock,
        sample_user_entity: User
    ) -> None:
        """Test update user with rowcount check."""
        # Setup - simulate no rows affected
        mock_result = MagicMock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result

        # Execute & Verify
        with pytest.raises(UserNotFoundError):
            await repository.update(sample_user_entity)
