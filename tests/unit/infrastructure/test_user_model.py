"""Tests for User database model."""

from datetime import datetime
from uuid import uuid4


from app.infrastructure.database.models.user_model import UserModel


class TestUserModel:
    """Test cases for UserModel."""

    def test_create_user_model_minimal(self):
        """Test creating UserModel with minimal data."""
        user_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        user = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed_password_here",
            is_active=True,
            is_verified=False,
            created_at=created_at,
            updated_at=updated_at,
        )

        assert user.id == user_id
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "hashed_password_here"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.first_name is None
        assert user.last_name is None
        assert user.training_experience_years is None
        assert user.deleted_at is None

    def test_create_user_model_full(self):
        """Test creating UserModel with full data."""
        user_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()
        deleted_at = datetime.now()

        user = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="secure_hash_123",
            first_name="John",
            last_name="Doe",
            is_active=True,
            is_verified=True,
            training_experience_years=5,
            created_at=created_at,
            updated_at=updated_at,
            deleted_at=deleted_at,
        )

        assert user.id == user_id
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "secure_hash_123"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.is_active is True
        assert user.is_verified is True
        assert user.training_experience_years == 5
        assert user.created_at == created_at
        assert user.updated_at == updated_at
        assert user.deleted_at == deleted_at

    def test_user_model_string_representation(self):
        """Test UserModel string representation."""
        user = UserModel(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        str_repr = str(user)
        assert "UserModel" in str_repr
        assert "<EMAIL>" in str_repr

    def test_user_model_repr(self):
        """Test UserModel repr."""
        user_id = uuid4()
        user = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        repr_str = repr(user)
        assert "UserModel" in repr_str
        assert str(user_id) in repr_str
        assert "<EMAIL>" in repr_str

    def test_user_model_table_name(self):
        """Test that UserModel has correct table name."""
        assert UserModel.__tablename__ == "users"

    def test_user_model_required_fields(self):
        """Test that UserModel has all required fields."""
        user = UserModel(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Test that all required fields are present
        assert hasattr(user, "id")
        assert hasattr(user, "email")
        assert hasattr(user, "hashed_password")
        assert hasattr(user, "is_active")
        assert hasattr(user, "is_verified")
        assert hasattr(user, "created_at")
        assert hasattr(user, "updated_at")

        # Test optional fields
        assert hasattr(user, "first_name")
        assert hasattr(user, "last_name")
        assert hasattr(user, "training_experience_years")
        assert hasattr(user, "deleted_at")

    def test_user_model_default_values(self):
        """Test UserModel default values."""
        user = UserModel(
            email="<EMAIL>",
            hashed_password="hash",
        )

        # Test default values
        assert user.is_active is True
        assert user.is_verified is False
        assert user.first_name is None
        assert user.last_name is None
        assert user.training_experience_years is None
        assert user.deleted_at is None

    def test_user_model_email_uniqueness_constraint(self):
        """Test that email field has unique constraint."""
        # This test verifies the model structure, not database behavior
        email_column = UserModel.__table__.columns["email"]
        assert email_column.unique is True

    def test_user_model_nullable_fields(self):
        """Test nullable field constraints."""
        # Required fields should not be nullable
        assert UserModel.__table__.columns["id"].nullable is False
        assert UserModel.__table__.columns["email"].nullable is False
        assert UserModel.__table__.columns["hashed_password"].nullable is False
        assert UserModel.__table__.columns["is_active"].nullable is False
        assert UserModel.__table__.columns["is_verified"].nullable is False
        assert UserModel.__table__.columns["created_at"].nullable is False
        assert UserModel.__table__.columns["updated_at"].nullable is False

        # Optional fields should be nullable
        assert UserModel.__table__.columns["first_name"].nullable is True
        assert UserModel.__table__.columns["last_name"].nullable is True
        assert UserModel.__table__.columns["training_experience_years"].nullable is True
        assert UserModel.__table__.columns["deleted_at"].nullable is True

    def test_user_model_column_types(self):
        """Test column types."""
        from sqlalchemy import Boolean, DateTime, Integer, String
        from sqlalchemy.dialects.postgresql import UUID

        # Test column types
        assert isinstance(UserModel.__table__.columns["id"].type, UUID)
        assert isinstance(UserModel.__table__.columns["email"].type, String)
        assert isinstance(UserModel.__table__.columns["hashed_password"].type, String)
        assert isinstance(UserModel.__table__.columns["first_name"].type, String)
        assert isinstance(UserModel.__table__.columns["last_name"].type, String)
        assert isinstance(UserModel.__table__.columns["is_active"].type, Boolean)
        assert isinstance(UserModel.__table__.columns["is_verified"].type, Boolean)
        assert isinstance(
            UserModel.__table__.columns["training_experience_years"].type, Integer
        )
        assert isinstance(UserModel.__table__.columns["created_at"].type, DateTime)
        assert isinstance(UserModel.__table__.columns["updated_at"].type, DateTime)
        assert isinstance(UserModel.__table__.columns["deleted_at"].type, DateTime)

    def test_user_model_string_length_constraints(self):
        """Test string field length constraints."""
        # Email should have reasonable length limit
        email_column = UserModel.__table__.columns["email"]
        assert email_column.type.length == 255

        # Name fields should have reasonable length limits
        first_name_column = UserModel.__table__.columns["first_name"]
        assert first_name_column.type.length == 100

        last_name_column = UserModel.__table__.columns["last_name"]
        assert last_name_column.type.length == 100

    def test_user_model_equality(self):
        """Test UserModel equality based on ID."""
        user_id = uuid4()
        user1 = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hash1",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        user2 = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hash2",
            is_active=False,
            is_verified=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        user3 = UserModel(
            id=uuid4(),
            email="<EMAIL>",
            hashed_password="hash3",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Users with same ID should be equal
        assert user1 == user2

        # Users with different IDs should not be equal
        assert user1 != user3
        assert user2 != user3

    def test_user_model_hash(self):
        """Test UserModel hash based on ID."""
        user_id = uuid4()
        user1 = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hash1",
            is_active=True,
            is_verified=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        user2 = UserModel(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hash2",
            is_active=False,
            is_verified=True,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        # Users with same ID should have same hash
        assert hash(user1) == hash(user2)

        # Test that users can be used in sets
        user_set = {user1, user2}
        assert len(user_set) == 1  # Should be treated as same user
