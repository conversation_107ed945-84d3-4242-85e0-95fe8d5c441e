"""Unit tests for password handler.

Tests password hashing, verification, and security features.
"""

import pytest

from app.infrastructure.auth.password_handler import Password<PERSON>andler


@pytest.mark.unit
@pytest.mark.auth
class TestPasswordHandler:
    """Test password handling functionality."""

    @pytest.fixture
    def password_handler(self) -> PasswordHandler:
        """Create password handler instance."""
        return PasswordHandler()

    def test_hash_password(self, password_handler: PasswordHandler) -> None:
        """Test password hashing."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Verify hash properties
        assert isinstance(hashed, str)
        assert len(hashed) > 50  # bcrypt hashes are long
        assert hashed != password  # Should not be plain text
        assert hashed.startswith("$2b$")  # bcrypt format

    def test_hash_password_different_results(
        self, password_handler: PasswordHandler
    ) -> None:
        """Test that hashing same password produces different results (salt)."""
        password = "TestPassword123!"

        hash1 = password_handler.hash_password(password)
        hash2 = password_handler.hash_password(password)

        # Should be different due to salt
        assert hash1 != hash2

    def test_verify_password_correct(self, password_handler: PasswordHandler) -> None:
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Verification should succeed
        assert password_handler.verify_password(password, hashed) is True

    def test_verify_password_incorrect(self, password_handler: PasswordHandler) -> None:
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = password_handler.hash_password(password)

        # Verification should fail
        assert password_handler.verify_password(wrong_password, hashed) is False

    def test_verify_password_empty(self, password_handler: PasswordHandler) -> None:
        """Test password verification with empty password."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Empty password should fail
        assert password_handler.verify_password("", hashed) is False

    def test_verify_password_case_sensitive(
        self, password_handler: PasswordHandler
    ) -> None:
        """Test that password verification is case sensitive."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Different case should fail
        assert password_handler.verify_password("testpassword123!", hashed) is False
        assert password_handler.verify_password("TESTPASSWORD123!", hashed) is False

    def test_verify_password_invalid_hash(
        self, password_handler: PasswordHandler
    ) -> None:
        """Test password verification with invalid hash."""
        password = "TestPassword123!"

        # Invalid hashes should not crash and should return False
        invalid_hashes = [
            "invalid_hash",
            "",
            "not_a_bcrypt_hash",
            "$2b$invalid",
        ]

        for invalid_hash in invalid_hashes:
            assert password_handler.verify_password(password, invalid_hash) is False

    def test_hash_special_characters(self, password_handler: PasswordHandler) -> None:
        """Test hashing passwords with special characters."""
        special_passwords = [
            "Pass@#$%^&*()123!",
            "Pässwörd123!",
            "密码123!",
            "🔒Password123!",
        ]

        for password in special_passwords:
            hashed = password_handler.hash_password(password)

            # Should hash successfully
            assert isinstance(hashed, str)
            assert len(hashed) > 50

            # Should verify correctly
            assert password_handler.verify_password(password, hashed) is True

    def test_hash_long_password(self, password_handler: PasswordHandler) -> None:
        """Test hashing very long passwords."""
        # Create a very long password
        long_password = "A" * 1000 + "123!"

        hashed = password_handler.hash_password(long_password)

        # Should handle long passwords
        assert isinstance(hashed, str)
        assert password_handler.verify_password(long_password, hashed) is True

    def test_needs_update_fresh_hash(self, password_handler: PasswordHandler) -> None:
        """Test needs_update with fresh hash."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Fresh hash should not need update
        assert password_handler.needs_update(hashed) is False

    def test_needs_update_old_hash(self, password_handler: PasswordHandler) -> None:
        """Test needs_update with old hash format."""
        # Create a hash with lower rounds (simulating old hash)
        from passlib.context import CryptContext

        old_context = CryptContext(schemes=["bcrypt"], bcrypt__rounds=4)
        old_hash = old_context.hash("TestPassword123!")

        # Should indicate update needed (current context uses 12 rounds)
        assert password_handler.needs_update(old_hash) is True

    def test_needs_update_invalid_hash(self, password_handler: PasswordHandler) -> None:
        """Test needs_update with invalid hash."""
        invalid_hashes = [
            "invalid_hash",
            "",
            "not_a_hash",
        ]

        for invalid_hash in invalid_hashes:
            # Should handle invalid hashes gracefully
            result = password_handler.needs_update(invalid_hash)
            assert isinstance(result, bool)

    def test_bcrypt_rounds(self, password_handler: PasswordHandler) -> None:
        """Test that bcrypt uses appropriate number of rounds."""
        password = "TestPassword123!"
        hashed = password_handler.hash_password(password)

        # Extract rounds from hash (bcrypt format: $2b$rounds$salt+hash)
        parts = hashed.split("$")
        rounds = int(parts[2])

        # Should use 12 rounds for security
        assert rounds == 12

    def test_thread_safety(self, password_handler: PasswordHandler) -> None:
        """Test that password handler is thread-safe."""
        import threading

        password = "TestPassword123!"
        results = []
        errors = []

        def hash_and_verify():
            try:
                hashed = password_handler.hash_password(password)
                verified = password_handler.verify_password(password, hashed)
                results.append(verified)
            except Exception as e:
                errors.append(e)

        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=hash_and_verify)
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # All operations should succeed
        assert len(errors) == 0
        assert len(results) == 10
        assert all(result is True for result in results)
