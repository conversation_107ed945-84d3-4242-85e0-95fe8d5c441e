"""
Unit tests for user repository implementation.

Tests the SQLAlchemy user repository with real database operations.
"""

import pytest
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession

from app.infrastructure.database.repositories.user_repository import UserRepository
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserNotFoundError, DuplicateEmailError
from tests.factories import UserFactory


@pytest.mark.unit
@pytest.mark.database
@pytest.mark.real
class TestUserRepository:
    """Test cases for user repository implementation."""

    @pytest.fixture
    async def repository(self, test_db_session: AsyncSession) -> UserRepository:
        """Create user repository with test database session."""
        return UserRepository(test_db_session)

    @pytest.fixture
    async def sample_user_data(self) -> dict:
        """Create sample user data for testing."""
        return UserFactory.create_user_data(email="<EMAIL>")

    async def test_create_user_success(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test successful user creation."""
        # Create user entity
        user = UserFactory.create_user_entity(**sample_user_data)
        
        # Execute
        created_user = await repository.create_user(user)
        
        # Verify
        assert created_user.id is not None
        assert created_user.email == sample_user_data["email"]
        assert created_user.first_name == sample_user_data["first_name"]
        assert created_user.last_name == sample_user_data["last_name"]
        assert created_user.training_experience_years == sample_user_data["training_experience_years"]
        assert created_user.is_active is True
        assert created_user.is_verified is False
        assert created_user.created_at is not None
        assert created_user.updated_at is not None

    async def test_create_user_duplicate_email(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test user creation with duplicate email."""
        # Create first user
        user1 = UserFactory.create_user_entity(**sample_user_data)
        await repository.create_user(user1)
        
        # Try to create second user with same email
        user2 = UserFactory.create_user_entity(**sample_user_data)
        
        # Execute & Verify
        with pytest.raises(DuplicateEmailError, match="already exists"):
            await repository.create_user(user2)

    async def test_get_user_by_id_success(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test successful user retrieval by ID."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        created_user = await repository.create_user(user)
        
        # Execute
        retrieved_user = await repository.get_user_by_id(created_user.id)
        
        # Verify
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email
        assert retrieved_user.first_name == created_user.first_name

    async def test_get_user_by_id_not_found(
        self,
        repository: UserRepository,
    ) -> None:
        """Test user retrieval by non-existent ID."""
        # Execute
        user = await repository.get_user_by_id(uuid4())
        
        # Verify
        assert user is None

    async def test_get_user_by_email_success(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test successful user retrieval by email."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        created_user = await repository.create_user(user)
        
        # Execute
        retrieved_user = await repository.get_user_by_email(sample_user_data["email"])
        
        # Verify
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email

    async def test_get_user_by_email_case_insensitive(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test user retrieval by email is case insensitive."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        await repository.create_user(user)
        
        # Execute with different case
        retrieved_user = await repository.get_user_by_email(sample_user_data["email"].upper())
        
        # Verify
        assert retrieved_user is not None
        assert retrieved_user.email == sample_user_data["email"].lower()

    async def test_get_user_by_email_not_found(
        self,
        repository: UserRepository,
    ) -> None:
        """Test user retrieval by non-existent email."""
        # Execute
        user = await repository.get_user_by_email("<EMAIL>")
        
        # Verify
        assert user is None

    async def test_update_user_success(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test successful user update."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        created_user = await repository.create_user(user)
        
        # Update user data
        created_user.first_name = "Updated"
        created_user.last_name = "Name"
        created_user.training_experience_years = 10
        
        # Execute
        updated_user = await repository.update_user(created_user)
        
        # Verify
        assert updated_user.first_name == "Updated"
        assert updated_user.last_name == "Name"
        assert updated_user.training_experience_years == 10
        assert updated_user.updated_at > created_user.created_at

    async def test_update_user_not_found(
        self,
        repository: UserRepository,
    ) -> None:
        """Test update of non-existent user."""
        # Create user entity with non-existent ID
        user = UserFactory.create_user_entity(
            id=uuid4(),
            email="<EMAIL>"
        )
        
        # Execute & Verify
        with pytest.raises(UserNotFoundError):
            await repository.update_user(user)

    async def test_delete_user_success(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test successful user deletion (soft delete)."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        created_user = await repository.create_user(user)
        
        # Execute
        result = await repository.delete_user(created_user.id)
        
        # Verify
        assert result is True
        
        # Verify user is soft deleted (not returned in normal queries)
        retrieved_user = await repository.get_user_by_id(created_user.id)
        assert retrieved_user is None

    async def test_delete_user_not_found(
        self,
        repository: UserRepository,
    ) -> None:
        """Test deletion of non-existent user."""
        # Execute
        result = await repository.delete_user(uuid4())
        
        # Verify
        assert result is False

    async def test_list_users_pagination(
        self,
        repository: UserRepository,
    ) -> None:
        """Test user listing with pagination."""
        # Create multiple users
        users = []
        for i in range(25):
            user_data = UserFactory.create_user_data(email=f"user{i}@example.com")
            user = UserFactory.create_user_entity(**user_data)
            created_user = await repository.create_user(user)
            users.append(created_user)
        
        # Execute - first page
        page1_users = await repository.list_users(page=1, page_size=10)
        
        # Verify
        assert len(page1_users) == 10
        
        # Execute - second page
        page2_users = await repository.list_users(page=2, page_size=10)
        
        # Verify
        assert len(page2_users) == 10
        
        # Verify no overlap
        page1_ids = {user.id for user in page1_users}
        page2_ids = {user.id for user in page2_users}
        assert page1_ids.isdisjoint(page2_ids)

    async def test_list_users_ordering(
        self,
        repository: UserRepository,
    ) -> None:
        """Test user listing is ordered by creation date."""
        # Create users with slight delay
        import asyncio
        
        user1_data = UserFactory.create_user_data(email="<EMAIL>")
        user1 = UserFactory.create_user_entity(**user1_data)
        created_user1 = await repository.create_user(user1)
        
        await asyncio.sleep(0.01)  # Small delay
        
        user2_data = UserFactory.create_user_data(email="<EMAIL>")
        user2 = UserFactory.create_user_entity(**user2_data)
        created_user2 = await repository.create_user(user2)
        
        # Execute
        users = await repository.list_users(page=1, page_size=10)
        
        # Verify - newest first
        user_ids = [user.id for user in users]
        assert user_ids.index(created_user2.id) < user_ids.index(created_user1.id)

    async def test_user_exists_by_email_true(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test user existence check returns True for existing user."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        await repository.create_user(user)
        
        # Execute
        exists = await repository.user_exists_by_email(sample_user_data["email"])
        
        # Verify
        assert exists is True

    async def test_user_exists_by_email_false(
        self,
        repository: UserRepository,
    ) -> None:
        """Test user existence check returns False for non-existent user."""
        # Execute
        exists = await repository.user_exists_by_email("<EMAIL>")
        
        # Verify
        assert exists is False

    async def test_user_exists_by_email_case_insensitive(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test user existence check is case insensitive."""
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        await repository.create_user(user)
        
        # Execute with different case
        exists = await repository.user_exists_by_email(sample_user_data["email"].upper())
        
        # Verify
        assert exists is True

    async def test_repository_performance_email_lookup(
        self,
        repository: UserRepository,
        sample_user_data: dict,
    ) -> None:
        """Test email lookup performance for mobile optimization."""
        import time
        
        # Create user
        user = UserFactory.create_user_entity(**sample_user_data)
        await repository.create_user(user)
        
        # Measure performance
        start_time = time.perf_counter()
        retrieved_user = await repository.get_user_by_email(sample_user_data["email"])
        end_time = time.perf_counter()
        
        # Verify
        assert retrieved_user is not None
        
        # Performance check (should be < 50ms for unit test with SQLite)
        query_time = (end_time - start_time) * 1000
        assert query_time < 50, f"Email lookup took {query_time:.2f}ms (should be < 50ms)"

    async def test_repository_handles_special_characters(
        self,
        repository: UserRepository,
    ) -> None:
        """Test repository handles special characters in names."""
        # Create user with special characters
        user_data = UserFactory.create_user_data(
            email="<EMAIL>",
            first_name="José",
            last_name="O'Connor-Smith"
        )
        user = UserFactory.create_user_entity(**user_data)
        created_user = await repository.create_user(user)
        
        # Retrieve and verify
        retrieved_user = await repository.get_user_by_id(created_user.id)
        assert retrieved_user.first_name == "José"
        assert retrieved_user.last_name == "O'Connor-Smith"

    async def test_repository_handles_null_values(
        self,
        repository: UserRepository,
    ) -> None:
        """Test repository handles null values correctly."""
        # Create user with minimal data
        user_data = UserFactory.create_user_data(
            email="<EMAIL>",
            first_name=None,
            last_name=None,
            training_experience_years=None
        )
        user = UserFactory.create_user_entity(**user_data)
        created_user = await repository.create_user(user)
        
        # Retrieve and verify
        retrieved_user = await repository.get_user_by_id(created_user.id)
        assert retrieved_user.first_name is None
        assert retrieved_user.last_name is None
        assert retrieved_user.training_experience_years is None
