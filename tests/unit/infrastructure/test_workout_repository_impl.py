"""
Unit tests for workout repository implementation.

Tests for SQLAlchemy workout repository implementation including
CRUD operations, search, and statistics.
"""

import pytest
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession

from app.domain.entities.workout import (
    ExerciseSet,
    ExerciseSetCreateRequest,
    ExerciseSetUpdateRequest,
    Workout,
    WorkoutCreateRequest,
    WorkoutSearchFilters,
    WorkoutStatus,
    WorkoutUpdateRequest,
)
from app.infrastructure.database.models.workout_model import ExerciseSetModel, WorkoutModel
from app.infrastructure.database.repositories.workout_repository_impl import WorkoutRepositoryImpl


@pytest.mark.unit
@pytest.mark.infrastructure
class TestWorkoutRepositoryImpl:
    """Test workout repository implementation."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def workout_repository(self, mock_session):
        """Create workout repository with mock session."""
        return WorkoutRepositoryImpl(mock_session)
    
    @pytest.fixture
    def sample_workout_model(self):
        """Create sample workout model."""
        now = datetime.utcnow()
        return WorkoutModel(
            id=uuid4(),
            user_id=uuid4(),
            name="Test Workout",
            workout_date=now,
            started_at=None,
            completed_at=None,
            total_volume_load=None,
            average_rpe=None,
            total_sets=0,
            total_exercises=0,
            duration_minutes=None,
            notes=None,
            is_template=False,
            template_name=None,
            created_at=now,
            updated_at=now,
            deleted_at=None,
            exercise_sets=[],
        )
    
    @pytest.fixture
    def sample_exercise_set_model(self):
        """Create sample exercise set model."""
        now = datetime.utcnow()
        return ExerciseSetModel(
            id=uuid4(),
            workout_id=uuid4(),
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
            reps_target=None,
            rir_target=None,
            rir_actual=None,
            rpe=None,
            rest_seconds=None,
            tempo=None,
            range_of_motion=None,
            is_warmup=False,
            is_dropset=False,
            is_failure=False,
            notes=None,
            created_at=now,
            updated_at=now,
        )
    
    async def test_create_workout(self, workout_repository, mock_session):
        """Test workout creation."""
        user_id = uuid4()
        request = WorkoutCreateRequest(
            name="Test Workout",
            workout_date=datetime.utcnow(),
        )

        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()

        # Mock the _model_to_entity method to return a proper workout entity
        with patch.object(workout_repository, '_model_to_entity') as mock_convert:
            expected_workout = Workout(
                id=uuid4(),
                user_id=user_id,
                name=request.name,
                workout_date=request.workout_date,
                started_at=None,
                completed_at=None,
                total_volume_load=None,
                average_rpe=None,
                total_sets=0,
                total_exercises=0,
                duration_minutes=None,
                notes=None,
                is_template=False,
                template_name=None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                deleted_at=None,
                exercise_sets=[],
            )
            mock_convert.return_value = expected_workout

            result = await workout_repository.create_workout(request, user_id)

            assert isinstance(result, Workout)
            assert result.name == request.name
            assert result.user_id == user_id
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
            mock_convert.assert_called_once()
    
    async def test_get_workout_by_id_found(self, workout_repository, mock_session, sample_workout_model):
        """Test getting workout by ID when found."""
        workout_id = sample_workout_model.id
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_workout_model
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.get_workout_by_id(workout_id)
        
        assert isinstance(result, Workout)
        assert result.id == workout_id
        mock_session.execute.assert_called_once()
    
    async def test_get_workout_by_id_not_found(self, workout_repository, mock_session):
        """Test getting workout by ID when not found."""
        workout_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.get_workout_by_id(workout_id)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_update_workout(self, workout_repository, mock_session, sample_workout_model):
        """Test workout update."""
        workout_id = sample_workout_model.id
        request = WorkoutUpdateRequest(name="Updated Workout")
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_workout_by_id for return value
        with patch.object(workout_repository, 'get_workout_by_id') as mock_get:
            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Updated Workout",
                workout_date=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout
            
            result = await workout_repository.update_workout(workout_id, request)
            
            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(workout_id)
    
    async def test_update_workout_not_found(self, workout_repository, mock_session):
        """Test workout update when not found."""
        workout_id = uuid4()
        request = WorkoutUpdateRequest(name="Updated Workout")
        
        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.update_workout(workout_id, request)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    async def test_delete_workout(self, workout_repository, mock_session):
        """Test workout deletion."""
        workout_id = uuid4()
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.delete_workout(workout_id)
        
        assert result is True
        mock_session.execute.assert_called_once()
    
    async def test_delete_workout_not_found(self, workout_repository, mock_session):
        """Test workout deletion when not found."""
        workout_id = uuid4()
        
        # Mock update result with no rows affected
        mock_result = Mock()
        mock_result.rowcount = 0
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.delete_workout(workout_id)
        
        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_search_workouts(self, workout_repository, mock_session, sample_workout_model):
        """Test workout search."""
        filters = WorkoutSearchFilters(user_id=uuid4())
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_workout_model]
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.search_workouts(filters)
        
        assert len(result) == 1
        assert isinstance(result[0], Workout)
        mock_session.execute.assert_called_once()
    
    async def test_count_workouts(self, workout_repository, mock_session):
        """Test workout count."""
        filters = WorkoutSearchFilters(user_id=uuid4())
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar.return_value = 5
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.count_workouts(filters)
        
        assert result == 5
        mock_session.execute.assert_called_once()
    
    async def test_get_active_workout(self, workout_repository, mock_session, sample_workout_model):
        """Test getting active workout."""
        user_id = uuid4()
        
        # Set workout as in progress
        sample_workout_model.started_at = datetime.utcnow()
        sample_workout_model.completed_at = None
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_workout_model
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.get_active_workout(user_id)
        
        assert isinstance(result, Workout)
        assert result.status == WorkoutStatus.IN_PROGRESS
        mock_session.execute.assert_called_once()
    
    async def test_start_workout(self, workout_repository, mock_session, sample_workout_model):
        """Test starting workout."""
        workout_id = sample_workout_model.id
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_workout_by_id for return value
        with patch.object(workout_repository, 'get_workout_by_id') as mock_get:
            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Test Workout",
                workout_date=datetime.utcnow(),
                started_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout
            
            result = await workout_repository.start_workout(workout_id)
            
            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(workout_id)
    
    async def test_complete_workout(self, workout_repository, mock_session, sample_workout_model):
        """Test completing workout."""
        workout_id = sample_workout_model.id
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_workout_by_id and _update_workout_statistics
        with patch.object(workout_repository, 'get_workout_by_id') as mock_get, \
             patch.object(workout_repository, '_update_workout_statistics') as mock_update_stats:
            
            mock_workout = Workout(
                id=workout_id,
                user_id=uuid4(),
                name="Test Workout",
                workout_date=datetime.utcnow(),
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_workout
            mock_update_stats.return_value = None
            
            result = await workout_repository.complete_workout(workout_id)
            
            assert result == mock_workout
            mock_session.execute.assert_called_once()
            mock_get.assert_called()  # Called twice - once after update, once after stats update
            mock_update_stats.assert_called_once_with(workout_id)
    
    async def test_create_exercise_set(self, workout_repository, mock_session, sample_exercise_set_model):
        """Test exercise set creation."""
        workout_id = uuid4()
        request = ExerciseSetCreateRequest(
            exercise_id=uuid4(),
            set_number=1,
            weight_kg=Decimal("100.0"),
            reps_completed=10,
        )
        
        # Mock session operations
        mock_session.add = Mock()
        mock_session.flush = AsyncMock()
        
        with patch('app.infrastructure.database.repositories.workout_repository_impl.uuid4') as mock_uuid:
            mock_uuid.return_value = sample_exercise_set_model.id
            
            result = await workout_repository.create_exercise_set(workout_id, request)
            
            assert isinstance(result, ExerciseSet)
            assert result.workout_id == workout_id
            assert result.exercise_id == request.exercise_id
            mock_session.add.assert_called_once()
            mock_session.flush.assert_called_once()
    
    async def test_get_exercise_set_by_id(self, workout_repository, mock_session, sample_exercise_set_model):
        """Test getting exercise set by ID."""
        set_id = sample_exercise_set_model.id
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_set_model
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.get_exercise_set_by_id(set_id)
        
        assert isinstance(result, ExerciseSet)
        assert result.id == set_id
        mock_session.execute.assert_called_once()
    
    async def test_update_exercise_set(self, workout_repository, mock_session, sample_exercise_set_model):
        """Test exercise set update."""
        set_id = sample_exercise_set_model.id
        request = ExerciseSetUpdateRequest(weight_kg=Decimal("120.0"))
        
        # Mock update result
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        # Mock get_exercise_set_by_id for return value
        with patch.object(workout_repository, 'get_exercise_set_by_id') as mock_get:
            mock_set = ExerciseSet(
                id=set_id,
                workout_id=uuid4(),
                exercise_id=uuid4(),
                set_number=1,
                weight_kg=Decimal("120.0"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            mock_get.return_value = mock_set
            
            result = await workout_repository.update_exercise_set(set_id, request)
            
            assert result == mock_set
            mock_session.execute.assert_called_once()
            mock_get.assert_called_once_with(set_id)
    
    async def test_delete_exercise_set(self, workout_repository, mock_session, sample_exercise_set_model):
        """Test exercise set deletion."""
        set_id = sample_exercise_set_model.id
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_exercise_set_model
        mock_session.execute.return_value = mock_result
        mock_session.delete = AsyncMock()
        
        result = await workout_repository.delete_exercise_set(set_id)
        
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.delete.assert_called_once_with(sample_exercise_set_model)
    
    async def test_delete_exercise_set_not_found(self, workout_repository, mock_session):
        """Test exercise set deletion when not found."""
        set_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.delete_exercise_set(set_id)
        
        assert result is False
        mock_session.execute.assert_called_once()
    
    async def test_get_workout_sets(self, workout_repository, mock_session, sample_exercise_set_model):
        """Test getting workout sets."""
        workout_id = uuid4()
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_exercise_set_model]
        mock_session.execute.return_value = mock_result
        
        result = await workout_repository.get_workout_sets(workout_id)
        
        assert len(result) == 1
        assert isinstance(result[0], ExerciseSet)
        mock_session.execute.assert_called_once()
    
    async def test_model_to_entity_conversion(self, workout_repository, sample_workout_model):
        """Test workout model to entity conversion."""
        result = workout_repository._model_to_entity(sample_workout_model)
        
        assert isinstance(result, Workout)
        assert result.id == sample_workout_model.id
        assert result.user_id == sample_workout_model.user_id
        assert result.name == sample_workout_model.name
    
    async def test_set_model_to_entity_conversion(self, workout_repository, sample_exercise_set_model):
        """Test exercise set model to entity conversion."""
        result = workout_repository._set_model_to_entity(sample_exercise_set_model)
        
        assert isinstance(result, ExerciseSet)
        assert result.id == sample_exercise_set_model.id
        assert result.workout_id == sample_exercise_set_model.workout_id
        assert result.exercise_id == sample_exercise_set_model.exercise_id
