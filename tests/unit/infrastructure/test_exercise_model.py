"""Tests for Exercise database models."""

from datetime import datetime
from uuid import uuid4


from app.infrastructure.database.models.exercise_model import (
    ExerciseAuditLogModel,
    ExerciseMediaModel,
    ExerciseModel,
)


class TestExerciseModel:
    """Test cases for ExerciseModel."""

    def test_create_exercise_model_minimal(self):
        """Test creating ExerciseModel with minimal data."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        exercise = ExerciseModel(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            name="Push-up",
            primary_muscle_group="chest",
            movement_pattern="push",
            difficulty_level="beginner",
            created_at=created_at,
            updated_at=updated_at,
        )

        assert exercise.id == exercise_id
        assert exercise.exercise_uuid == exercise_uuid
        assert exercise.name == "Push-up"
        assert exercise.primary_muscle_group == "chest"
        assert exercise.movement_pattern == "push"
        assert exercise.difficulty_level == "beginner"
        assert exercise.version == 1
        assert exercise.is_current_version is True
        assert exercise.is_active is True
        assert exercise.is_approved is False

    def test_create_exercise_model_full(self):
        """Test creating ExerciseModel with full data."""
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_by = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        exercise = ExerciseModel(
            id=exercise_id,
            exercise_uuid=exercise_uuid,
            version=2,
            is_current_version=True,
            name="Barbell Bench Press",
            description="Classic compound chest exercise",
            primary_muscle_group="chest",
            secondary_muscle_groups=["triceps", "shoulders"],
            movement_pattern="push",
            equipment_required=["barbell", "plate"],
            difficulty_level="intermediate",
            video_url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            form_cues=["Keep back flat", "Control the weight"],
            setup_instructions="Lie on bench, grip bar shoulder-width apart",
            execution_steps=["Lower bar to chest", "Press up explosively"],
            common_mistakes=["Bouncing off chest", "Flaring elbows too wide"],
            safety_notes="Always use a spotter for heavy weights",
            is_active=True,
            is_approved=True,
            approval_status="approved",
            created_by=created_by,
            created_at=created_at,
            updated_at=updated_at,
            version_notes="Updated form cues",
            change_reason="content_update",
        )

        assert exercise.version == 2
        assert exercise.description == "Classic compound chest exercise"
        assert exercise.secondary_muscle_groups == ["triceps", "shoulders"]
        assert exercise.equipment_required == ["barbell", "plate"]
        assert exercise.form_cues == ["Keep back flat", "Control the weight"]
        assert exercise.is_approved is True
        assert exercise.approval_status == "approved"
        assert exercise.created_by == created_by

    def test_exercise_model_string_representation(self):
        """Test ExerciseModel string representation."""
        exercise = ExerciseModel(
            id=uuid4(),
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group="chest",
            movement_pattern="push",
            difficulty_level="beginner",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        str_repr = str(exercise)
        assert "ExerciseModel" in str_repr
        assert "Test Exercise" in str_repr

    def test_exercise_model_table_name(self):
        """Test that ExerciseModel has correct table name."""
        assert ExerciseModel.__tablename__ == "exercises"

    def test_exercise_model_default_values(self):
        """Test ExerciseModel default values."""
        exercise = ExerciseModel(
            exercise_uuid=uuid4(),
            name="Test Exercise",
            primary_muscle_group="chest",
            movement_pattern="push",
            difficulty_level="beginner",
        )

        assert exercise.version == 1
        assert exercise.is_current_version is True
        assert exercise.is_active is True
        assert exercise.is_approved is False
        assert exercise.approval_status == "pending"
        assert exercise.change_reason == "initial_creation"


class TestExerciseMediaModel:
    """Test cases for ExerciseMediaModel."""

    def test_create_exercise_media_model_minimal(self):
        """Test creating ExerciseMediaModel with minimal data."""
        media_id = uuid4()
        exercise_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        media = ExerciseMediaModel(
            id=media_id,
            exercise_id=exercise_id,
            media_type="video",
            url="https://example.com/video.mp4",
            created_at=created_at,
            updated_at=updated_at,
        )

        assert media.id == media_id
        assert media.exercise_id == exercise_id
        assert media.media_type == "video"
        assert media.url == "https://example.com/video.mp4"
        assert media.sort_order == 0
        assert media.is_primary is False
        assert media.is_active is True

    def test_create_exercise_media_model_full(self):
        """Test creating ExerciseMediaModel with full data."""
        media_id = uuid4()
        exercise_id = uuid4()
        created_at = datetime.now()
        updated_at = datetime.now()

        media = ExerciseMediaModel(
            id=media_id,
            exercise_id=exercise_id,
            media_type="video",
            url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",
            title="Exercise Demo",
            description="Demonstration video",
            file_size_bytes=1024000,
            duration_seconds=60,
            width_pixels=1920,
            height_pixels=1080,
            mime_type="video/mp4",
            sort_order=1,
            is_primary=True,
            is_active=True,
            created_at=created_at,
            updated_at=updated_at,
        )

        assert media.title == "Exercise Demo"
        assert media.description == "Demonstration video"
        assert media.file_size_bytes == 1024000
        assert media.duration_seconds == 60
        assert media.width_pixels == 1920
        assert media.height_pixels == 1080
        assert media.mime_type == "video/mp4"
        assert media.sort_order == 1
        assert media.is_primary is True

    def test_exercise_media_model_string_representation(self):
        """Test ExerciseMediaModel string representation."""
        media_id = uuid4()
        exercise_id = uuid4()
        media = ExerciseMediaModel(
            id=media_id,
            exercise_id=exercise_id,
            media_type="video",
            url="https://example.com/video.mp4",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        str_repr = str(media)
        assert "ExerciseMediaModel" in str_repr
        assert str(media_id) in str_repr
        assert str(exercise_id) in str_repr
        assert "video" in str_repr

    def test_exercise_media_model_table_name(self):
        """Test that ExerciseMediaModel has correct table name."""
        assert ExerciseMediaModel.__tablename__ == "exercise_media"

    def test_exercise_media_model_default_values(self):
        """Test ExerciseMediaModel default values."""
        media = ExerciseMediaModel(
            exercise_id=uuid4(),
            media_type="video",
            url="https://example.com/video.mp4",
        )

        assert media.sort_order == 0
        assert media.is_primary is False
        assert media.is_active is True


class TestExerciseAuditLogModel:
    """Test cases for ExerciseAuditLogModel."""

    def test_create_exercise_audit_log_model_minimal(self):
        """Test creating ExerciseAuditLogModel with minimal data."""
        log_id = uuid4()
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        created_at = datetime.now()

        log = ExerciseAuditLogModel(
            id=log_id,
            exercise_id=exercise_id,
            exercise_uuid=exercise_uuid,
            action="created",
            created_at=created_at,
        )

        assert log.id == log_id
        assert log.exercise_id == exercise_id
        assert log.exercise_uuid == exercise_uuid
        assert log.action == "created"
        assert log.user_id is None
        assert log.ip_address is None
        assert log.user_agent is None

    def test_create_exercise_audit_log_model_full(self):
        """Test creating ExerciseAuditLogModel with full data."""
        log_id = uuid4()
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        user_id = uuid4()
        created_at = datetime.now()

        log = ExerciseAuditLogModel(
            id=log_id,
            exercise_id=exercise_id,
            exercise_uuid=exercise_uuid,
            action="updated",
            user_id=user_id,
            ip_address="***********",
            user_agent="Mozilla/5.0",
            field_changes={"name": "old_name -> new_name"},
            old_values={"name": "old_name"},
            new_values={"name": "new_name"},
            notes="Updated exercise name",
            created_at=created_at,
        )

        assert log.user_id == user_id
        assert log.ip_address == "***********"
        assert log.user_agent == "Mozilla/5.0"
        assert log.field_changes == {"name": "old_name -> new_name"}
        assert log.old_values == {"name": "old_name"}
        assert log.new_values == {"name": "new_name"}
        assert log.notes == "Updated exercise name"

    def test_exercise_audit_log_model_string_representation(self):
        """Test ExerciseAuditLogModel string representation."""
        log_id = uuid4()
        exercise_id = uuid4()
        exercise_uuid = uuid4()
        log = ExerciseAuditLogModel(
            id=log_id,
            exercise_id=exercise_id,
            exercise_uuid=exercise_uuid,
            action="created",
            created_at=datetime.now(),
        )

        str_repr = str(log)
        assert "ExerciseAuditLogModel" in str_repr
        assert str(log_id) in str_repr
        assert str(exercise_id) in str_repr
        assert "created" in str_repr

    def test_exercise_audit_log_model_table_name(self):
        """Test that ExerciseAuditLogModel has correct table name."""
        assert ExerciseAuditLogModel.__tablename__ == "exercise_audit_logs"

    def test_exercise_audit_log_model_required_fields(self):
        """Test that ExerciseAuditLogModel has all required fields."""
        log = ExerciseAuditLogModel(
            exercise_id=uuid4(),
            exercise_uuid=uuid4(),
            action="created",
            created_at=datetime.now(),
        )

        assert hasattr(log, "id")
        assert hasattr(log, "exercise_id")
        assert hasattr(log, "exercise_uuid")
        assert hasattr(log, "action")
        assert hasattr(log, "created_at")
        assert hasattr(log, "user_id")
        assert hasattr(log, "ip_address")
        assert hasattr(log, "user_agent")
        assert hasattr(log, "field_changes")
        assert hasattr(log, "old_values")
        assert hasattr(log, "new_values")
        assert hasattr(log, "notes")
