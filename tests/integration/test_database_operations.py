"""
Integration tests for database operations.

Tests database connections, transactions, repository implementations,
and data persistence across the application.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from uuid import uuid4

from app.infrastructure.database import get_database_session, db_manager
from app.infrastructure.database.repositories.user_repository_impl import UserRepositoryImpl
from app.domain.entities.user import User
from app.domain.exceptions.auth_exceptions import UserAlreadyExistsError, UserNotFoundError
from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.database
class TestDatabaseConnections:
    """Test database connection and session management."""
    
    async def test_database_session_creation(self) -> None:
        """Test database session can be created."""
        async for session in get_database_session():
            assert session is not None
            assert isinstance(session, AsyncSession)
            break
    
    async def test_database_query_execution(self) -> None:
        """Test basic database query execution."""
        async for session in get_database_session():
            result = await session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row is not None
            assert row.test_value == 1
            break
    
    async def test_database_transaction_rollback(self) -> None:
        """Test database transaction rollback functionality."""
        async for session in get_database_session():
            try:
                # Start transaction
                await session.begin()
                
                # Execute some operation
                await session.execute(text("SELECT 1"))
                
                # Force rollback
                await session.rollback()
                
                # Should not raise exception
                assert True
            except Exception as e:
                pytest.fail(f"Transaction rollback failed: {e}")
            break
    
    async def test_database_health_check(self) -> None:
        """Test database health check functionality."""
        try:
            # This should work if database is available
            health_status = await db_manager.health_check()
            assert health_status is not None
        except Exception:
            # If health_check method doesn't exist, test basic connection
            async for session in get_database_session():
                result = await session.execute(text("SELECT 1"))
                assert result is not None
                break


@pytest.mark.integration
@pytest.mark.database
class TestUserRepositoryIntegration:
    """Test user repository with real database operations."""
    
    @pytest.fixture
    async def repository(self) -> UserRepositoryImpl:
        """Create repository with real database session."""
        async for session in get_database_session():
            return UserRepositoryImpl(session)
    
    async def test_create_user_success(self, repository: UserRepositoryImpl) -> None:
        """Test successful user creation in database."""
        user_entity = UserFactory.create_user_entity()
        
        created_user = await repository.create(user_entity)
        
        assert created_user is not None
        assert created_user.id is not None
        assert created_user.email == user_entity.email
        assert created_user.first_name == user_entity.first_name
        assert created_user.last_name == user_entity.last_name
        assert created_user.training_experience_years == user_entity.training_experience_years
        assert created_user.is_active == user_entity.is_active
        assert created_user.created_at is not None
        assert created_user.updated_at is not None
    
    async def test_create_user_duplicate_email(self, repository: UserRepositoryImpl) -> None:
        """Test user creation with duplicate email."""
        user_entity = UserFactory.create_user_entity()
        
        # Create first user
        await repository.create(user_entity)
        
        # Try to create user with same email
        duplicate_user = UserFactory.create_user_entity()
        duplicate_user.email = user_entity.email
        
        with pytest.raises(UserAlreadyExistsError):
            await repository.create(duplicate_user)
    
    async def test_get_user_by_id_success(self, repository: UserRepositoryImpl) -> None:
        """Test successful user retrieval by ID."""
        # Create user first
        user_entity = UserFactory.create_user_entity()
        created_user = await repository.create(user_entity)
        
        # Retrieve by ID
        retrieved_user = await repository.get_by_id(created_user.id)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email
        assert retrieved_user.first_name == created_user.first_name
        assert retrieved_user.last_name == created_user.last_name
    
    async def test_get_user_by_id_not_found(self, repository: UserRepositoryImpl) -> None:
        """Test user retrieval with non-existent ID."""
        non_existent_id = uuid4()
        
        result = await repository.get_by_id(non_existent_id)
        
        assert result is None
    
    async def test_get_user_by_email_success(self, repository: UserRepositoryImpl) -> None:
        """Test successful user retrieval by email."""
        # Create user first
        user_entity = UserFactory.create_user_entity()
        created_user = await repository.create(user_entity)
        
        # Retrieve by email
        retrieved_user = await repository.get_by_email(created_user.email)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.email == created_user.email
    
    async def test_get_user_by_email_not_found(self, repository: UserRepositoryImpl) -> None:
        """Test user retrieval with non-existent email."""
        result = await repository.get_by_email("<EMAIL>")
        
        assert result is None
    
    async def test_update_user_success(self, repository: UserRepositoryImpl) -> None:
        """Test successful user update."""
        # Create user first
        user_entity = UserFactory.create_user_entity()
        created_user = await repository.create(user_entity)
        
        # Update user
        created_user.first_name = "UpdatedFirst"
        created_user.last_name = "UpdatedLast"
        created_user.training_experience_years = 10
        
        updated_user = await repository.update(created_user)
        
        assert updated_user is not None
        assert updated_user.id == created_user.id
        assert updated_user.first_name == "UpdatedFirst"
        assert updated_user.last_name == "UpdatedLast"
        assert updated_user.training_experience_years == 10
        assert updated_user.updated_at > updated_user.created_at
    
    async def test_update_user_not_found(self, repository: UserRepositoryImpl) -> None:
        """Test user update with non-existent user."""
        non_existent_user = UserFactory.create_user_entity()
        non_existent_user.id = uuid4()
        
        with pytest.raises(UserNotFoundError):
            await repository.update(non_existent_user)
    
    async def test_delete_user_success(self, repository: UserRepositoryImpl) -> None:
        """Test successful user deletion (soft delete)."""
        # Create user first
        user_entity = UserFactory.create_user_entity()
        created_user = await repository.create(user_entity)
        
        # Delete user
        result = await repository.delete(created_user.id)
        
        assert result is True
        
        # User should not be retrievable after deletion
        deleted_user = await repository.get_by_id(created_user.id)
        assert deleted_user is None
    
    async def test_delete_user_not_found(self, repository: UserRepositoryImpl) -> None:
        """Test user deletion with non-existent ID."""
        non_existent_id = uuid4()
        
        result = await repository.delete(non_existent_id)
        
        assert result is False
    
    async def test_exists_by_email_true(self, repository: UserRepositoryImpl) -> None:
        """Test email existence check for existing user."""
        # Create user first
        user_entity = UserFactory.create_user_entity()
        created_user = await repository.create(user_entity)
        
        # Check existence
        exists = await repository.exists_by_email(created_user.email)
        
        assert exists is True
    
    async def test_exists_by_email_false(self, repository: UserRepositoryImpl) -> None:
        """Test email existence check for non-existent user."""
        exists = await repository.exists_by_email("<EMAIL>")
        
        assert exists is False
    
    async def test_multiple_user_operations(self, repository: UserRepositoryImpl) -> None:
        """Test multiple user operations in sequence."""
        users = []
        
        # Create multiple users
        for i in range(5):
            user_entity = UserFactory.create_user_entity()
            user_entity.email = f"user{i}@example.com"
            created_user = await repository.create(user_entity)
            users.append(created_user)
        
        # Verify all users exist
        for user in users:
            retrieved_user = await repository.get_by_id(user.id)
            assert retrieved_user is not None
            assert retrieved_user.email == user.email
        
        # Update some users
        for i, user in enumerate(users[:3]):
            user.first_name = f"Updated{i}"
            updated_user = await repository.update(user)
            assert updated_user.first_name == f"Updated{i}"
        
        # Delete some users
        for user in users[3:]:
            result = await repository.delete(user.id)
            assert result is True
        
        # Verify deletions
        for user in users[3:]:
            deleted_user = await repository.get_by_id(user.id)
            assert deleted_user is None
