"""
Integration tests for error handling across the application.

Tests error scenarios, exception handling, and proper error responses
in various failure conditions.
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from unittest.mock import patch, AsyncMock
from sqlalchemy.exc import SQLAlchemyError

from app.main import app
from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.error_handling
class TestAPIErrorHandling:
    """Test API error handling and response formatting."""
    
    async def test_404_not_found_error(self, async_client: AsyncClient) -> None:
        """Test 404 error for non-existent endpoints."""
        response = await async_client.get("/api/v1/nonexistent")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], str)
    
    async def test_405_method_not_allowed(self, async_client: AsyncClient) -> None:
        """Test 405 error for wrong HTTP methods."""
        # Try POST on GET-only endpoint
        response = await async_client.post("/api/v1/health")
        
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
        
        data = response.json()
        assert "detail" in data
    
    async def test_422_validation_error(self, async_client: AsyncClient) -> None:
        """Test 422 validation error for invalid request data."""
        invalid_data = {
            "email": "invalid-email",
            "password": "short",
            "training_experience_years": -1
        }
        
        response = await async_client.post("/api/v1/auth/register", json=invalid_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], list)
        
        # Should have validation errors for each invalid field
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "email" in error_fields
        assert "password" in error_fields
        assert "training_experience_years" in error_fields
    
    async def test_400_bad_request_duplicate_user(self, async_client: AsyncClient) -> None:
        """Test 400 error for duplicate user registration."""
        user_data = UserFactory.create_user_data()
        
        # Register first user
        response1 = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response1.status_code == status.HTTP_201_CREATED
        
        # Try to register duplicate
        response2 = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST
        
        data = response2.json()
        assert "detail" in data
        assert "already exists" in data["detail"].lower()
    
    async def test_401_unauthorized_no_token(self, async_client: AsyncClient) -> None:
        """Test 401 error for missing authentication token."""
        response = await async_client.get("/api/v1/auth/me")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        data = response.json()
        assert "detail" in data
    
    async def test_401_unauthorized_invalid_token(self, async_client: AsyncClient) -> None:
        """Test 401 error for invalid authentication token."""
        headers = {"Authorization": "Bearer invalid_token"}
        
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        data = response.json()
        assert "detail" in data
    
    async def test_401_unauthorized_expired_token(self, async_client: AsyncClient) -> None:
        """Test 401 error for expired authentication token."""
        # Create an expired token (this would need JWT manipulation)
        expired_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid"
        headers = {"Authorization": f"Bearer {expired_token}"}
        
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_invalid_json_request(self, async_client: AsyncClient) -> None:
        """Test error handling for invalid JSON in request body."""
        # Send malformed JSON
        response = await async_client.post(
            "/api/v1/auth/register",
            content="invalid json content",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_missing_required_fields(self, async_client: AsyncClient) -> None:
        """Test error handling for missing required fields."""
        incomplete_data = {
            "email": "<EMAIL>"
            # Missing password and other required fields
        }
        
        response = await async_client.post("/api/v1/auth/register", json=incomplete_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        data = response.json()
        assert "detail" in data
        
        # Should have error for missing password
        error_fields = [error["loc"][-1] for error in data["detail"]]
        assert "password" in error_fields
    
    async def test_large_request_body(self, async_client: AsyncClient) -> None:
        """Test error handling for excessively large request bodies."""
        # Create a very large request
        large_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "A" * 10000,  # Very long name
            "last_name": "B" * 10000
        }
        
        response = await async_client.post("/api/v1/auth/register", json=large_data)
        
        # Should either reject or handle gracefully
        assert response.status_code in [
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_400_BAD_REQUEST
        ]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.error_handling
class TestDatabaseErrorHandling:
    """Test database error handling scenarios."""
    
    @patch('app.infrastructure.database.connection.db_manager')
    async def test_database_connection_failure(
        self, 
        mock_db_manager: AsyncMock,
        async_client: AsyncClient
    ) -> None:
        """Test handling of database connection failures."""
        # Mock database connection failure
        mock_db_manager.get_session.side_effect = SQLAlchemyError("Database connection failed")
        
        user_data = UserFactory.create_user_data()
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        # Should return 500 internal server error
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @patch('app.infrastructure.database.repositories.user_repository_impl.UserRepositoryImpl.create')
    async def test_database_transaction_failure(
        self,
        mock_create: AsyncMock,
        async_client: AsyncClient
    ) -> None:
        """Test handling of database transaction failures."""
        # Mock repository failure
        mock_create.side_effect = SQLAlchemyError("Transaction failed")
        
        user_data = UserFactory.create_user_data()
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        # Should handle database error gracefully
        assert response.status_code in [
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            status.HTTP_400_BAD_REQUEST
        ]


@pytest.mark.integration
@pytest.mark.error_handling
class TestSecurityErrorHandling:
    """Test security-related error handling."""
    
    async def test_sql_injection_attempt(self, async_client: AsyncClient) -> None:
        """Test handling of SQL injection attempts."""
        malicious_data = {
            "email": "<EMAIL>'; DROP TABLE users; --",
            "password": "SecurePass123!",
            "first_name": "'; DELETE FROM users; --"
        }
        
        response = await async_client.post("/api/v1/auth/register", json=malicious_data)
        
        # Should either reject malicious input or sanitize it
        if response.status_code == status.HTTP_201_CREATED:
            # If accepted, verify no SQL injection occurred
            data = response.json()
            user_str = str(data)
            assert "DROP TABLE" not in user_str
            assert "DELETE FROM" not in user_str
        else:
            # Should be validation error
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_xss_attempt(self, async_client: AsyncClient) -> None:
        """Test handling of XSS attempts."""
        xss_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "<script>alert('xss')</script>",
            "last_name": "javascript:alert('xss')"
        }
        
        response = await async_client.post("/api/v1/auth/register", json=xss_data)
        
        # Should either reject or sanitize XSS attempts
        if response.status_code == status.HTTP_201_CREATED:
            # If accepted, verify no script execution
            data = response.json()
            user_str = str(data)
            assert "<script>" not in user_str
            assert "javascript:" not in user_str
        else:
            # Should be validation error
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_path_traversal_attempt(self, async_client: AsyncClient) -> None:
        """Test handling of path traversal attempts."""
        # Try to access files outside API scope
        malicious_paths = [
            "/api/v1/../../../etc/passwd",
            "/api/v1/auth/../../config",
            "/api/v1/health/../../../app/config.py"
        ]
        
        for path in malicious_paths:
            response = await async_client.get(path)
            
            # Should return 404 or 400, not expose file system
            assert response.status_code in [
                status.HTTP_404_NOT_FOUND,
                status.HTTP_400_BAD_REQUEST
            ]
            
            # Should not expose file contents
            if response.status_code == status.HTTP_200_OK:
                content = response.text.lower()
                assert "root:" not in content  # Unix passwd file
                assert "secret" not in content
                assert "password" not in content


@pytest.mark.integration
@pytest.mark.error_handling
class TestErrorResponseFormat:
    """Test error response format consistency."""
    
    async def test_error_response_structure(self, async_client: AsyncClient) -> None:
        """Test that all error responses have consistent structure."""
        # Test various error scenarios
        error_scenarios = [
            ("/api/v1/nonexistent", "GET", None),  # 404
            ("/api/v1/health", "POST", None),      # 405
            ("/api/v1/auth/register", "POST", {"invalid": "data"}),  # 422
        ]
        
        for url, method, data in error_scenarios:
            if method == "GET":
                response = await async_client.get(url)
            elif method == "POST":
                response = await async_client.post(url, json=data)
            
            # All error responses should have detail field
            if response.status_code >= 400:
                error_data = response.json()
                assert "detail" in error_data
                
                # Detail should be string or list
                assert isinstance(error_data["detail"], (str, list))
    
    async def test_error_response_headers(self, async_client: AsyncClient) -> None:
        """Test error response headers."""
        response = await async_client.get("/api/v1/nonexistent")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        # Should have proper content type
        assert "content-type" in response.headers
        assert response.headers["content-type"] == "application/json"
    
    async def test_error_response_no_sensitive_data(self, async_client: AsyncClient) -> None:
        """Test that error responses don't expose sensitive data."""
        # Try various error scenarios
        response = await async_client.get("/api/v1/nonexistent")
        
        error_text = response.text.lower()
        
        # Should not expose sensitive information
        sensitive_terms = [
            "database_url", "secret_key", "password", "token",
            "traceback", "file path", "internal error"
        ]
        
        for term in sensitive_terms:
            assert term not in error_text, f"Error response exposed: {term}"
