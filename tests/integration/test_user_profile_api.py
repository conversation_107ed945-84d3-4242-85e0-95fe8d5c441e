"""Integration tests for user profile API endpoints.

Tests the complete user profile management flow with real services.
"""

import pytest
from httpx import AsyncClient

from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.auth
@pytest.mark.real
class TestUserProfileAPI:
    """Integration tests for user profile API endpoints."""

    async def test_get_user_profile_success(self, async_client: AsyncClient) -> None:
        """Test successful user profile retrieval."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Get user profile
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)

        # Verify
        assert response.status_code == 200
        profile_data = response.json()

        assert profile_data["email"] == user_data["email"]
        assert profile_data["first_name"] == user_data["first_name"]
        assert profile_data["last_name"] == user_data["last_name"]
        assert (
            profile_data["training_experience_years"]
            == user_data["training_experience_years"]
        )
        assert profile_data["is_active"] is True
        assert profile_data["is_verified"] is False
        assert "id" in profile_data
        assert "created_at" in profile_data
        assert "updated_at" in profile_data

        # Verify sensitive data is not exposed
        assert "hashed_password" not in profile_data
        assert "deleted_at" not in profile_data

    async def test_get_user_profile_unauthorized(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile retrieval without authentication."""
        response = await async_client.get("/api/v1/auth/me")

        assert response.status_code == 401
        error_data = response.json()
        assert "detail" in error_data

    async def test_get_user_profile_invalid_token(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile retrieval with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)

        assert response.status_code == 401
        error_data = response.json()
        assert "detail" in error_data

    async def test_update_user_profile_success(self, async_client: AsyncClient) -> None:
        """Test successful user profile update."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Update profile
        update_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "training_experience_years": 5,
        }
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )

        # Verify
        assert response.status_code == 200
        updated_profile = response.json()

        assert updated_profile["first_name"] == "Updated"
        assert updated_profile["last_name"] == "Name"
        assert updated_profile["training_experience_years"] == 5
        assert updated_profile["email"] == user_data["email"]  # Unchanged

    async def test_update_user_profile_partial(self, async_client: AsyncClient) -> None:
        """Test partial user profile update."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Update only first name
        update_data = {"first_name": "OnlyFirst"}
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )

        # Verify
        assert response.status_code == 200
        updated_profile = response.json()

        assert updated_profile["first_name"] == "OnlyFirst"
        assert updated_profile["last_name"] == user_data["last_name"]  # Unchanged
        assert (
            updated_profile["training_experience_years"]
            == user_data["training_experience_years"]
        )  # Unchanged

    async def test_update_user_profile_unauthorized(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile update without authentication."""
        update_data = {"first_name": "Unauthorized"}
        response = await async_client.put("/api/v1/auth/me", json=update_data)

        assert response.status_code == 401

    async def test_update_user_profile_invalid_data(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile update with invalid data."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Try to update with invalid training experience
        update_data = {"training_experience_years": -1}
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )

        # Verify
        assert response.status_code == 422
        error_data = response.json()
        assert "detail" in error_data

    async def test_update_user_profile_empty_strings(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile update with empty strings."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Update with empty strings
        update_data = {"first_name": "", "last_name": ""}
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )

        # Verify
        assert response.status_code == 200
        updated_profile = response.json()

        # Empty strings should be converted to null
        assert updated_profile["first_name"] is None
        assert updated_profile["last_name"] is None

    async def test_update_user_profile_special_characters(
        self, async_client: AsyncClient
    ) -> None:
        """Test user profile update with special characters."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]

        # Update with special characters
        update_data = {"first_name": "José", "last_name": "O'Connor-Smith"}
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )

        # Verify
        assert response.status_code == 200
        updated_profile = response.json()

        assert updated_profile["first_name"] == "José"
        assert updated_profile["last_name"] == "O'Connor-Smith"

    async def test_profile_api_mobile_performance(
        self, async_client: AsyncClient
    ) -> None:
        """Test profile API performance for mobile optimization."""
        import time

        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}

        # Test GET profile performance
        start_time = time.perf_counter()
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        end_time = time.perf_counter()

        assert response.status_code == 200
        get_time = (end_time - start_time) * 1000
        assert get_time < 200, f"GET profile took {get_time:.2f}ms (should be < 200ms)"

        # Test PUT profile performance
        update_data = {"first_name": "Performance"}
        start_time = time.perf_counter()
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )
        end_time = time.perf_counter()

        assert response.status_code == 200
        put_time = (end_time - start_time) * 1000
        assert put_time < 300, f"PUT profile took {put_time:.2f}ms (should be < 300ms)"

    async def test_profile_api_payload_size(self, async_client: AsyncClient) -> None:
        """Test profile API payload sizes for mobile optimization."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}

        # Test GET profile payload size
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200

        get_payload_size = len(response.content)
        assert (
            get_payload_size < 1024
        ), f"GET profile payload {get_payload_size} bytes (should be < 1KB)"

        # Test PUT profile payload size
        update_data = {"first_name": "Updated"}
        response = await async_client.put(
            "/api/v1/auth/me", json=update_data, headers=headers
        )
        assert response.status_code == 200

        put_payload_size = len(response.content)
        assert (
            put_payload_size < 1024
        ), f"PUT profile payload {put_payload_size} bytes (should be < 1KB)"

    async def test_profile_api_concurrent_updates(
        self, async_client: AsyncClient
    ) -> None:
        """Test profile API handles concurrent updates."""
        import asyncio

        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}

        # Perform concurrent updates
        async def update_profile(name_suffix: str):
            update_data = {"first_name": f"Concurrent{name_suffix}"}
            return await async_client.put(
                "/api/v1/auth/me", json=update_data, headers=headers
            )

        # Execute concurrent updates
        tasks = [update_profile(str(i)) for i in range(5)]
        responses = await asyncio.gather(*tasks)

        # Verify all updates succeeded (last one wins)
        for response in responses:
            assert response.status_code == 200

        # Verify final state
        final_response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert final_response.status_code == 200
        final_profile = final_response.json()
        assert final_profile["first_name"].startswith("Concurrent")

    async def test_profile_api_error_handling(self, async_client: AsyncClient) -> None:
        """Test profile API error handling."""
        # Register user first
        user_data = UserFactory.create_user_data(email="<EMAIL>")
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Get access token
        register_data = register_response.json()
        access_token = register_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}

        # Test various error scenarios
        error_scenarios = [
            ({"training_experience_years": 100}, 422),  # Too high
            ({"training_experience_years": "invalid"}, 422),  # Wrong type
            ({"invalid_field": "value"}, 422),  # Unknown field
        ]

        for update_data, expected_status in error_scenarios:
            response = await async_client.put(
                "/api/v1/auth/me", json=update_data, headers=headers
            )
            assert response.status_code == expected_status

            error_data = response.json()
            assert "detail" in error_data
