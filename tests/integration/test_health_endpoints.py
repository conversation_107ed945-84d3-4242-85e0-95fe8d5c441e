"""
Integration tests for health check endpoints.

Tests the health check API endpoints with real database connections.
"""

import pytest
from fastapi import status
from httpx import AsyncClient


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.health
class TestHealthEndpoints:
    """Integration tests for health check endpoints."""

    async def test_basic_health_check(self, async_client: AsyncClient) -> None:
        """Test basic health check endpoint."""
        response = await async_client.get("/api/v1/health")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "service" in data
        assert "version" in data
        assert "environment" in data

    async def test_database_health_check(self, async_client: AsyncClient) -> None:
        """Test database health check endpoint."""
        response = await async_client.get("/api/v1/health/db")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert data["database"] == "connected"
        assert "timestamp" in data
        assert "message" in data

    async def test_detailed_health_check(self, async_client: AsyncClient) -> None:
        """Test detailed health check endpoint."""
        response = await async_client.get("/api/v1/health/detailed")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "service" in data
        assert "components" in data
        assert "database" in data["components"]
        assert data["components"]["database"]["status"] == "healthy"

    async def test_health_check_performance(self, async_client: AsyncClient) -> None:
        """Test health check endpoint performance."""
        import time

        start_time = time.time()
        response = await async_client.get("/api/v1/health")
        end_time = time.time()

        assert response.status_code == status.HTTP_200_OK

        # Health check should be fast (< 2 seconds in test environment)
        response_time = end_time - start_time
        assert response_time < 2.0

    async def test_multiple_concurrent_health_checks(self, async_client: AsyncClient) -> None:
        """Test multiple concurrent health check requests."""
        import asyncio

        # Make 5 concurrent health check requests
        tasks = [
            async_client.get("/api/v1/health")
            for _ in range(5)
        ]

        responses = await asyncio.gather(*tasks)

        # All should succeed
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "healthy"

    async def test_health_check_headers(self, async_client: AsyncClient) -> None:
        """Test health check response headers."""
        response = await async_client.get("/api/v1/health")

        assert response.status_code == status.HTTP_200_OK

        # Should have appropriate headers
        assert "content-type" in response.headers
        assert response.headers["content-type"] == "application/json"

    async def test_health_check_no_sensitive_data(self, async_client: AsyncClient) -> None:
        """Test that health checks don't expose sensitive data."""
        response = await async_client.get("/api/v1/health")

        assert response.status_code == status.HTTP_200_OK

        response_text = response.text.lower()

        # Should not contain sensitive information
        sensitive_terms = [
            "password", "secret", "key", "token", "credential",
            "database_url", "redis_url", "private"
        ]

        for term in sensitive_terms:
            assert term not in response_text, f"Health check exposed sensitive term: {term}"
