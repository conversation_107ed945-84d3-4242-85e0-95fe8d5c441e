"""
Integration tests for health check endpoints.

Tests the health check API endpoints with real database connections.
"""

import pytest
from fastapi import status
from httpx import AsyncClient


@pytest.mark.integration
class TestHealthEndpoints:
    """Integration tests for health check endpoints."""

    async def test_basic_health_check(self, async_client: AsyncClient) -> None:
        """Test basic health check endpoint."""
        response = await async_client.get("/api/v1/health")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "service" in data
        assert "version" in data
        assert "environment" in data

    async def test_database_health_check(self, async_client: AsyncClient) -> None:
        """Test database health check endpoint."""
        response = await async_client.get("/api/v1/health/db")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert data["database"] == "connected"
        assert "timestamp" in data
        assert "message" in data

    async def test_detailed_health_check(self, async_client: AsyncClient) -> None:
        """Test detailed health check endpoint."""
        response = await async_client.get("/api/v1/health/detailed")

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "service" in data
        assert "components" in data
        assert "database" in data["components"]
        assert data["components"]["database"]["status"] == "healthy"
