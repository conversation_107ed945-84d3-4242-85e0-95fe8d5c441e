"""
Comprehensive API integration tests using test factories.

Tests realistic scenarios with varied data using the test factories
to ensure robust API behavior across different inputs and edge cases.
"""

import async<PERSON>
from typing import Any, Dict, List

import pytest
from fastapi import status
from httpx import AsyncClient

from tests.factories import (
    PerformanceTestFactory,
    SecurityTestFactory,
    TestDataFactory,
    UserFactory,
)


@pytest.mark.integration
@pytest.mark.api
class TestComprehensiveUserRegistration:
    """Comprehensive user registration tests with realistic data."""

    async def test_register_multiple_unique_users(
        self, async_client: AsyncClient
    ) -> None:
        """Test registering multiple users with unique data."""
        users_data = TestDataFactory.create_multiple_users(count=10)

        registered_users = []

        for user_data in users_data:
            response = await async_client.post("/api/v1/auth/register", json=user_data)

            assert response.status_code == status.HTTP_201_CREATED

            data = response.json()
            assert data["user"]["email"] == user_data["email"]
            assert data["user"]["first_name"] == user_data["first_name"]
            assert data["user"]["last_name"] == user_data["last_name"]

            registered_users.append(data["user"])

        # Verify all users have unique IDs and emails
        user_ids = [user["id"] for user in registered_users]
        user_emails = [user["email"] for user in registered_users]

        assert len(set(user_ids)) == len(user_ids)  # All IDs unique
        assert len(set(user_emails)) == len(user_emails)  # All emails unique

    async def test_register_with_edge_case_names(
        self, async_client: AsyncClient
    ) -> None:
        """Test registration with various edge case names."""
        edge_case_names = TestDataFactory.create_edge_case_names()

        for i, name in enumerate(edge_case_names):
            user_data = UserFactory.create_user_data(
                email=f"edge_case_{i}@test.com",
                first_name=name,
                last_name=f"LastName{i}",
            )

            response = await async_client.post("/api/v1/auth/register", json=user_data)

            # Some edge cases should be rejected, others accepted
            if response.status_code == status.HTTP_201_CREATED:
                data = response.json()
                # If accepted, verify the name is properly handled
                assert data["user"]["email"] == user_data["email"]
            else:
                # If rejected, should be a validation error
                assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_register_with_training_experience_edge_cases(
        self, async_client: AsyncClient
    ) -> None:
        """Test registration with edge case training experience values."""
        edge_cases = TestDataFactory.create_training_experience_edge_cases()

        for i, experience in enumerate(edge_cases):
            user_data = UserFactory.create_user_data(
                email=f"experience_{i}@test.com", training_experience_years=experience
            )

            response = await async_client.post("/api/v1/auth/register", json=user_data)

            if 0 <= experience <= 50:  # Valid range
                assert response.status_code == status.HTTP_201_CREATED
                data = response.json()
                assert data["user"]["training_experience_years"] == experience
            else:  # Invalid range
                assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_register_concurrent_unique_users(
        self, async_client: AsyncClient
    ) -> None:
        """Test concurrent registration of unique users."""
        concurrent_data = TestDataFactory.create_concurrent_test_data(count=15)

        async def register_user(user_data: Dict[str, Any]) -> Dict[str, Any]:
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            return {
                "status_code": response.status_code,
                "data": response.json() if response.status_code == 201 else None,
                "email": user_data["email"],
            }

        # Execute concurrent registrations
        tasks = [register_user(user_data) for user_data in concurrent_data]
        results = await asyncio.gather(*tasks)

        # All should succeed since emails are unique
        successful_registrations = [r for r in results if r["status_code"] == 201]
        assert len(successful_registrations) == len(concurrent_data)

        # Verify all have unique user IDs
        user_ids = [r["data"]["user"]["id"] for r in successful_registrations]
        assert len(set(user_ids)) == len(user_ids)


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.security
class TestSecurityWithRealisticData:
    """Security tests with realistic attack scenarios."""

    async def test_malicious_input_handling(self, async_client: AsyncClient) -> None:
        """Test handling of various malicious inputs."""
        injection_cases = SecurityTestFactory.create_injection_test_cases()

        for case in injection_cases[:10]:  # Test first 10 cases to avoid too long test
            user_data = UserFactory.create_user_data()
            user_data[case["field"]] = case["value"]

            response = await async_client.post("/api/v1/auth/register", json=user_data)

            # Should either reject malicious input or sanitize it
            if response.status_code == status.HTTP_201_CREATED:
                data = response.json()
                # Ensure no script execution or dangerous content
                user_str = str(data["user"])
                assert "<script>" not in user_str
                assert "DROP TABLE" not in user_str
                assert "javascript:" not in user_str
            else:
                # Should be validation error for malicious input
                assert response.status_code in [
                    status.HTTP_422_UNPROCESSABLE_ENTITY,
                    status.HTTP_400_BAD_REQUEST,
                ]

    async def test_sql_injection_attempts(self, async_client: AsyncClient) -> None:
        """Test SQL injection attempts in various fields."""
        sql_injections = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "1' UNION SELECT * FROM users--",
        ]

        for i, injection in enumerate(sql_injections):
            # Test in email field
            user_data = UserFactory.create_user_data(email=f"test{i}@example.com")
            user_data["first_name"] = injection

            response = await async_client.post("/api/v1/auth/register", json=user_data)

            # Should handle injection attempts safely
            if response.status_code == status.HTTP_201_CREATED:
                # If accepted, verify no SQL injection occurred
                data = response.json()
                assert "DROP TABLE" not in str(data)
                assert "UNION SELECT" not in str(data)
            else:
                # Should reject with validation error
                assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.integration
@pytest.mark.api
class TestRealisticUserWorkflows:
    """Test realistic user workflows and scenarios."""

    async def test_complete_user_lifecycle(self, async_client: AsyncClient) -> None:
        """Test complete user lifecycle from registration to profile updates."""
        # 1. Register user
        user_data = UserFactory.create_user_data()

        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == status.HTTP_201_CREATED

        registration_data = response.json()
        access_token = registration_data["access_token"]
        user_id = registration_data["user"]["id"]

        # 2. Login with same credentials
        login_data = {"email": user_data["email"], "password": user_data["password"]}

        response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == status.HTTP_200_OK

        login_response = response.json()
        assert login_response["user"]["id"] == user_id

        # 3. Get profile
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == status.HTTP_200_OK

        profile_data = response.json()
        assert profile_data["id"] == user_id
        assert profile_data["email"] == user_data["email"]

        # 4. Update profile multiple times
        for i in range(3):
            update_data = UserFactory.create_update_user_dto()

            response = await async_client.put(
                "/api/v1/auth/me", headers=headers, json=update_data.dict()
            )
            assert response.status_code == status.HTTP_200_OK

            updated_profile = response.json()
            assert updated_profile["first_name"] == update_data.first_name
            assert updated_profile["last_name"] == update_data.last_name
            assert (
                updated_profile["training_experience_years"]
                == update_data.training_experience_years
            )

    async def test_multiple_user_interactions(self, async_client: AsyncClient) -> None:
        """Test interactions between multiple users."""
        # Create multiple users
        users_data = TestDataFactory.create_multiple_users(count=5)
        registered_users = []

        # Register all users
        for user_data in users_data:
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == status.HTTP_201_CREATED

            data = response.json()
            registered_users.append(
                {
                    "user_data": user_data,
                    "registration_response": data,
                    "access_token": data["access_token"],
                }
            )

        # Each user performs various operations
        for user_info in registered_users:
            headers = {"Authorization": f"Bearer {user_info['access_token']}"}

            # Get own profile
            response = await async_client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == status.HTTP_200_OK

            # Update profile
            update_data = UserFactory.create_update_user_dto()
            response = await async_client.put(
                "/api/v1/auth/me", headers=headers, json=update_data.dict()
            )
            assert response.status_code == status.HTTP_200_OK

            # Login again
            login_data = {
                "email": user_info["user_data"]["email"],
                "password": user_info["user_data"]["password"],
            }
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == status.HTTP_200_OK

    async def test_error_recovery_scenarios(self, async_client: AsyncClient) -> None:
        """Test error scenarios and recovery."""
        user_data = UserFactory.create_user_data()

        # 1. Register user successfully
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == status.HTTP_201_CREATED

        registration_data = response.json()
        access_token = registration_data["access_token"]

        # 2. Try to register same user again (should fail)
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == status.HTTP_409_CONFLICT

        # 3. Login with wrong password (should fail)
        wrong_login = {"email": user_data["email"], "password": "WrongPassword123!"}
        response = await async_client.post("/api/v1/auth/login", json=wrong_login)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # 4. Login with correct password (should succeed)
        correct_login = {"email": user_data["email"], "password": user_data["password"]}
        response = await async_client.post("/api/v1/auth/login", json=correct_login)
        assert response.status_code == status.HTTP_200_OK

        # 5. Use invalid token (should fail)
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        response = await async_client.get("/api/v1/auth/me", headers=invalid_headers)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # 6. Use valid token (should succeed)
        valid_headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.get("/api/v1/auth/me", headers=valid_headers)
        assert response.status_code == status.HTTP_200_OK
