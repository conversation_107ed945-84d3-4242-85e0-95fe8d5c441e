"""
Performance integration tests.

Tests API performance, response times, and scalability characteristics.
"""

import asyncio
import time
from statistics import mean, median
from typing import Any, Dict, List

import pytest
from fastapi import status
from httpx import AsyncClient

from app.infrastructure.database.models.user_model import UserModel


@pytest.mark.integration
@pytest.mark.performance
class TestAPIPerformance:
    """Test API endpoint performance."""

    async def test_health_check_response_time(self, async_client: AsyncClient) -> None:
        """Test health check endpoint response time."""
        start_time = time.time()
        response = await async_client.get("/api/v1/health")
        end_time = time.time()

        response_time = (end_time - start_time) * 1000  # Convert to milliseconds

        assert response.status_code == status.HTTP_200_OK
        assert response_time < 100  # Should respond within 100ms

    async def test_database_health_check_response_time(
        self, async_client: AsyncClient
    ) -> None:
        """Test database health check response time."""
        start_time = time.time()
        response = await async_client.get("/api/v1/health/db")
        end_time = time.time()

        response_time = (end_time - start_time) * 1000

        assert response.status_code == status.HTTP_200_OK
        assert response_time < 200  # Database query should be fast

    async def test_user_registration_response_time(
        self, async_client: AsyncClient, sample_user_data: Dict[str, Any]
    ) -> None:
        """Test user registration response time."""
        # Use unique email for this test
        test_data = sample_user_data.copy()
        test_data["email"] = f"perf_test_{int(time.time())}@example.com"

        start_time = time.time()
        response = await async_client.post("/api/v1/auth/register", json=test_data)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000

        assert response.status_code == status.HTTP_201_CREATED
        assert response_time < 500  # Registration should complete within 500ms

    async def test_user_login_response_time(
        self,
        async_client: AsyncClient,
        created_user: UserModel,
        sample_user_data: Dict[str, Any],
    ) -> None:
        """Test user login response time."""
        login_data = {
            "email": created_user.email,
            "password": sample_user_data["password"],
        }

        start_time = time.time()
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000

        assert response.status_code == status.HTTP_200_OK
        assert response_time < 300  # Login should be fast

    async def test_profile_retrieval_response_time(
        self, async_client: AsyncClient, auth_headers: Dict[str, str]
    ) -> None:
        """Test profile retrieval response time."""
        start_time = time.time()
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000

        assert response.status_code == status.HTTP_200_OK
        assert response_time < 150  # Profile retrieval should be very fast


@pytest.mark.integration
@pytest.mark.performance
class TestConcurrentRequests:
    """Test performance under concurrent load."""

    async def test_concurrent_health_checks(self, async_client: AsyncClient) -> None:
        """Test concurrent health check requests."""
        num_requests = 50

        async def make_request():
            start_time = time.time()
            response = await async_client.get("/api/v1/health")
            end_time = time.time()
            return response.status_code, (end_time - start_time) * 1000

        # Execute concurrent requests
        tasks = [make_request() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)

        # Analyze results
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]

        # All requests should succeed
        assert all(code == status.HTTP_200_OK for code in status_codes)

        # Performance metrics
        avg_response_time = mean(response_times)
        median_response_time = median(response_times)
        max_response_time = max(response_times)

        assert avg_response_time < 200  # Average should be reasonable
        assert median_response_time < 150  # Median should be good
        assert max_response_time < 1000  # No request should take too long

    async def test_concurrent_user_registrations(
        self, async_client: AsyncClient
    ) -> None:
        """Test concurrent user registrations with unique emails."""
        num_requests = 20

        async def register_user(index: int):
            user_data = {
                "email": f"concurrent_user_{index}_{int(time.time())}@example.com",
                "password": "SecurePass123!",
                "first_name": f"User{index}",
                "last_name": "Test",
            }

            start_time = time.time()
            response = await async_client.post("/api/v1/auth/register", json=user_data)
            end_time = time.time()

            return response.status_code, (end_time - start_time) * 1000

        # Execute concurrent registrations
        tasks = [register_user(i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks)

        # Analyze results
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]

        # All registrations should succeed
        success_count = sum(
            1 for code in status_codes if code == status.HTTP_201_CREATED
        )
        assert success_count == num_requests

        # Performance should remain reasonable under load
        avg_response_time = mean(response_times)
        assert avg_response_time < 1000  # Should handle concurrent load well

    async def test_concurrent_logins_same_user(
        self,
        async_client: AsyncClient,
        created_user: UserModel,
        sample_user_data: Dict[str, Any],
    ) -> None:
        """Test concurrent login attempts for the same user."""
        num_requests = 30

        login_data = {
            "email": created_user.email,
            "password": sample_user_data["password"],
        }

        async def login_attempt():
            start_time = time.time()
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            end_time = time.time()
            return response.status_code, (end_time - start_time) * 1000

        # Execute concurrent logins
        tasks = [login_attempt() for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)

        # Analyze results
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]

        # All logins should succeed
        assert all(code == status.HTTP_200_OK for code in status_codes)

        # Performance metrics
        avg_response_time = mean(response_times)
        max_response_time = max(response_times)

        assert avg_response_time < 800  # Should handle concurrent logins
        assert max_response_time < 2000  # No login should take too long


@pytest.mark.integration
@pytest.mark.performance
class TestMemoryAndResourceUsage:
    """Test memory usage and resource consumption."""

    async def test_memory_usage_during_operations(
        self, async_client: AsyncClient, sample_user_data: Dict[str, Any]
    ) -> None:
        """Test memory usage during typical operations."""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Perform multiple operations
        for i in range(10):
            # Register user
            user_data = sample_user_data.copy()
            user_data["email"] = f"memory_test_{i}@example.com"

            response = await async_client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == status.HTTP_201_CREATED

            # Login
            login_data = {
                "email": user_data["email"],
                "password": user_data["password"],
            }
            response = await async_client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == status.HTTP_200_OK

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (less than 50MB for these operations)
        assert memory_increase < 50

    async def test_response_size_efficiency(
        self, async_client: AsyncClient, sample_user_data: Dict[str, Any]
    ) -> None:
        """Test that API responses are efficiently sized."""
        # Register user
        response = await async_client.post(
            "/api/v1/auth/register", json=sample_user_data
        )
        assert response.status_code == status.HTTP_201_CREATED

        # Check response size
        response_size = len(response.content)

        # Registration response should be reasonable size (less than 2KB)
        assert response_size < 2048

        # Login and check profile response size
        login_data = {
            "email": sample_user_data["email"],
            "password": sample_user_data["password"],
        }

        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        login_size = len(login_response.content)

        # Login response should also be reasonable
        assert login_size < 2048


@pytest.mark.integration
@pytest.mark.performance
class TestDatabasePerformance:
    """Test database-related performance."""

    async def test_database_connection_efficiency(
        self, async_client: AsyncClient
    ) -> None:
        """Test database connection handling efficiency."""
        # Make multiple requests that require database access
        num_requests = 50

        async def db_request():
            start_time = time.time()
            response = await async_client.get("/api/v1/health/db")
            end_time = time.time()
            return response.status_code, (end_time - start_time) * 1000

        # Execute requests sequentially to test connection reuse
        results = []
        for _ in range(num_requests):
            result = await db_request()
            results.append(result)

        # Analyze results
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]

        # All requests should succeed
        assert all(code == status.HTTP_200_OK for code in status_codes)

        # Response times should be consistent (good connection pooling)
        avg_response_time = mean(response_times)
        response_time_variance = max(response_times) - min(response_times)

        assert avg_response_time < 100  # Should be fast
        assert response_time_variance < 200  # Should be consistent
