"""
Integration tests specifically designed to boost API endpoint coverage.

These tests target the missing lines in auth.py and health.py endpoints
to push coverage towards 95%.
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from unittest.mock import patch, AsyncMock

from app.main import app
from app.domain.exceptions.auth_exceptions import (
    UserAlreadyExistsError,
    InvalidCredentialsError,
    UserNotFoundError,
    InvalidEmailError,
    WeakPasswordError,
    InactiveUserError
)
from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.coverage_boost
class TestAuthEndpointCoverage:
    """Integration tests to boost auth endpoint coverage."""
    
    async def test_register_user_already_exists_error(self) -> None:
        """Test registration with UserAlreadyExistsError."""
        user_data = UserFactory.create_user_data()
        
        with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = UserAlreadyExistsError("User already exists")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.post("/api/v1/auth/register", json=user_data)
                
                assert response.status_code == status.HTTP_400_BAD_REQUEST
                assert "already exists" in response.text.lower()
    
    async def test_register_invalid_email_error(self) -> None:
        """Test registration with InvalidEmailError."""
        user_data = UserFactory.create_user_data()
        
        with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = InvalidEmailError("Invalid email format")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.post("/api/v1/auth/register", json=user_data)
                
                assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
                assert "email" in response.text.lower()
    
    async def test_register_weak_password_error(self) -> None:
        """Test registration with WeakPasswordError."""
        user_data = UserFactory.create_user_data()
        
        with patch('app.presentation.api.v1.auth.get_register_user_use_case') as mock_dep:
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = WeakPasswordError("Password too weak")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.post("/api/v1/auth/register", json=user_data)
                
                assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
                assert "password" in response.text.lower()
    
    async def test_login_invalid_credentials_error(self) -> None:
        """Test login with InvalidCredentialsError."""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = InvalidCredentialsError("Invalid credentials")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_401_UNAUTHORIZED
                assert "credentials" in response.text.lower()
    
    async def test_login_inactive_user_error(self) -> None:
        """Test login with InactiveUserError."""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with patch('app.presentation.api.v1.auth.get_authenticate_user_use_case') as mock_dep:
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = InactiveUserError("User is inactive")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_401_UNAUTHORIZED
                assert "inactive" in response.text.lower()
    
    async def test_get_profile_user_not_found_error(self) -> None:
        """Test get profile with UserNotFoundError."""
        with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
             patch('app.presentation.api.v1.auth.get_user_profile_use_case') as mock_dep:
            
            mock_current_user.return_value = "non-existent-user-id"
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = UserNotFoundError("User not found")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get(
                    "/api/v1/auth/me",
                    headers={"Authorization": "Bearer fake-token"}
                )
                
                assert response.status_code == status.HTTP_404_NOT_FOUND
                assert "not found" in response.text.lower()
    
    async def test_update_profile_user_not_found_error(self) -> None:
        """Test update profile with UserNotFoundError."""
        update_data = {
            "first_name": "Updated",
            "last_name": "Name"
        }
        
        with patch('app.presentation.api.v1.auth.get_current_user') as mock_current_user, \
             patch('app.presentation.api.v1.auth.get_update_user_profile_use_case') as mock_dep:
            
            mock_current_user.return_value = "non-existent-user-id"
            mock_use_case = AsyncMock()
            mock_use_case.execute.side_effect = UserNotFoundError("User not found")
            mock_dep.return_value = mock_use_case
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.put(
                    "/api/v1/auth/me",
                    json=update_data,
                    headers={"Authorization": "Bearer fake-token"}
                )
                
                assert response.status_code == status.HTTP_404_NOT_FOUND
                assert "not found" in response.text.lower()


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.coverage_boost
class TestHealthEndpointCoverage:
    """Integration tests to boost health endpoint coverage."""
    
    async def test_health_endpoint_basic(self) -> None:
        """Test basic health endpoint."""
        async with AsyncClient(app=app, base_url="http://testserver") as client:
            response = await client.get("/api/v1/health")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "healthy"
            assert "timestamp" in data
            assert "service" in data
            assert "version" in data
    
    async def test_health_endpoint_detailed_all_healthy(self) -> None:
        """Test detailed health endpoint when all components are healthy."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock healthy components
            mock_db.health_check.return_value = True
            mock_memory.return_value.percent = 50.0
            mock_disk.return_value.percent = 60.0
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/detailed")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "healthy"
                assert "checks" in data
                assert "database" in data["checks"]
                assert "memory" in data["checks"]
                assert "disk" in data["checks"]
    
    async def test_health_endpoint_detailed_database_unhealthy(self) -> None:
        """Test detailed health endpoint with unhealthy database."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock unhealthy database
            mock_db.health_check.return_value = False
            mock_memory.return_value.percent = 30.0
            mock_disk.return_value.percent = 40.0
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/detailed")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "degraded"
                assert data["checks"]["database"]["status"] == "unhealthy"
    
    async def test_health_endpoint_detailed_high_memory(self) -> None:
        """Test detailed health endpoint with high memory usage."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock high memory usage
            mock_db.health_check.return_value = True
            mock_memory.return_value.percent = 95.0
            mock_disk.return_value.percent = 40.0
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/detailed")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "degraded"
                assert data["checks"]["memory"]["status"] == "unhealthy"
    
    async def test_health_endpoint_detailed_high_disk(self) -> None:
        """Test detailed health endpoint with high disk usage."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock high disk usage
            mock_db.health_check.return_value = True
            mock_memory.return_value.percent = 30.0
            mock_disk.return_value.percent = 98.0
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/detailed")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "degraded"
                assert data["checks"]["disk"]["status"] == "unhealthy"
    
    async def test_health_endpoint_detailed_multiple_issues(self) -> None:
        """Test detailed health endpoint with multiple unhealthy components."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock multiple issues
            mock_db.health_check.return_value = False
            mock_memory.return_value.percent = 92.0
            mock_disk.return_value.percent = 96.0
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/detailed")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "unhealthy"
                assert data["checks"]["database"]["status"] == "unhealthy"
                assert data["checks"]["memory"]["status"] == "unhealthy"
                assert data["checks"]["disk"]["status"] == "unhealthy"
    
    async def test_health_endpoint_database_only(self) -> None:
        """Test database-only health endpoint."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db:
            mock_db.health_check.return_value = True
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/database")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "healthy"
                assert "database" in data
                assert data["database"]["connected"] is True
    
    async def test_health_endpoint_database_unhealthy(self) -> None:
        """Test database health endpoint when database is unhealthy."""
        with patch('app.infrastructure.database.connection.db_manager') as mock_db:
            mock_db.health_check.return_value = False
            
            async with AsyncClient(app=app, base_url="http://testserver") as client:
                response = await client.get("/api/v1/health/database")
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "unhealthy"
                assert data["database"]["connected"] is False
