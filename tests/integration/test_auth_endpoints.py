"""
Integration tests for authentication API endpoints.

Tests complete authentication flows including registration, login,
profile management, and error handling scenarios.
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.infrastructure.database import get_database_session
from tests.factories import UserFactory


@pytest.mark.integration
@pytest.mark.api
@pytest.mark.auth
class TestAuthenticationEndpoints:
    """Test authentication API endpoints with real database."""
    
    async def test_user_registration_success(self, async_client: AsyncClient) -> None:
        """Test successful user registration."""
        user_data = UserFactory.create_user_data()
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        
        data = response.json()
        assert "user" in data
        assert "access_token" in data
        assert "refresh_token" in data
        assert "token_type" in data
        
        # Verify user data
        user = data["user"]
        assert user["email"] == user_data["email"]
        assert user["first_name"] == user_data["first_name"]
        assert user["last_name"] == user_data["last_name"]
        assert user["training_experience_years"] == user_data["training_experience_years"]
        assert user["is_active"] is True
        assert user["is_verified"] is False
        assert "id" in user
        assert "created_at" in user
        assert "updated_at" in user
        
        # Verify tokens
        assert data["token_type"] == "bearer"
        assert len(data["access_token"]) > 0
        assert len(data["refresh_token"]) > 0
    
    async def test_user_registration_duplicate_email(self, async_client: AsyncClient) -> None:
        """Test registration with duplicate email."""
        user_data = UserFactory.create_user_data()
        
        # Register first user
        response1 = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response1.status_code == status.HTTP_201_CREATED
        
        # Try to register with same email
        response2 = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST
        
        error_data = response2.json()
        assert "detail" in error_data
        assert "already exists" in error_data["detail"].lower()
    
    async def test_user_registration_invalid_data(self, async_client: AsyncClient) -> None:
        """Test registration with invalid data."""
        invalid_data = {
            "email": "invalid-email",
            "password": "weak",
            "training_experience_years": -1
        }
        
        response = await async_client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        error_data = response.json()
        assert "detail" in error_data
        assert isinstance(error_data["detail"], list)
    
    async def test_user_login_success(self, async_client: AsyncClient) -> None:
        """Test successful user login."""
        # Register user first
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        # Login with same credentials
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"]
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert "user" in data
        assert "access_token" in data
        assert "refresh_token" in data
        assert "token_type" in data
        
        # Verify user data matches registration
        user = data["user"]
        registered_user = register_response.json()["user"]
        assert user["id"] == registered_user["id"]
        assert user["email"] == registered_user["email"]
        assert user["first_name"] == registered_user["first_name"]
        assert user["last_name"] == registered_user["last_name"]
    
    async def test_user_login_invalid_credentials(self, async_client: AsyncClient) -> None:
        """Test login with invalid credentials."""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        error_data = response.json()
        assert "detail" in error_data
        assert "invalid" in error_data["detail"].lower()
    
    async def test_user_login_invalid_password(self, async_client: AsyncClient) -> None:
        """Test login with correct email but wrong password."""
        # Register user first
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        # Login with wrong password
        login_data = {
            "email": user_data["email"],
            "password": "wrongpassword"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_get_user_profile_success(self, async_client: AsyncClient) -> None:
        """Test successful profile retrieval."""
        # Register and login user
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        access_token = register_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Get profile
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        
        profile_data = response.json()
        registered_user = register_response.json()["user"]
        
        assert profile_data["id"] == registered_user["id"]
        assert profile_data["email"] == registered_user["email"]
        assert profile_data["first_name"] == registered_user["first_name"]
        assert profile_data["last_name"] == registered_user["last_name"]
        assert profile_data["training_experience_years"] == registered_user["training_experience_years"]
    
    async def test_get_user_profile_unauthorized(self, async_client: AsyncClient) -> None:
        """Test profile retrieval without authentication."""
        response = await async_client.get("/api/v1/auth/me")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_get_user_profile_invalid_token(self, async_client: AsyncClient) -> None:
        """Test profile retrieval with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_update_user_profile_success(self, async_client: AsyncClient) -> None:
        """Test successful profile update."""
        # Register user
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        access_token = register_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Update profile
        update_data = {
            "first_name": "UpdatedFirst",
            "last_name": "UpdatedLast",
            "training_experience_years": 5
        }
        
        response = await async_client.put("/api/v1/auth/me", headers=headers, json=update_data)
        assert response.status_code == status.HTTP_200_OK
        
        updated_profile = response.json()
        assert updated_profile["first_name"] == "UpdatedFirst"
        assert updated_profile["last_name"] == "UpdatedLast"
        assert updated_profile["training_experience_years"] == 5
        
        # Verify email and other fields unchanged
        original_user = register_response.json()["user"]
        assert updated_profile["email"] == original_user["email"]
        assert updated_profile["id"] == original_user["id"]
    
    async def test_update_user_profile_partial(self, async_client: AsyncClient) -> None:
        """Test partial profile update."""
        # Register user
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        access_token = register_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Update only first name
        update_data = {"first_name": "PartialUpdate"}
        
        response = await async_client.put("/api/v1/auth/me", headers=headers, json=update_data)
        assert response.status_code == status.HTTP_200_OK
        
        updated_profile = response.json()
        assert updated_profile["first_name"] == "PartialUpdate"
        
        # Verify other fields unchanged
        original_user = register_response.json()["user"]
        assert updated_profile["last_name"] == original_user["last_name"]
        assert updated_profile["training_experience_years"] == original_user["training_experience_years"]
    
    async def test_update_user_profile_unauthorized(self, async_client: AsyncClient) -> None:
        """Test profile update without authentication."""
        update_data = {"first_name": "Unauthorized"}
        
        response = await async_client.put("/api/v1/auth/me", json=update_data)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    async def test_complete_user_workflow(self, async_client: AsyncClient) -> None:
        """Test complete user workflow: register -> login -> get profile -> update profile."""
        # 1. Register
        user_data = UserFactory.create_user_data()
        register_response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        register_data = register_response.json()
        user_id = register_data["user"]["id"]
        
        # 2. Login
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"]
        }
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == status.HTTP_200_OK
        
        access_token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # 3. Get profile
        profile_response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert profile_response.status_code == status.HTTP_200_OK
        assert profile_response.json()["id"] == user_id
        
        # 4. Update profile
        update_data = {"first_name": "WorkflowTest"}
        update_response = await async_client.put("/api/v1/auth/me", headers=headers, json=update_data)
        assert update_response.status_code == status.HTTP_200_OK
        assert update_response.json()["first_name"] == "WorkflowTest"
        
        # 5. Verify update persisted
        final_profile_response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert final_profile_response.status_code == status.HTTP_200_OK
        assert final_profile_response.json()["first_name"] == "WorkflowTest"
