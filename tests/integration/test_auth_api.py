"""Comprehensive integration tests for authentication API endpoints.

Tests all authentication-related endpoints with various scenarios
including success cases, error cases, and edge cases.
"""

from typing import Any

import pytest
from fastapi import status
from httpx import AsyncClient

from app.infrastructure.database.models.user_model import UserModel


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.api
class TestUserRegistration:
    """Test user registration endpoint."""

    async def test_register_user_success(
        self, async_client: AsyncClient, sample_user_data: dict[str, Any]
    ) -> None:
        """Test successful user registration."""
        response = await async_client.post(
            "/api/v1/auth/register", json=sample_user_data
        )

        assert response.status_code == status.HTTP_201_CREATED

        data = response.json()
        assert "user" in data
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"

        # Verify user data
        user = data["user"]
        assert user["email"] == sample_user_data["email"]
        assert user["first_name"] == sample_user_data["first_name"]
        assert user["last_name"] == sample_user_data["last_name"]
        assert (
            user["training_experience_years"]
            == sample_user_data["training_experience_years"]
        )
        assert user["is_active"] is True
        assert user["is_verified"] is False
        assert "id" in user
        assert "created_at" in user
        assert "updated_at" in user

        # Verify tokens are valid strings
        assert isinstance(data["access_token"], str)
        assert len(data["access_token"]) > 50
        assert isinstance(data["refresh_token"], str)
        assert len(data["refresh_token"]) > 50

    async def test_register_user_minimal_data(self, async_client: AsyncClient) -> None:
        """Test registration with minimal required data."""
        minimal_data = {"email": "<EMAIL>", "password": "SecurePass123!"}

        response = await async_client.post("/api/v1/auth/register", json=minimal_data)

        assert response.status_code == status.HTTP_201_CREATED

        data = response.json()
        user = data["user"]
        assert user["email"] == minimal_data["email"]
        assert user["first_name"] is None
        assert user["last_name"] is None
        assert user["training_experience_years"] is None

    async def test_register_user_duplicate_email(
        self, async_client: AsyncClient, created_user: UserModel
    ) -> None:
        """Test registration with existing email."""
        duplicate_data = {
            "email": created_user.email,
            "password": "AnotherPass123!",
            "first_name": "Another",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=duplicate_data)

        assert response.status_code == status.HTTP_409_CONFLICT

        data = response.json()
        assert "already exists" in data["detail"].lower()

    @pytest.mark.parametrize(
        "invalid_email",
        [
            "invalid-email",
            "@example.com",
            "test@",
            "test.example.com",
            "",
            "   ",
        ],
    )
    async def test_register_user_invalid_email(
        self, async_client: AsyncClient, invalid_email: str
    ) -> None:
        """Test registration with invalid email formats."""
        user_data = {
            "email": invalid_email,
            "password": "SecurePass123!",
            "first_name": "Test",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.parametrize(
        "weak_password,expected_error",
        [
            ("weak", "at least 8 characters"),
            ("nouppercase123!", "uppercase"),
            ("NOLOWERCASE123!", "lowercase"),
            ("NoDigitHere!", "digit"),
            ("", "cannot be empty"),
        ],
    )
    async def test_register_user_weak_password(
        self, async_client: AsyncClient, weak_password: str, expected_error: str
    ) -> None:
        """Test registration with weak passwords."""
        user_data = {
            "email": "<EMAIL>",
            "password": weak_password,
            "first_name": "Test",
            "last_name": "User",
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        data = response.json()
        assert expected_error.lower() in str(data["detail"]).lower()

    async def test_register_user_invalid_training_years(
        self, async_client: AsyncClient
    ) -> None:
        """Test registration with invalid training experience years."""
        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "training_experience_years": -1,  # Invalid negative value
        }

        response = await async_client.post("/api/v1/auth/register", json=user_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.api
class TestUserLogin:
    """Test user login endpoint."""

    async def test_login_success(
        self,
        async_client: AsyncClient,
        created_user: UserModel,
        sample_user_data: dict[str, Any],
    ) -> None:
        """Test successful user login."""
        login_data = {
            "email": created_user.email,
            "password": sample_user_data["password"],
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert "user" in data
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"

        # Verify user data
        user = data["user"]
        assert user["email"] == created_user.email
        assert user["id"] == str(created_user.id)

    async def test_login_invalid_email(self, async_client: AsyncClient) -> None:
        """Test login with non-existent email."""
        login_data = {"email": "<EMAIL>", "password": "SecurePass123!"}

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        data = response.json()
        assert "invalid" in data["detail"].lower()

    async def test_login_invalid_password(
        self, async_client: AsyncClient, created_user: UserModel
    ) -> None:
        """Test login with wrong password."""
        login_data = {"email": created_user.email, "password": "WrongPassword123!"}

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        data = response.json()
        assert "invalid" in data["detail"].lower()

    async def test_login_missing_fields(self, async_client: AsyncClient) -> None:
        """Test login with missing required fields."""
        # Missing password
        response = await async_client.post(
            "/api/v1/auth/login", json={"email": "<EMAIL>"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Missing email
        response = await async_client.post(
            "/api/v1/auth/login", json={"password": "SecurePass123!"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Empty request
        response = await async_client.post("/api/v1/auth/login", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.api
class TestUserProfile:
    """Test user profile endpoints."""

    async def test_get_profile_success(
        self,
        async_client: AsyncClient,
        auth_headers: dict[str, str],
        created_user: UserModel,
    ) -> None:
        """Test successful profile retrieval."""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["email"] == created_user.email
        assert data["first_name"] == created_user.first_name
        assert data["last_name"] == created_user.last_name
        assert data["id"] == str(created_user.id)

    async def test_get_profile_unauthorized(self, async_client: AsyncClient) -> None:
        """Test profile retrieval without authentication."""
        response = await async_client.get("/api/v1/auth/me")

        assert response.status_code == status.HTTP_403_FORBIDDEN

    async def test_get_profile_invalid_token(
        self, async_client: AsyncClient, invalid_auth_headers: dict[str, str]
    ) -> None:
        """Test profile retrieval with invalid token."""
        response = await async_client.get(
            "/api/v1/auth/me", headers=invalid_auth_headers
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    async def test_get_profile_expired_token(
        self, async_client: AsyncClient, expired_auth_headers: dict[str, str]
    ) -> None:
        """Test profile retrieval with expired token."""
        response = await async_client.get(
            "/api/v1/auth/me", headers=expired_auth_headers
        )

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        data = response.json()
        assert "expired" in data["detail"].lower()

    async def test_update_profile_success(
        self,
        async_client: AsyncClient,
        auth_headers: dict[str, str],
        sample_user_update_data: dict[str, Any],
    ) -> None:
        """Test successful profile update."""
        response = await async_client.put(
            "/api/v1/auth/me", headers=auth_headers, json=sample_user_update_data
        )

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["first_name"] == sample_user_update_data["first_name"]
        assert data["last_name"] == sample_user_update_data["last_name"]
        assert (
            data["training_experience_years"]
            == sample_user_update_data["training_experience_years"]
        )

    async def test_update_profile_partial(
        self, async_client: AsyncClient, auth_headers: dict[str, str]
    ) -> None:
        """Test partial profile update."""
        update_data = {"first_name": "UpdatedName"}

        response = await async_client.put(
            "/api/v1/auth/me", headers=auth_headers, json=update_data
        )

        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data["first_name"] == "UpdatedName"

    async def test_update_profile_unauthorized(
        self, async_client: AsyncClient, sample_user_update_data: dict[str, Any]
    ) -> None:
        """Test profile update without authentication."""
        response = await async_client.put(
            "/api/v1/auth/me", json=sample_user_update_data
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
