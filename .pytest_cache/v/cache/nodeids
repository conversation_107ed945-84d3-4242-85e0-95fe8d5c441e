["tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_execute_inactive_user", "tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_execute_invalid_credentials", "tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_execute_success", "tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_response_structure", "tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_token_generation_failure", "tests/unit/application/test_authenticate_user_use_case.py::TestAuthenticateUserUseCase::test_user_dto_conversion", "tests/unit/application/test_get_user_profile_use_case.py::TestGetUserProfileUseCase::test_dto_conversion_completeness", "tests/unit/application/test_get_user_profile_use_case.py::TestGetUserProfileUseCase::test_execute_success", "tests/unit/application/test_get_user_profile_use_case.py::TestGetUserProfileUseCase::test_execute_user_not_found", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_execute_invalid_email", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_execute_minimal_data", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_execute_success", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_execute_user_already_exists", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_execute_weak_password", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_response_structure", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_token_generation_failure", "tests/unit/application/test_register_user_use_case.py::TestRegisterUserUseCase::test_user_dto_conversion", "tests/unit/application/test_update_user_profile_use_case.py::TestUpdateUserProfileUseCase::test_dto_conversion_after_update", "tests/unit/application/test_update_user_profile_use_case.py::TestUpdateUserProfileUseCase::test_execute_empty_update", "tests/unit/application/test_update_user_profile_use_case.py::TestUpdateUserProfileUseCase::test_execute_partial_update", "tests/unit/application/test_update_user_profile_use_case.py::TestUpdateUserProfileUseCase::test_execute_success", "tests/unit/application/test_update_user_profile_use_case.py::TestUpdateUserProfileUseCase::test_execute_user_not_found", "tests/unit/application/test_user_dto.py::TestCreateUserDTO::test_create_dto_equality", "tests/unit/application/test_user_dto.py::TestCreateUserDTO::test_create_dto_immutability", "tests/unit/application/test_user_dto.py::TestCreateUserDTO::test_create_dto_inequality", "tests/unit/application/test_user_dto.py::TestCreateUserDTO::test_create_with_all_fields", "tests/unit/application/test_user_dto.py::TestCreateUserDTO::test_create_with_minimal_fields", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_dto_dict_conversion", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_dto_dict_exclude_none", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_dto_equality", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_dto_immutability", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_with_all_fields", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_with_no_fields", "tests/unit/application/test_user_dto.py::TestUpdateUserDTO::test_update_with_partial_fields", "tests/unit/application/test_user_dto.py::TestUserDTO::test_dto_equality", "tests/unit/application/test_user_dto.py::TestUserDTO::test_dto_immutability", "tests/unit/application/test_user_dto.py::TestUserDTO::test_dto_string_representation", "tests/unit/application/test_user_dto.py::TestUserDTO::test_from_entity", "tests/unit/application/test_user_dto.py::TestUserDTO::test_from_entity_with_none_values", "tests/unit/domain/test_auth_service.py::TestAuthServiceAuthentication::test_authenticate_deleted_user", "tests/unit/domain/test_auth_service.py::TestAuthServiceAuthentication::test_authenticate_inactive_user", "tests/unit/domain/test_auth_service.py::TestAuthServiceAuthentication::test_authenticate_user_not_found", "tests/unit/domain/test_auth_service.py::TestAuthServiceAuthentication::test_authenticate_user_success", "tests/unit/domain/test_auth_service.py::TestAuthServiceAuthentication::test_authenticate_user_wrong_password", "tests/unit/domain/test_auth_service.py::TestAuthServiceRegistration::test_register_user_duplicate_email", "tests/unit/domain/test_auth_service.py::TestAuthServiceRegistration::test_register_user_minimal_data", "tests/unit/domain/test_auth_service.py::TestAuthServiceRegistration::test_register_user_success", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_get_user_by_id_deleted_user", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_get_user_by_id_not_found", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_get_user_by_id_success", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_update_user_profile_not_found", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_update_user_profile_partial", "tests/unit/domain/test_auth_service.py::TestAuthServiceUserManagement::test_update_user_profile_success", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_activate_deactivate", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_can_authenticate_active_user", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_can_authenticate_deleted_user", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_can_authenticate_inactive_user", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_empty_email", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_invalid_email", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_password_no_digit", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_password_no_lowercase", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_password_no_uppercase", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_valid_data", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_create_user_with_weak_password", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_email_normalization", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_entity_equality", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_get_full_name", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_get_full_name_first_only", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_get_full_name_last_only", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_get_full_name_none", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_soft_delete_and_restore", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_update_profile", "tests/unit/domain/test_user_entity.py::TestUserEntity::test_verify_email", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_bcrypt_rounds", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_hash_long_password", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_hash_password", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_hash_password_different_results", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_hash_special_characters", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_needs_update_fresh_hash", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_needs_update_invalid_hash", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_needs_update_old_hash", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_thread_safety", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_verify_password_case_sensitive", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_verify_password_correct", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_verify_password_empty", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_verify_password_incorrect", "tests/unit/infrastructure/test_password_handler.py::TestPasswordHandler::test_verify_password_invalid_hash", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_create_success", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_create_user_already_exists", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_delete_success", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_delete_user_not_found", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_exists_by_email_false", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_exists_by_email_true", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_get_by_email_not_found", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_get_by_email_success", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_get_by_id_not_found", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_get_by_id_success", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_model_to_entity_conversion", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_update_success", "tests/unit/infrastructure/test_user_repository_impl.py::TestUserRepositoryImpl::test_update_user_not_found", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_id_expired_token", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_id_invalid_token", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_id_success", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_id_wrong_token_type", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_not_found", "tests/unit/presentation/test_dependencies.py::TestAuthenticationDependencies::test_get_current_user_success", "tests/unit/presentation/test_dependencies.py::TestDependencyInjection::test_async_dependency_handling", "tests/unit/presentation/test_dependencies.py::TestDependencyInjection::test_dependency_chain_structure", "tests/unit/presentation/test_dependencies.py::TestDependencyInjection::test_dependency_isolation", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_auth_service", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_authenticate_user_use_case", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_register_user_use_case", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_update_user_profile_use_case", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_user_profile_use_case", "tests/unit/presentation/test_dependencies.py::TestServiceDependencies::test_get_user_repository", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_login_request_invalid_email", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_login_request_valid", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_login_response_valid", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_refresh_token_request_valid", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_refresh_token_response_valid", "tests/unit/presentation/test_schemas.py::TestAuthSchemas::test_schema_json_serialization", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_email[@example.com]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_email[]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_email[invalid-email]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_email[test.example.com]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_email[test@]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_invalid_training_years", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_minimal", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_name_validation", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_valid", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_weak_password[NOLOWERCASE123!]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_weak_password[NoDigitHere!]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_weak_password[]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_weak_password[nouppercase123!]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_create_request_weak_password[weak]", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_response_valid", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_response_with_none_values", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_update_request_empty", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_update_request_partial", "tests/unit/presentation/test_schemas.py::TestUserSchemas::test_user_update_request_valid", "tests/unit/test_config.py::TestSettings::test_api_prefix_validation", "tests/unit/test_config.py::TestSettings::test_cors_settings", "tests/unit/test_config.py::TestSettings::test_database_url_default", "tests/unit/test_config.py::TestSettings::test_database_url_override", "tests/unit/test_config.py::TestSettings::test_debug_false", "tests/unit/test_config.py::TestSettings::test_debug_truthy_values", "tests/unit/test_config.py::TestSettings::test_default_settings", "tests/unit/test_config.py::TestSettings::test_environment_specific_settings", "tests/unit/test_config.py::TestSettings::test_environment_variable_override", "tests/unit/test_config.py::TestSettings::test_jwt_settings", "tests/unit/test_config.py::TestSettings::test_list_environment_variables", "tests/unit/test_config.py::TestSettings::test_model_config", "tests/unit/test_config.py::TestSettings::test_numeric_environment_variables", "tests/unit/test_config.py::TestSettings::test_redis_url_default", "tests/unit/test_config.py::TestSettings::test_redis_url_override", "tests/unit/test_config.py::TestSettings::test_secret_key_generation", "tests/unit/test_config.py::TestSettings::test_settings_immutability", "tests/unit/test_config.py::TestSettings::test_settings_repr", "tests/unit/test_config.py::TestSettings::test_settings_validation", "tests/unit/test_config.py::TestSettings::test_short_secret_key_handling", "tests/unit/test_main.py::TestApplicationIntegration::test_app_basic_functionality", "tests/unit/test_main.py::TestApplicationIntegration::test_app_cors_headers", "tests/unit/test_main.py::TestApplicationIntegration::test_app_docs_endpoint", "tests/unit/test_main.py::TestApplicationIntegration::test_app_error_handling", "tests/unit/test_main.py::TestApplicationIntegration::test_app_method_not_allowed", "tests/unit/test_main.py::TestApplicationIntegration::test_app_openapi_endpoint", "tests/unit/test_main.py::TestApplicationIntegration::test_app_redoc_endpoint", "tests/unit/test_main.py::TestApplicationIntegration::test_app_request_validation", "tests/unit/test_main.py::TestApplicationIntegration::test_app_test_client_creation", "tests/unit/test_main.py::TestMainApplication::test_app_creation", "tests/unit/test_main.py::TestMainApplication::test_app_debug_mode", "tests/unit/test_main.py::TestMainApplication::test_app_exception_handlers", "tests/unit/test_main.py::TestMainApplication::test_app_metadata", "tests/unit/test_main.py::TestMainApplication::test_app_middleware_order", "tests/unit/test_main.py::TestMainApplication::test_app_openapi_configuration", "tests/unit/test_main.py::TestMainApplication::test_app_routes_registered", "tests/unit/test_main.py::TestMainApplication::test_app_shutdown_events", "tests/unit/test_main.py::TestMainApplication::test_app_startup_events", "tests/unit/test_main.py::TestMainApplication::test_cors_middleware_configured", "tests/unit/test_main.py::TestMainApplication::test_create_application_function"]