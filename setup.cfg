[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    alembic/versions,
    .mypy_cache,
    .pytest_cache
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401
    # Test files can have unused imports and long lines
    tests/*:F401,E501
max-complexity = 10
