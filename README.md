# Renaissance Periodization Training API

A comprehensive hypertrophy-focused training platform implementing evidence-based periodization principles with Clean Architecture.

## 🏗️ Architecture

This project follows Clean Architecture principles with clear separation of concerns:

- **Domain Layer**: Business logic, entities, and domain services
- **Application Layer**: Use cases and application-specific business rules  
- **Infrastructure Layer**: Database, external services, and framework implementations
- **Presentation Layer**: API endpoints, schemas, and HTTP concerns

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL 15+ (or use Docker)

### Development Setup

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/periodisation
   ```

2. **Set up Python environment**:
   ```bash
   # Create virtual environment
   python3.11 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements/dev.txt
   ```

3. **Environment configuration**:
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your settings
   # At minimum, set a secure SECRET_KEY
   ```

4. **Start services with Docker**:
   ```bash
   cd docker
   docker-compose up -d
   ```

5. **Run database migrations**:
   ```bash
   # Generate initial migration
   alembic revision --autogenerate -m "Initial user tables"
   
   # Apply migrations
   alembic upgrade head
   ```

6. **Start the development server**:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

7. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/api/v1/health
   - Database Admin: http://localhost:5050 (pgAdmin)

## 🧪 Testing

### Run Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test types
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m auth          # Authentication tests only
```

### Code Quality
```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .

# Run all quality checks
pre-commit run --all-files
```

## 📊 Current Progress - Phase 1: Foundation Infrastructure

### ✅ Completed
- [x] Clean Architecture project structure
- [x] Configuration management with Pydantic
- [x] Domain entities (User) with business rules
- [x] Domain services (AuthService)
- [x] Repository pattern with interfaces
- [x] Use cases for user management
- [x] Infrastructure layer setup
- [x] JWT authentication system
- [x] Password hashing with bcrypt
- [x] Database models with SQLAlchemy
- [x] Repository implementations
- [x] API endpoints for authentication
- [x] Pydantic schemas for requests/responses
- [x] FastAPI application setup
- [x] Docker development environment
- [x] Basic testing framework
- [x] Code quality tools configuration

### 🔄 In Progress
- [ ] Database migrations setup
- [ ] Integration tests completion
- [ ] CI/CD pipeline setup

### 📋 Next Steps
- [ ] Complete Phase 1 testing
- [ ] Set up local CI/CD pipeline
- [ ] Begin Phase 2: Exercise Database & Workout Logging

## 🔧 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get current user profile
- `PUT /api/v1/auth/me` - Update user profile

### Health Checks
- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/db` - Database health check
- `GET /api/v1/health/detailed` - Detailed system health

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    training_experience_years INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);
```

### Refresh Tokens Table
```sql
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🏛️ Project Structure

```
app/
├── domain/                 # Business logic layer
│   ├── entities/          # Core business objects
│   ├── repositories/      # Abstract interfaces
│   ├── services/          # Domain services
│   └── exceptions/        # Domain exceptions
├── application/           # Application layer
│   ├── use_cases/        # Application business rules
│   ├── dto/              # Data transfer objects
│   └── interfaces/       # Application interfaces
├── infrastructure/       # Infrastructure layer
│   ├── database/         # Database implementation
│   ├── auth/             # Authentication implementation
│   └── external/         # External service integrations
├── presentation/         # Presentation layer
│   ├── api/              # API routes
│   ├── schemas/          # Pydantic models
│   └── middleware/       # Custom middleware
├── config.py             # Configuration management
└── main.py               # FastAPI application entry point
```

## 🔐 Security Features

- JWT-based authentication with refresh tokens
- Password hashing with bcrypt (12 rounds)
- Input validation with Pydantic
- SQL injection prevention with parameterized queries
- CORS protection
- Rate limiting ready
- Secure headers middleware

## 📈 Performance Targets

- API Response Time: < 200ms (95th percentile)
- Database Query Time: < 50ms average
- Test Coverage: > 90%
- Memory Usage: < 256MB baseline

## 🤝 Contributing

1. Follow Clean Architecture principles
2. Write tests for all new features
3. Ensure code quality checks pass
4. Update documentation as needed
5. Follow conventional commit messages

## 📚 Documentation

- [Phase 1 PRD](docs/phase1_foundation_prd.md) - Detailed requirements for foundation
- [Master PRD](docs/master_prd.md) - Overall project roadmap
- API Documentation: Available at `/docs` when running

## 🐳 Docker Services

- **app**: FastAPI application (port 8000)
- **db**: PostgreSQL database (port 5432)
- **test_db**: Test database (port 5433)
- **redis**: Redis cache (port 6379)
- **pgadmin**: Database admin interface (port 5050)

## 📝 License

MIT License - see LICENSE file for details.
